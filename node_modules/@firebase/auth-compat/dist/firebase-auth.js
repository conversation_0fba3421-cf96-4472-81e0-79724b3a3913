((e,t)=>{"object"==typeof exports&&"undefined"!=typeof module?t(require("@firebase/app-compat"),require("@firebase/app")):"function"==typeof define&&define.amd?define(["@firebase/app-compat","@firebase/app"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).firebase,e.firebase.INTERNAL.modularAPIs)})(this,function(Gi,Ki){try{!function(){function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var F=t(Gi);let V=()=>{},x={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(n,e){if(!Array.isArray(n))throw Error("encodeByteArray takes an array as a parameter");this.init_();var i=e?this.byteToCharMapWebSafe_:this.byteToCharMap_,s=[];for(let r=0;r<n.length;r+=3){var a=n[r],o=r+1<n.length,l=o?n[r+1]:0,c=r+2<n.length,d=c?n[r+2]:0;let e=(15&l)<<2|d>>6,t=63&d;c||(t=64,o)||(e=64),s.push(i[a>>2],i[(3&a)<<4|l>>4],i[e],i[t])}return s.join("")},encodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?btoa(e):this.encodeByteArray((r=>{var n=[];let i=0;for(let t=0;t<r.length;t++){let e=r.charCodeAt(t);e<128?n[i++]=e:(e<2048?n[i++]=e>>6|192:(55296==(64512&e)&&t+1<r.length&&56320==(64512&r.charCodeAt(t+1))?(e=65536+((1023&e)<<10)+(1023&r.charCodeAt(++t)),n[i++]=e>>18|240,n[i++]=e>>12&63|128):n[i++]=e>>12|224,n[i++]=e>>6&63|128),n[i++]=63&e|128)}return n})(e),t)},decodeString(r,n){if(this.HAS_NATIVE_SUPPORT&&!n)return atob(r);{var i=this.decodeStringToByteArray(r,n);var s=[];let e=0,t=0;for(;e<i.length;){var a,o,l,c=i[e++];c<128?s[t++]=String.fromCharCode(c):191<c&&c<224?(a=i[e++],s[t++]=String.fromCharCode((31&c)<<6|63&a)):239<c&&c<365?(a=((7&c)<<18|(63&i[e++])<<12|(63&i[e++])<<6|63&i[e++])-65536,s[t++]=String.fromCharCode(55296+(a>>10)),s[t++]=String.fromCharCode(56320+(1023&a))):(o=i[e++],l=i[e++],s[t++]=String.fromCharCode((15&c)<<12|(63&o)<<6|63&l))}return s.join("");return}},decodeStringToByteArray(t,e){this.init_();var r=e?this.charToByteMapWebSafe_:this.charToByteMap_,n=[];for(let e=0;e<t.length;){var i=r[t.charAt(e++)],s=e<t.length?r[t.charAt(e)]:0,a=++e<t.length?r[t.charAt(e)]:64,o=++e<t.length?r[t.charAt(e)]:64;if(++e,null==i||null==s||null==a||null==o)throw new j;n.push(i<<2|s>>4),64!==a&&(n.push(s<<4&240|a>>2),64!==o)&&n.push(a<<6&192|o)}return n},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),(this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e)>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}};class j extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}let H=function(e){try{return x.decodeString(e,!0)}catch(e){console.error("base64Decode failed: ",e)}return null};let W=()=>(()=>{if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("Unable to locate global object.")})().__FIREBASE_DEFAULTS__,q=()=>{var e;return"undefined"!=typeof process&&void 0!==process.env&&(e=process.env.__FIREBASE_DEFAULTS__)?JSON.parse(e):void 0},B=()=>{if("undefined"!=typeof document){let e;try{e=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(e){return}var t=e&&H(e[1]);return t&&JSON.parse(t)}},z=()=>{try{return V()||W()||q()||B()}catch(e){console.info("Unable to get __FIREBASE_DEFAULTS__ due to: "+e)}};var n;function G(e){return e.endsWith(".cloudworkstations.dev")}let K={};let J=!1;function Y(e,t){if("undefined"!=typeof window&&"undefined"!=typeof document&&G(window.location.host)&&K[e]!==t&&!K[e]&&!J){K[e]=t;let l="__firebase__banner";let c=0<(()=>{var e,t={prod:[],emulator:[]};for(e of Object.keys(K))(K[e]?t.emulator:t.prod).push(e);return t})().prod.length;function d(e){return"__firebase__banner__"+e}function u(){var e=document.createElement("span");return e.style.cursor="pointer",e.style.marginLeft="16px",e.style.fontSize="24px",e.innerHTML=" &times;",e.onclick=()=>{var e;J=!0,(e=document.getElementById(l))&&e.remove()},e}function r(){var e,t=(e=>{let t=document.getElementById(e),r=!1;return t||((t=document.createElement("div")).setAttribute("id",e),r=!0),{created:r,element:t}})(l),r=d("text"),n=document.getElementById(r)||document.createElement("span"),i=d("learnmore"),s=document.getElementById(i)||document.createElement("a"),a=d("preprendIcon"),o=document.getElementById(a)||document.createElementNS("http://www.w3.org/2000/svg","svg");t.created&&(t=t.element,(e=t).style.display="flex",e.style.background="#7faaf0",e.style.position="fixed",e.style.bottom="5px",e.style.left="5px",e.style.padding=".5em",e.style.borderRadius="5px",e.style.alignItems="center",(e=s).setAttribute("id",i),e.innerText="Learn more",e.href="https://firebase.google.com/docs/studio/preview-apps#preview-backend",e.setAttribute("target","__blank"),e.style.paddingLeft="5px",e.style.textDecoration="underline",i=u(),e=a,(a=o).setAttribute("width","24"),a.setAttribute("id",e),a.setAttribute("height","24"),a.setAttribute("viewBox","0 0 24 24"),a.setAttribute("fill","none"),a.style.marginLeft="-6px",t.append(o,n,s,i),document.body.appendChild(t)),c?(n.innerText="Preview backend disconnected.",o.innerHTML=`<g clip-path="url(#clip0_6013_33858)">
<path d="M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6013_33858">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`):(o.innerHTML=`<g clip-path="url(#clip0_6083_34804)">
<path d="M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6083_34804">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`,n.innerText="Preview backend running in this workspace."),n.setAttribute("id",r)}"loading"===document.readyState?window.addEventListener("DOMContentLoaded",r):r()}}function l(){return"undefined"!=typeof navigator&&"string"==typeof navigator.userAgent?navigator.userAgent:""}function $(){var e=null==(e=z())?void 0:e.forceEnvironment;if("node"===e)return!0;if("browser"===e)return!1;try{return"[object process]"===Object.prototype.toString.call(global.process)}catch(e){return!1}}function X(){var e="object"==typeof chrome?chrome.runtime:"object"==typeof browser?browser.runtime:void 0;return"object"==typeof e&&void 0!==e.id}function Z(){return"object"==typeof navigator&&"ReactNative"===navigator.product}function Q(){var e=l();return 0<=e.indexOf("MSIE ")||0<=e.indexOf("Trident/")}function ee(){try{return"object"==typeof indexedDB}catch(e){return!1}}class c extends Error{constructor(e,t,r){super(t),this.code=e,this.customData=r,this.name="FirebaseError",Object.setPrototypeOf(this,c.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,te.prototype.create)}}class te{constructor(e,t,r){this.service=e,this.serviceName=t,this.errors=r}create(e,...t){var n,t=t[0]||{},r=this.service+"/"+e,e=this.errors[e],e=e?(n=t,e.replace(re,(e,t)=>{var r=n[t];return null!=r?String(r):`<${t}?>`})):"Error",e=this.serviceName+`: ${e} (${r}).`;return new c(r,e,t)}}let re=/\{\$([^}]+)}/g;function ne(e,t){if(e!==t){var r,n,i=Object.keys(e),s=Object.keys(t);for(r of i){if(!s.includes(r))return!1;var a=e[r],o=t[r];if(ie(a)&&ie(o)){if(!ne(a,o))return!1}else if(a!==o)return!1}for(n of s)if(!i.includes(n))return!1}return!0}function ie(e){return null!==e&&"object"==typeof e}function se(r){let n=[];for(let[t,e]of Object.entries(r))Array.isArray(e)?e.forEach(e=>{n.push(encodeURIComponent(t)+"="+encodeURIComponent(e))}):n.push(encodeURIComponent(t)+"="+encodeURIComponent(e));return n.length?"&"+n.join("&"):""}function ae(e){let r={};return e.replace(/^\?/,"").split("&").forEach(e=>{var t;e&&([e,t]=e.split("="),r[decodeURIComponent(e)]=decodeURIComponent(t))}),r}function oe(e){var t,r=e.indexOf("?");return r?(t=e.indexOf("#",r),e.substring(r,0<t?t:void 0)):""}class le{constructor(e,t){this.observers=[],this.unsubscribes=[],this.observerCount=0,this.task=Promise.resolve(),this.finalized=!1,this.onNoObservers=t,this.task.then(()=>{e(this)}).catch(e=>{this.error(e)})}next(t){this.forEachObserver(e=>{e.next(t)})}error(t){this.forEachObserver(e=>{e.error(t)}),this.close(t)}complete(){this.forEachObserver(e=>{e.complete()}),this.close()}subscribe(e,t,r){let n;if(void 0===e&&void 0===t&&void 0===r)throw new Error("Missing Observer.");void 0===(n=((e,t)=>{if("object"==typeof e&&null!==e)for(var r of t)if(r in e&&"function"==typeof e[r])return 1})(e,["next","error","complete"])?e:{next:e,error:t,complete:r}).next&&(n.next=ce),void 0===n.error&&(n.error=ce),void 0===n.complete&&(n.complete=ce);e=this.unsubscribeOne.bind(this,this.observers.length);return this.finalized&&this.task.then(()=>{try{this.finalError?n.error(this.finalError):n.complete()}catch(e){}}),this.observers.push(n),e}unsubscribeOne(e){void 0!==this.observers&&void 0!==this.observers[e]&&(delete this.observers[e],--this.observerCount,0===this.observerCount)&&void 0!==this.onNoObservers&&this.onNoObservers(this)}forEachObserver(t){if(!this.finalized)for(let e=0;e<this.observers.length;e++)this.sendOne(e,t)}sendOne(e,t){this.task.then(()=>{if(void 0!==this.observers&&void 0!==this.observers[e])try{t(this.observers[e])}catch(e){"undefined"!=typeof console&&console.error&&console.error(e)}})}close(e){this.finalized||(this.finalized=!0,void 0!==e&&(this.finalError=e),this.task.then(()=>{this.observers=void 0,this.onNoObservers=void 0}))}}function ce(){}function s(e){return e&&e._delegate?e._delegate:e}(e=n=n||{})[e.DEBUG=0]="DEBUG",e[e.VERBOSE=1]="VERBOSE",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.SILENT=5]="SILENT";let de={debug:n.DEBUG,verbose:n.VERBOSE,info:n.INFO,warn:n.WARN,error:n.ERROR,silent:n.SILENT},ue=n.INFO,he={[n.DEBUG]:"log",[n.VERBOSE]:"log",[n.INFO]:"info",[n.WARN]:"warn",[n.ERROR]:"error"},pe=(e,t,...r)=>{if(!(t<e.logLevel)){var n=(new Date).toISOString(),i=he[t];if(!i)throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`);console[i](`[${n}]  ${e.name}:`,...r)}};function me(e,t){var r={};for(i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(r[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,i=Object.getOwnPropertySymbols(e);n<i.length;n++)t.indexOf(i[n])<0&&Object.prototype.propertyIsEnumerable.call(e,i[n])&&(r[i[n]]=e[i[n]]);return r}class ge{constructor(e,t,r){this.name=e,this.instanceFactory=t,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}let fe={FACEBOOK:"facebook.com",GITHUB:"github.com",GOOGLE:"google.com",PASSWORD:"password",PHONE:"phone",TWITTER:"twitter.com"},ve={EMAIL_SIGNIN:"EMAIL_SIGNIN",PASSWORD_RESET:"PASSWORD_RESET",RECOVER_EMAIL:"RECOVER_EMAIL",REVERT_SECOND_FACTOR_ADDITION:"REVERT_SECOND_FACTOR_ADDITION",VERIFY_AND_CHANGE_EMAIL:"VERIFY_AND_CHANGE_EMAIL",VERIFY_EMAIL:"VERIFY_EMAIL"};function _e(){return{"dependent-sdk-initialized-before-auth":"Another Firebase SDK was initialized and is trying to use Auth before Auth is initialized. Please be sure to call `initializeAuth` or `getAuth` before starting any other Firebase SDK."}}function ye(){return{"admin-restricted-operation":"This operation is restricted to administrators only.","argument-error":"","app-not-authorized":"This app, identified by the domain where it's hosted, is not authorized to use Firebase Authentication with the provided API key. Review your key configuration in the Google API console.","app-not-installed":"The requested mobile application corresponding to the identifier (Android package name or iOS bundle ID) provided is not installed on this device.","captcha-check-failed":"The reCAPTCHA response token provided is either invalid, expired, already used or the domain associated with it does not match the list of whitelisted domains.","code-expired":"The SMS code has expired. Please re-send the verification code to try again.","cordova-not-ready":"Cordova framework is not ready.","cors-unsupported":"This browser is not supported.","credential-already-in-use":"This credential is already associated with a different user account.","custom-token-mismatch":"The custom token corresponds to a different audience.","requires-recent-login":"This operation is sensitive and requires recent authentication. Log in again before retrying this request.","dependent-sdk-initialized-before-auth":"Another Firebase SDK was initialized and is trying to use Auth before Auth is initialized. Please be sure to call `initializeAuth` or `getAuth` before starting any other Firebase SDK.","dynamic-link-not-activated":"Please activate Dynamic Links in the Firebase Console and agree to the terms and conditions.","email-change-needs-verification":"Multi-factor users must always have a verified email.","email-already-in-use":"The email address is already in use by another account.","emulator-config-failed":'Auth instance has already been used to make a network call. Auth can no longer be configured to use the emulator. Try calling "connectAuthEmulator()" sooner.',"expired-action-code":"The action code has expired.","cancelled-popup-request":"This operation has been cancelled due to another conflicting popup being opened.","internal-error":"An internal AuthError has occurred.","invalid-app-credential":"The phone verification request contains an invalid application verifier. The reCAPTCHA token response is either invalid or expired.","invalid-app-id":"The mobile app identifier is not registered for the current project.","invalid-user-token":"This user's credential isn't valid for this project. This can happen if the user's token has been tampered with, or if the user isn't for the project associated with this API key.","invalid-auth-event":"An internal AuthError has occurred.","invalid-verification-code":"The SMS verification code used to create the phone auth credential is invalid. Please resend the verification code sms and be sure to use the verification code provided by the user.","invalid-continue-uri":"The continue URL provided in the request is invalid.","invalid-cordova-configuration":"The following Cordova plugins must be installed to enable OAuth sign-in: cordova-plugin-buildinfo, cordova-universal-links-plugin, cordova-plugin-browsertab, cordova-plugin-inappbrowser and cordova-plugin-customurlscheme.","invalid-custom-token":"The custom token format is incorrect. Please check the documentation.","invalid-dynamic-link-domain":"The provided dynamic link domain is not configured or authorized for the current project.","invalid-email":"The email address is badly formatted.","invalid-emulator-scheme":"Emulator URL must start with a valid scheme (http:// or https://).","invalid-api-key":"Your API key is invalid, please check you have copied it correctly.","invalid-cert-hash":"The SHA-1 certificate hash provided is invalid.","invalid-credential":"The supplied auth credential is incorrect, malformed or has expired.","invalid-message-payload":"The email template corresponding to this action contains invalid characters in its message. Please fix by going to the Auth email templates section in the Firebase Console.","invalid-multi-factor-session":"The request does not contain a valid proof of first factor successful sign-in.","invalid-oauth-provider":"EmailAuthProvider is not supported for this operation. This operation only supports OAuth providers.","invalid-oauth-client-id":"The OAuth client ID provided is either invalid or does not match the specified API key.","unauthorized-domain":"This domain is not authorized for OAuth operations for your Firebase project. Edit the list of authorized domains from the Firebase console.","invalid-action-code":"The action code is invalid. This can happen if the code is malformed, expired, or has already been used.","wrong-password":"The password is invalid or the user does not have a password.","invalid-persistence-type":"The specified persistence type is invalid. It can only be local, session or none.","invalid-phone-number":"The format of the phone number provided is incorrect. Please enter the phone number in a format that can be parsed into E.164 format. E.164 phone numbers are written in the format [+][country code][subscriber number including area code].","invalid-provider-id":"The specified provider ID is invalid.","invalid-recipient-email":"The email corresponding to this action failed to send as the provided recipient email address is invalid.","invalid-sender":"The email template corresponding to this action contains an invalid sender email or name. Please fix by going to the Auth email templates section in the Firebase Console.","invalid-verification-id":"The verification ID used to create the phone auth credential is invalid.","invalid-tenant-id":"The Auth instance's tenant ID is invalid.","login-blocked":"Login blocked by user-provided method: {$originalMessage}","missing-android-pkg-name":"An Android Package Name must be provided if the Android App is required to be installed.","auth-domain-config-required":"Be sure to include authDomain when calling firebase.initializeApp(), by following the instructions in the Firebase console.","missing-app-credential":"The phone verification request is missing an application verifier assertion. A reCAPTCHA response token needs to be provided.","missing-verification-code":"The phone auth credential was created with an empty SMS verification code.","missing-continue-uri":"A continue URL must be provided in the request.","missing-iframe-start":"An internal AuthError has occurred.","missing-ios-bundle-id":"An iOS Bundle ID must be provided if an App Store ID is provided.","missing-or-invalid-nonce":"The request does not contain a valid nonce. This can occur if the SHA-256 hash of the provided raw nonce does not match the hashed nonce in the ID token payload.","missing-password":"A non-empty password must be provided","missing-multi-factor-info":"No second factor identifier is provided.","missing-multi-factor-session":"The request is missing proof of first factor successful sign-in.","missing-phone-number":"To send verification codes, provide a phone number for the recipient.","missing-verification-id":"The phone auth credential was created with an empty verification ID.","app-deleted":"This instance of FirebaseApp has been deleted.","multi-factor-info-not-found":"The user does not have a second factor matching the identifier provided.","multi-factor-auth-required":"Proof of ownership of a second factor is required to complete sign-in.","account-exists-with-different-credential":"An account already exists with the same email address but different sign-in credentials. Sign in using a provider associated with this email address.","network-request-failed":"A network AuthError (such as timeout, interrupted connection or unreachable host) has occurred.","no-auth-event":"An internal AuthError has occurred.","no-such-provider":"User was not linked to an account with the given provider.","null-user":"A null user object was provided as the argument for an operation which requires a non-null user object.","operation-not-allowed":"The given sign-in provider is disabled for this Firebase project. Enable it in the Firebase console, under the sign-in method tab of the Auth section.","operation-not-supported-in-this-environment":'This operation is not supported in the environment this application is running on. "location.protocol" must be http, https or chrome-extension and web storage must be enabled.',"popup-blocked":"Unable to establish a connection with the popup. It may have been blocked by the browser.","popup-closed-by-user":"The popup has been closed by the user before finalizing the operation.","provider-already-linked":"User can only be linked to one identity for the given provider.","quota-exceeded":"The project's quota for this operation has been exceeded.","redirect-cancelled-by-user":"The redirect operation has been cancelled by the user before finalizing.","redirect-operation-pending":"A redirect sign-in operation is already pending.","rejected-credential":"The request contains malformed or mismatching credentials.","second-factor-already-in-use":"The second factor is already enrolled on this account.","maximum-second-factor-count-exceeded":"The maximum allowed number of second factors on a user has been exceeded.","tenant-id-mismatch":"The provided tenant ID does not match the Auth instance's tenant ID",timeout:"The operation has timed out.","user-token-expired":"The user's credential is no longer valid. The user must sign in again.","too-many-requests":"We have blocked all requests from this device due to unusual activity. Try again later.","unauthorized-continue-uri":"The domain of the continue URL is not whitelisted.  Please whitelist the domain in the Firebase console.","unsupported-first-factor":"Enrolling a second factor or signing in with a multi-factor account requires sign-in with a supported first factor.","unsupported-persistence-type":"The current environment does not support the specified persistence type.","unsupported-tenant-operation":"This operation is not supported in a multi-tenant context.","unverified-email":"The operation requires a verified email.","user-cancelled":"The user did not grant your application the permissions it requested.","user-not-found":"There is no user record corresponding to this identifier. The user may have been deleted.","user-disabled":"The user account has been disabled by an administrator.","user-mismatch":"The supplied credentials do not correspond to the previously signed in user.","user-signed-out":"","weak-password":"The password must be 6 characters long or more.","web-storage-unsupported":"This browser is not supported or 3rd party cookies and data may be disabled.","already-initialized":"initializeAuth() has already been called with different options. To avoid this error, call initializeAuth() with the same options as when it was originally called, or call getAuth() to return the already initialized instance.","missing-recaptcha-token":"The reCAPTCHA token is missing when sending request to the backend.","invalid-recaptcha-token":"The reCAPTCHA token is invalid when sending request to the backend.","invalid-recaptcha-action":"The reCAPTCHA action is invalid when sending request to the backend.","recaptcha-not-enabled":"reCAPTCHA Enterprise integration is not enabled for this project.","missing-client-type":"The reCAPTCHA client type is missing when sending request to the backend.","missing-recaptcha-version":"The reCAPTCHA version is missing when sending request to the backend.","invalid-req-type":"Invalid request parameters.","invalid-recaptcha-version":"The reCAPTCHA version is invalid when sending request to the backend.","unsupported-password-policy-schema-version":"The password policy received from the backend uses a schema version that is not supported by this version of the Firebase SDK.","password-does-not-meet-requirements":"The password does not meet the requirements.","invalid-hosting-link-domain":"The provided Hosting link domain is not configured in Firebase Hosting or is not owned by the current project. This cannot be a default Hosting domain (`web.app` or `firebaseapp.com`)."}}let Ie=_e,we=new te("auth","Firebase",_e()),Te=new class{constructor(e){this.name=e,this._logLevel=ue,this._logHandler=pe,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in n))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?de[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,n.DEBUG,...e),this._logHandler(this,n.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,n.VERBOSE,...e),this._logHandler(this,n.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,n.INFO,...e),this._logHandler(this,n.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,n.WARN,...e),this._logHandler(this,n.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,n.ERROR,...e),this._logHandler(this,n.ERROR,...e)}}("@firebase/auth");function Ee(e,...t){Te.logLevel<=n.ERROR&&Te.error(`Auth (${Ki.SDK_VERSION}): `+e,...t)}function d(e,...t){throw Se(e,...t)}function u(e,...t){return Se(e,...t)}function be(e,t,r){r=Object.assign(Object.assign({},Ie()),{[t]:r});return new te("auth","Firebase",r).create(t,{appName:e.name})}function h(e){return be(e,"operation-not-supported-in-this-environment","Operations that alter the current user are not supported in conjunction with FirebaseServerApp")}function ke(e,t,r){if(!(t instanceof r))throw r.name!==t.constructor.name&&d(e,"argument-error"),be(e,"argument-error",`Type of ${t.constructor.name} does not match expected instance.`+"Did you pass a reference from a different Auth SDK?")}function Se(e,...t){var r,n;return"string"!=typeof e?(r=t[0],(n=[...t.slice(1)])[0]&&(n[0].appName=e.name),e._errorFactory.create(r,...n)):we.create(e,...t)}function m(e,t,...r){if(!e)throw Se(t,...r)}function i(e){e="INTERNAL ASSERTION FAILED: "+e;throw Ee(e),new Error(e)}function a(e,t){e||i(t)}function Re(){var e;return"undefined"!=typeof self&&(null==(e=self.location)?void 0:e.href)||""}function Ae(){return"http:"===Pe()||"https:"===Pe()}function Pe(){var e;return"undefined"!=typeof self&&(null==(e=self.location)?void 0:e.protocol)||null}class Ce{constructor(e,t){a((this.shortDelay=e)<(this.longDelay=t),"Short delay should be less than long delay!"),this.isMobile="undefined"!=typeof window&&!!(window.cordova||window.phonegap||window.PhoneGap)&&/ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(l())||Z()}get(){return"undefined"!=typeof navigator&&navigator&&"onLine"in navigator&&"boolean"==typeof navigator.onLine&&(Ae()||X()||"connection"in navigator)&&!navigator.onLine?Math.min(5e3,this.shortDelay):this.isMobile?this.longDelay:this.shortDelay}}function Oe(e,t){a(e.emulator,"Emulator should always be set here");e=e.emulator.url;return t?""+e+(t.startsWith("/")?t.slice(1):t):e}class Ne{static initialize(e,t,r){this.fetchImpl=e,t&&(this.headersImpl=t),r&&(this.responseImpl=r)}static fetch(){return this.fetchImpl||("undefined"!=typeof self&&"fetch"in self?self.fetch:"undefined"!=typeof globalThis&&globalThis.fetch?globalThis.fetch:"undefined"!=typeof fetch?fetch:void i("Could not find fetch implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill"))}static headers(){return this.headersImpl||("undefined"!=typeof self&&"Headers"in self?self.Headers:"undefined"!=typeof globalThis&&globalThis.Headers?globalThis.Headers:"undefined"!=typeof Headers?Headers:void i("Could not find Headers implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill"))}static response(){return this.responseImpl||("undefined"!=typeof self&&"Response"in self?self.Response:"undefined"!=typeof globalThis&&globalThis.Response?globalThis.Response:"undefined"!=typeof Response?Response:void i("Could not find Response implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill"))}}let Le={CREDENTIAL_MISMATCH:"custom-token-mismatch",MISSING_CUSTOM_TOKEN:"internal-error",INVALID_IDENTIFIER:"invalid-email",MISSING_CONTINUE_URI:"internal-error",INVALID_PASSWORD:"wrong-password",MISSING_PASSWORD:"missing-password",INVALID_LOGIN_CREDENTIALS:"invalid-credential",EMAIL_EXISTS:"email-already-in-use",PASSWORD_LOGIN_DISABLED:"operation-not-allowed",INVALID_IDP_RESPONSE:"invalid-credential",INVALID_PENDING_TOKEN:"invalid-credential",FEDERATED_USER_ID_ALREADY_LINKED:"credential-already-in-use",MISSING_REQ_TYPE:"internal-error",EMAIL_NOT_FOUND:"user-not-found",RESET_PASSWORD_EXCEED_LIMIT:"too-many-requests",EXPIRED_OOB_CODE:"expired-action-code",INVALID_OOB_CODE:"invalid-action-code",MISSING_OOB_CODE:"internal-error",CREDENTIAL_TOO_OLD_LOGIN_AGAIN:"requires-recent-login",INVALID_ID_TOKEN:"invalid-user-token",TOKEN_EXPIRED:"user-token-expired",USER_NOT_FOUND:"user-token-expired",TOO_MANY_ATTEMPTS_TRY_LATER:"too-many-requests",PASSWORD_DOES_NOT_MEET_REQUIREMENTS:"password-does-not-meet-requirements",INVALID_CODE:"invalid-verification-code",INVALID_SESSION_INFO:"invalid-verification-id",INVALID_TEMPORARY_PROOF:"invalid-credential",MISSING_SESSION_INFO:"missing-verification-id",SESSION_EXPIRED:"code-expired",MISSING_ANDROID_PACKAGE_NAME:"missing-android-pkg-name",UNAUTHORIZED_DOMAIN:"unauthorized-continue-uri",INVALID_OAUTH_CLIENT_ID:"invalid-oauth-client-id",ADMIN_ONLY_OPERATION:"admin-restricted-operation",INVALID_MFA_PENDING_CREDENTIAL:"invalid-multi-factor-session",MFA_ENROLLMENT_NOT_FOUND:"multi-factor-info-not-found",MISSING_MFA_ENROLLMENT_ID:"missing-multi-factor-info",MISSING_MFA_PENDING_CREDENTIAL:"missing-multi-factor-session",SECOND_FACTOR_EXISTS:"second-factor-already-in-use",SECOND_FACTOR_LIMIT_EXCEEDED:"maximum-second-factor-count-exceeded",BLOCKING_FUNCTION_ERROR_RESPONSE:"internal-error",RECAPTCHA_NOT_ENABLED:"recaptcha-not-enabled",MISSING_RECAPTCHA_TOKEN:"missing-recaptcha-token",INVALID_RECAPTCHA_TOKEN:"invalid-recaptcha-token",INVALID_RECAPTCHA_ACTION:"invalid-recaptcha-action",MISSING_CLIENT_TYPE:"missing-client-type",MISSING_RECAPTCHA_VERSION:"missing-recaptcha-version",INVALID_RECAPTCHA_VERSION:"invalid-recaptcha-version",INVALID_REQ_TYPE:"invalid-req-type"},De=["/v1/accounts:signInWithCustomToken","/v1/accounts:signInWithEmailLink","/v1/accounts:signInWithIdp","/v1/accounts:signInWithPassword","/v1/accounts:signInWithPhoneNumber","/v1/token"],Me=new Ce(3e4,6e4);function o(e,t){return e.tenantId&&!t.tenantId?Object.assign(Object.assign({},t),{tenantId:e.tenantId}):t}async function p(i,s,a,o,e={}){return Ue(i,e,async()=>{let e={},t={};o&&("GET"===s?t=o:e={body:JSON.stringify(o)});var r=se(Object.assign({key:i.config.apiKey},t)).slice(1),n=await i._getAdditionalHeaders(),n=(n["Content-Type"]="application/json",i.languageCode&&(n["X-Firebase-Locale"]=i.languageCode),Object.assign({method:s,headers:n},e));return"undefined"!=typeof navigator&&"Cloudflare-Workers"===navigator.userAgent||(n.referrerPolicy="no-referrer"),i.emulatorConfig&&G(i.emulatorConfig.host)&&(n.credentials="include"),Ne.fetch()(await Fe(i,i.config.apiHost,a,r),n)})}async function Ue(t,e,r){t._canInitEmulator=!1;e=Object.assign(Object.assign({},Le),e);try{var n=new Ve(t),i=await Promise.race([r(),n.promise]),s=(n.clearNetworkTimeout(),await i.json());if("needConfirmation"in s)throw xe(t,"account-exists-with-different-credential",s);if(i.ok&&!("errorMessage"in s))return s;var[a,o]=(i.ok?s.errorMessage:s.error.message).split(" : ");if("FEDERATED_USER_ID_ALREADY_LINKED"===a)throw xe(t,"credential-already-in-use",s);if("EMAIL_EXISTS"===a)throw xe(t,"email-already-in-use",s);if("USER_DISABLED"===a)throw xe(t,"user-disabled",s);var l=e[a]||a.toLowerCase().replace(/[_\s]+/g,"-");if(o)throw be(t,l,o);d(t,l)}catch(e){if(e instanceof c)throw e;d(t,"network-request-failed",{message:String(e)})}}async function r(e,t,r,n,i={}){t=await p(e,t,r,n,i);return"mfaPendingCredential"in t&&d(e,"multi-factor-auth-required",{_serverResponse:t}),t}async function Fe(e,t,r,n){t=""+t+r+"?"+n,n=e,e=n.config.emulator?Oe(e.config,t):e.config.apiScheme+"://"+t;if(De.includes(r)&&(await n._persistenceManagerAvailable,"COOKIE"===n._getPersistenceType()))return n._getPersistence()._getFinalTarget(e).toString();return e}class Ve{clearNetworkTimeout(){clearTimeout(this.timer)}constructor(e){this.auth=e,this.timer=null,this.promise=new Promise((e,t)=>{this.timer=setTimeout(()=>t(u(this.auth,"network-request-failed")),Me.get())})}}function xe(e,t,r){var n={appName:e.name},e=(r.email&&(n.email=r.email),r.phoneNumber&&(n.phoneNumber=r.phoneNumber),u(e,t,n));return e.customData._tokenResponse=r,e}function je(e){return void 0!==e&&void 0!==e.getResponse}function He(e){return void 0!==e&&void 0!==e.enterprise}class We{constructor(e){if(this.siteKey="",this.recaptchaEnforcementState=[],void 0===e.recaptchaKey)throw new Error("recaptchaKey undefined");this.siteKey=e.recaptchaKey.split("/")[3],this.recaptchaEnforcementState=e.recaptchaEnforcementState}getProviderEnforcementState(t){if(this.recaptchaEnforcementState&&0!==this.recaptchaEnforcementState.length)for(let e of this.recaptchaEnforcementState)if(e.provider&&e.provider===t){switch(e.enforcementState){case"ENFORCE":return"ENFORCE";case"AUDIT":return"AUDIT";case"OFF":return"OFF";default:return"ENFORCEMENT_STATE_UNSPECIFIED"}return}return null}isProviderEnabled(e){return"ENFORCE"===this.getProviderEnforcementState(e)||"AUDIT"===this.getProviderEnforcementState(e)}isAnyProviderEnabled(){return this.isProviderEnabled("EMAIL_PASSWORD_PROVIDER")||this.isProviderEnabled("PHONE_PROVIDER")}}async function qe(e,t){return p(e,"GET","/v2/recaptchaConfig",o(e,t))}async function Be(e,t){return p(e,"POST","/v1/accounts:lookup",t)}function ze(e){if(e)try{var t=new Date(Number(e));if(!isNaN(t.getTime()))return t.toUTCString()}catch(e){}}function Ge(e){return 1e3*Number(e)}function Ke(e){var[e,t,r]=e.split(".");if(void 0===e||void 0===t||void 0===r)return Ee("JWT malformed, contained fewer than 3 sections"),null;try{var n=H(t);return n?JSON.parse(n):(Ee("Failed to decode base64 JWT payload"),null)}catch(e){return Ee("Caught error parsing JWT payload as JSON",null==e?void 0:e.toString()),null}}function Je(e){e=Ke(e);return m(e,"internal-error"),m(void 0!==e.exp,"internal-error"),m(void 0!==e.iat,"internal-error"),Number(e.exp)-Number(e.iat)}async function g(t,e,r=!1){if(r)return e;try{return await e}catch(e){throw e instanceof c&&(r=[e.code][0],"auth/user-disabled"===r||"auth/user-token-expired"===r)&&(t.auth.currentUser===t&&await t.auth.signOut()),e}}class Ye{constructor(e){this.user=e,this.isRunning=!1,this.timerId=null,this.errorBackoff=3e4}_start(){this.isRunning||(this.isRunning=!0,this.schedule())}_stop(){this.isRunning&&(this.isRunning=!1,null!==this.timerId)&&clearTimeout(this.timerId)}getInterval(e){return e?(e=this.errorBackoff,this.errorBackoff=Math.min(2*this.errorBackoff,96e4),e):(this.errorBackoff=3e4,e=(null!=(e=this.user.stsTokenManager.expirationTime)?e:0)-Date.now()-3e5,Math.max(0,e))}schedule(e=!1){this.isRunning&&(e=this.getInterval(e),this.timerId=setTimeout(async()=>{await this.iteration()},e))}async iteration(){try{await this.user.getIdToken(!0)}catch(e){return void("auth/network-request-failed"===(null==e?void 0:e.code)&&this.schedule(!0))}this.schedule()}}class $e{constructor(e,t){this.createdAt=e,this.lastLoginAt=t,this._initializeTime()}_initializeTime(){this.lastSignInTime=ze(this.lastLoginAt),this.creationTime=ze(this.createdAt)}_copy(e){this.createdAt=e.createdAt,this.lastLoginAt=e.lastLoginAt,this._initializeTime()}toJSON(){return{createdAt:this.createdAt,lastLoginAt:this.lastLoginAt}}}async function Xe(e){var t=e.auth,r=await e.getIdToken(),r=await g(e,Be(t,{idToken:r})),t=(m(null==r?void 0:r.users.length,t,"internal-error"),r.users[0]),r=(e._notifyReloadListener(t),null!=(r=t.providerUserInfo)&&r.length?Ze(t.providerUserInfo):[]),r=((e,r)=>[...e=e.filter(t=>!r.some(e=>e.providerId===t.providerId)),...r])(e.providerData,r),n=!(e.email&&t.passwordHash||null!=r&&r.length),n=!!e.isAnonymous&&n,r={uid:t.localId,displayName:t.displayName||null,photoURL:t.photoUrl||null,email:t.email||null,emailVerified:t.emailVerified||!1,phoneNumber:t.phoneNumber||null,tenantId:t.tenantId||null,providerData:r,metadata:new $e(t.createdAt,t.lastLoginAt),isAnonymous:n};Object.assign(e,r)}function Ze(e){return e.map(e=>{var t=e.providerId,e=me(e,["providerId"]);return{providerId:t,uid:e.rawId||"",displayName:e.displayName||null,email:e.email||null,phoneNumber:e.phoneNumber||null,photoURL:e.photoUrl||null}})}class Qe{constructor(){this.refreshToken=null,this.accessToken=null,this.expirationTime=null}get isExpired(){return!this.expirationTime||Date.now()>this.expirationTime-3e4}updateFromServerResponse(e){m(e.idToken,"internal-error"),m(void 0!==e.idToken,"internal-error"),m(void 0!==e.refreshToken,"internal-error");var t="expiresIn"in e&&void 0!==e.expiresIn?Number(e.expiresIn):Je(e.idToken);this.updateTokensAndExpiration(e.idToken,e.refreshToken,t)}updateFromIdToken(e){m(0!==e.length,"internal-error");var t=Je(e);this.updateTokensAndExpiration(e,null,t)}async getToken(e,t=!1){return t||!this.accessToken||this.isExpired?(m(this.refreshToken,e,"user-token-expired"),this.refreshToken?(await this.refresh(e,this.refreshToken),this.accessToken):null):this.accessToken}clearRefreshToken(){this.refreshToken=null}async refresh(e,t){i=t;var n,i,{accessToken:e,refreshToken:t,expiresIn:r}=await{accessToken:(t=await Ue(n=e,{},async()=>{var e=se({grant_type:"refresh_token",refresh_token:i}).slice(1),{tokenApiHost:t,apiKey:r}=n.config,t=await Fe(n,t,"/v1/token","key="+r),r=await n._getAdditionalHeaders(),r=(r["Content-Type"]="application/x-www-form-urlencoded",{method:"POST",headers:r,body:e});return n.emulatorConfig&&G(n.emulatorConfig.host)&&(r.credentials="include"),Ne.fetch()(t,r)})).access_token,expiresIn:t.expires_in,refreshToken:t.refresh_token};this.updateTokensAndExpiration(e,t,Number(r))}updateTokensAndExpiration(e,t,r){this.refreshToken=t||null,this.accessToken=e||null,this.expirationTime=Date.now()+1e3*r}static fromJSON(e,t){var{refreshToken:t,accessToken:r,expirationTime:n}=t,i=new Qe;return t&&(m("string"==typeof t,"internal-error",{appName:e}),i.refreshToken=t),r&&(m("string"==typeof r,"internal-error",{appName:e}),i.accessToken=r),n&&(m("number"==typeof n,"internal-error",{appName:e}),i.expirationTime=n),i}toJSON(){return{refreshToken:this.refreshToken,accessToken:this.accessToken,expirationTime:this.expirationTime}}_assign(e){this.accessToken=e.accessToken,this.refreshToken=e.refreshToken,this.expirationTime=e.expirationTime}_clone(){return Object.assign(new Qe,this.toJSON())}_performRefresh(){return i("not implemented")}}function f(e,t){m("string"==typeof e||void 0===e,"internal-error",{appName:t})}class v{constructor(e){var{uid:t,auth:r,stsTokenManager:n}=e,e=me(e,["uid","auth","stsTokenManager"]);this.providerId="firebase",this.proactiveRefresh=new Ye(this),this.reloadUserInfo=null,this.reloadListener=null,this.uid=t,this.auth=r,this.stsTokenManager=n,this.accessToken=n.accessToken,this.displayName=e.displayName||null,this.email=e.email||null,this.emailVerified=e.emailVerified||!1,this.phoneNumber=e.phoneNumber||null,this.photoURL=e.photoURL||null,this.isAnonymous=e.isAnonymous||!1,this.tenantId=e.tenantId||null,this.providerData=e.providerData?[...e.providerData]:[],this.metadata=new $e(e.createdAt||void 0,e.lastLoginAt||void 0)}async getIdToken(e){e=await g(this,this.stsTokenManager.getToken(this.auth,e));return m(e,this.auth,"internal-error"),this.accessToken!==e&&(this.accessToken=e,await this.auth._persistUserIfCurrent(this),this.auth._notifyListenersIfCurrent(this)),e}getIdTokenResult(e){return(async(e,t=!1)=>{var e=s(e),r=Ke(t=await e.getIdToken(t)),n=(m(r&&r.exp&&r.auth_time&&r.iat,e.auth,"internal-error"),null==(e="object"==typeof r.firebase?r.firebase:void 0)?void 0:e.sign_in_provider);return{claims:r,token:t,authTime:ze(Ge(r.auth_time)),issuedAtTime:ze(Ge(r.iat)),expirationTime:ze(Ge(r.exp)),signInProvider:n||null,signInSecondFactor:(null==e?void 0:e.sign_in_second_factor)||null}})(this,e)}reload(){return(async e=>{await Xe(e=s(e)),await e.auth._persistUserIfCurrent(e),e.auth._notifyListenersIfCurrent(e)})(this)}_assign(e){this!==e&&(m(this.uid===e.uid,this.auth,"internal-error"),this.displayName=e.displayName,this.photoURL=e.photoURL,this.email=e.email,this.emailVerified=e.emailVerified,this.phoneNumber=e.phoneNumber,this.isAnonymous=e.isAnonymous,this.tenantId=e.tenantId,this.providerData=e.providerData.map(e=>Object.assign({},e)),this.metadata._copy(e.metadata),this.stsTokenManager._assign(e.stsTokenManager))}_clone(e){e=new v(Object.assign(Object.assign({},this),{auth:e,stsTokenManager:this.stsTokenManager._clone()}));return e.metadata._copy(this.metadata),e}_onReload(e){m(!this.reloadListener,this.auth,"internal-error"),this.reloadListener=e,this.reloadUserInfo&&(this._notifyReloadListener(this.reloadUserInfo),this.reloadUserInfo=null)}_notifyReloadListener(e){this.reloadListener?this.reloadListener(e):this.reloadUserInfo=e}_startProactiveRefresh(){this.proactiveRefresh._start()}_stopProactiveRefresh(){this.proactiveRefresh._stop()}async _updateTokensIfNecessary(e,t=!1){let r=!1;e.idToken&&e.idToken!==this.stsTokenManager.accessToken&&(this.stsTokenManager.updateFromServerResponse(e),r=!0),t&&await Xe(this),await this.auth._persistUserIfCurrent(this),r&&this.auth._notifyListenersIfCurrent(this)}async delete(){var e;return Ki._isFirebaseServerApp(this.auth.app)?Promise.reject(h(this.auth)):(e=await this.getIdToken(),await g(this,(async(e,t)=>p(e,"POST","/v1/accounts:delete",t))(this.auth,{idToken:e})),this.stsTokenManager.clearRefreshToken(),this.auth.signOut())}toJSON(){return Object.assign(Object.assign({uid:this.uid,email:this.email||void 0,emailVerified:this.emailVerified,displayName:this.displayName||void 0,isAnonymous:this.isAnonymous,photoURL:this.photoURL||void 0,phoneNumber:this.phoneNumber||void 0,tenantId:this.tenantId||void 0,providerData:this.providerData.map(e=>Object.assign({},e)),stsTokenManager:this.stsTokenManager.toJSON(),_redirectEventId:this._redirectEventId},this.metadata.toJSON()),{apiKey:this.auth.config.apiKey,appName:this.auth.name})}get refreshToken(){return this.stsTokenManager.refreshToken||""}static _fromJSON(e,t){var r=null!=(r=t.displayName)?r:void 0,n=null!=(n=t.email)?n:void 0,i=null!=(i=t.phoneNumber)?i:void 0,s=null!=(s=t.photoURL)?s:void 0,a=null!=(a=t.tenantId)?a:void 0,o=null!=(o=t._redirectEventId)?o:void 0,l=null!=(l=t.createdAt)?l:void 0,c=null!=(c=t.lastLoginAt)?c:void 0,{uid:t,emailVerified:d,isAnonymous:u,providerData:h,stsTokenManager:p}=t,p=(m(t&&p,e,"internal-error"),Qe.fromJSON(this.name,p)),t=(m("string"==typeof t,e,"internal-error"),f(r,e.name),f(n,e.name),m("boolean"==typeof d,e,"internal-error"),m("boolean"==typeof u,e,"internal-error"),f(i,e.name),f(s,e.name),f(a,e.name),f(o,e.name),f(l,e.name),f(c,e.name),new v({uid:t,auth:e,email:n,emailVerified:d,displayName:r,isAnonymous:u,photoURL:s,phoneNumber:i,tenantId:a,stsTokenManager:p,createdAt:l,lastLoginAt:c}));return h&&Array.isArray(h)&&(t.providerData=h.map(e=>Object.assign({},e))),o&&(t._redirectEventId=o),t}static async _fromIdTokenResponse(e,t,r=!1){var n=new Qe,t=(n.updateFromServerResponse(t),new v({uid:t.localId,auth:e,stsTokenManager:n,isAnonymous:r}));return await Xe(t),t}static async _fromGetAccountInfoResponse(e,t,r){var t=t.users[0],n=(m(void 0!==t.localId,"internal-error"),void 0!==t.providerUserInfo?Ze(t.providerUserInfo):[]),i=!(t.email&&t.passwordHash||null!=n&&n.length),s=new Qe,r=(s.updateFromIdToken(r),new v({uid:t.localId,auth:e,stsTokenManager:s,isAnonymous:i})),e={uid:t.localId,displayName:t.displayName||null,photoURL:t.photoUrl||null,email:t.email||null,emailVerified:t.emailVerified||!1,phoneNumber:t.phoneNumber||null,tenantId:t.tenantId||null,providerData:n,metadata:new $e(t.createdAt,t.lastLoginAt),isAnonymous:!(t.email&&t.passwordHash||null!=n&&n.length)};return Object.assign(r,e),r}}let et=new Map;function _(e){a(e instanceof Function,"Expected a class definition");var t=et.get(e);return t?a(t instanceof e,"Instance stored in cache mismatched with class"):(t=new e,et.set(e,t)),t}class tt{constructor(){this.type="NONE",this.storage={}}async _isAvailable(){return!0}async _set(e,t){this.storage[e]=t}async _get(e){e=this.storage[e];return void 0===e?null:e}async _remove(e){delete this.storage[e]}_addListener(e,t){}_removeListener(e,t){}}tt.type="NONE";let rt=tt;function y(e,t,r){return`firebase:${e}:${t}:`+r}class nt{constructor(e,t,r){this.persistence=e,this.auth=t,this.userKey=r;var{config:e,name:r}=this.auth;this.fullUserKey=y(this.userKey,e.apiKey,r),this.fullPersistenceKey=y("persistence",e.apiKey,r),this.boundEventHandler=t._onStorageEvent.bind(t),this.persistence._addListener(this.fullUserKey,this.boundEventHandler)}setCurrentUser(e){return this.persistence._set(this.fullUserKey,e.toJSON())}async getCurrentUser(){var e,t=await this.persistence._get(this.fullUserKey);return t?"string"==typeof t?(e=await Be(this.auth,{idToken:t}).catch(()=>{}))?v._fromGetAccountInfoResponse(this.auth,e,t):null:v._fromJSON(this.auth,t):null}removeCurrentUser(){return this.persistence._remove(this.fullUserKey)}savePersistenceForRedirect(){return this.persistence._set(this.fullPersistenceKey,this.persistence.type)}async setPersistence(e){var t;if(this.persistence!==e)return t=await this.getCurrentUser(),await this.removeCurrentUser(),this.persistence=e,t?this.setCurrentUser(t):void 0}delete(){this.persistence._removeListener(this.fullUserKey,this.boundEventHandler)}static async create(t,e,r="authUser"){if(!e.length)return new nt(_(rt),t,r);var n,i=(await Promise.all(e.map(async e=>{if(await e._isAvailable())return e}))).filter(e=>e);let s=i[0]||_(rt),a=y(r,t.config.apiKey,t.name),o=null;for(n of e)try{var l=await n._get(a);if(l){let e;if("string"==typeof l){var c=await Be(t,{idToken:l}).catch(()=>{});if(!c)break;e=await v._fromGetAccountInfoResponse(t,c,l)}else e=v._fromJSON(t,l);n!==s&&(o=e),s=n;break}}catch(e){}i=i.filter(e=>e._shouldAllowMigration);return s._shouldAllowMigration&&i.length&&(s=i[0],o&&await s._set(a,o.toJSON()),await Promise.all(e.map(async e=>{if(e!==s)try{await e._remove(a)}catch(e){}}))),new nt(s,t,r)}}function it(e){var t=e.toLowerCase();return t.includes("opera/")||t.includes("opr/")||t.includes("opios/")?"Opera":lt(t)?"IEMobile":t.includes("msie")||t.includes("trident/")?"IE":t.includes("edge/")?"Edge":st(t)?"Firefox":t.includes("silk/")?"Silk":dt(t)?"Blackberry":ut(t)?"Webos":at(t)?"Safari":!t.includes("chrome/")&&!ot(t)||t.includes("edge/")?ct(t)?"Android":2===(null==(t=e.match(/([a-zA-Z\d\.]+)\/[a-zA-Z\d\.]*$/))?void 0:t.length)?t[1]:"Other":"Chrome"}function st(e=l()){return/firefox\//i.test(e)}function at(e=l()){e=e.toLowerCase();return e.includes("safari/")&&!e.includes("chrome/")&&!e.includes("crios/")&&!e.includes("android")}function ot(e=l()){return/crios\//i.test(e)}function lt(e=l()){return/iemobile/i.test(e)}function ct(e=l()){return/android/i.test(e)}function dt(e=l()){return/blackberry/i.test(e)}function ut(e=l()){return/webos/i.test(e)}function ht(e=l()){return/iphone|ipad|ipod/i.test(e)||/macintosh/i.test(e)&&/mobile/i.test(e)}function pt(e=l()){return ht(e)||ct(e)||ut(e)||dt(e)||/windows phone/i.test(e)||lt(e)}function mt(e,t=[]){let r;switch(e){case"Browser":r=it(l());break;case"Worker":r=it(l())+"-"+e;break;default:r=e}t=t.length?t.join(","):"FirebaseCore-web";return`${r}/JsCore/${Ki.SDK_VERSION}/`+t}class gt{constructor(e){this.auth=e,this.queue=[]}pushCallback(n,e){var t=r=>new Promise((e,t)=>{try{e(n(r))}catch(e){t(e)}});t.onAbort=e,this.queue.push(t);let r=this.queue.length-1;return()=>{this.queue[r]=()=>Promise.resolve()}}async runMiddleware(e){if(this.auth.currentUser!==e){var t=[];try{for(var r of this.queue)await r(e),r.onAbort&&t.push(r.onAbort)}catch(e){t.reverse();for(var n of t)try{n()}catch(e){}throw this.auth._errorFactory.create("login-blocked",{originalMessage:null==e?void 0:e.message})}}}}class ft{constructor(e){var t,r=e.customStrengthOptions;this.customStrengthOptions={},this.customStrengthOptions.minPasswordLength=null!=(t=r.minPasswordLength)?t:6,r.maxPasswordLength&&(this.customStrengthOptions.maxPasswordLength=r.maxPasswordLength),void 0!==r.containsLowercaseCharacter&&(this.customStrengthOptions.containsLowercaseLetter=r.containsLowercaseCharacter),void 0!==r.containsUppercaseCharacter&&(this.customStrengthOptions.containsUppercaseLetter=r.containsUppercaseCharacter),void 0!==r.containsNumericCharacter&&(this.customStrengthOptions.containsNumericCharacter=r.containsNumericCharacter),void 0!==r.containsNonAlphanumericCharacter&&(this.customStrengthOptions.containsNonAlphanumericCharacter=r.containsNonAlphanumericCharacter),this.enforcementState=e.enforcementState,"ENFORCEMENT_STATE_UNSPECIFIED"===this.enforcementState&&(this.enforcementState="OFF"),this.allowedNonAlphanumericCharacters=null!=(r=null==(t=e.allowedNonAlphanumericCharacters)?void 0:t.join(""))?r:"",this.forceUpgradeOnSignin=null!=(t=e.forceUpgradeOnSignin)&&t,this.schemaVersion=e.schemaVersion}validatePassword(e){var t={isValid:!0,passwordPolicy:this};return this.validatePasswordLengthOptions(e,t),this.validatePasswordCharacterOptions(e,t),t.isValid&&(t.isValid=null==(e=t.meetsMinPasswordLength)||e),t.isValid&&(t.isValid=null==(e=t.meetsMaxPasswordLength)||e),t.isValid&&(t.isValid=null==(e=t.containsLowercaseLetter)||e),t.isValid&&(t.isValid=null==(e=t.containsUppercaseLetter)||e),t.isValid&&(t.isValid=null==(e=t.containsNumericCharacter)||e),t.isValid&&(t.isValid=null==(e=t.containsNonAlphanumericCharacter)||e),t}validatePasswordLengthOptions(e,t){var r=this.customStrengthOptions.minPasswordLength,n=this.customStrengthOptions.maxPasswordLength;r&&(t.meetsMinPasswordLength=e.length>=r),n&&(t.meetsMaxPasswordLength=e.length<=n)}validatePasswordCharacterOptions(t,r){var n;this.updatePasswordCharacterOptionsStatuses(r,!1,!1,!1,!1);for(let e=0;e<t.length;e++)n=t.charAt(e),this.updatePasswordCharacterOptionsStatuses(r,"a"<=n&&n<="z","A"<=n&&n<="Z","0"<=n&&n<="9",this.allowedNonAlphanumericCharacters.includes(n))}updatePasswordCharacterOptionsStatuses(e,t,r,n,i){this.customStrengthOptions.containsLowercaseLetter&&!e.containsLowercaseLetter&&(e.containsLowercaseLetter=t),this.customStrengthOptions.containsUppercaseLetter&&!e.containsUppercaseLetter&&(e.containsUppercaseLetter=r),this.customStrengthOptions.containsNumericCharacter&&!e.containsNumericCharacter&&(e.containsNumericCharacter=n),this.customStrengthOptions.containsNonAlphanumericCharacter&&!e.containsNonAlphanumericCharacter&&(e.containsNonAlphanumericCharacter=i)}}class vt{constructor(e,t,r,n){this.app=e,this.heartbeatServiceProvider=t,this.appCheckServiceProvider=r,this.config=n,this.currentUser=null,this.emulatorConfig=null,this.operations=Promise.resolve(),this.authStateSubscription=new _t(this),this.idTokenSubscription=new _t(this),this.beforeStateQueue=new gt(this),this.redirectUser=null,this.isProactiveRefreshEnabled=!1,this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION=1,this._canInitEmulator=!0,this._isInitialized=!1,this._deleted=!1,this._initializationPromise=null,this._popupRedirectResolver=null,this._errorFactory=we,this._agentRecaptchaConfig=null,this._tenantRecaptchaConfigs={},this._projectPasswordPolicy=null,this._tenantPasswordPolicies={},this._resolvePersistenceManagerAvailable=void 0,this.lastNotifiedUid=void 0,this.languageCode=null,this.tenantId=null,this.settings={appVerificationDisabledForTesting:!1},this.frameworks=[],this.name=e.name,this.clientVersion=n.sdkClientVersion,this._persistenceManagerAvailable=new Promise(e=>this._resolvePersistenceManagerAvailable=e)}_initializeWithPersistence(t,r){return r&&(this._popupRedirectResolver=_(r)),this._initializationPromise=this.queue(async()=>{var e;if(!this._deleted&&(this.persistenceManager=await nt.create(this,t),null!=(e=this._resolvePersistenceManagerAvailable)&&e.call(this),!this._deleted)){if(null!=(e=this._popupRedirectResolver)&&e._shouldInitProactively)try{await this._popupRedirectResolver._initialize(this)}catch(e){}await this.initializeCurrentUser(r),this.lastNotifiedUid=(null==(e=this.currentUser)?void 0:e.uid)||null,this._deleted||(this._isInitialized=!0)}}),this._initializationPromise}async _onStorageEvent(){var e;!this._deleted&&(e=await this.assertedPersistence.getCurrentUser(),this.currentUser||e)&&(this.currentUser&&e&&this.currentUser.uid===e.uid?(this._currentUser._assign(e),await this.currentUser.getIdToken()):await this._updateCurrentUser(e,!0))}async initializeCurrentUserFromIdToken(e){try{var t=await Be(this,{idToken:e}),r=await v._fromGetAccountInfoResponse(this,t,e);await this.directlySetCurrentUser(r)}catch(e){console.warn("FirebaseServerApp could not login user with provided authIdToken: ",e),await this.directlySetCurrentUser(null)}}async initializeCurrentUser(e){if(Ki._isFirebaseServerApp(this.app)){let t=this.app.settings.authIdToken;return t?new Promise(e=>{setTimeout(()=>this.initializeCurrentUserFromIdToken(t).then(e,e))}):this.directlySetCurrentUser(null)}var t,r,n=await this.assertedPersistence.getCurrentUser();let i=n,s=!1;if(e&&this.config.authDomain&&(await this.getOrInitRedirectPersistenceManager(),t=null==(t=this.redirectUser)?void 0:t._redirectEventId,r=null==i?void 0:i._redirectEventId,e=await this.tryRedirectSignIn(e),t&&t!==r||null==e||!e.user||(i=e.user,s=!0)),!i)return this.directlySetCurrentUser(null);if(i._redirectEventId)return m(this._popupRedirectResolver,this,"argument-error"),await this.getOrInitRedirectPersistenceManager(),this.redirectUser&&this.redirectUser._redirectEventId===i._redirectEventId?this.directlySetCurrentUser(i):this.reloadAndSetCurrentUserOrClear(i);if(s)try{await this.beforeStateQueue.runMiddleware(i)}catch(e){i=n,this._popupRedirectResolver._overrideRedirectResult(this,()=>Promise.reject(e))}return i?this.reloadAndSetCurrentUserOrClear(i):this.directlySetCurrentUser(null)}async tryRedirectSignIn(e){let t=null;try{t=await this._popupRedirectResolver._completeRedirectFn(this,e,!0)}catch(e){await this._setRedirectUser(null)}return t}async reloadAndSetCurrentUserOrClear(e){try{await Xe(e)}catch(e){if("auth/network-request-failed"!==(null==e?void 0:e.code))return this.directlySetCurrentUser(null)}return this.directlySetCurrentUser(e)}useDeviceLanguage(){var e;this.languageCode="undefined"!=typeof navigator&&((e=navigator).languages&&e.languages[0]||e.language)||null}async _delete(){this._deleted=!0}async updateCurrentUser(e){return Ki._isFirebaseServerApp(this.app)?Promise.reject(h(this)):((e=e?s(e):null)&&m(e.auth.config.apiKey===this.config.apiKey,this,"invalid-user-token"),this._updateCurrentUser(e&&e._clone(this)))}async _updateCurrentUser(e,t=!1){if(!this._deleted)return e&&m(this.tenantId===e.tenantId,this,"tenant-id-mismatch"),t||await this.beforeStateQueue.runMiddleware(e),this.queue(async()=>{await this.directlySetCurrentUser(e),this.notifyAuthListeners()})}async signOut(){return Ki._isFirebaseServerApp(this.app)?Promise.reject(h(this)):(await this.beforeStateQueue.runMiddleware(null),(this.redirectPersistenceManager||this._popupRedirectResolver)&&await this._setRedirectUser(null),this._updateCurrentUser(null,!0))}setPersistence(e){return Ki._isFirebaseServerApp(this.app)?Promise.reject(h(this)):this.queue(async()=>{await this.assertedPersistence.setPersistence(_(e))})}_getRecaptchaConfig(){return null==this.tenantId?this._agentRecaptchaConfig:this._tenantRecaptchaConfigs[this.tenantId]}async validatePassword(e){this._getPasswordPolicyInternal()||await this._updatePasswordPolicy();var t=this._getPasswordPolicyInternal();return t.schemaVersion!==this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION?Promise.reject(this._errorFactory.create("unsupported-password-policy-schema-version",{})):t.validatePassword(e)}_getPasswordPolicyInternal(){return null===this.tenantId?this._projectPasswordPolicy:this._tenantPasswordPolicies[this.tenantId]}async _updatePasswordPolicy(){var e=await p(e=this,"GET","/v2/passwordPolicy",o(e,{})),e=new ft(e);null===this.tenantId?this._projectPasswordPolicy=e:this._tenantPasswordPolicies[this.tenantId]=e}_getPersistenceType(){return this.assertedPersistence.persistence.type}_getPersistence(){return this.assertedPersistence.persistence}_updateErrorMap(e){this._errorFactory=new te("auth","Firebase",e())}onAuthStateChanged(e,t,r){return this.registerStateListener(this.authStateSubscription,e,t,r)}beforeAuthStateChanged(e,t){return this.beforeStateQueue.pushCallback(e,t)}onIdTokenChanged(e,t,r){return this.registerStateListener(this.idTokenSubscription,e,t,r)}authStateReady(){return new Promise((t,r)=>{if(this.currentUser)t();else{let e=this.onAuthStateChanged(()=>{e(),t()},r)}})}async revokeAccessToken(e){var t;this.currentUser&&(e={providerId:"apple.com",tokenType:"ACCESS_TOKEN",token:e,idToken:await this.currentUser.getIdToken()},null!=this.tenantId&&(e.tenantId=this.tenantId),await p(t=this,"POST","/v2/accounts:revokeToken",o(t,e)))}toJSON(){var e;return{apiKey:this.config.apiKey,authDomain:this.config.authDomain,appName:this.name,currentUser:null==(e=this._currentUser)?void 0:e.toJSON()}}async _setRedirectUser(e,t){t=await this.getOrInitRedirectPersistenceManager(t);return null===e?t.removeCurrentUser():t.setCurrentUser(e)}async getOrInitRedirectPersistenceManager(e){return this.redirectPersistenceManager||(m(e=e&&_(e)||this._popupRedirectResolver,this,"argument-error"),this.redirectPersistenceManager=await nt.create(this,[_(e._redirectPersistence)],"redirectUser"),this.redirectUser=await this.redirectPersistenceManager.getCurrentUser()),this.redirectPersistenceManager}async _redirectUserForId(e){var t;return this._isInitialized&&await this.queue(async()=>{}),(null==(t=this._currentUser)?void 0:t._redirectEventId)===e?this._currentUser:(null==(t=this.redirectUser)?void 0:t._redirectEventId)===e?this.redirectUser:null}async _persistUserIfCurrent(e){if(e===this.currentUser)return this.queue(async()=>this.directlySetCurrentUser(e))}_notifyListenersIfCurrent(e){e===this.currentUser&&this.notifyAuthListeners()}_key(){return`${this.config.authDomain}:${this.config.apiKey}:`+this.name}_startProactiveRefresh(){this.isProactiveRefreshEnabled=!0,this.currentUser&&this._currentUser._startProactiveRefresh()}_stopProactiveRefresh(){this.isProactiveRefreshEnabled=!1,this.currentUser&&this._currentUser._stopProactiveRefresh()}get _currentUser(){return this.currentUser}notifyAuthListeners(){var e;this._isInitialized&&(this.idTokenSubscription.next(this.currentUser),e=null!=(e=null==(e=this.currentUser)?void 0:e.uid)?e:null,this.lastNotifiedUid!==e)&&(this.lastNotifiedUid=e,this.authStateSubscription.next(this.currentUser))}registerStateListener(t,r,n,i){if(this._deleted)return()=>{};let e="function"==typeof r?r:r.next.bind(r),s=!1;var a=this._isInitialized?Promise.resolve():this._initializationPromise;if(m(a,this,"internal-error"),a.then(()=>{s||e(this.currentUser)}),"function"==typeof r){let e=t.addObserver(r,n,i);return()=>{s=!0,e()}}{let e=t.addObserver(r);return()=>{s=!0,e()}}}async directlySetCurrentUser(e){this.currentUser&&this.currentUser!==e&&this._currentUser._stopProactiveRefresh(),e&&this.isProactiveRefreshEnabled&&e._startProactiveRefresh(),(this.currentUser=e)?await this.assertedPersistence.setCurrentUser(e):await this.assertedPersistence.removeCurrentUser()}queue(e){return this.operations=this.operations.then(e,e),this.operations}get assertedPersistence(){return m(this.persistenceManager,this,"internal-error"),this.persistenceManager}_logFramework(e){e&&!this.frameworks.includes(e)&&(this.frameworks.push(e),this.frameworks.sort(),this.clientVersion=mt(this.config.clientPlatform,this._getFrameworks()))}_getFrameworks(){return this.frameworks}async _getAdditionalHeaders(){var e={"X-Client-Version":this.clientVersion},t=(this.app.options.appId&&(e["X-Firebase-gmpid"]=this.app.options.appId),await(null==(t=this.heartbeatServiceProvider.getImmediate({optional:!0}))?void 0:t.getHeartbeatsHeader())),t=(t&&(e["X-Firebase-Client"]=t),await this._getAppCheckToken());return t&&(e["X-Firebase-AppCheck"]=t),e}async _getAppCheckToken(){var e,t,r;return Ki._isFirebaseServerApp(this.app)&&this.app.settings.appCheckToken?this.app.settings.appCheckToken:(null!=(e=await(null==(e=this.appCheckServiceProvider.getImmediate({optional:!0}))?void 0:e.getToken()))&&e.error&&(t="Error while retrieving App Check token: "+e.error,r=[],Te.logLevel<=n.WARN)&&Te.warn(`Auth (${Ki.SDK_VERSION}): `+t,...r),null==e?void 0:e.token)}}function I(e){return s(e)}class _t{constructor(e){var t;this.auth=e,this.observer=null,this.addObserver=(e=e=>this.observer=e,(e=new le(e,t)).subscribe.bind(e))}get next(){return m(this.observer,this.auth,"internal-error"),this.observer.next.bind(this.observer)}}let yt={async loadJS(){throw new Error("Unable to load external scripts")},recaptchaV2Script:"",recaptchaEnterpriseScript:"",gapiScript:""};function It(e){return yt.loadJS(e)}function wt(e){return"__"+e+Math.floor(1e6*Math.random())}class Tt{constructor(e){this.auth=e,this.counter=1e12,this._widgets=new Map}render(e,t){var r=this.counter;return this._widgets.set(r,new kt(e,this.auth.name,t||{})),this.counter++,r}reset(e){var t,e=e||1e12;null!=(t=this._widgets.get(e))&&t.delete(),this._widgets.delete(e)}getResponse(e){return(null==(e=this._widgets.get(e||1e12))?void 0:e.getResponse())||""}async execute(e){return null!=(e=this._widgets.get(e||1e12))&&e.execute(),""}}class Et{constructor(){this.enterprise=new bt}ready(e){e()}execute(e,t){return Promise.resolve("token")}render(e,t){return""}}class bt{ready(e){e()}execute(e,t){return Promise.resolve("token")}render(e,t){return""}}class kt{constructor(e,t,r){this.params=r,this.timerId=null,this.deleted=!1,this.responseToken=null,this.clickHandler=()=>{this.execute()};r="string"==typeof e?document.getElementById(e):e;m(r,"argument-error",{appName:t}),this.container=r,this.isVisible="invisible"!==this.params.size,this.isVisible?this.execute():this.container.addEventListener("click",this.clickHandler)}getResponse(){return this.checkIfDeleted(),this.responseToken}delete(){this.checkIfDeleted(),this.deleted=!0,this.timerId&&(clearTimeout(this.timerId),this.timerId=null),this.container.removeEventListener("click",this.clickHandler)}execute(){this.checkIfDeleted(),this.timerId||(this.timerId=window.setTimeout(()=>{this.responseToken=(t=>{var r=[],n="1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";for(let e=0;e<t;e++)r.push(n.charAt(Math.floor(Math.random()*n.length)));return r.join("")})(50);let{callback:e,"expired-callback":t}=this.params;if(e)try{e(this.responseToken)}catch(e){}this.timerId=window.setTimeout(()=>{if(this.timerId=null,this.responseToken=null,t)try{t()}catch(e){}this.isVisible&&this.execute()},6e4)},500))}checkIfDeleted(){if(this.deleted)throw new Error("reCAPTCHA mock was already deleted!")}}let St="NO_RECAPTCHA";class Rt{constructor(e){this.type="recaptcha-enterprise",this.auth=I(e)}async verify(i="verify",e=!1){function s(e,t,r){let n=window.grecaptcha;He(n)?n.enterprise.ready(()=>{n.enterprise.execute(e,{action:i}).then(e=>{t(e)}).catch(()=>{t(St)})}):r(Error("No reCAPTCHA enterprise script loaded."))}return this.auth.settings.appVerificationDisabledForTesting?(new Et).execute("siteKey",{action:"verify"}):new Promise((r,n)=>{(async n=>{if(!e){if(null==n.tenantId&&null!=n._agentRecaptchaConfig)return n._agentRecaptchaConfig.siteKey;if(null!=n.tenantId&&void 0!==n._tenantRecaptchaConfigs[n.tenantId])return n._tenantRecaptchaConfigs[n.tenantId].siteKey}return new Promise(async(t,r)=>{qe(n,{clientType:"CLIENT_TYPE_WEB",version:"RECAPTCHA_ENTERPRISE"}).then(e=>{if(void 0!==e.recaptchaKey)return e=new We(e),null==n.tenantId?n._agentRecaptchaConfig=e:n._tenantRecaptchaConfigs[n.tenantId]=e,t(e.siteKey);r(new Error("recaptcha Enterprise site key undefined"))}).catch(e=>{r(e)})})})(this.auth).then(t=>{if(!e&&He(window.grecaptcha))s(t,r,n);else if("undefined"==typeof window)n(new Error("RecaptchaVerifier is only supported in browser"));else{let e=yt.recaptchaEnterpriseScript;0!==e.length&&(e+=t),It(e).then(()=>{s(t,r,n)}).catch(e=>{n(e)})}}).catch(e=>{n(e)})})}}async function At(t,e,r,n=!1,i=!1){t=new Rt(t);let s;if(i)s=St;else try{s=await t.verify(r)}catch(e){s=await t.verify(r,!0)}i=Object.assign({},e);return"mfaSmsEnrollment"===r||"mfaSmsSignIn"===r?"phoneEnrollmentInfo"in i?(t=i.phoneEnrollmentInfo.phoneNumber,e=i.phoneEnrollmentInfo.recaptchaToken,Object.assign(i,{phoneEnrollmentInfo:{phoneNumber:t,recaptchaToken:e,captchaResponse:s,clientType:"CLIENT_TYPE_WEB",recaptchaVersion:"RECAPTCHA_ENTERPRISE"}})):"phoneSignInInfo"in i&&(r=i.phoneSignInInfo.recaptchaToken,Object.assign(i,{phoneSignInInfo:{recaptchaToken:r,captchaResponse:s,clientType:"CLIENT_TYPE_WEB",recaptchaVersion:"RECAPTCHA_ENTERPRISE"}})):(n?Object.assign(i,{captchaResp:s}):Object.assign(i,{captchaResponse:s}),Object.assign(i,{clientType:"CLIENT_TYPE_WEB"}),Object.assign(i,{recaptchaVersion:"RECAPTCHA_ENTERPRISE"})),i}async function w(r,n,i,s,e){var t;return"EMAIL_PASSWORD_PROVIDER"===e?null!=(t=r._getRecaptchaConfig())&&t.isProviderEnabled("EMAIL_PASSWORD_PROVIDER")?(t=await At(r,n,i,"getOobCode"===i),s(r,t)):s(r,n).catch(async e=>{var t;return"auth/missing-recaptcha-token"===e.code?(console.log(i+" is protected by reCAPTCHA Enterprise for this project. Automatically triggering the reCAPTCHA flow and restarting the flow."),t=await At(r,n,i,"getOobCode"===i),s(r,t)):Promise.reject(e)}):"PHONE_PROVIDER"===e?null!=(t=r._getRecaptchaConfig())&&t.isProviderEnabled("PHONE_PROVIDER")?(t=await At(r,n,i),s(r,t).catch(async e=>{var t;if("AUDIT"===(null==(t=r._getRecaptchaConfig())?void 0:t.getProviderEnforcementState("PHONE_PROVIDER"))&&("auth/missing-recaptcha-token"===e.code||"auth/invalid-app-credential"===e.code))return console.log(`Failed to verify with reCAPTCHA Enterprise. Automatically triggering the reCAPTCHA v2 flow to complete the ${i} flow.`),t=await At(r,n,i,!1,!0),s(r,t);return Promise.reject(e)})):(t=await At(r,n,i,!1,!0),s(r,t)):Promise.reject(e+" provider is not supported.")}function Pt(e,t,r){var e=I(e),r=(m(/^https?:\/\//.test(t),e,"invalid-emulator-scheme"),!(null==r||!r.disableWarnings)),n=Ct(t),{host:t,port:i}=(e=>{var t=Ct(e);return(e=/(\/\/)?([^?#/]+)/.exec(e.substr(t.length)))?(t=e[2].split("@").pop()||"",(e=/^(\[[^\]]+\])(:|$)/.exec(t))?{host:e=e[1],port:Ot(t.substr(e.length+1))}:([e,t]=t.split(":"),{host:e,port:Ot(t)})):{host:"",port:null}})(t),s=null===i?"":":"+i,a={url:n+`//${t}${s}/`},i=Object.freeze({host:t,port:i,protocol:n.replace(":",""),options:Object.freeze({disableWarnings:r})});function o(){var e=document.createElement("p"),t=e.style;e.innerText="Running in emulator mode. Do not use with production credentials.",t.position="fixed",t.width="100%",t.backgroundColor="#ffffff",t.border=".1em solid #000000",t.color="#b50000",t.bottom="0px",t.left="0px",t.margin="0px",t.zIndex="10000",t.textAlign="center",e.classList.add("firebase-emulator-warning"),document.body.appendChild(e)}e._canInitEmulator?(e.config.emulator=a,e.emulatorConfig=i,e.settings.appVerificationDisabledForTesting=!0,G(t)?((async e=>(await fetch(e,{credentials:"include"})).ok)(n+"//"+t+s),Y("Auth",!0)):r||("undefined"!=typeof console&&"function"==typeof console.info&&console.info("WARNING: You are using the Auth Emulator, which is intended for local testing only.  Do not use with production credentials."),"undefined"!=typeof window&&"undefined"!=typeof document&&("loading"===document.readyState?window.addEventListener("DOMContentLoaded",o):o()))):(m(e.config.emulator&&e.emulatorConfig,e,"emulator-config-failed"),m(ne(a,e.config.emulator)&&ne(i,e.emulatorConfig),e,"emulator-config-failed"))}function Ct(e){var t=e.indexOf(":");return t<0?"":e.substr(0,t+1)}function Ot(e){return!e||(e=Number(e),isNaN(e))?null:e}class Nt{constructor(e,t){this.providerId=e,this.signInMethod=t}toJSON(){return i("not implemented")}_getIdTokenResponse(e){return i("not implemented")}_linkToIdToken(e,t){return i("not implemented")}_getReauthenticationResolver(e){return i("not implemented")}}async function Lt(e,t){return p(e,"POST","/v1/accounts:resetPassword",o(e,t))}async function Dt(e,t){return p(e,"POST","/v1/accounts:signUp",t)}async function Mt(e,t){return r(e,"POST","/v1/accounts:signInWithPassword",o(e,t))}async function Ut(e,t){return p(e,"POST","/v1/accounts:sendOobCode",o(e,t))}async function Ft(e,t){return Ut(e,t)}async function Vt(e,t){return Ut(e,t)}class xt extends Nt{constructor(e,t,r,n=null){super("password",r),this._email=e,this._password=t,this._tenantId=n}static _fromEmailAndPassword(e,t){return new xt(e,t,"password")}static _fromEmailAndCode(e,t,r=null){return new xt(e,t,"emailLink",r)}toJSON(){return{email:this._email,password:this._password,signInMethod:this.signInMethod,tenantId:this._tenantId}}static fromJSON(e){e="string"==typeof e?JSON.parse(e):e;if(null!=e&&e.email&&null!=e&&e.password){if("password"===e.signInMethod)return this._fromEmailAndPassword(e.email,e.password);if("emailLink"===e.signInMethod)return this._fromEmailAndCode(e.email,e.password,e.tenantId)}return null}async _getIdTokenResponse(e){switch(this.signInMethod){case"password":return w(e,{returnSecureToken:!0,email:this._email,password:this._password,clientType:"CLIENT_TYPE_WEB"},"signInWithPassword",Mt,"EMAIL_PASSWORD_PROVIDER");case"emailLink":return(async(e,t)=>r(e,"POST","/v1/accounts:signInWithEmailLink",o(e,t)))(e,{email:this._email,oobCode:this._password});default:d(e,"internal-error")}}async _linkToIdToken(e,t){switch(this.signInMethod){case"password":return w(e,{idToken:t,returnSecureToken:!0,email:this._email,password:this._password,clientType:"CLIENT_TYPE_WEB"},"signUpPassword",Dt,"EMAIL_PASSWORD_PROVIDER");case"emailLink":return(async(e,t)=>r(e,"POST","/v1/accounts:signInWithEmailLink",o(e,t)))(e,{idToken:t,email:this._email,oobCode:this._password});default:d(e,"internal-error")}}_getReauthenticationResolver(e){return this._getIdTokenResponse(e)}}async function T(e,t){return r(e,"POST","/v1/accounts:signInWithIdp",o(e,t))}class E extends Nt{constructor(){super(...arguments),this.pendingToken=null}static _fromParams(e){var t=new E(e.providerId,e.signInMethod);return e.idToken||e.accessToken?(e.idToken&&(t.idToken=e.idToken),e.accessToken&&(t.accessToken=e.accessToken),e.nonce&&!e.pendingToken&&(t.nonce=e.nonce),e.pendingToken&&(t.pendingToken=e.pendingToken)):e.oauthToken&&e.oauthTokenSecret?(t.accessToken=e.oauthToken,t.secret=e.oauthTokenSecret):d("argument-error"),t}toJSON(){return{idToken:this.idToken,accessToken:this.accessToken,secret:this.secret,nonce:this.nonce,pendingToken:this.pendingToken,providerId:this.providerId,signInMethod:this.signInMethod}}static fromJSON(e){var e="string"==typeof e?JSON.parse(e):e,{providerId:t,signInMethod:r}=e,e=me(e,["providerId","signInMethod"]);return t&&r?((t=new E(t,r)).idToken=e.idToken||void 0,t.accessToken=e.accessToken||void 0,t.secret=e.secret,t.nonce=e.nonce,t.pendingToken=e.pendingToken||null,t):null}_getIdTokenResponse(e){return T(e,this.buildRequest())}_linkToIdToken(e,t){var r=this.buildRequest();return r.idToken=t,T(e,r)}_getReauthenticationResolver(e){var t=this.buildRequest();return t.autoCreate=!1,T(e,t)}buildRequest(){var e,t={requestUri:"http://localhost",returnSecureToken:!0};return this.pendingToken?t.pendingToken=this.pendingToken:(e={},this.idToken&&(e.id_token=this.idToken),this.accessToken&&(e.access_token=this.accessToken),this.secret&&(e.oauth_token_secret=this.secret),e.providerId=this.providerId,this.nonce&&!this.pendingToken&&(e.nonce=this.nonce),t.postBody=se(e)),t}}async function jt(e,t){return p(e,"POST","/v1/accounts:sendVerificationCode",o(e,t))}let Ht={USER_NOT_FOUND:"user-not-found"};class Wt extends Nt{constructor(e){super("phone","phone"),this.params=e}static _fromVerification(e,t){return new Wt({verificationId:e,verificationCode:t})}static _fromTokenResponse(e,t){return new Wt({phoneNumber:e,temporaryProof:t})}_getIdTokenResponse(e){return(async(e,t)=>r(e,"POST","/v1/accounts:signInWithPhoneNumber",o(e,t)))(e,this._makeVerificationRequest())}_linkToIdToken(e,t){return(async(e,t)=>{if((t=await r(e,"POST","/v1/accounts:signInWithPhoneNumber",o(e,t))).temporaryProof)throw xe(e,"account-exists-with-different-credential",t);return t})(e,Object.assign({idToken:t},this._makeVerificationRequest()))}_getReauthenticationResolver(e){return(async(e,t)=>r(e,"POST","/v1/accounts:signInWithPhoneNumber",o(e,Object.assign(Object.assign({},t),{operation:"REAUTH"})),Ht))(e,this._makeVerificationRequest())}_makeVerificationRequest(){var{temporaryProof:e,phoneNumber:t,verificationId:r,verificationCode:n}=this.params;return e&&t?{temporaryProof:e,phoneNumber:t}:{sessionInfo:r,code:n}}toJSON(){var e={providerId:this.providerId};return this.params.phoneNumber&&(e.phoneNumber=this.params.phoneNumber),this.params.temporaryProof&&(e.temporaryProof=this.params.temporaryProof),this.params.verificationCode&&(e.verificationCode=this.params.verificationCode),this.params.verificationId&&(e.verificationId=this.params.verificationId),e}static fromJSON(e){var{verificationId:e,verificationCode:t,phoneNumber:r,temporaryProof:n}=e="string"==typeof e?JSON.parse(e):e;return t||e||r||n?new Wt({verificationId:e,verificationCode:t,phoneNumber:r,temporaryProof:n}):null}}class qt{constructor(e){var e=ae(oe(e)),t=null!=(t=e.apiKey)?t:null,r=null!=(r=e.oobCode)?r:null,n=(e=>{switch(e){case"recoverEmail":return"RECOVER_EMAIL";case"resetPassword":return"PASSWORD_RESET";case"signIn":return"EMAIL_SIGNIN";case"verifyEmail":return"VERIFY_EMAIL";case"verifyAndChangeEmail":return"VERIFY_AND_CHANGE_EMAIL";case"revertSecondFactorAddition":return"REVERT_SECOND_FACTOR_ADDITION";default:return null}})(null!=(n=e.mode)?n:null);m(t&&r&&n,"argument-error"),this.apiKey=t,this.operation=n,this.code=r,this.continueUrl=null!=(t=e.continueUrl)?t:null,this.languageCode=null!=(n=e.lang)?n:null,this.tenantId=null!=(r=e.tenantId)?r:null}static parseLink(e){t=ae(oe(e=e)).link,r=t?ae(oe(t)).deep_link_id:null;var t,r,n=((n=ae(oe(e)).deep_link_id)?ae(oe(n)).link:null)||n||r||t||e;try{return new qt(n)}catch(e){return null}}}class Bt{constructor(){this.providerId=Bt.PROVIDER_ID}static credential(e,t){return xt._fromEmailAndPassword(e,t)}static credentialWithLink(e,t){t=qt.parseLink(t);return m(t,"argument-error"),xt._fromEmailAndCode(e,t.code,t.tenantId)}}Bt.PROVIDER_ID="password",Bt.EMAIL_PASSWORD_SIGN_IN_METHOD="password",Bt.EMAIL_LINK_SIGN_IN_METHOD="emailLink";class b{constructor(e){this.providerId=e,this.defaultLanguageCode=null,this.customParameters={}}setDefaultLanguage(e){this.defaultLanguageCode=e}setCustomParameters(e){return this.customParameters=e,this}getCustomParameters(){return this.customParameters}}class zt extends b{constructor(){super(...arguments),this.scopes=[]}addScope(e){return this.scopes.includes(e)||this.scopes.push(e),this}getScopes(){return[...this.scopes]}}class Gt extends zt{static credentialFromJSON(e){e="string"==typeof e?JSON.parse(e):e;return m("providerId"in e&&"signInMethod"in e,"argument-error"),E._fromParams(e)}credential(e){return this._credential(Object.assign(Object.assign({},e),{nonce:e.rawNonce}))}_credential(e){return m(e.idToken||e.accessToken,"argument-error"),E._fromParams(Object.assign(Object.assign({},e),{providerId:this.providerId,signInMethod:this.providerId}))}static credentialFromResult(e){return Gt.oauthCredentialFromTaggedObject(e)}static credentialFromError(e){return Gt.oauthCredentialFromTaggedObject(e.customData||{})}static oauthCredentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{oauthIdToken:e,oauthAccessToken:t,oauthTokenSecret:r,pendingToken:n,nonce:i,providerId:s}=e;if(!(t||r||e||n))return null;if(!s)return null;try{return new Gt(s)._credential({idToken:e,accessToken:t,nonce:i,pendingToken:n})}catch(e){return null}}}class k extends zt{constructor(){super("facebook.com")}static credential(e){return E._fromParams({providerId:k.PROVIDER_ID,signInMethod:k.FACEBOOK_SIGN_IN_METHOD,accessToken:e})}static credentialFromResult(e){return k.credentialFromTaggedObject(e)}static credentialFromError(e){return k.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!(e&&"oauthAccessToken"in e))return null;if(!e.oauthAccessToken)return null;try{return k.credential(e.oauthAccessToken)}catch(e){return null}}}k.FACEBOOK_SIGN_IN_METHOD="facebook.com",k.PROVIDER_ID="facebook.com";class S extends zt{constructor(){super("google.com"),this.addScope("profile")}static credential(e,t){return E._fromParams({providerId:S.PROVIDER_ID,signInMethod:S.GOOGLE_SIGN_IN_METHOD,idToken:e,accessToken:t})}static credentialFromResult(e){return S.credentialFromTaggedObject(e)}static credentialFromError(e){return S.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{oauthIdToken:e,oauthAccessToken:t}=e;if(!e&&!t)return null;try{return S.credential(e,t)}catch(e){return null}}}S.GOOGLE_SIGN_IN_METHOD="google.com",S.PROVIDER_ID="google.com";class R extends zt{constructor(){super("github.com")}static credential(e){return E._fromParams({providerId:R.PROVIDER_ID,signInMethod:R.GITHUB_SIGN_IN_METHOD,accessToken:e})}static credentialFromResult(e){return R.credentialFromTaggedObject(e)}static credentialFromError(e){return R.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!(e&&"oauthAccessToken"in e))return null;if(!e.oauthAccessToken)return null;try{return R.credential(e.oauthAccessToken)}catch(e){return null}}}R.GITHUB_SIGN_IN_METHOD="github.com",R.PROVIDER_ID="github.com";class Kt extends Nt{constructor(e,t){super(e,e),this.pendingToken=t}_getIdTokenResponse(e){return T(e,this.buildRequest())}_linkToIdToken(e,t){var r=this.buildRequest();return r.idToken=t,T(e,r)}_getReauthenticationResolver(e){var t=this.buildRequest();return t.autoCreate=!1,T(e,t)}toJSON(){return{signInMethod:this.signInMethod,providerId:this.providerId,pendingToken:this.pendingToken}}static fromJSON(e){var{providerId:e,signInMethod:t,pendingToken:r}="string"==typeof e?JSON.parse(e):e;return e&&t&&r&&e===t?new Kt(e,r):null}static _create(e,t){return new Kt(e,t)}buildRequest(){return{requestUri:"http://localhost",returnSecureToken:!0,pendingToken:this.pendingToken}}}class Jt extends b{constructor(e){m(e.startsWith("saml."),"argument-error"),super(e)}static credentialFromResult(e){return Jt.samlCredentialFromTaggedObject(e)}static credentialFromError(e){return Jt.samlCredentialFromTaggedObject(e.customData||{})}static credentialFromJSON(e){e=Kt.fromJSON(e);return m(e,"argument-error"),e}static samlCredentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{pendingToken:e,providerId:t}=e;if(!e||!t)return null;try{return Kt._create(t,e)}catch(e){return null}}}class A extends zt{constructor(){super("twitter.com")}static credential(e,t){return E._fromParams({providerId:A.PROVIDER_ID,signInMethod:A.TWITTER_SIGN_IN_METHOD,oauthToken:e,oauthTokenSecret:t})}static credentialFromResult(e){return A.credentialFromTaggedObject(e)}static credentialFromError(e){return A.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{oauthAccessToken:e,oauthTokenSecret:t}=e;if(!e||!t)return null;try{return A.credential(e,t)}catch(e){return null}}}async function Yt(e,t){return r(e,"POST","/v1/accounts:signUp",o(e,t))}A.TWITTER_SIGN_IN_METHOD="twitter.com",A.PROVIDER_ID="twitter.com";class P{constructor(e){this.user=e.user,this.providerId=e.providerId,this._tokenResponse=e._tokenResponse,this.operationType=e.operationType}static async _fromIdTokenResponse(e,t,r,n=!1){e=await v._fromIdTokenResponse(e,r,n),n=$t(r);return new P({user:e,providerId:n,_tokenResponse:r,operationType:t})}static async _forOperation(e,t,r){await e._updateTokensIfNecessary(r,!0);var n=$t(r);return new P({user:e,providerId:n,_tokenResponse:r,operationType:t})}}function $t(e){return e.providerId||("phoneNumber"in e?"phone":null)}class Xt extends c{constructor(e,t,r,n){super(t.code,t.message),this.operationType=r,this.user=n,Object.setPrototypeOf(this,Xt.prototype),this.customData={appName:e.name,tenantId:null!=(n=e.tenantId)?n:void 0,_serverResponse:t.customData._serverResponse,operationType:r}}static _fromErrorAndOperation(e,t,r,n){return new Xt(e,t,r,n)}}function Zt(t,r,e,n){return("reauthenticate"===r?e._getReauthenticationResolver(t):e._getIdTokenResponse(t)).catch(e=>{if("auth/multi-factor-auth-required"===e.code)throw Xt._fromErrorAndOperation(t,e,r,n);throw e})}function Qt(e){return new Set(e.map(({providerId:e})=>e).filter(e=>!!e))}async function er(e,t){var e=s(e),r=(await rr(!0,e,t),r=e.auth,t={idToken:await e.getIdToken(),deleteProvider:[t]},await p(r,"POST","/v1/accounts:update",t)).providerUserInfo;let n=Qt(r||[]);return e.providerData=e.providerData.filter(e=>n.has(e.providerId)),n.has("phone")||(e.phoneNumber=null),await e.auth._persistUserIfCurrent(e),e}async function tr(e,t,r=!1){t=await g(e,t._linkToIdToken(e.auth,await e.getIdToken()),r);return P._forOperation(e,"link",t)}async function rr(e,t,r){await Xe(t);var n=!1===e?"provider-already-linked":"no-such-provider";m(Qt(t.providerData).has(r)===e,t.auth,n)}async function nr(e,t,r=!1){var n=e.auth;if(Ki._isFirebaseServerApp(n.app))return Promise.reject(h(n));var i="reauthenticate";try{var s=await g(e,Zt(n,i,t,e),r),a=(m(s.idToken,n,"internal-error"),Ke(s.idToken)),o=(m(a,n,"internal-error"),a).sub;return m(e.uid===o,n,"user-mismatch"),P._forOperation(e,i,s)}catch(e){throw"auth/user-not-found"===(null==e?void 0:e.code)&&d(n,"user-mismatch"),e}}async function ir(e,t,r=!1){return Ki._isFirebaseServerApp(e.app)?Promise.reject(h(e)):(t=await Zt(e,"signIn",t),t=await P._fromIdTokenResponse(e,"signIn",t),r||await e._updateCurrentUser(t.user),t)}async function sr(e,t){return ir(I(e),t)}async function ar(e,t){e=s(e);return await rr(!1,e,t.providerId),tr(e,t)}async function or(e,t){return nr(s(e),t)}async function lr(e,t){return Ki._isFirebaseServerApp(e.app)?Promise.reject(h(e)):(t=await r(e=I(e),"POST","/v1/accounts:signInWithCustomToken",o(e,{token:t,returnSecureToken:!0})),t=await P._fromIdTokenResponse(e,"signIn",t),await e._updateCurrentUser(t.user),t)}class cr{constructor(e,t){this.factorId=e,this.uid=t.mfaEnrollmentId,this.enrollmentTime=new Date(t.enrolledAt).toUTCString(),this.displayName=t.displayName}static _fromServerResponse(e,t){return"phoneInfo"in t?dr._fromServerResponse(e,t):"totpInfo"in t?ur._fromServerResponse(e,t):d(e,"internal-error")}}class dr extends cr{constructor(e){super("phone",e),this.phoneNumber=e.phoneInfo}static _fromServerResponse(e,t){return new dr(t)}}class ur extends cr{constructor(e){super("totp",e)}static _fromServerResponse(e,t){return new ur(t)}}function hr(e,t,r){var n;m(0<(null==(n=r.url)?void 0:n.length),e,"invalid-continue-uri"),m(void 0===r.dynamicLinkDomain||0<r.dynamicLinkDomain.length,e,"invalid-dynamic-link-domain"),m(void 0===r.linkDomain||0<r.linkDomain.length,e,"invalid-hosting-link-domain"),t.continueUrl=r.url,t.dynamicLinkDomain=r.dynamicLinkDomain,t.linkDomain=r.linkDomain,t.canHandleCodeInApp=r.handleCodeInApp,r.iOS&&(m(0<r.iOS.bundleId.length,e,"missing-ios-bundle-id"),t.iOSBundleId=r.iOS.bundleId),r.android&&(m(0<r.android.packageName.length,e,"missing-android-pkg-name"),t.androidInstallApp=r.android.installApp,t.androidMinimumVersionCode=r.android.minimumVersion,t.androidPackageName=r.android.packageName)}async function pr(e){e=I(e);e._getPasswordPolicyInternal()&&await e._updatePasswordPolicy()}async function mr(e,t){await p(e=s(e),"POST","/v1/accounts:update",o(e,{oobCode:t}))}async function gr(e,t){var r=s(e),n=await Lt(r,{oobCode:t}),e=n.requestType;switch(m(e,r,"internal-error"),e){case"EMAIL_SIGNIN":break;case"VERIFY_AND_CHANGE_EMAIL":m(n.newEmail,r,"internal-error");break;case"REVERT_SECOND_FACTOR_ADDITION":m(n.mfaInfo,r,"internal-error");default:m(n.email,r,"internal-error")}let i=null;return n.mfaInfo&&(i=cr._fromServerResponse(I(r),n.mfaInfo)),{data:{email:("VERIFY_AND_CHANGE_EMAIL"===n.requestType?n.newEmail:n.email)||null,previousEmail:("VERIFY_AND_CHANGE_EMAIL"===n.requestType?n.email:n.newEmail)||null,multiFactorInfo:i},operation:e}}async function fr(e,t){var r=Ae()?Re():"http://localhost",e=(await p(e=s(e),"POST","/v1/accounts:createAuthUri",o(e,{identifier:t,continueUri:r}))).signinMethods;return e||[]}async function vr(e,t){var r=s(e),n={requestType:"VERIFY_EMAIL",idToken:await e.getIdToken()},t=(t&&hr(r.auth,n,t),await Ut(r.auth,n)).email;t!==e.email&&await e.reload()}async function _r(e,t,r){var n=s(e),t={requestType:"VERIFY_AND_CHANGE_EMAIL",idToken:await e.getIdToken(),newEmail:t},r=(r&&hr(n.auth,t,r),await Ut(n.auth,t)).email;r!==e.email&&await e.reload()}async function yr(e,{displayName:t,photoURL:r}){var n;void 0===t&&void 0===r||(n=await(e=s(e)).getIdToken(),n=await g(e,(async(e,t)=>p(e,"POST","/v1/accounts:update",t))(e.auth,{idToken:n,displayName:t,photoUrl:r,returnSecureToken:!0})),e.displayName=n.displayName||null,e.photoURL=n.photoUrl||null,(t=e.providerData.find(({providerId:e})=>"password"===e))&&(t.displayName=e.displayName,t.photoURL=e.photoURL),await e._updateTokensIfNecessary(n))}async function Ir(e,t,r){var n=e.auth,i={idToken:await e.getIdToken(),returnSecureToken:!0},t=(t&&(i.email=t),r&&(i.password=r),await g(e,(async(e,t)=>p(e,"POST","/v1/accounts:update",t))(n,i)));await e._updateTokensIfNecessary(t,!0)}class wr{constructor(e,t,r={}){this.isNewUser=e,this.providerId=t,this.profile=r}}class Tr extends wr{constructor(e,t,r,n){super(e,t,r),this.username=n}}class Er extends wr{constructor(e,t){super(e,"facebook.com",t)}}class br extends Tr{constructor(e,t){super(e,"github.com",t,"string"==typeof(null==t?void 0:t.login)?null==t?void 0:t.login:null)}}class kr extends wr{constructor(e,t){super(e,"google.com",t)}}class Sr extends Tr{constructor(e,t,r){super(e,"twitter.com",t,r)}}function Rr(e){var{user:e,_tokenResponse:t}=e;if(e.isAnonymous&&!t)return{providerId:null,isNewUser:!1,profile:null};var r=t;if(!r)return null;var n=r.providerId,i=r.rawUserInfo?JSON.parse(r.rawUserInfo):{},s=r.isNewUser||"identitytoolkit#SignupNewUserResponse"===r.kind;if(!n&&null!=r&&r.idToken){e=null==(e=null==(e=Ke(r.idToken))?void 0:e.firebase)?void 0:e.sign_in_provider;if(e)return e="anonymous"!==e&&"custom"!==e?e:null,new wr(s,e)}if(!n)return null;switch(n){case"facebook.com":return new Er(s,i);case"github.com":return new br(s,i);case"google.com":return new kr(s,i);case"twitter.com":return new Sr(s,i,r.screenName||null);case"custom":case"anonymous":return new wr(s,null);default:return new wr(s,n,i)}}class Ar{constructor(e,t,r){this.type=e,this.credential=t,this.user=r}static _fromIdtoken(e,t){return new Ar("enroll",e,t)}static _fromMfaPendingCredential(e){return new Ar("signin",e)}toJSON(){return{multiFactorSession:{["enroll"===this.type?"idToken":"pendingCredential"]:this.credential}}}static fromJSON(e){var t;if(null!=e&&e.multiFactorSession){if(null!=(t=e.multiFactorSession)&&t.pendingCredential)return Ar._fromMfaPendingCredential(e.multiFactorSession.pendingCredential);if(null!=(t=e.multiFactorSession)&&t.idToken)return Ar._fromIdtoken(e.multiFactorSession.idToken)}return null}}class Pr{constructor(e,t,r){this.session=e,this.hints=t,this.signInResolver=r}static _fromError(e,n){let i=I(e),s=n.customData._serverResponse;e=(s.mfaInfo||[]).map(e=>cr._fromServerResponse(i,e));m(s.mfaPendingCredential,i,"internal-error");let a=Ar._fromMfaPendingCredential(s.mfaPendingCredential);return new Pr(a,e,async e=>{var e=await e._process(i,a),t=(delete s.mfaInfo,delete s.mfaPendingCredential,Object.assign(Object.assign({},s),{idToken:e.idToken,refreshToken:e.refreshToken}));switch(n.operationType){case"signIn":var r=await P._fromIdTokenResponse(i,n.operationType,t);return await i._updateCurrentUser(r.user),r;case"reauthenticate":return m(n.user,i,"internal-error"),P._forOperation(n.user,n.operationType,t);default:d(i,"internal-error")}})}async resolveSignIn(e){return this.signInResolver(e)}}function Cr(e,t){return p(e,"POST","/v2/accounts/mfaEnrollment:start",o(e,t))}class Or{constructor(t){this.user=t,this.enrolledFactors=[],t._onReload(e=>{e.mfaInfo&&(this.enrolledFactors=e.mfaInfo.map(e=>cr._fromServerResponse(t.auth,e)))})}static _fromUser(e){return new Or(e)}async getSession(){return Ar._fromIdtoken(await this.user.getIdToken(),this.user)}async enroll(e,t){var r=await this.getSession(),e=await g(this.user,e._process(this.user.auth,r,t));return await this.user._updateTokensIfNecessary(e),this.user.reload()}async unenroll(e){let t="string"==typeof e?e:e.uid;var r,n,e=await this.user.getIdToken();try{var i=await g(this.user,(r=this.user.auth,n={idToken:e,mfaEnrollmentId:t},p(r,"POST","/v2/accounts/mfaEnrollment:withdraw",o(r,n))));this.enrolledFactors=this.enrolledFactors.filter(({uid:e})=>e!==t),await this.user._updateTokensIfNecessary(i),await this.user.reload()}catch(e){throw e}}}let Nr=new WeakMap;let Lr="__sak";class Dr{constructor(e,t){this.storageRetriever=e,this.type=t}_isAvailable(){try{return this.storage?(this.storage.setItem(Lr,"1"),this.storage.removeItem(Lr),Promise.resolve(!0)):Promise.resolve(!1)}catch(e){return Promise.resolve(!1)}}_set(e,t){return this.storage.setItem(e,JSON.stringify(t)),Promise.resolve()}_get(e){e=this.storage.getItem(e);return Promise.resolve(e?JSON.parse(e):null)}_remove(e){return this.storage.removeItem(e),Promise.resolve()}get storage(){return this.storageRetriever()}}class Mr extends Dr{constructor(){super(()=>window.localStorage,"LOCAL"),this.boundEventHandler=(e,t)=>this.onStorageEvent(e,t),this.listeners={},this.localCache={},this.pollTimer=null,this.fallbackToPolling=pt(),this._shouldAllowMigration=!0}forAllChangedKeys(e){for(var t of Object.keys(this.listeners)){var r=this.storage.getItem(t),n=this.localCache[t];r!==n&&e(t,n,r)}}onStorageEvent(e,r=!1){if(e.key){let t=e.key;r?this.detachListener():this.stopPolling();var n=()=>{var e=this.storage.getItem(t);!r&&this.localCache[t]===e||this.notifyListeners(t,e)},i=this.storage.getItem(t);Q()&&10===document.documentMode&&i!==e.newValue&&e.newValue!==e.oldValue?setTimeout(n,10):n()}else this.forAllChangedKeys((e,t,r)=>{this.notifyListeners(e,r)})}notifyListeners(e,t){this.localCache[e]=t;e=this.listeners[e];if(e)for(var r of Array.from(e))r(t&&JSON.parse(t))}startPolling(){this.stopPolling(),this.pollTimer=setInterval(()=>{this.forAllChangedKeys((e,t,r)=>{this.onStorageEvent(new StorageEvent("storage",{key:e,oldValue:t,newValue:r}),!0)})},1e3)}stopPolling(){this.pollTimer&&(clearInterval(this.pollTimer),this.pollTimer=null)}attachListener(){window.addEventListener("storage",this.boundEventHandler)}detachListener(){window.removeEventListener("storage",this.boundEventHandler)}_addListener(e,t){0===Object.keys(this.listeners).length&&(this.fallbackToPolling?this.startPolling():this.attachListener()),this.listeners[e]||(this.listeners[e]=new Set,this.localCache[e]=this.storage.getItem(e)),this.listeners[e].add(t)}_removeListener(e,t){this.listeners[e]&&(this.listeners[e].delete(t),0===this.listeners[e].size)&&delete this.listeners[e],0===Object.keys(this.listeners).length&&(this.detachListener(),this.stopPolling())}async _set(e,t){await super._set(e,t),this.localCache[e]=JSON.stringify(t)}async _get(e){var t=await super._get(e);return this.localCache[e]=JSON.stringify(t),t}async _remove(e){await super._remove(e),delete this.localCache[e]}}Mr.type="LOCAL";let Ur=Mr;class Fr extends Dr{constructor(){super(()=>window.sessionStorage,"SESSION")}_addListener(e,t){}_removeListener(e,t){}}Fr.type="SESSION";let Vr=Fr;class xr{constructor(e){this.eventTarget=e,this.handlersMap={},this.boundEventHandler=this.handleEvent.bind(this)}static _getInstance(t){var e=this.receivers.find(e=>e.isListeningto(t));return e||(e=new xr(t),this.receivers.push(e),e)}isListeningto(e){return this.eventTarget===e}async handleEvent(e){let t=e,{eventId:r,eventType:n,data:i}=t.data;var e=this.handlersMap[n];null!=e&&e.size&&(t.ports[0].postMessage({status:"ack",eventId:r,eventType:n}),e=Array.from(e).map(async e=>e(t.origin,i)),e=await Promise.all(e.map(async e=>{try{return{fulfilled:!0,value:await e}}catch(e){return{fulfilled:!1,reason:e}}})),t.ports[0].postMessage({status:"done",eventId:r,eventType:n,response:e}))}_subscribe(e,t){0===Object.keys(this.handlersMap).length&&this.eventTarget.addEventListener("message",this.boundEventHandler),this.handlersMap[e]||(this.handlersMap[e]=new Set),this.handlersMap[e].add(t)}_unsubscribe(e,t){this.handlersMap[e]&&t&&this.handlersMap[e].delete(t),t&&0!==this.handlersMap[e].size||delete this.handlersMap[e],0===Object.keys(this.handlersMap).length&&this.eventTarget.removeEventListener("message",this.boundEventHandler)}}function jr(e="",t=10){let r="";for(let e=0;e<t;e++)r+=Math.floor(10*Math.random());return e+r}xr.receivers=[];class Hr{constructor(e){this.target=e,this.handlers=new Set}removeMessageHandler(e){e.messageChannel&&(e.messageChannel.port1.removeEventListener("message",e.onMessage),e.messageChannel.port1.close()),this.handlers.delete(e)}async _send(e,t,a=50){let o="undefined"!=typeof MessageChannel?new MessageChannel:null;if(!o)throw new Error("connection_unavailable");let l,c;return new Promise((r,n)=>{let i=jr("",20),s=(o.port1.start(),setTimeout(()=>{n(new Error("unsupported_event"))},a));c={messageChannel:o,onMessage(e){var t=e;if(t.data.eventId===i)switch(t.data.status){case"ack":clearTimeout(s),l=setTimeout(()=>{n(new Error("timeout"))},3e3);break;case"done":clearTimeout(l),r(t.data.response);break;default:clearTimeout(s),clearTimeout(l),n(new Error("invalid_response"))}}},this.handlers.add(c),o.port1.addEventListener("message",c.onMessage),this.target.postMessage({eventType:e,eventId:i,data:t},[o.port2])}).finally(()=>{c&&this.removeMessageHandler(c)})}}function C(){return window}function Wr(){return void 0!==C().WorkerGlobalScope&&"function"==typeof C().importScripts}let qr="firebaseLocalStorageDb",Br="firebaseLocalStorage",zr="fbase_key";class Gr{constructor(e){this.request=e}toPromise(){return new Promise((e,t)=>{this.request.addEventListener("success",()=>{e(this.request.result)}),this.request.addEventListener("error",()=>{t(this.request.error)})})}}function Kr(e,t){return e.transaction([Br],t?"readwrite":"readonly").objectStore(Br)}function Jr(){let n=indexedDB.open(qr,1);return new Promise((t,r)=>{n.addEventListener("error",()=>{r(n.error)}),n.addEventListener("upgradeneeded",()=>{var e=n.result;try{e.createObjectStore(Br,{keyPath:zr})}catch(e){r(e)}}),n.addEventListener("success",async()=>{var e=n.result;e.objectStoreNames.contains(Br)?t(e):(e.close(),e=indexedDB.deleteDatabase(qr),await new Gr(e).toPromise(),t(await Jr()))})})}async function Yr(e,t,r){e=Kr(e,!0).put({fbase_key:t,value:r});return new Gr(e).toPromise()}function $r(e,t){e=Kr(e,!0).delete(t);return new Gr(e).toPromise()}class Xr{constructor(){this.type="LOCAL",this._shouldAllowMigration=!0,this.listeners={},this.localCache={},this.pollTimer=null,this.pendingWrites=0,this.receiver=null,this.sender=null,this.serviceWorkerReceiverAvailable=!1,this.activeServiceWorker=null,this._workerInitializationPromise=this.initializeServiceWorkerMessaging().then(()=>{},()=>{})}async _openDb(){return this.db||(this.db=await Jr()),this.db}async _withRetries(e){let t=0;for(;;)try{return await e(await this._openDb())}catch(e){if(3<t++)throw e;this.db&&(this.db.close(),this.db=void 0)}}async initializeServiceWorkerMessaging(){return Wr()?this.initializeReceiver():this.initializeSender()}async initializeReceiver(){this.receiver=xr._getInstance(Wr()?self:null),this.receiver._subscribe("keyChanged",async(e,t)=>({keyProcessed:(await this._poll()).includes(t.key)})),this.receiver._subscribe("ping",async(e,t)=>["keyChanged"])}async initializeSender(){var e,t;this.activeServiceWorker=await(async()=>{if(null==navigator||!navigator.serviceWorker)return null;try{return(await navigator.serviceWorker.ready).active}catch(e){return null}})(),this.activeServiceWorker&&(this.sender=new Hr(this.activeServiceWorker),t=await this.sender._send("ping",{},800))&&null!=(e=t[0])&&e.fulfilled&&null!=(e=t[0])&&e.value.includes("keyChanged")&&(this.serviceWorkerReceiverAvailable=!0)}async notifyServiceWorker(e){var t;if(this.sender&&this.activeServiceWorker&&((null==(t=null==navigator?void 0:navigator.serviceWorker)?void 0:t.controller)||null)===this.activeServiceWorker)try{await this.sender._send("keyChanged",{key:e},this.serviceWorkerReceiverAvailable?800:50)}catch(e){}}async _isAvailable(){try{var e;return indexedDB?(await Yr(e=await Jr(),Lr,"1"),await $r(e,Lr),!0):!1}catch(e){}return!1}async _withPendingWrite(e){this.pendingWrites++;try{await e()}finally{this.pendingWrites--}}async _set(t,r){return this._withPendingWrite(async()=>(await this._withRetries(e=>Yr(e,t,r)),this.localCache[t]=r,this.notifyServiceWorker(t)))}async _get(t){var e=await this._withRetries(e=>(async(e,t)=>(e=Kr(e,!1).get(t),void 0===(t=await new Gr(e).toPromise())?null:t.value))(e,t));return this.localCache[t]=e}async _remove(t){return this._withPendingWrite(async()=>(await this._withRetries(e=>$r(e,t)),delete this.localCache[t],this.notifyServiceWorker(t)))}async _poll(){var e=await this._withRetries(e=>{e=Kr(e,!1).getAll();return new Gr(e).toPromise()});if(!e)return[];if(0!==this.pendingWrites)return[];var t,r=[],n=new Set;if(0!==e.length)for(var{fbase_key:i,value:s}of e)n.add(i),JSON.stringify(this.localCache[i])!==JSON.stringify(s)&&(this.notifyListeners(i,s),r.push(i));for(t of Object.keys(this.localCache))this.localCache[t]&&!n.has(t)&&(this.notifyListeners(t,null),r.push(t));return r}notifyListeners(e,t){this.localCache[e]=t;e=this.listeners[e];if(e)for(var r of Array.from(e))r(t)}startPolling(){this.stopPolling(),this.pollTimer=setInterval(async()=>this._poll(),800)}stopPolling(){this.pollTimer&&(clearInterval(this.pollTimer),this.pollTimer=null)}_addListener(e,t){0===Object.keys(this.listeners).length&&this.startPolling(),this.listeners[e]||(this.listeners[e]=new Set,this._get(e)),this.listeners[e].add(t)}_removeListener(e,t){this.listeners[e]&&(this.listeners[e].delete(t),0===this.listeners[e].size)&&delete this.listeners[e],0===Object.keys(this.listeners).length&&this.stopPolling()}}Xr.type="LOCAL";let Zr=Xr;function Qr(e,t){return p(e,"POST","/v2/accounts/mfaSignIn:start",o(e,t))}let en=wt("rcb"),tn=new Ce(3e4,6e4);class rn{constructor(){var e;this.hostLanguage="",this.counter=0,this.librarySeparatelyLoaded=!(null==(e=C().grecaptcha)||!e.render)}load(i,s=""){var e;return m((e=s).length<=6&&/^\s*[a-zA-Z0-9\-]*\s*$/.test(e),i,"argument-error"),this.shouldResolveImmediately(s)&&je(C().grecaptcha)?Promise.resolve(C().grecaptcha):new Promise((t,r)=>{let n=C().setTimeout(()=>{r(u(i,"network-request-failed"))},tn.get());C()[en]=()=>{C().clearTimeout(n),delete C()[en];var e=C().grecaptcha;if(e&&je(e)){let r=e.render;e.render=(e,t)=>{e=r(e,t);return this.counter++,e},this.hostLanguage=s,t(e)}else r(u(i,"internal-error"))},It(yt.recaptchaV2Script+"?"+se({onload:en,render:"explicit",hl:s})).catch(()=>{clearTimeout(n),r(u(i,"internal-error"))})})}clearedOneInstance(){this.counter--}shouldResolveImmediately(e){var t;return!(null==(t=C().grecaptcha)||!t.render)&&(e===this.hostLanguage||0<this.counter||this.librarySeparatelyLoaded)}}class nn{async load(e){return new Tt(e)}clearedOneInstance(){}}let sn="recaptcha",an={theme:"light",type:"image"};class on{constructor(e,t,r=Object.assign({},an)){this.parameters=r,this.type=sn,this.destroyed=!1,this.widgetId=null,this.tokenChangeListeners=new Set,this.renderPromise=null,this.recaptcha=null,this.auth=I(e),this.isInvisible="invisible"===this.parameters.size,m("undefined"!=typeof document,this.auth,"operation-not-supported-in-this-environment");r="string"==typeof t?document.getElementById(t):t;m(r,this.auth,"argument-error"),this.container=r,this.parameters.callback=this.makeTokenCallback(this.parameters.callback),this._recaptchaLoader=new(this.auth.settings.appVerificationDisabledForTesting?nn:rn),this.validateStartingState()}async verify(){this.assertNotDestroyed();let e=await this.render(),n=this.getAssertedRecaptcha();var t=n.getResponse(e);return t||new Promise(t=>{let r=e=>{e&&(this.tokenChangeListeners.delete(r),t(e))};this.tokenChangeListeners.add(r),this.isInvisible&&n.execute(e)})}render(){try{this.assertNotDestroyed()}catch(e){return Promise.reject(e)}return this.renderPromise||(this.renderPromise=this.makeRenderPromise().catch(e=>{throw this.renderPromise=null,e})),this.renderPromise}_reset(){this.assertNotDestroyed(),null!==this.widgetId&&this.getAssertedRecaptcha().reset(this.widgetId)}clear(){this.assertNotDestroyed(),this.destroyed=!0,this._recaptchaLoader.clearedOneInstance(),this.isInvisible||this.container.childNodes.forEach(e=>{this.container.removeChild(e)})}validateStartingState(){m(!this.parameters.sitekey,this.auth,"argument-error"),m(this.isInvisible||!this.container.hasChildNodes(),this.auth,"argument-error"),m("undefined"!=typeof document,this.auth,"operation-not-supported-in-this-environment")}makeTokenCallback(r){return t=>{var e;this.tokenChangeListeners.forEach(e=>e(t)),"function"==typeof r?r(t):"string"==typeof r&&"function"==typeof(e=C()[r])&&e(t)}}assertNotDestroyed(){m(!this.destroyed,this.auth,"internal-error")}async makeRenderPromise(){if(await this.init(),!this.widgetId){let e=this.container;var t;this.isInvisible||(t=document.createElement("div"),e.appendChild(t),e=t),this.widgetId=this.getAssertedRecaptcha().render(e,this.parameters)}return this.widgetId}async init(){m(Ae()&&!Wr(),this.auth,"internal-error"),await(()=>{let t=null;return new Promise(e=>{"complete"===document.readyState?e():(t=()=>e(),window.addEventListener("load",t))}).catch(e=>{throw t&&window.removeEventListener("load",t),e})})(),this.recaptcha=await this._recaptchaLoader.load(this.auth,this.auth.languageCode||void 0);var e=await((await p(this.auth,"GET","/v1/recaptchaParams")).recaptchaSiteKey||"");m(e,this.auth,"internal-error"),this.parameters.sitekey=e}getAssertedRecaptcha(){return m(this.recaptcha,this.auth,"internal-error"),this.recaptcha}}class ln{constructor(e,t){this.verificationId=e,this.onConfirmation=t}confirm(e){e=Wt._fromVerification(this.verificationId,e);return this.onConfirmation(e)}}async function cn(t,r,n){var i,e,s,a,o,l,c,d;if(!t._getRecaptchaConfig())try{s=await qe(e=I(e=t),{clientType:"CLIENT_TYPE_WEB",version:"RECAPTCHA_ENTERPRISE"}),s=new We(s),null==e.tenantId?e._agentRecaptchaConfig=s:e._tenantRecaptchaConfigs[e.tenantId]=s,await(!s.isAnyProviderEnabled()||!new Rt(e).verify())}catch(e){console.log("Failed to initialize reCAPTCHA Enterprise config. Triggering the reCAPTCHA v2 verification.")}try{let e;return("session"in(e="string"==typeof r?{phoneNumber:r}:r)?(a=e.session,"phoneNumber"in e?(m("enroll"===a.type,t,"internal-error"),o={idToken:a.credential,phoneEnrollmentInfo:{phoneNumber:e.phoneNumber,clientType:"CLIENT_TYPE_WEB"}},(await w(t,o,"mfaSmsEnrollment",async(e,t)=>t.phoneEnrollmentInfo.captchaResponse===St?(m((null==n?void 0:n.type)===sn,e,"argument-error"),Cr(e,await dn(e,t,n))):Cr(e,t),"PHONE_PROVIDER").catch(e=>Promise.reject(e))).phoneSessionInfo):(m("signin"===a.type,t,"internal-error"),m(l=(null==(i=e.multiFactorHint)?void 0:i.uid)||e.multiFactorUid,t,"missing-multi-factor-info"),c={mfaPendingCredential:a.credential,mfaEnrollmentId:l,phoneSignInInfo:{clientType:"CLIENT_TYPE_WEB"}},(await w(t,c,"mfaSmsSignIn",async(e,t)=>t.phoneSignInInfo.captchaResponse===St?(m((null==n?void 0:n.type)===sn,e,"argument-error"),Qr(e,await dn(e,t,n))):Qr(e,t),"PHONE_PROVIDER").catch(e=>Promise.reject(e))).phoneResponseInfo)):(d={phoneNumber:e.phoneNumber,clientType:"CLIENT_TYPE_WEB"},await w(t,d,"sendVerificationCode",async(e,t)=>t.captchaResponse===St?(m((null==n?void 0:n.type)===sn,e,"argument-error"),jt(e,await dn(e,t,n))):jt(e,t),"PHONE_PROVIDER").catch(e=>Promise.reject(e)))).sessionInfo}finally{null!=n&&n._reset()}}async function dn(e,t,r){m(r.type===sn,e,"argument-error");var n,i,s,r=await r.verify(),e=(m("string"==typeof r,e,"argument-error"),Object.assign({},t));return"phoneEnrollmentInfo"in e?(t=e.phoneEnrollmentInfo.phoneNumber,i=e.phoneEnrollmentInfo.captchaResponse,s=e.phoneEnrollmentInfo.clientType,n=e.phoneEnrollmentInfo.recaptchaVersion,Object.assign(e,{phoneEnrollmentInfo:{phoneNumber:t,recaptchaToken:r,captchaResponse:i,clientType:s,recaptchaVersion:n}})):"phoneSignInInfo"in e?(t=e.phoneSignInInfo.captchaResponse,i=e.phoneSignInInfo.clientType,s=e.phoneSignInInfo.recaptchaVersion,Object.assign(e,{phoneSignInInfo:{recaptchaToken:r,captchaResponse:t,clientType:i,recaptchaVersion:s}})):Object.assign(e,{recaptchaToken:r}),e}class O{constructor(e){this.providerId=O.PROVIDER_ID,this.auth=I(e)}verifyPhoneNumber(e,t){return cn(this.auth,e,s(t))}static credential(e,t){return Wt._fromVerification(e,t)}static credentialFromResult(e){return O.credentialFromTaggedObject(e)}static credentialFromError(e){return O.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){var t;return e&&({phoneNumber:e,temporaryProof:t}=e,e)&&t?Wt._fromTokenResponse(e,t):null}}function un(e,t){return t?_(t):(m(e._popupRedirectResolver,e,"argument-error"),e._popupRedirectResolver)}O.PROVIDER_ID="phone",O.PHONE_SIGN_IN_METHOD="phone";class hn extends Nt{constructor(e){super("custom","custom"),this.params=e}_getIdTokenResponse(e){return T(e,this._buildIdpRequest())}_linkToIdToken(e,t){return T(e,this._buildIdpRequest(t))}_getReauthenticationResolver(e){return T(e,this._buildIdpRequest())}_buildIdpRequest(e){var t={requestUri:this.params.requestUri,sessionId:this.params.sessionId,postBody:this.params.postBody,tenantId:this.params.tenantId,pendingToken:this.params.pendingToken,returnSecureToken:!0,returnIdpCredential:!0};return e&&(t.idToken=e),t}}function pn(e){return ir(e.auth,new hn(e),e.bypassAuthState)}function mn(e){var{auth:t,user:r}=e;return m(r,t,"internal-error"),nr(r,new hn(e),e.bypassAuthState)}async function gn(e){var{auth:t,user:r}=e;return m(r,t,"internal-error"),tr(r,new hn(e),e.bypassAuthState)}class fn{constructor(e,t,r,n,i=!1){this.auth=e,this.resolver=r,this.user=n,this.bypassAuthState=i,this.pendingPromise=null,this.eventManager=null,this.filter=Array.isArray(t)?t:[t]}execute(){return new Promise(async(e,t)=>{this.pendingPromise={resolve:e,reject:t};try{this.eventManager=await this.resolver._initialize(this.auth),await this.onExecution(),this.eventManager.registerConsumer(this)}catch(e){this.reject(e)}})}async onAuthEvent(e){var{urlResponse:e,sessionId:t,postBody:r,tenantId:n,error:i,type:s}=e;if(i)this.reject(i);else{i={auth:this.auth,requestUri:e,sessionId:t,tenantId:n||void 0,postBody:r||void 0,user:this.user,bypassAuthState:this.bypassAuthState};try{this.resolve(await this.getIdpTask(s)(i))}catch(e){this.reject(e)}}}onError(e){this.reject(e)}getIdpTask(e){switch(e){case"signInViaPopup":case"signInViaRedirect":return pn;case"linkViaPopup":case"linkViaRedirect":return gn;case"reauthViaPopup":case"reauthViaRedirect":return mn;default:d(this.auth,"internal-error")}}resolve(e){a(this.pendingPromise,"Pending promise was never set"),this.pendingPromise.resolve(e),this.unregisterAndCleanUp()}reject(e){a(this.pendingPromise,"Pending promise was never set"),this.pendingPromise.reject(e),this.unregisterAndCleanUp()}unregisterAndCleanUp(){this.eventManager&&this.eventManager.unregisterConsumer(this),this.pendingPromise=null,this.cleanUp()}}let vn=new Ce(2e3,1e4);class N extends fn{constructor(e,t,r,n,i){super(e,t,n,i),this.provider=r,this.authWindow=null,this.pollId=null,N.currentPopupAction&&N.currentPopupAction.cancel(),N.currentPopupAction=this}async executeNotNull(){var e=await this.execute();return m(e,this.auth,"internal-error"),e}async onExecution(){a(1===this.filter.length,"Popup operations only handle one event");var e=jr();this.authWindow=await this.resolver._openPopup(this.auth,this.provider,this.filter[0],e),this.authWindow.associatedEvent=e,this.resolver._originValidation(this.auth).catch(e=>{this.reject(e)}),this.resolver._isIframeWebStorageSupported(this.auth,e=>{e||this.reject(u(this.auth,"web-storage-unsupported"))}),this.pollUserCancellation()}get eventId(){var e;return(null==(e=this.authWindow)?void 0:e.associatedEvent)||null}cancel(){this.reject(u(this.auth,"cancelled-popup-request"))}cleanUp(){this.authWindow&&this.authWindow.close(),this.pollId&&window.clearTimeout(this.pollId),this.authWindow=null,this.pollId=null,N.currentPopupAction=null}pollUserCancellation(){let t=()=>{var e;null!=(e=null==(e=this.authWindow)?void 0:e.window)&&e.closed?this.pollId=window.setTimeout(()=>{this.pollId=null,this.reject(u(this.auth,"popup-closed-by-user"))},8e3):this.pollId=window.setTimeout(t,vn.get())};t()}}N.currentPopupAction=null;let _n="pendingRedirect",yn=new Map;class In extends fn{constructor(e,t,r=!1){super(e,["signInViaRedirect","linkViaRedirect","reauthViaRedirect","unknown"],t,void 0,r),this.eventId=null}async execute(){let t=yn.get(this.auth._key());if(!t){try{let e=await(async(e,t)=>{var r;return t=bn(t),!!await(e=En(e))._isAvailable()&&(r="true"===await e._get(t),await e._remove(t),r)})(this.resolver,this.auth)?await super.execute():null;t=()=>Promise.resolve(e)}catch(e){t=()=>Promise.reject(e)}yn.set(this.auth._key(),t)}return this.bypassAuthState||yn.set(this.auth._key(),()=>Promise.resolve(null)),t()}async onAuthEvent(e){if("signInViaRedirect"===e.type)return super.onAuthEvent(e);if("unknown"===e.type)this.resolve(null);else if(e.eventId){var t=await this.auth._redirectUserForId(e.eventId);if(t)return this.user=t,super.onAuthEvent(e);this.resolve(null)}}async onExecution(){}cleanUp(){}}async function wn(e,t){return En(e)._set(bn(t),"true")}function Tn(e,t){yn.set(e._key(),t)}function En(e){return _(e._redirectPersistence)}function bn(e){return y(_n,e.config.apiKey,e.name)}function kn(e,t,r){return(async(e,t,r)=>{var n;return Ki._isFirebaseServerApp(e.app)?Promise.reject(h(e)):(n=I(e),ke(e,t,b),await n._initializationPromise,await wn(e=un(n,r),n),e._openRedirect(n,t,"signInViaRedirect"))})(e,t,r)}function Sn(e,t,r){return(async(e,t,r)=>{if(ke((e=s(e)).auth,t,b),Ki._isFirebaseServerApp(e.auth.app))return Promise.reject(h(e.auth));await e.auth._initializationPromise;await wn(r=un(e.auth,r),e.auth);var n=await Pn(e);return r._openRedirect(e.auth,t,"reauthViaRedirect",n)})(e,t,r)}function Rn(e,t,r){return(async(e,t,r)=>{ke((e=s(e)).auth,t,b),await e.auth._initializationPromise;var r=un(e.auth,r),n=(await rr(!1,e,t.providerId),await wn(r,e.auth),await Pn(e));return r._openRedirect(e.auth,t,"linkViaRedirect",n)})(e,t,r)}async function An(e,t,r=!1){var n;return Ki._isFirebaseServerApp(e.app)?Promise.reject(h(e)):(n=un(e=I(e),t),(n=await new In(e,n,r).execute())&&!r&&(delete n.user._redirectEventId,await e._persistUserIfCurrent(n.user),await e._setRedirectUser(null,t)),n)}async function Pn(e){var t=jr(e.uid+":::");return e._redirectEventId=t,await e.auth._setRedirectUser(e),await e.auth._persistUserIfCurrent(e),t}class Cn{constructor(e){this.auth=e,this.cachedEventUids=new Set,this.consumers=new Set,this.queuedRedirectEvent=null,this.hasHandledPotentialRedirect=!1,this.lastProcessedEventTime=Date.now()}registerConsumer(e){this.consumers.add(e),this.queuedRedirectEvent&&this.isEventForConsumer(this.queuedRedirectEvent,e)&&(this.sendToConsumer(this.queuedRedirectEvent,e),this.saveEventToCache(this.queuedRedirectEvent),this.queuedRedirectEvent=null)}unregisterConsumer(e){this.consumers.delete(e)}onEvent(t){if(this.hasEventBeenHandled(t))return!1;let r=!1;return this.consumers.forEach(e=>{this.isEventForConsumer(t,e)&&(r=!0,this.sendToConsumer(t,e),this.saveEventToCache(t))}),this.hasHandledPotentialRedirect||!(e=>{switch(e.type){case"signInViaRedirect":case"linkViaRedirect":case"reauthViaRedirect":return 1;case"unknown":return Nn(e);default:return}})(t)||(this.hasHandledPotentialRedirect=!0,r)||(this.queuedRedirectEvent=t,r=!0),r}sendToConsumer(e,t){var r;e.error&&!Nn(e)?(r=(null==(r=e.error.code)?void 0:r.split("auth/")[1])||"internal-error",t.onError(u(this.auth,r))):t.onAuthEvent(e)}isEventForConsumer(e,t){var r=null===t.eventId||!!e.eventId&&e.eventId===t.eventId;return t.filter.includes(e.type)&&r}hasEventBeenHandled(e){return 6e5<=Date.now()-this.lastProcessedEventTime&&this.cachedEventUids.clear(),this.cachedEventUids.has(On(e))}saveEventToCache(e){this.cachedEventUids.add(On(e)),this.lastProcessedEventTime=Date.now()}}function On(e){return[e.type,e.eventId,e.sessionId,e.tenantId].filter(e=>e).join("-")}function Nn({type:e,error:t}){return"unknown"===e&&"auth/no-auth-event"===(null==t?void 0:t.code)}async function Ln(e,t={}){return p(e,"GET","/v1/projects",t)}let Dn=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,Mn=/^https?/;async function Un(e){if(!e.config.emulator){var t,r=(await Ln(e)).authorizedDomains;for(t of r)try{if((e=>{var t,r=Re(),{protocol:n,hostname:i}=new URL(r);return e.startsWith("chrome-extension://")?""===(t=new URL(e)).hostname&&""===i?"chrome-extension:"===n&&e.replace("chrome-extension://","")===r.replace("chrome-extension://",""):"chrome-extension:"===n&&t.hostname===i:Mn.test(n)&&(Dn.test(e)?i===e:(r=e.replace(/\./g,"\\."),(t=new RegExp("^(.+\\."+r+"|"+r+")$","i")).test(i)))})(t))return}catch(e){}d(e,"unauthorized-domain")}}let Fn=new Ce(3e4,6e4);function Vn(){var t=C().___jsl;if(null!=t&&t.H)for(var e of Object.keys(t.H))if(t.H[e].r=t.H[e].r||[],t.H[e].L=t.H[e].L||[],t.H[e].r=[...t.H[e].L],t.CP)for(let e=0;e<t.CP.length;e++)t.CP[e]=null}function xn(i){return new Promise((e,t)=>{var r;function n(){Vn(),gapi.load("gapi.iframes",{callback:()=>{e(gapi.iframes.getContext())},ontimeout:()=>{Vn(),t(u(i,"network-request-failed"))},timeout:Fn.get()})}if(null!=(r=null==(r=C().gapi)?void 0:r.iframes)&&r.Iframe)e(gapi.iframes.getContext());else{if(null==(r=C().gapi)||!r.load)return r=wt("iframefcb"),C()[r]=()=>{gapi.load?n():t(u(i,"network-request-failed"))},It(yt.gapiScript+"?onload="+r).catch(e=>t(e));n()}}).catch(e=>{throw jn=null,e})}let jn=null;let Hn=new Ce(5e3,15e3),Wn="__/auth/iframe",qn="emulator/auth/iframe",Bn={style:{position:"absolute",top:"-100px",width:"1px",height:"1px"},"aria-hidden":"true",tabindex:"-1"},zn=new Map([["identitytoolkit.googleapis.com","p"],["staging-identitytoolkit.sandbox.googleapis.com","s"],["test-identitytoolkit.sandbox.googleapis.com","t"]]);async function Gn(a){n=a;var e,t,r,n=await(jn=jn||xn(n)),i=C().gapi;return m(i,a,"internal-error"),n.open({where:document.body,url:(m((t=(n=a).config).authDomain,n,"auth-domain-config-required"),e=t.emulator?Oe(t,qn):`https://${n.config.authDomain}/`+Wn,t={apiKey:t.apiKey,appName:n.name,v:Ki.SDK_VERSION},(r=zn.get(n.config.apiHost))&&(t.eid=r),(r=n._getFrameworks()).length&&(t.fw=r.join(",")),e+"?"+se(t).slice(1)),messageHandlersFilter:i.iframes.CROSS_ORIGIN_IFRAMES_FILTER,attributes:Bn,dontclear:!0},s=>new Promise(async(e,t)=>{await s.restyle({setHideOnLeave:!1});let r=u(a,"network-request-failed"),n=C().setTimeout(()=>{t(r)},Hn.get());function i(){C().clearTimeout(n),e(s)}s.ping(i).then(i,()=>{t(r)})}))}let Kn={location:"yes",resizable:"yes",statusbar:"yes",toolbar:"no"};class Jn{constructor(e){this.window=e,this.associatedEvent=null}close(){if(this.window)try{this.window.close()}catch(e){}}}function Yn(e,t,r,n=500,i=600){var s=Math.max((window.screen.availHeight-i)/2,0).toString(),a=Math.max((window.screen.availWidth-n)/2,0).toString();let o="";var n=Object.assign(Object.assign({},Kn),{width:n.toString(),height:i.toString(),top:s,left:a}),i=l().toLowerCase(),s=(r&&(o=ot(i)?"_blank":r),st(i)&&(t=t||"http://localhost",n.scrollbars="yes"),Object.entries(n).reduce((e,[t,r])=>""+e+t+`=${r},`,""));if([a=l()]=[i],ht(a)&&null!=(a=window.navigator)&&a.standalone&&"_self"!==o)return r=t||"",n=o,(i=document.createElement("a")).href=r,i.target=n,(r=document.createEvent("MouseEvent")).initMouseEvent("click",!0,!0,window,1,0,0,0,0,!1,!1,!1,!1,1,null),i.dispatchEvent(r),new Jn(null);a=window.open(t||"",o,s);m(a,e,"popup-blocked");try{a.focus()}catch(e){}return new Jn(a)}let $n="__/auth/handler",Xn="emulator/auth/handler",Zn=encodeURIComponent("fac");async function Qn(e,t,r,n,i,s){m(e.config.authDomain,e,"auth-domain-config-required"),m(e.config.apiKey,e,"invalid-api-key");var a={apiKey:e.config.apiKey,appName:e.name,authType:r,redirectUrl:n,v:Ki.SDK_VERSION,eventId:i};if(t instanceof b){t.setDefaultLanguage(e.languageCode),a.providerId=t.providerId||"",(e=>{for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t))return;return 1})(t.getCustomParameters())||(a.customParameters=JSON.stringify(t.getCustomParameters()));for(var[o,l]of Object.entries(s||{}))a[o]=l}t instanceof zt&&0<(r=t.getScopes().filter(e=>""!==e)).length&&(a.scopes=r.join(",")),e.tenantId&&(a.tid=e.tenantId);var c,d=a;for(c of Object.keys(d))void 0===d[c]&&delete d[c];n=await e._getAppCheckToken(),i=n?`#${Zn}=`+encodeURIComponent(n):"";return`${s=[e.config][0],s.emulator?Oe(s,Xn):`https://${s.authDomain}/`+$n}?`+se(d).slice(1)+i}let ei="webStorageSupport";class ti{constructor(){this.eventManagers={},this.iframes={},this.originValidationPromises={},this._redirectPersistence=Vr,this._completeRedirectFn=An,this._overrideRedirectResult=Tn}async _openPopup(e,t,r,n){a(null==(i=this.eventManagers[e._key()])?void 0:i.manager,"_initialize() not called before _openPopup()");var i=await Qn(e,t,r,Re(),n);return Yn(e,i,jr())}async _openRedirect(e,t,r,n){await this._originValidation(e);e=await Qn(e,t,r,Re(),n);return C().location.href=e,new Promise(()=>{})}_initialize(e){let r=e._key();if(this.eventManagers[r]){let{manager:e,promise:t}=this.eventManagers[r];return e?Promise.resolve(e):(a(t,"If manager is not set, promise should be"),t)}let t=this.initAndGetManager(e);return this.eventManagers[r]={promise:t},t.catch(()=>{delete this.eventManagers[r]}),t}async initAndGetManager(t){var e=await Gn(t);let r=new Cn(t);return e.register("authEvent",e=>(m(null==e?void 0:e.authEvent,t,"invalid-auth-event"),{status:r.onEvent(e.authEvent)?"ACK":"ERROR"}),gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER),this.eventManagers[t._key()]={manager:r},this.iframes[t._key()]=e,r}_isIframeWebStorageSupported(t,r){this.iframes[t._key()].send(ei,{type:ei},e=>{e=null==(e=null==e?void 0:e[0])?void 0:e[ei];void 0!==e&&r(!!e),d(t,"internal-error")},gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER)}_originValidation(e){var t=e._key();return this.originValidationPromises[t]||(this.originValidationPromises[t]=Un(e)),this.originValidationPromises[t]}get _shouldInitProactively(){return pt()||at()||ht()}}let ri=ti;class ni extends class{constructor(e){this.factorId=e}_process(e,t,r){switch(t.type){case"enroll":return this._finalizeEnroll(e,t.credential,r);case"signin":return this._finalizeSignIn(e,t.credential);default:return i("unexpected MultiFactorSessionType")}}}{constructor(e){super("phone"),this.credential=e}static _fromCredential(e){return new ni(e)}_finalizeEnroll(e,t,r){return e=e,t={idToken:t,displayName:r,phoneVerificationInfo:this.credential._makeVerificationRequest()},p(e,"POST","/v2/accounts/mfaEnrollment:finalize",o(e,t))}_finalizeSignIn(e,t){return e=e,t={mfaPendingCredential:t,phoneVerificationInfo:this.credential._makeVerificationRequest()},p(e,"POST","/v2/accounts/mfaSignIn:finalize",o(e,t))}}class ii{constructor(){}static assertion(e){return ni._fromCredential(e)}}ii.FACTOR_ID="phone";var e="@firebase/auth";class si{constructor(e){this.auth=e,this.internalListeners=new Map}getUid(){var e;return this.assertAuthConfigured(),(null==(e=this.auth.currentUser)?void 0:e.uid)||null}async getToken(e){return this.assertAuthConfigured(),await this.auth._initializationPromise,this.auth.currentUser?{accessToken:await this.auth.currentUser.getIdToken(e)}:null}addAuthTokenListener(t){var e;this.assertAuthConfigured(),this.internalListeners.has(t)||(e=this.auth.onIdTokenChanged(e=>{t((null==e?void 0:e.stsTokenManager.accessToken)||null)}),this.internalListeners.set(t,e),this.updateProactiveRefresh())}removeAuthTokenListener(e){this.assertAuthConfigured();var t=this.internalListeners.get(e);t&&(this.internalListeners.delete(e),t(),this.updateProactiveRefresh())}assertAuthConfigured(){m(this.auth._initializationPromise,"dependent-sdk-initialized-before-auth")}updateProactiveRefresh(){0<this.internalListeners.size?this.auth._startProactiveRefresh():this.auth._stopProactiveRefresh()}}var ai,oi;function li(){return window}ai="authIdTokenMaxAge",null!=(Ti=z())&&Ti["_"+ai],yt={loadJS(n){return new Promise((e,r)=>{var t=document.createElement("script");t.setAttribute("src",n),t.onload=e,t.onerror=e=>{var t=u("internal-error");t.customData=e,r(t)},t.type="text/javascript",t.charset="UTF-8",(null!=(e=null==(e=document.getElementsByTagName("head"))?void 0:e[0])?e:document).appendChild(t)})},gapiScript:"https://apis.google.com/js/api.js",recaptchaV2Script:"https://www.google.com/recaptcha/api.js",recaptchaEnterpriseScript:"https://www.google.com/recaptcha/enterprise.js?render="},oi="Browser",Ki._registerComponent(new ge("auth",(e,{options:t})=>{var r=e.getProvider("app").getImmediate(),n=e.getProvider("heartbeat"),e=e.getProvider("app-check-internal"),{apiKey:i,authDomain:s}=r.options,i=(m(i&&!i.includes(":"),"invalid-api-key",{appName:r.name}),{apiKey:i,authDomain:s,clientPlatform:oi,apiHost:"identitytoolkit.googleapis.com",tokenApiHost:"securetoken.googleapis.com",apiScheme:"https",sdkClientVersion:mt(oi)}),s=new vt(r,n,e,i);return r=s,e=(null==(n=t)?void 0:n.persistence)||[],e=(Array.isArray(e)?e:[e]).map(_),null!=n&&n.errorMap&&r._updateErrorMap(n.errorMap),r._initializeWithPersistence(e,null==n?void 0:n.popupRedirectResolver),s},"PUBLIC").setInstantiationMode("EXPLICIT").setInstanceCreatedCallback((e,t,r)=>{e.getProvider("auth-internal").initialize()})),Ki._registerComponent(new ge("auth-internal",e=>{var e=I(e.getProvider("auth").getImmediate());return e=e,new si(e)},"PRIVATE").setInstantiationMode("EXPLICIT")),Ki.registerVersion(e,"1.10.7",(e=>{switch(e){case"Node":return"node";case"ReactNative":return"rn";case"Worker":return"webworker";case"Cordova":return"cordova";case"WebExtension":return"web-extension";default:return}})(oi)),Ki.registerVersion(e,"1.10.7","esm2017");async function ci(e,t,r){var n=li().BuildInfo,i=(a(t.sessionId,"AuthEvent did not contain a session ID"),await(async e=>(e=(t=>{if(a(/[0-9a-zA-Z]+/.test(t),"Can only convert alpha-numeric strings"),"undefined"!=typeof TextEncoder)return(new TextEncoder).encode(t);var e=new ArrayBuffer(t.length),r=new Uint8Array(e);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r})(e),e=await crypto.subtle.digest("SHA-256",e),(e=Array.from(new Uint8Array(e))).map(e=>e.toString(16).padStart(2,"0")).join("")))(t.sessionId)),s={};return ht()?s.ibi=n.packageName:ct()?s.apn=n.packageName:d(e,"operation-not-supported-in-this-environment"),n.displayName&&(s.appDisplayName=n.displayName),s.sessionId=i,Qn(e,r,t.type,void 0,null!=(n=t.eventId)?n:void 0,s)}function di(n){let i=li().cordova;return new Promise(r=>{i.plugins.browsertab.isAvailable(e=>{let t=null;e?i.plugins.browsertab.openUrl(n):t=i.InAppBrowser.open(n,(e=l(),/(iPad|iPhone|iPod).*OS 7_\d/i.test(e)||/(iPad|iPhone|iPod).*OS 8_\d/i.test(e)?"_blank":"_system"),"location=yes"),r(t)})})}let ui=20;class hi extends Cn{constructor(){super(...arguments),this.passiveListeners=new Set,this.initPromise=new Promise(e=>{this.resolveInitialized=e})}addPassiveListener(e){this.passiveListeners.add(e)}removePassiveListener(e){this.passiveListeners.delete(e)}resetRedirect(){this.queuedRedirectEvent=null,this.hasHandledPotentialRedirect=!1}onEvent(t){return this.resolveInitialized(),this.passiveListeners.forEach(e=>e(t)),super.onEvent(t)}async initialized(){await this.initPromise}}function pi(e,t,r=null){return{type:t,eventId:r,urlResponse:null,sessionId:(()=>{var t=[],r="1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";for(let e=0;e<ui;e++){var n=Math.floor(Math.random()*r.length);t.push(r.charAt(n))}return t.join("")})(),postBody:null,tenantId:e.tenantId,error:u(e,"no-auth-event")}}async function mi(e){var t=await fi()._get(vi(e));return t&&await fi()._remove(vi(e)),t}function gi(e,t){i=_i(t=t),n=i.link?decodeURIComponent(i.link):void 0,r=_i(n).link,i=i.deep_link_id?decodeURIComponent(i.deep_link_id):void 0;var r,n,i=_i(i).link||i||r||n||t;return i.includes("/__/auth/callback")?(t=(n=null==(r=null==(t=null==(n=(r=_i(i)).firebaseError?(e=>{try{return JSON.parse(e)}catch(e){return null}})(decodeURIComponent(r.firebaseError)):null)?void 0:n.code)?void 0:t.split("auth/"))?void 0:r[1])?u(n):null)?{type:e.type,eventId:e.eventId,tenantId:e.tenantId,error:t,urlResponse:null,sessionId:null,postBody:null}:{type:e.type,eventId:e.eventId,tenantId:e.tenantId,sessionId:e.sessionId,urlResponse:i,postBody:null}:null}function fi(){return _(Ur)}function vi(e){return y("authEvent",e.config.apiKey,e.name)}function _i(e){var t;return null!=e&&e.includes("?")?([t,...e]=e.split("?"),ae(e.join("?"))):{}}class yi{constructor(){this._redirectPersistence=Vr,this._shouldInitProactively=!0,this.eventManagers=new Map,this.originValidationPromises={},this._completeRedirectFn=An,this._overrideRedirectResult=Tn}async _initialize(e){var t=e._key();let r=this.eventManagers.get(t);return r||(r=new hi(e),this.eventManagers.set(t,r),this.attachCallbackListeners(e,r)),r}_openPopup(e){d(e,"operation-not-supported-in-this-environment")}async _openRedirect(e,t,r,n){i=e,a=li(),m("function"==typeof(null==(s=null==a?void 0:a.universalLinks)?void 0:s.subscribe),i,"invalid-cordova-configuration",{missingPlugin:"cordova-universal-links-plugin-fix"}),m(void 0!==(null==(s=null==a?void 0:a.BuildInfo)?void 0:s.packageName),i,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-buildInfo"}),m("function"==typeof(null==(s=null==(s=null==(s=null==a?void 0:a.cordova)?void 0:s.plugins)?void 0:s.browsertab)?void 0:s.openUrl),i,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-browsertab"}),m("function"==typeof(null==(s=null==(s=null==(s=null==a?void 0:a.cordova)?void 0:s.plugins)?void 0:s.browsertab)?void 0:s.isAvailable),i,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-browsertab"}),m("function"==typeof(null==(a=null==(s=null==a?void 0:a.cordova)?void 0:s.InAppBrowser)?void 0:a.open),i,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-inappbrowser"});var i,s=await this._initialize(e),a=(await s.initialized(),s.resetRedirect(),yn.clear(),await this._originValidation(e),pi(e,r,n)),n=(i=e,r=a,await fi()._set(vi(i),r),await ci(e,a,t));return(async(a,o,l)=>{let c=li().cordova,d=()=>{};try{await new Promise((t,e)=>{let r=null;function n(){t();var e=null==(e=c.plugins.browsertab)?void 0:e.close;"function"==typeof e&&e(),"function"==typeof(null==l?void 0:l.close)&&l.close()}function i(){r=r||window.setTimeout(()=>{e(u(a,"redirect-cancelled-by-user"))},2e3)}function s(){"visible"===(null==document?void 0:document.visibilityState)&&i()}o.addPassiveListener(n),document.addEventListener("resume",i,!1),ct()&&document.addEventListener("visibilitychange",s,!1),d=()=>{o.removePassiveListener(n),document.removeEventListener("resume",i,!1),document.removeEventListener("visibilitychange",s,!1),r&&window.clearTimeout(r)}})}finally{d()}})(e,s,await di(n))}_isIframeWebStorageSupported(e,t){throw new Error("Method not implemented.")}_originValidation(e){var t=e._key();return this.originValidationPromises[t]||(this.originValidationPromises[t]=(async e=>{var t=li().BuildInfo,r={};ht()?r.iosBundleId=t.packageName:ct()?r.androidPackageName=t.packageName:d(e,"operation-not-supported-in-this-environment"),await Ln(e,r)})(e)),this.originValidationPromises[t]}attachCallbackListeners(n,i){var{universalLinks:e,handleOpenURL:t,BuildInfo:r}=li();let s=setTimeout(async()=>{await mi(n),i.onEvent(wi())},500),a=async e=>{clearTimeout(s);var t=await mi(n);let r=null;t&&null!=e&&e.url&&(r=gi(t,e.url)),i.onEvent(r||wi())},o=(void 0!==e&&"function"==typeof e.subscribe&&e.subscribe(null,a),t),l=r.packageName.toLowerCase()+"://";li().handleOpenURL=async e=>{if(e.toLowerCase().startsWith(l)&&a({url:e}),"function"==typeof o)try{o(e)}catch(e){console.error(e)}}}}let Ii=yi;function wi(){return{type:"unknown",eventId:null,sessionId:null,urlResponse:null,postBody:null,tenantId:null,error:u("no-auth-event")}}var Ti;function Ei(){var e;return(null==(e=null==self?void 0:self.location)?void 0:e.protocol)||null}function bi(e=l()){return!("file:"!==Ei()&&"ionic:"!==Ei()&&"capacitor:"!==Ei()||!e.toLowerCase().match(/iphone|ipad|ipod|android/))}function ki(e=l()){return Q()&&11===(null==document?void 0:document.documentMode)||([e=l()]=[e],/Edge\/\d+/.test(e))}function Si(){try{var e=self.localStorage,t=jr();if(e)return e.setItem(t,"1"),e.removeItem(t),!ki()||ee()}catch(e){return Ri()&&ee()}return!1}function Ri(){return"undefined"!=typeof global&&"WorkerGlobalScope"in global&&"importScripts"in global}function Ai(){return("http:"===Ei()||"https:"===Ei()||X()||bi())&&!(Z()||$())&&Si()&&!Ri()}function Pi(){return bi()&&"undefined"!=typeof document}let L={LOCAL:"local",NONE:"none",SESSION:"session"},Ci=m,Oi="persistence";async function Ni(e){await e._initializationPromise;var t=Li(),r=y(Oi,e.config.apiKey,e.name);t&&t.setItem(r,e._getPersistenceType())}function Li(){var e;try{return(null===(e="undefined"!=typeof window?window:null)?void 0:e.sessionStorage)||null}catch(e){return null}}let Di=m;class D{constructor(){this.browserResolver=_(ri),this.cordovaResolver=_(Ii),this.underlyingResolver=null,this._redirectPersistence=Vr,this._completeRedirectFn=An,this._overrideRedirectResult=Tn}async _initialize(e){return await this.selectUnderlyingResolver(),this.assertedUnderlyingResolver._initialize(e)}async _openPopup(e,t,r,n){return await this.selectUnderlyingResolver(),this.assertedUnderlyingResolver._openPopup(e,t,r,n)}async _openRedirect(e,t,r,n){return await this.selectUnderlyingResolver(),this.assertedUnderlyingResolver._openRedirect(e,t,r,n)}_isIframeWebStorageSupported(e,t){this.assertedUnderlyingResolver._isIframeWebStorageSupported(e,t)}_originValidation(e){return this.assertedUnderlyingResolver._originValidation(e)}get _shouldInitProactively(){return Pi()||this.browserResolver._shouldInitProactively}get assertedUnderlyingResolver(){return Di(this.underlyingResolver,"internal-error"),this.underlyingResolver}async selectUnderlyingResolver(){var e;this.underlyingResolver||(e=await(!!Pi()&&new Promise(e=>{let t=setTimeout(()=>{e(!1)},1e3);document.addEventListener("deviceready",()=>{clearTimeout(t),e(!0)})})),this.underlyingResolver=e?this.cordovaResolver:this.browserResolver)}}function Mi(e){return e.unwrap()}function Ui(e,t){var r,n,i=null==(i=t.customData)?void 0:i._tokenResponse;"auth/multi-factor-auth-required"===(null==t?void 0:t.code)?t.resolver=new xi(e,(r=t,e=s(e=e),m((n=r).customData.operationType,e,"argument-error"),m(null==(r=n.customData._serverResponse)?void 0:r.mfaPendingCredential,e,"argument-error"),Pr._fromError(e,n))):i&&(e=Fi(r=t))&&(r.credential=e,r.tenantId=i.tenantId||void 0,r.email=i.email||void 0,r.phoneNumber=i.phoneNumber||void 0)}function Fi(e){var t=(e instanceof c?e.customData:e)._tokenResponse;if(!t)return null;if(!(e instanceof c)&&"temporaryProof"in t&&"phoneNumber"in t)return O.credentialFromResult(e);var r=t.providerId;if(!r||r===fe.PASSWORD)return null;let n;switch(r){case fe.GOOGLE:n=S;break;case fe.FACEBOOK:n=k;break;case fe.GITHUB:n=R;break;case fe.TWITTER:n=A;break;default:var{oauthIdToken:i,oauthAccessToken:s,oauthTokenSecret:a,pendingToken:o,nonce:l}=t;return s||a||i||o?o?r.startsWith("saml.")?Kt._create(r,o):E._fromParams({providerId:r,signInMethod:r,pendingToken:o,idToken:i,accessToken:s}):new Gt(r).credential({idToken:i,accessToken:s,rawNonce:l}):null}return e instanceof c?n.credentialFromError(e):n.credentialFromResult(e)}function M(t,e){return e.catch(e=>{throw e instanceof c&&Ui(t,e),e}).then(e=>{var t=e.operationType,r=e.user;return{operationType:t,credential:Fi(e),additionalUserInfo:Rr(e),user:U.getOrCreate(r)}})}async function Vi(t,e){let r=await e;return{verificationId:r.verificationId,confirm:e=>M(t,r.confirm(e))}}class xi{constructor(e,t){this.resolver=t,this.auth=e.wrapped()}get session(){return this.resolver.session}get hints(){return this.resolver.hints}resolveSignIn(e){return M(Mi(this.auth),this.resolver.resolveSignIn(e))}}class U{constructor(e){this._delegate=e,this.multiFactor=(e=s(e=e),Nr.has(e)||Nr.set(e,Or._fromUser(e)),Nr.get(e))}static getOrCreate(e){return U.USER_MAP.has(e)||U.USER_MAP.set(e,new U(e)),U.USER_MAP.get(e)}delete(){return this._delegate.delete()}reload(){return this._delegate.reload()}toJSON(){return this._delegate.toJSON()}getIdTokenResult(e){return this._delegate.getIdTokenResult(e)}getIdToken(e){return this._delegate.getIdToken(e)}linkAndRetrieveDataWithCredential(e){return this.linkWithCredential(e)}async linkWithCredential(e){return M(this.auth,ar(this._delegate,e))}async linkWithPhoneNumber(e,t){return Vi(this.auth,(async(e,t,r)=>{let n=s(e);return await rr(!1,n,"phone"),e=await cn(n.auth,t,s(r)),new ln(e,e=>ar(n,e))})(this._delegate,e,t))}async linkWithPopup(e){return M(this.auth,(async(e,t,r)=>(ke((e=s(e)).auth,t,b),r=un(e.auth,r),new N(e.auth,"linkViaPopup",t,r,e).executeNotNull()))(this._delegate,e,D))}async linkWithRedirect(e){return await Ni(I(this.auth)),Rn(this._delegate,e,D)}reauthenticateAndRetrieveDataWithCredential(e){return this.reauthenticateWithCredential(e)}async reauthenticateWithCredential(e){return M(this.auth,or(this._delegate,e))}reauthenticateWithPhoneNumber(e,t){return Vi(this.auth,(async(e,t,r)=>{let n=s(e);return Ki._isFirebaseServerApp(n.auth.app)?Promise.reject(h(n.auth)):(e=await cn(n.auth,t,s(r)),new ln(e,e=>or(n,e)))})(this._delegate,e,t))}reauthenticateWithPopup(e){return M(this.auth,(async(e,t,r)=>(e=s(e),Ki._isFirebaseServerApp(e.auth.app)?Promise.reject(u(e.auth,"operation-not-supported-in-this-environment")):(ke(e.auth,t,b),r=un(e.auth,r),new N(e.auth,"reauthViaPopup",t,r,e).executeNotNull())))(this._delegate,e,D))}async reauthenticateWithRedirect(e){return await Ni(I(this.auth)),Sn(this._delegate,e,D)}sendEmailVerification(e){return vr(this._delegate,e)}async unlink(e){return await er(this._delegate,e),this}updateEmail(e){return t=this._delegate,e=e,t=s(t),Ki._isFirebaseServerApp(t.auth.app)?Promise.reject(h(t.auth)):Ir(t,e,null);var t}updatePassword(e){return Ir(s(this._delegate),null,e)}updatePhoneNumber(e){return(async(e,t)=>{if(e=s(e),Ki._isFirebaseServerApp(e.auth.app))return Promise.reject(h(e.auth));await tr(e,t)})(this._delegate,e)}updateProfile(e){return yr(this._delegate,e)}verifyBeforeUpdateEmail(e,t){return _r(this._delegate,e,t)}get emailVerified(){return this._delegate.emailVerified}get isAnonymous(){return this._delegate.isAnonymous}get metadata(){return this._delegate.metadata}get phoneNumber(){return this._delegate.phoneNumber}get providerData(){return this._delegate.providerData}get refreshToken(){return this._delegate.refreshToken}get tenantId(){return this._delegate.tenantId}get displayName(){return this._delegate.displayName}get email(){return this._delegate.email}get photoURL(){return this._delegate.photoURL}get providerId(){return this._delegate.providerId}get uid(){return this._delegate.uid}get auth(){return this._delegate.auth}}U.USER_MAP=new WeakMap;let ji=m;class Hi{constructor(e,t){var r,n;this.app=e,t.isInitialized()?this._delegate=t.getImmediate():(r=e.options.apiKey,ji(r,"invalid-api-key",{appName:e.name}),ji(r,"invalid-api-key",{appName:e.name}),n="undefined"!=typeof window?D:void 0,this._delegate=t.initialize({options:{persistence:((e,t)=>{var r=((e,t)=>{var r=Li();if(!r)return[];switch(e=y(Oi,e,t),r.getItem(e)){case L.NONE:return[rt];case L.LOCAL:return[Zr,Vr];case L.SESSION:return[Vr];default:return[]}})(e,t);if("undefined"==typeof self||r.includes(Zr)||r.push(Zr),"undefined"!=typeof window)for(var n of[Ur,Vr])r.includes(n)||r.push(n);return r.includes(rt)||r.push(rt),r})(r,e.name),popupRedirectResolver:n}}),this._delegate._updateErrorMap(ye)),this.linkUnderlyingAuth()}get emulatorConfig(){return this._delegate.emulatorConfig}get currentUser(){return this._delegate.currentUser?U.getOrCreate(this._delegate.currentUser):null}get languageCode(){return this._delegate.languageCode}set languageCode(e){this._delegate.languageCode=e}get settings(){return this._delegate.settings}get tenantId(){return this._delegate.tenantId}set tenantId(e){this._delegate.tenantId=e}useDeviceLanguage(){this._delegate.useDeviceLanguage()}signOut(){return this._delegate.signOut()}useEmulator(e,t){Pt(this._delegate,e,t)}applyActionCode(e){return mr(this._delegate,e)}checkActionCode(e){return gr(this._delegate,e)}confirmPasswordReset(e,t){return(async(t,e,r)=>{await Lt(s(t),{oobCode:e,newPassword:r}).catch(async e=>{throw"auth/password-does-not-meet-requirements"===e.code&&pr(t),e})})(this._delegate,e,t)}async createUserWithEmailAndPassword(e,t){return M(this._delegate,(async(t,e,r)=>{var n;return Ki._isFirebaseServerApp(t.app)?Promise.reject(h(t)):(e=await w(n=I(t),{returnSecureToken:!0,email:e,password:r,clientType:"CLIENT_TYPE_WEB"},"signUpPassword",Yt,"EMAIL_PASSWORD_PROVIDER").catch(e=>{throw"auth/password-does-not-meet-requirements"===e.code&&pr(t),e}),r=await P._fromIdTokenResponse(n,"signIn",e),await n._updateCurrentUser(r.user),r)})(this._delegate,e,t))}fetchProvidersForEmail(e){return this.fetchSignInMethodsForEmail(e)}fetchSignInMethodsForEmail(e){return fr(this._delegate,e)}isSignInWithEmailLink(e){return this._delegate,e=e,"EMAIL_SIGNIN"===(null==(e=qt.parseLink(e))?void 0:e.operation)}async getRedirectResult(){ji(Ai(),this._delegate,"operation-not-supported-in-this-environment");t=this._delegate,e=D,await I(t)._initializationPromise;var e,t=await An(t,e,!1);return t?M(this._delegate,Promise.resolve(t)):{credential:null,user:null}}addFrameworkForLogging(e){I(this._delegate)._logFramework(e)}onAuthStateChanged(e,t,r){var{next:e,error:t,complete:r}=Wi(e,t,r);return this._delegate.onAuthStateChanged(e,t,r)}onIdTokenChanged(e,t,r){var{next:e,error:t,complete:r}=Wi(e,t,r);return this._delegate.onIdTokenChanged(e,t,r)}sendSignInLinkToEmail(e,t){return(async(e,t,r)=>{let n=I(e);t=e={requestType:"EMAIL_SIGNIN",email:t,clientType:"CLIENT_TYPE_WEB"},m((r=r).handleCodeInApp,n,"argument-error"),r&&hr(n,t,r),await w(n,e,"getOobCode",Vt,"EMAIL_PASSWORD_PROVIDER")})(this._delegate,e,t)}sendPasswordResetEmail(e,t){return(async(e,t,r)=>{e=I(e),t={requestType:"PASSWORD_RESET",email:t,clientType:"CLIENT_TYPE_WEB"},r&&hr(e,t,r),await w(e,t,"getOobCode",Ft,"EMAIL_PASSWORD_PROVIDER")})(this._delegate,e,t||void 0)}async setPersistence(e){var t,r;t=this._delegate,r=e,Ci(Object.values(L).includes(r),t,"invalid-persistence-type"),Z()?Ci(r!==L.SESSION,t,"unsupported-persistence-type"):$()?Ci(r===L.NONE,t,"unsupported-persistence-type"):Ri()?Ci(r===L.NONE||r===L.LOCAL&&ee(),t,"unsupported-persistence-type"):Ci(r===L.NONE||Si(),t,"unsupported-persistence-type");let n;switch(e){case L.SESSION:n=Vr;break;case L.LOCAL:var i=await _(Zr)._isAvailable();n=i?Zr:Ur;break;case L.NONE:n=rt;break;default:return d("argument-error",{appName:this._delegate.name})}return this._delegate.setPersistence(n)}signInAndRetrieveDataWithCredential(e){return this.signInWithCredential(e)}signInAnonymously(){return M(this._delegate,(async e=>{var t;return Ki._isFirebaseServerApp(e.app)?Promise.reject(h(e)):(await(e=I(e))._initializationPromise,null!=(t=e.currentUser)&&t.isAnonymous?new P({user:e.currentUser,providerId:null,operationType:"signIn"}):(t=await Yt(e,{returnSecureToken:!0}),t=await P._fromIdTokenResponse(e,"signIn",t,!0),await e._updateCurrentUser(t.user),t))})(this._delegate))}signInWithCredential(e){return M(this._delegate,sr(this._delegate,e))}signInWithCustomToken(e){return M(this._delegate,lr(this._delegate,e))}signInWithEmailAndPassword(e,t){return M(this._delegate,(r=this._delegate,e=e,t=t,Ki._isFirebaseServerApp(r.app)?Promise.reject(h(r)):sr(s(r),Bt.credential(e,t)).catch(async e=>{throw"auth/password-does-not-meet-requirements"===e.code&&pr(r),e})));var r}signInWithEmailLink(e,t){return M(this._delegate,(async(e,t,r)=>Ki._isFirebaseServerApp(e.app)?Promise.reject(h(e)):(e=s(e),m((t=Bt.credentialWithLink(t,r||Re()))._tenantId===(e.tenantId||null),e,"tenant-id-mismatch"),sr(e,t)))(this._delegate,e,t))}signInWithPhoneNumber(e,t){return Vi(this._delegate,(async(e,t,r)=>{if(Ki._isFirebaseServerApp(e.app))return Promise.reject(h(e));let n=I(e);return e=await cn(n,t,s(r)),new ln(e,e=>sr(n,e))})(this._delegate,e,t))}async signInWithPopup(e){return ji(Ai(),this._delegate,"operation-not-supported-in-this-environment"),M(this._delegate,(async(e,t,r)=>{var n;return Ki._isFirebaseServerApp(e.app)?Promise.reject(u(e,"operation-not-supported-in-this-environment")):(n=I(e),ke(e,t,b),e=un(n,r),new N(n,"signInViaPopup",t,e).executeNotNull())})(this._delegate,e,D))}async signInWithRedirect(e){return ji(Ai(),this._delegate,"operation-not-supported-in-this-environment"),await Ni(this._delegate),kn(this._delegate,e,D)}updateCurrentUser(e){return this._delegate.updateCurrentUser(e)}verifyPasswordResetCode(e){return(async(e,t)=>(e=(await gr(s(e),t)).data).email)(this._delegate,e)}unwrap(){return this._delegate}_delete(){return this._delegate._delete()}linkUnderlyingAuth(){this._delegate.wrapped=()=>this}}function Wi(e,t,r){let n=e,i=("function"!=typeof e&&({next:n,error:t,complete:r}=e),n);return{next:e=>i(e&&U.getOrCreate(e)),error:t,complete:r}}Hi.Persistence=L;class qi{static credential(e,t){return O.credential(e,t)}constructor(){this.providerId="phone",this._delegate=new O(Mi(F.default.auth()))}verifyPhoneNumber(e,t){return this._delegate.verifyPhoneNumber(e,t)}unwrap(){return this._delegate}}qi.PHONE_SIGN_IN_METHOD=O.PHONE_SIGN_IN_METHOD,qi.PROVIDER_ID=O.PROVIDER_ID;let Bi=m;class zi{constructor(e,t,r=F.default.app()){var n;Bi(null==(n=r.options)?void 0:n.apiKey,"invalid-api-key",{appName:r.name}),this._delegate=new on(r.auth(),e,t),this.type=this._delegate.type}clear(){this._delegate.clear()}render(){return this._delegate.render()}verify(){return this._delegate.verify()}}(Ti=F.default).INTERNAL.registerComponent(new ge("auth-compat",e=>{var t=e.getProvider("app-compat").getImmediate(),e=e.getProvider("auth");return new Hi(t,e)},"PUBLIC").setServiceProps({ActionCodeInfo:{Operation:{EMAIL_SIGNIN:ve.EMAIL_SIGNIN,PASSWORD_RESET:ve.PASSWORD_RESET,RECOVER_EMAIL:ve.RECOVER_EMAIL,REVERT_SECOND_FACTOR_ADDITION:ve.REVERT_SECOND_FACTOR_ADDITION,VERIFY_AND_CHANGE_EMAIL:ve.VERIFY_AND_CHANGE_EMAIL,VERIFY_EMAIL:ve.VERIFY_EMAIL}},EmailAuthProvider:Bt,FacebookAuthProvider:k,GithubAuthProvider:R,GoogleAuthProvider:S,OAuthProvider:Gt,SAMLAuthProvider:Jt,PhoneAuthProvider:qi,PhoneMultiFactorGenerator:ii,RecaptchaVerifier:zi,TwitterAuthProvider:A,Auth:Hi,AuthCredential:Nt,Error:c}).setInstantiationMode("LAZY").setMultipleInstances(!1)),Ti.registerVersion("@firebase/auth-compat","0.5.27")}.apply(this,arguments)}catch(e){throw console.error(e),new Error("Cannot instantiate firebase-auth.js - be sure to load firebase-app.js first.")}});//# sourceMappingURL=firebase-auth.js.map
