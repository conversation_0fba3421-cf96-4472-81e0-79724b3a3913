{"version": 3, "sources": ["../../src/jwt/signJwt.ts", "../../src/jwt/index.ts"], "sourcesContent": ["import { SignJWTError } from '../errors';\nimport { runtime } from '../runtime';\nimport { base64url } from '../util/rfc4648';\nimport { getCryptoAlgorithm } from './algorithms';\nimport { importKey } from './cryptoKeys';\nimport type { JwtReturnType } from './types';\n\nexport interface SignJwtOptions {\n  algorithm?: string;\n  header?: Record<string, unknown>;\n}\n\nfunction encodeJwtData(value: unknown): string {\n  const stringified = JSON.stringify(value);\n  const encoder = new TextEncoder();\n  const encoded = encoder.encode(stringified);\n  return base64url.stringify(encoded, { pad: false });\n}\n\n/**\n * Signs a JSON Web Token (JWT) with the given payload, key, and options.\n * This function is intended to be used *internally* by other Clerk packages and typically\n * should not be used directly.\n *\n * @internal\n * @param payload The payload to include in the JWT.\n * @param key The key to use for signing the JWT. Can be a string or a JsonWebKey.\n * @param options The options to use for signing the JWT.\n * @returns A Promise that resolves to the signed JWT string.\n * @throws An error if no algorithm is specified or if the specified algorithm is unsupported.\n * @throws An error if there is an issue with importing the key or signing the JWT.\n */\nexport async function signJwt(\n  payload: Record<string, unknown>,\n  key: string | JsonWebKey,\n  options: SignJwtOptions,\n): Promise<JwtReturnType<string, Error>> {\n  if (!options.algorithm) {\n    throw new Error('No algorithm specified');\n  }\n  const encoder = new TextEncoder();\n\n  const algorithm = getCryptoAlgorithm(options.algorithm);\n  if (!algorithm) {\n    return {\n      errors: [new SignJWTError(`Unsupported algorithm ${options.algorithm}`)],\n    };\n  }\n\n  const cryptoKey = await importKey(key, algorithm, 'sign');\n  const header = options.header || { typ: 'JWT' };\n\n  header.alg = options.algorithm;\n  payload.iat = Math.floor(Date.now() / 1000);\n\n  const encodedHeader = encodeJwtData(header);\n  const encodedPayload = encodeJwtData(payload);\n  const firstPart = `${encodedHeader}.${encodedPayload}`;\n\n  try {\n    const signature = await runtime.crypto.subtle.sign(algorithm, cryptoKey, encoder.encode(firstPart));\n    const encodedSignature = `${firstPart}.${base64url.stringify(new Uint8Array(signature), { pad: false })}`;\n    return { data: encodedSignature };\n  } catch (error) {\n    return { errors: [new SignJWTError((error as Error)?.message)] };\n  }\n}\n", "import { withLegacyReturn, withLegacySyncReturn } from './legacyReturn';\nimport { signJwt as _signJwt } from './signJwt';\nimport { decodeJwt as _decodeJwt, hasValidSignature as _hasValidSignature, verifyJwt as _verifyJwt } from './verifyJwt';\n\nexport type { VerifyJwtOptions } from './verifyJwt';\nexport type { SignJwtOptions } from './signJwt';\n\n// Introduce compatibility layer to avoid more breaking changes\n// TODO(dimkl): This (probably be drop in the next major version)\n\nexport const verifyJwt = withLegacyReturn(_verifyJwt);\nexport const decodeJwt = withLegacySyncReturn(_decodeJwt);\n\nexport const signJwt = withLegacyReturn(_signJwt);\nexport const hasValidSignature = withLegacyReturn(_hasValidSignature);\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAYA,SAAS,cAAc,OAAwB;AAC7C,QAAM,cAAc,KAAK,UAAU,KAAK;AACxC,QAAM,UAAU,IAAI,YAAY;AAChC,QAAM,UAAU,QAAQ,OAAO,WAAW;AAC1C,SAAO,UAAU,UAAU,SAAS,EAAE,KAAK,MAAM,CAAC;AACpD;AAeA,eAAsB,QACpB,SACA,KACA,SACuC;AACvC,MAAI,CAAC,QAAQ,WAAW;AACtB,UAAM,IAAI,MAAM,wBAAwB;AAAA,EAC1C;AACA,QAAM,UAAU,IAAI,YAAY;AAEhC,QAAM,YAAY,mBAAmB,QAAQ,SAAS;AACtD,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,MACL,QAAQ,CAAC,IAAI,aAAa,yBAAyB,QAAQ,SAAS,EAAE,CAAC;AAAA,IACzE;AAAA,EACF;AAEA,QAAM,YAAY,MAAM,UAAU,KAAK,WAAW,MAAM;AACxD,QAAM,SAAS,QAAQ,UAAU,EAAE,KAAK,MAAM;AAE9C,SAAO,MAAM,QAAQ;AACrB,UAAQ,MAAM,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI;AAE1C,QAAM,gBAAgB,cAAc,MAAM;AAC1C,QAAM,iBAAiB,cAAc,OAAO;AAC5C,QAAM,YAAY,GAAG,aAAa,IAAI,cAAc;AAEpD,MAAI;AACF,UAAM,YAAY,MAAM,QAAQ,OAAO,OAAO,KAAK,WAAW,WAAW,QAAQ,OAAO,SAAS,CAAC;AAClG,UAAM,mBAAmB,GAAG,SAAS,IAAI,UAAU,UAAU,IAAI,WAAW,SAAS,GAAG,EAAE,KAAK,MAAM,CAAC,CAAC;AACvG,WAAO,EAAE,MAAM,iBAAiB;AAAA,EAClC,SAAS,OAAO;AACd,WAAO,EAAE,QAAQ,CAAC,IAAI,aAAc,OAAiB,OAAO,CAAC,EAAE;AAAA,EACjE;AACF;;;ACxDO,IAAMA,aAAY,iBAAiB,SAAU;AAC7C,IAAMC,aAAY,qBAAqB,SAAU;AAEjD,IAAMC,WAAU,iBAAiB,OAAQ;AACzC,IAAMC,qBAAoB,iBAAiB,iBAAkB;", "names": ["verifyJwt", "decodeJwt", "signJwt", "hasValidSignature"]}