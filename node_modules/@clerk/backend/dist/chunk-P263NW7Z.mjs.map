{"version": 3, "sources": ["../src/jwt/legacyReturn.ts"], "sourcesContent": ["import type { JwtReturnType } from './types';\n\n// TODO(dimkl): Will be probably be dropped in next major version\nexport function withLegacyReturn<T extends (...args: any[]) => Promise<JwtReturnType<any, any>>>(cb: T) {\n  return async (...args: Parameters<T>): Promise<NonNullable<Awaited<ReturnType<T>>['data']>> | never => {\n    const { data, errors } = await cb(...args);\n    if (errors) {\n      throw errors[0];\n    }\n    return data;\n  };\n}\n\n// TODO(dimkl): Will be probably be dropped in next major version\nexport function withLegacySyncReturn<T extends (...args: any[]) => JwtReturnType<any, any>>(cb: T) {\n  return (...args: Parameters<T>): NonNullable<Awaited<ReturnType<T>>['data']> | never => {\n    const { data, errors } = cb(...args);\n    if (errors) {\n      throw errors[0];\n    }\n    return data;\n  };\n}\n"], "mappings": ";AAGO,SAAS,iBAAiF,IAAO;AACtG,SAAO,UAAU,SAAsF;AACrG,UAAM,EAAE,MAAM,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI;AACzC,QAAI,QAAQ;AACV,YAAM,OAAO,CAAC;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AACF;AAGO,SAAS,qBAA4E,IAAO;AACjG,SAAO,IAAI,SAA6E;AACtF,UAAM,EAAE,MAAM,OAAO,IAAI,GAAG,GAAG,IAAI;AACnC,QAAI,QAAQ;AACV,YAAM,OAAO,CAAC;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AACF;", "names": []}