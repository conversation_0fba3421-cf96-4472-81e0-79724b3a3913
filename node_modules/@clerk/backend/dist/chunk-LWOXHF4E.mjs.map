{"version": 3, "sources": ["../src/util/shared.ts"], "sourcesContent": ["export { addClerkPrefix, getScriptUrl, getClerkJsMajorVersionOrTag } from '@clerk/shared/url';\nexport { retry } from '@clerk/shared/retry';\nexport {\n  isDevelopmentFromSecretKey,\n  isProductionFromSecretKey,\n  parsePublishable<PERSON><PERSON>,\n  getCookieSuffix,\n  getSuffixedCookieName,\n} from '@clerk/shared/keys';\nexport { deprecated, deprecatedProperty } from '@clerk/shared/deprecated';\n\nimport { buildErrorThrower } from '@clerk/shared/error';\nimport { createDevOrStagingUrlCache } from '@clerk/shared/keys';\n// TODO: replace packageName with `${PACKAGE_NAME}@${PACKAGE_VERSION}` from tsup.config.ts\nexport const errorThrower = buildErrorThrower({ packageName: '@clerk/backend' });\n\nexport const { isDevOrStagingUrl } = createDevOrStagingUrlCache();\n"], "mappings": ";AAAA,SAAS,gBAAgB,cAAc,mCAAmC;AAC1E,SAAS,aAAa;AACtB;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,YAAY,0BAA0B;AAE/C,SAAS,yBAAyB;AAClC,SAAS,kCAAkC;AAEpC,IAAM,eAAe,kBAAkB,EAAE,aAAa,iBAAiB,CAAC;AAExE,IAAM,EAAE,kBAAkB,IAAI,2BAA2B;", "names": []}