// src/util/shared.ts
import { addClerkPrefix, getScriptUrl, getClerkJsMajorVersionOrTag } from "@clerk/shared/url";
import { retry } from "@clerk/shared/retry";
import {
  isDevelopmentFromSecretKey,
  isProductionFromSecretKey,
  parsePublishable<PERSON>ey,
  getCookieSuffix,
  getSuffixedCookieName
} from "@clerk/shared/keys";
import { deprecated, deprecatedProperty } from "@clerk/shared/deprecated";
import { buildErrorThrower } from "@clerk/shared/error";
import { createDevOrStagingUrlCache } from "@clerk/shared/keys";
var errorThrower = buildErrorThrower({ packageName: "@clerk/backend" });
var { isDevOrStagingUrl } = createDevOrStagingUrlCache();

export {
  errorThrower,
  retry,
  isDevelopmentFromSecretKey,
  parsePublishable<PERSON>ey,
  getCookieSuffix,
  getSuffixedCookieName,
  deprecated
};
//# sourceMappingURL=chunk-LWOXHF4E.mjs.map