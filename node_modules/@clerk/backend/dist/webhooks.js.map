{"version": 3, "sources": ["../src/webhooks.ts", "../src/util/shared.ts"], "sourcesContent": ["import { getEnvVariable } from '@clerk/shared/getEnvVariable';\nimport crypto from 'crypto';\nimport { errorThrower } from 'src/util/shared';\n\nimport type { WebhookEvent } from './api/resources/Webhooks';\n\n/**\n * @inline\n */\nexport type VerifyWebhookOptions = {\n  /**\n   * The signing secret for the webhook. It's recommended to use the [`CLERK_WEBHOOK_SIGNING_SECRET` environment variable](https://clerk.com/docs/deployments/clerk-environment-variables#webhooks) instead.\n   */\n  signingSecret?: string;\n};\n\nconst SVIX_ID_HEADER = 'svix-id';\nconst SVIX_TIMESTAMP_HEADER = 'svix-timestamp';\nconst SVIX_SIGNATURE_HEADER = 'svix-signature';\n\nconst REQUIRED_SVIX_HEADERS = [SVIX_ID_HEADER, SVIX_TIMESTAMP_HEADER, SVIX_SIGNATURE_HEADER] as const;\n\nexport * from './api/resources/Webhooks';\n\n/**\n * Verifies the authenticity of a webhook request using Svix. Returns a promise that resolves to the verified webhook event data.\n *\n * @param request - The request object.\n * @param options - Optional configuration object.\n *\n * @displayFunctionSignature\n *\n * @example\n * See the [guide on syncing data](https://clerk.com/docs/webhooks/sync-data) for more comprehensive and framework-specific examples that you can copy and paste into your app.\n *\n * ```ts\n * try {\n *   const evt = await verifyWebhook(request)\n *\n *   // Access the event data\n *   const { id } = evt.data\n *   const eventType = evt.type\n *\n *   // Handle specific event types\n *   if (evt.type === 'user.created') {\n *     console.log('New user created:', evt.data.id)\n *     // Handle user creation\n *   }\n *\n *   return new Response('Success', { status: 200 })\n * } catch (err) {\n *   console.error('Webhook verification failed:', err)\n *   return new Response('Webhook verification failed', { status: 400 })\n * }\n * ```\n */\nexport async function verifyWebhook(request: Request, options: VerifyWebhookOptions = {}): Promise<WebhookEvent> {\n  const secret = options.signingSecret ?? getEnvVariable('CLERK_WEBHOOK_SIGNING_SECRET');\n  const svixId = request.headers.get(SVIX_ID_HEADER);\n  const svixTimestamp = request.headers.get(SVIX_TIMESTAMP_HEADER);\n  const svixSignature = request.headers.get(SVIX_SIGNATURE_HEADER);\n\n  if (!secret) {\n    return errorThrower.throw(\n      'Missing webhook signing secret. Set the CLERK_WEBHOOK_SIGNING_SECRET environment variable with the webhook secret from the Clerk Dashboard.',\n    );\n  }\n\n  if (!svixId || !svixTimestamp || !svixSignature) {\n    const missingHeaders = REQUIRED_SVIX_HEADERS.filter(header => !request.headers.has(header));\n    return errorThrower.throw(`Missing required Svix headers: ${missingHeaders.join(', ')}`);\n  }\n\n  const body = await request.text();\n\n  const signedContent = `${svixId}.${svixTimestamp}.${body}`;\n\n  const secretBytes = Buffer.from(secret.split('_')[1], 'base64');\n\n  const constructedSignature = crypto.createHmac('sha256', secretBytes).update(signedContent).digest('base64');\n\n  // svixSignature can be a string with one or more space separated signatures\n  if (svixSignature.split(' ').includes(constructedSignature)) {\n    return errorThrower.throw('Incoming webhook does not have a valid signature');\n  }\n\n  const payload = JSON.parse(body);\n\n  return {\n    type: payload.type,\n    object: 'event',\n    data: payload.data,\n  } as WebhookEvent;\n}\n", "export { addClerkPrefix, getScriptUrl, getClerkJsMajorVersionOrTag } from '@clerk/shared/url';\nexport { retry } from '@clerk/shared/retry';\nexport {\n  isDevelopmentFromSecretKey,\n  isProductionFromSecretKey,\n  parsePublishable<PERSON><PERSON>,\n  getCookieSuffix,\n  getSuffixedCookieName,\n} from '@clerk/shared/keys';\nexport { deprecated, deprecatedProperty } from '@clerk/shared/deprecated';\n\nimport { buildErrorThrower } from '@clerk/shared/error';\nimport { createDevOrStagingUrlCache } from '@clerk/shared/keys';\n// TODO: replace packageName with `${PACKAGE_NAME}@${PACKAGE_VERSION}` from tsup.config.ts\nexport const errorThrower = buildErrorThrower({ packageName: '@clerk/backend' });\n\nexport const { isDevOrStagingUrl } = createDevOrStagingUrlCache();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,4BAA+B;AAC/B,oBAAmB;;;ACDnB,iBAA0E;AAC1E,mBAAsB;AACtB,kBAMO;AACP,wBAA+C;AAE/C,mBAAkC;AAClC,IAAAA,eAA2C;AAEpC,IAAM,mBAAe,gCAAkB,EAAE,aAAa,iBAAiB,CAAC;AAExE,IAAM,EAAE,kBAAkB,QAAI,yCAA2B;;;ADAhE,IAAM,iBAAiB;AACvB,IAAM,wBAAwB;AAC9B,IAAM,wBAAwB;AAE9B,IAAM,wBAAwB,CAAC,gBAAgB,uBAAuB,qBAAqB;AAoC3F,eAAsB,cAAc,SAAkB,UAAgC,CAAC,GAA0B;AAC/G,QAAM,SAAS,QAAQ,qBAAiB,sCAAe,8BAA8B;AACrF,QAAM,SAAS,QAAQ,QAAQ,IAAI,cAAc;AACjD,QAAM,gBAAgB,QAAQ,QAAQ,IAAI,qBAAqB;AAC/D,QAAM,gBAAgB,QAAQ,QAAQ,IAAI,qBAAqB;AAE/D,MAAI,CAAC,QAAQ;AACX,WAAO,aAAa;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AAEA,MAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,eAAe;AAC/C,UAAM,iBAAiB,sBAAsB,OAAO,YAAU,CAAC,QAAQ,QAAQ,IAAI,MAAM,CAAC;AAC1F,WAAO,aAAa,MAAM,kCAAkC,eAAe,KAAK,IAAI,CAAC,EAAE;AAAA,EACzF;AAEA,QAAM,OAAO,MAAM,QAAQ,KAAK;AAEhC,QAAM,gBAAgB,GAAG,MAAM,IAAI,aAAa,IAAI,IAAI;AAExD,QAAM,cAAc,OAAO,KAAK,OAAO,MAAM,GAAG,EAAE,CAAC,GAAG,QAAQ;AAE9D,QAAM,uBAAuB,cAAAC,QAAO,WAAW,UAAU,WAAW,EAAE,OAAO,aAAa,EAAE,OAAO,QAAQ;AAG3G,MAAI,cAAc,MAAM,GAAG,EAAE,SAAS,oBAAoB,GAAG;AAC3D,WAAO,aAAa,MAAM,kDAAkD;AAAA,EAC9E;AAEA,QAAM,UAAU,KAAK,MAAM,IAAI;AAE/B,SAAO;AAAA,IACL,MAAM,QAAQ;AAAA,IACd,QAAQ;AAAA,IACR,MAAM,QAAQ;AAAA,EAChB;AACF;", "names": ["import_keys", "crypto"]}