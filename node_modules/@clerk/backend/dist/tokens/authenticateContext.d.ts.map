{"version": 3, "file": "authenticateContext.d.ts", "sourceRoot": "", "sources": ["../../src/tokens/authenticateContext.ts"], "names": [], "mappings": "AAOA,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AACnD,OAAO,KAAK,EAAE,0BAA0B,EAAE,MAAM,SAAS,CAAC;AAE1D,UAAU,mBAAoB,SAAQ,0BAA0B;IAE9D,aAAa,EAAE,MAAM,GAAG,SAAS,CAAC;IAClC,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC;IAC3B,IAAI,EAAE,MAAM,GAAG,SAAS,CAAC;IACzB,aAAa,EAAE,MAAM,GAAG,SAAS,CAAC;IAClC,cAAc,EAAE,MAAM,GAAG,SAAS,CAAC;IACnC,QAAQ,EAAE,MAAM,GAAG,SAAS,CAAC;IAC7B,SAAS,EAAE,MAAM,GAAG,SAAS,CAAC;IAC9B,YAAY,EAAE,MAAM,GAAG,SAAS,CAAC;IACjC,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC;IAE3B,oBAAoB,EAAE,MAAM,GAAG,SAAS,CAAC;IACzC,oBAAoB,EAAE,MAAM,GAAG,SAAS,CAAC;IACzC,SAAS,EAAE,MAAM,CAAC;IAElB,eAAe,EAAE,MAAM,GAAG,SAAS,CAAC;IACpC,cAAc,EAAE,MAAM,GAAG,SAAS,CAAC;IACnC,cAAc,EAAE,MAAM,GAAG,SAAS,CAAC;IACnC,4BAA4B,EAAE,MAAM,CAAC;IAGrC,QAAQ,EAAE,GAAG,CAAC;IAEd,cAAc,EAAE,MAAM,CAAC;IACvB,YAAY,EAAE,MAAM,CAAC;IACrB,WAAW,EAAE,MAAM,CAAC;CACrB;AAED;;;;;GAKG;AACH,cAAM,mBAAoB,YAAW,mBAAmB;IAWpD,OAAO,CAAC,YAAY;IACpB,OAAO,CAAC,YAAY;IAXtB;;;;OAIG;IACH,IAAW,YAAY,IAAI,MAAM,GAAG,SAAS,CAE5C;gBAGS,YAAY,EAAE,MAAM,EACpB,YAAY,EAAE,YAAY,EAClC,OAAO,EAAE,0BAA0B;IAc9B,mBAAmB,IAAI,OAAO;IAyFrC,OAAO,CAAC,wBAAwB;IAchC,OAAO,CAAC,gBAAgB;IAaxB,OAAO,CAAC,gBAAgB;IAOxB,OAAO,CAAC,mBAAmB;IAY3B,OAAO,CAAC,aAAa;IAIrB,OAAO,CAAC,SAAS;IAIjB,OAAO,CAAC,SAAS;IAIjB,OAAO,CAAC,iBAAiB;IAIzB,OAAO,CAAC,6BAA6B;IAOrC,OAAO,CAAC,wBAAwB;IAoBhC,OAAO,CAAC,cAAc;IAQtB,OAAO,CAAC,sBAAsB;IAa9B,OAAO,CAAC,cAAc;CAGvB;AAED,YAAY,EAAE,mBAAmB,EAAE,CAAC;AAEpC,eAAO,MAAM,yBAAyB,GACpC,cAAc,YAAY,EAC1B,SAAS,0BAA0B,KAClC,OAAO,CAAC,mBAAmB,CAK7B,CAAC"}