{"version": 3, "file": "authStatus.d.ts", "sourceRoot": "", "sources": ["../../src/tokens/authStatus.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,UAAU,EAAE,qBAAqB,EAAE,MAAM,cAAc,CAAC;AAGtE,OAAO,KAAK,EAAE,4BAA4B,EAAE,MAAM,WAAW,CAAC;AAC9D,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AACjE,OAAO,KAAK,EACV,0BAA0B,EAC1B,kBAAkB,EAClB,mBAAmB,EACnB,4BAA4B,EAC7B,MAAM,eAAe,CAAC;AAOvB,OAAO,KAAK,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,MAAM,cAAc,CAAC;AACvE,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AACzC,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,SAAS,CAAC;AAE/C,eAAO,MAAM,UAAU;;;;CAIb,CAAC;AAEX,MAAM,MAAM,UAAU,GAAG,CAAC,OAAO,UAAU,CAAC,CAAC,MAAM,OAAO,UAAU,CAAC,CAAC;AAEtE,KAAK,MAAM,CAAC,CAAC,SAAS,SAAS,EAAE,aAAa,SAAS,OAAO,IAAI,CAAC,SAAS,gBAAgB,GACxF,aAAa,SAAS,IAAI,GACxB,CAAC,IAAI,CAAC,EAAE,qBAAqB,KAAK,kBAAkB,GACpD,MAAM,mBAAmB,GAC3B,aAAa,SAAS,IAAI,GACxB,MAAM,0BAA0B,CAAC,OAAO,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAC9D,MAAM,4BAA4B,CAAC,OAAO,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;AAEvE,MAAM,MAAM,kBAAkB,CAAC,CAAC,SAAS,SAAS,GAAG,gBAAgB,IAAI;IACvE,MAAM,EAAE,OAAO,UAAU,CAAC,QAAQ,CAAC;IACnC,MAAM,EAAE,IAAI,CAAC;IACb,OAAO,EAAE,IAAI,CAAC;IACd,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,cAAc,EAAE,MAAM,CAAC;IACvB,WAAW,EAAE,OAAO,CAAC;IACrB,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;IAClB,cAAc,EAAE,MAAM,CAAC;IACvB,cAAc,EAAE,MAAM,CAAC;IACvB;;OAEG;IACH,UAAU,EAAE,IAAI,CAAC;IACjB,eAAe,EAAE,IAAI,CAAC;IACtB,OAAO,EAAE,OAAO,CAAC;IACjB,KAAK,EAAE,MAAM,CAAC;IACd,SAAS,EAAE,CAAC,CAAC;IACb,MAAM,EAAE,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;CACzB,CAAC;AAEF,MAAM,MAAM,oBAAoB,CAAC,CAAC,SAAS,SAAS,GAAG,gBAAgB,IAAI;IACzE,MAAM,EAAE,OAAO,UAAU,CAAC,SAAS,CAAC;IACpC,MAAM,EAAE,UAAU,CAAC;IACnB,OAAO,EAAE,MAAM,CAAC;IAChB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,cAAc,EAAE,MAAM,CAAC;IACvB,WAAW,EAAE,OAAO,CAAC;IACrB,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;IAClB,cAAc,EAAE,MAAM,CAAC;IACvB,cAAc,EAAE,MAAM,CAAC;IACvB;;OAEG;IACH,UAAU,EAAE,KAAK,CAAC;IAClB,eAAe,EAAE,KAAK,CAAC;IACvB,SAAS,EAAE,CAAC,CAAC;IACb,OAAO,EAAE,OAAO,CAAC;IACjB,KAAK,EAAE,IAAI,CAAC;IACZ,MAAM,EAAE,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;CAC1B,CAAC;AAEF,MAAM,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,EAAE,QAAQ,GAAG,QAAQ,GAAG,WAAW,CAAC,GAAG;IAC7G,SAAS,EAAE,gBAAgB,CAAC;IAC5B,MAAM,EAAE,OAAO,UAAU,CAAC,SAAS,CAAC;IACpC,OAAO,EAAE,OAAO,CAAC;IACjB,MAAM,EAAE,MAAM,IAAI,CAAC;CACpB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,aAAa,GAAG,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;AAEjE;;GAEG;AACH,MAAM,MAAM,cAAc,GAAG,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;AAEpE,eAAO,MAAM,eAAe;;;;;;;;;;;;;;;;CAgBlB,CAAC;AAEX,MAAM,MAAM,eAAe,GAAG,CAAC,OAAO,eAAe,CAAC,CAAC,MAAM,OAAO,eAAe,CAAC,CAAC;AAErF,MAAM,MAAM,UAAU,GAAG,eAAe,GAAG,4BAA4B,CAAC;AAExE,MAAM,MAAM,YAAY,CAAC,CAAC,SAAS,SAAS,GAAG,gBAAgB,IAC3D,kBAAkB,CAAC,CAAC,CAAC,GACrB,oBAAoB,CAAC,CAAC,CAAC,GACvB,CAAC,CAAC,SAAS,gBAAgB,GAAG,cAAc,GAAG,KAAK,CAAC,CAAC;AAE1D,KAAK,kBAAkB,GAAG;IACxB,mBAAmB,EAAE,mBAAmB,CAAC;IACzC,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,KAAK,EAAE,MAAM,CAAC;IACd,SAAS,EAAE,SAAS,CAAC;CACtB,CAAC;AAEF,KAAK,cAAc,GACf,CAAC,kBAAkB,GAAG;IAAE,SAAS,EAAE,gBAAgB,CAAC;IAAC,aAAa,EAAE,UAAU,CAAA;CAAE,CAAC,GACjF,CAAC,kBAAkB,GAAG;IAAE,SAAS,EAAE,gBAAgB,CAAC;IAAC,WAAW,EAAE,eAAe,CAAA;CAAE,CAAC,CAAC;AAEzF,wBAAgB,QAAQ,CAAC,CAAC,SAAS,SAAS,EAAE,MAAM,EAAE,cAAc,GAAG;IAAE,SAAS,EAAE,CAAC,CAAA;CAAE,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAsC9G;AAED,KAAK,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG;IACzD,MAAM,EAAE,UAAU,CAAC;IACnB,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB,CAAC;AAEF,wBAAgB,SAAS,CAAC,CAAC,SAAS,SAAS,EAAE,MAAM,EAAE,eAAe,GAAG;IAAE,SAAS,EAAE,CAAC,CAAA;CAAE,GAAG,oBAAoB,CAAC,CAAC,CAAC,CA8BlH;AAED,wBAAgB,SAAS,CACvB,mBAAmB,EAAE,mBAAmB,EACxC,MAAM,EAAE,UAAU,EAClB,OAAO,oBAAK,EACZ,OAAO,EAAE,OAAO,GACf,cAAc,CAoBhB"}