import type { AuthenticateRequestOptions } from '../tokens/types';
import type { MachineTokenType } from './tokenTypes';
import { TokenType } from './tokenTypes';
export declare const M2M_TOKEN_PREFIX = "mt_";
export declare const OAUTH_TOKEN_PREFIX = "oat_";
export declare const API_KEY_PREFIX = "ak_";
/**
 * Checks if a token is a machine token by looking at its prefix.
 *
 * @remarks
 * In the future, this will support custom prefixes that can be prepended to the base prefixes
 * (e.g. "org_a_m2m_", "org_a_oauth_access_", "org_a_api_key_")
 *
 * @param token - The token string to check
 * @returns true if the token starts with a recognized machine token prefix
 */
export declare function isMachineTokenByPrefix(token: string): boolean;
/**
 * Gets the specific type of machine token based on its prefix.
 *
 * @remarks
 * In the future, this will support custom prefixes that can be prepended to the base prefixes
 * (e.g. "org_a_m2m_", "org_a_oauth_access_", "org_a_api_key_")
 *
 * @param token - The token string to check
 * @returns The specific MachineTokenType
 * @throws Error if the token doesn't match any known machine token prefix
 */
export declare function getMachineTokenType(token: string): MachineTokenType;
/**
 * Check if a token type is accepted given a requested token type or list of token types.
 *
 * @param tokenType - The token type to check (can be null if the token is invalid)
 * @param acceptsToken - The requested token type or list of token types
 * @returns true if the token type is accepted
 */
export declare const isTokenTypeAccepted: (tokenType: TokenType | null, acceptsToken: NonNullable<AuthenticateRequestOptions["acceptsToken"]>) => boolean;
/**
 * Checks if a token type string is a machine token type (api_key, machine_token, or oauth_token).
 *
 * @param type - The token type string to check
 * @returns true if the type is a machine token type
 */
export declare function isMachineTokenType(type: string): type is MachineTokenType;
//# sourceMappingURL=machine.d.ts.map