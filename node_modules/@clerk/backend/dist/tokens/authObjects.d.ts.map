{"version": 3, "file": "authObjects.d.ts", "sourceRoot": "", "sources": ["../../src/tokens/authObjects.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EACV,mCAAmC,EACnC,GAAG,EACH,UAAU,EACV,qBAAqB,EACrB,cAAc,EAEd,kBAAkB,EAClB,kCAAkC,EACnC,MAAM,cAAc,CAAC;AAEtB,OAAO,KAAK,EAAU,uBAAuB,EAAqC,MAAM,QAAQ,CAAC;AAGjG,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAEjE,OAAO,KAAK,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,MAAM,cAAc,CAAC;AAEvE,OAAO,KAAK,EAAE,0BAA0B,EAAE,eAAe,EAAE,MAAM,SAAS,CAAC;AAE3E;;GAEG;AACH,KAAK,mBAAmB,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AAC/C;;GAEG;AACH,KAAK,eAAe,GAAG,MAAM,mBAAmB,CAAC;AAEjD,KAAK,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AAElC;;GAEG;AACH,MAAM,MAAM,yBAAyB,GAAG,uBAAuB,GAAG;IAChE,KAAK,EAAE,MAAM,CAAC;CACf,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,kBAAkB,GAAG,kCAAkC,GAAG;IACpE;;OAEG;IACH,SAAS,EAAE,gBAAgB,CAAC;IAC5B;;OAEG;IACH,QAAQ,EAAE,cAAc,CAAC;IACzB;;OAEG;IACH,GAAG,EAAE,mCAAmC,CAAC;IACzC;;OAEG;IACH,KAAK,EAAE,eAAe,CAAC;IACvB,eAAe,EAAE,IAAI,CAAC;CACvB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,mBAAmB,GAAG;IAChC,aAAa,EAAE,IAAI,CAAC;IACpB,SAAS,EAAE,IAAI,CAAC;IAChB,aAAa,EAAE,kBAAkB,GAAG,IAAI,CAAC;IACzC,KAAK,EAAE,IAAI,CAAC;IACZ,SAAS,EAAE,gBAAgB,CAAC;IAC5B,MAAM,EAAE,IAAI,CAAC;IACb,KAAK,EAAE,IAAI,CAAC;IACZ,OAAO,EAAE,IAAI,CAAC;IACd,OAAO,EAAE,IAAI,CAAC;IACd,cAAc,EAAE,IAAI,CAAC;IACrB,qBAAqB,EAAE,IAAI,CAAC;IAC5B,QAAQ,EAAE,cAAc,CAAC;IACzB,GAAG,EAAE,mCAAmC,CAAC;IACzC,KAAK,EAAE,eAAe,CAAC;IACvB,eAAe,EAAE,KAAK,CAAC;CACxB,CAAC;AAEF;;;;;;GAMG;AACH,KAAK,+BAA+B,CAAC,cAAc,SAAS,OAAO,IAAI;IACrE,OAAO,EAAE,cAAc,SAAS,IAAI,GAE5B;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;QAAC,MAAM,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,IAAI,CAAA;KAAE,GACpE;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;QAAC,MAAM,EAAE,IAAI,CAAC;QAAC,KAAK,EAAE,MAAM,CAAA;KAAE,GACxE;QAAE,IAAI,EAAE,IAAI,CAAC;QAAC,MAAM,EAAE,IAAI,CAAC;QAAC,MAAM,EAAE,IAAI,CAAC;QAAC,KAAK,EAAE,IAAI,CAAA;KAAE,CAAC;IAC5D,aAAa,EAAE;QACb,IAAI,EAAE,cAAc,SAAS,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC;QAClD,MAAM,EAAE,cAAc,SAAS,IAAI,GAAG,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;QAC3D,SAAS,EAAE,cAAc,SAAS,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC;KACxD,CAAC;IACF,WAAW,EAAE;QACX,MAAM,EAAE,cAAc,SAAS,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC;QACpD,QAAQ,EAAE,cAAc,SAAS,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC;KACvD,CAAC;CACH,CAAC;AAEF;;;;;;;GAOG;AACH,MAAM,MAAM,0BAA0B,CAAC,CAAC,SAAS,gBAAgB,GAAG,gBAAgB,IAAI,CAAC,SAAS,GAAG,GACjG;IACE,EAAE,EAAE,MAAM,CAAC;IACX,OAAO,EAAE,MAAM,CAAC;IAChB,MAAM,EAAE,MAAM,EAAE,CAAC;IACjB,QAAQ,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,CAAC;IAChC,GAAG,EAAE,mCAAmC,CAAC;IACzC,KAAK,EAAE,eAAe,CAAC;IACvB,SAAS,EAAE,CAAC,CAAC;IACb,eAAe,EAAE,IAAI,CAAC;CACvB,GAAG,+BAA+B,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAC5C,KAAK,CAAC;AAEV;;;;;;;GAOG;AACH,MAAM,MAAM,4BAA4B,CAAC,CAAC,SAAS,gBAAgB,GAAG,gBAAgB,IAAI,CAAC,SAAS,GAAG,GACnG;IACE,EAAE,EAAE,IAAI,CAAC;IACT,OAAO,EAAE,IAAI,CAAC;IACd,MAAM,EAAE,IAAI,CAAC;IACb,QAAQ,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9B,GAAG,EAAE,mCAAmC,CAAC;IACzC,KAAK,EAAE,eAAe,CAAC;IACvB,SAAS,EAAE,CAAC,CAAC;IACb,eAAe,EAAE,KAAK,CAAC;CACxB,GAAG,+BAA+B,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAC7C,KAAK,CAAC;AAEV,MAAM,MAAM,sBAAsB,GAAG;IACnC,eAAe,EAAE,KAAK,CAAC;IACvB,SAAS,EAAE,IAAI,CAAC;IAChB,QAAQ,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9B,GAAG,EAAE,MAAM,KAAK,CAAC;IACjB,KAAK,EAAE,eAAe,CAAC;CACxB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,UAAU,GAClB,kBAAkB,GAClB,mBAAmB,GACnB,0BAA0B,GAC1B,4BAA4B,GAC5B,sBAAsB,CAAC;AAW3B;;GAEG;AACH,wBAAgB,kBAAkB,CAChC,mBAAmB,EAAE,OAAO,CAAC,mBAAmB,CAAC,EACjD,YAAY,EAAE,MAAM,EACpB,aAAa,EAAE,UAAU,GACxB,kBAAkB,CAkCpB;AAED;;GAEG;AACH,wBAAgB,mBAAmB,CACjC,SAAS,CAAC,EAAE,mBAAmB,EAC/B,oBAAoB,CAAC,EAAE,kBAAkB,GACxC,mBAAmB,CAkBrB;AAED;;GAEG;AACH,wBAAgB,0BAA0B,CAAC,CAAC,SAAS,gBAAgB,EACnE,SAAS,EAAE,CAAC,EACZ,KAAK,EAAE,MAAM,EACb,kBAAkB,EAAE,eAAe,EACnC,SAAS,CAAC,EAAE,mBAAmB,GAC9B,0BAA0B,CAAC,CAAC,CAAC,CAmD/B;AAED;;GAEG;AACH,wBAAgB,4BAA4B,CAAC,CAAC,SAAS,gBAAgB,EACrE,SAAS,EAAE,CAAC,EACZ,SAAS,CAAC,EAAE,mBAAmB,GAC9B,4BAA4B,CAAC,CAAC,CAAC,CA6CjC;AAED;;GAEG;AACH,wBAAgB,sBAAsB,IAAI,sBAAsB,CAQ/D;AAED;;;;;;;;GAQG;AACH,eAAO,MAAM,0BAA0B,GAAI,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,KAAG,CAKtF,CAAC;AAsBF;;GAEG;AACH,eAAO,MAAM,oBAAoB,GAC/B,KAAK,GAAG,EACR,yCAAgD,qBAAqB,GAAG,OAAO,CAAC,mBAAmB,CAAC,6CASrG,CAAC;AAEF;;;;;;;;;GASG;AACH,wBAAgB,6BAA6B,CAAC,EAC5C,UAAU,EACV,YAAqC,GACtC,EAAE;IACD,UAAU,EAAE,UAAU,CAAC;IACvB,YAAY,EAAE,0BAA0B,CAAC,cAAc,CAAC,CAAC;CAC1D,GAAG,UAAU,CAsBb"}