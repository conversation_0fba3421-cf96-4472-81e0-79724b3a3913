import { CnameTarget } from './CnameTarget';
import type { DomainJSON } from './JSON';
export declare class Domain {
    readonly id: string;
    readonly name: string;
    readonly isSatellite: boolean;
    readonly frontendApiUrl: string;
    readonly developmentOrigin: string;
    readonly cnameTargets: CnameTarget[];
    readonly accountsPortalUrl?: string | null | undefined;
    readonly proxyUrl?: string | null | undefined;
    constructor(id: string, name: string, isSatellite: boolean, frontendApiUrl: string, developmentOrigin: string, cnameTargets: CnameTarget[], accountsPortalUrl?: string | null | undefined, proxyUrl?: string | null | undefined);
    static fromJSON(data: DomainJSON): Domain;
}
//# sourceMappingURL=Domain.d.ts.map