import type { MachineTokenJSON } from './JSON';
export declare class MachineToken {
    readonly id: string;
    readonly name: string;
    readonly subject: string;
    readonly scopes: string[];
    readonly claims: Record<string, any> | null;
    readonly revoked: boolean;
    readonly revocationReason: string | null;
    readonly expired: boolean;
    readonly expiration: number | null;
    readonly createdBy: string | null;
    readonly creationReason: string | null;
    readonly createdAt: number;
    readonly updatedAt: number;
    constructor(id: string, name: string, subject: string, scopes: string[], claims: Record<string, any> | null, revoked: boolean, revocationReason: string | null, expired: boolean, expiration: number | null, createdBy: string | null, creationReason: string | null, createdAt: number, updatedAt: number);
    static fromJSON(data: MachineTokenJSON): MachineToken;
}
//# sourceMappingURL=MachineToken.d.ts.map