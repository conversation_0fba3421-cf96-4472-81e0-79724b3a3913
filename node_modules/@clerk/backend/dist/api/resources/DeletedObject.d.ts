import type { DeletedObjectJSON } from './JSON';
export declare class DeletedObject {
    readonly object: string;
    readonly id: string | null;
    readonly slug: string | null;
    readonly deleted: boolean;
    constructor(object: string, id: string | null, slug: string | null, deleted: boolean);
    static fromJSON(data: DeletedObjectJSON): DeletedObject;
}
//# sourceMappingURL=DeletedObject.d.ts.map