import type { JwtTemplateJSON } from './JSON';
export declare class JwtTemplate {
    readonly id: string;
    readonly name: string;
    readonly claims: object;
    readonly lifetime: number;
    readonly allowedClockSkew: number;
    readonly customSigningKey: boolean;
    readonly signingAlgorithm: string;
    readonly createdAt: number;
    readonly updatedAt: number;
    constructor(id: string, name: string, claims: object, lifetime: number, allowedClockSkew: number, customSigningKey: boolean, signingAlgorithm: string, createdAt: number, updatedAt: number);
    static fromJSON(data: JwtTemplateJSON): JwtTemplate;
}
//# sourceMappingURL=JwtTemplate.d.ts.map