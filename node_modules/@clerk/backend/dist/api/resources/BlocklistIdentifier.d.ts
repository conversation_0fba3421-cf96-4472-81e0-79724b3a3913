import type { BlocklistIdentifierType } from './Enums';
import type { BlocklistIdentifierJSON } from './JSON';
export declare class BlocklistIdentifier {
    readonly id: string;
    readonly identifier: string;
    readonly identifierType: BlocklistIdentifierType;
    readonly createdAt: number;
    readonly updatedAt: number;
    readonly instanceId?: string | undefined;
    constructor(id: string, identifier: string, identifierType: BlocklistIdentifierType, createdAt: number, updatedAt: number, instanceId?: string | undefined);
    static fromJSON(data: BlocklistIdentifierJSON): BlocklistIdentifier;
}
//# sourceMappingURL=BlocklistIdentifier.d.ts.map