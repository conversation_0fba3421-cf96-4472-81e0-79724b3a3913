import type { ProxyCheckJSON } from './JSON';
export declare class ProxyCheck {
    readonly id: string;
    readonly domainId: string;
    readonly lastRunAt: number | null;
    readonly proxyUrl: string;
    readonly successful: boolean;
    readonly createdAt: number;
    readonly updatedAt: number;
    constructor(id: string, domainId: string, lastRunAt: number | null, proxyUrl: string, successful: boolean, createdAt: number, updatedAt: number);
    static fromJSON(data: ProxyCheckJSON): ProxyCheck;
}
//# sourceMappingURL=ProxyCheck.d.ts.map