import type { AccountlessApplication } from '../resources/AccountlessApplication';
import { AbstractAPI } from './AbstractApi';
export declare class AccountlessApplicationAPI extends AbstractAPI {
    createAccountlessApplication(): Promise<AccountlessApplication>;
    completeAccountlessApplicationOnboarding(): Promise<AccountlessApplication>;
}
//# sourceMappingURL=AccountlessApplicationsAPI.d.ts.map