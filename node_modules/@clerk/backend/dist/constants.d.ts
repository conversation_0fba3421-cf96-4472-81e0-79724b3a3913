export declare const API_URL = "https://api.clerk.com";
export declare const API_VERSION = "v1";
export declare const USER_AGENT: string;
export declare const MAX_CACHE_LAST_UPDATED_AT_SECONDS: number;
export declare const SUPPORTED_BAPI_VERSION = "2025-04-10";
/**
 * @internal
 */
export declare const constants: {
    readonly Attributes: {
        readonly AuthToken: "__clerkAuthToken";
        readonly AuthSignature: "__clerkAuthSignature";
        readonly AuthStatus: "__clerkAuthStatus";
        readonly AuthReason: "__clerkAuthReason";
        readonly AuthMessage: "__clerkAuthMessage";
        readonly ClerkUrl: "__clerkUrl";
    };
    readonly Cookies: {
        readonly Session: "__session";
        readonly Refresh: "__refresh";
        readonly ClientUat: "__client_uat";
        readonly Handshake: "__clerk_handshake";
        readonly Dev<PERSON>rowser: "__clerk_db_jwt";
        readonly RedirectCount: "__clerk_redirect_count";
        readonly HandshakeNonce: "__clerk_handshake_nonce";
    };
    readonly Headers: {
        readonly Accept: "accept";
        readonly AuthMessage: "x-clerk-auth-message";
        readonly Authorization: "authorization";
        readonly AuthReason: "x-clerk-auth-reason";
        readonly AuthSignature: "x-clerk-auth-signature";
        readonly AuthStatus: "x-clerk-auth-status";
        readonly AuthToken: "x-clerk-auth-token";
        readonly CacheControl: "cache-control";
        readonly ClerkRedirectTo: "x-clerk-redirect-to";
        readonly ClerkRequestData: "x-clerk-request-data";
        readonly ClerkUrl: "x-clerk-clerk-url";
        readonly CloudFrontForwardedProto: "cloudfront-forwarded-proto";
        readonly ContentType: "content-type";
        readonly ContentSecurityPolicy: "content-security-policy";
        readonly ContentSecurityPolicyReportOnly: "content-security-policy-report-only";
        readonly EnableDebug: "x-clerk-debug";
        readonly ForwardedHost: "x-forwarded-host";
        readonly ForwardedPort: "x-forwarded-port";
        readonly ForwardedProto: "x-forwarded-proto";
        readonly Host: "host";
        readonly Location: "location";
        readonly Nonce: "x-nonce";
        readonly Origin: "origin";
        readonly Referrer: "referer";
        readonly SecFetchDest: "sec-fetch-dest";
        readonly UserAgent: "user-agent";
        readonly ReportingEndpoints: "reporting-endpoints";
    };
    readonly ContentTypes: {
        readonly Json: "application/json";
    };
    readonly QueryParameters: {
        readonly ClerkSynced: "__clerk_synced";
        readonly SuffixedCookies: "suffixed_cookies";
        readonly ClerkRedirectUrl: "__clerk_redirect_url";
        readonly DevBrowser: "__clerk_db_jwt";
        readonly Handshake: "__clerk_handshake";
        readonly HandshakeHelp: "__clerk_help";
        readonly LegacyDevBrowser: "__dev_session";
        readonly HandshakeReason: "__clerk_hs_reason";
        readonly HandshakeNonce: "__clerk_handshake_nonce";
    };
};
export type Constants = typeof constants;
//# sourceMappingURL=constants.d.ts.map