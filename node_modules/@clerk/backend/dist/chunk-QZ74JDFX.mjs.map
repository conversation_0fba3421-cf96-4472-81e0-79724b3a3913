{"version": 3, "sources": ["../src/constants.ts", "../src/createRedirect.ts", "../src/util/mergePreDefinedOptions.ts", "../src/util/optionsAssertions.ts", "../src/tokens/authenticateContext.ts", "../src/tokens/authObjects.ts", "../src/util/path.ts", "../src/api/endpoints/AbstractApi.ts", "../src/api/endpoints/ActorTokenApi.ts", "../src/api/endpoints/AccountlessApplicationsAPI.ts", "../src/api/endpoints/AllowlistIdentifierApi.ts", "../src/api/endpoints/APIKeysApi.ts", "../src/api/endpoints/BetaFeaturesApi.ts", "../src/api/endpoints/BlocklistIdentifierApi.ts", "../src/api/endpoints/ClientApi.ts", "../src/api/endpoints/DomainApi.ts", "../src/api/endpoints/EmailAddressApi.ts", "../src/api/endpoints/IdPOAuthAccessTokenApi.ts", "../src/api/endpoints/InstanceApi.ts", "../src/api/endpoints/InvitationApi.ts", "../src/api/endpoints/MachineTokensApi.ts", "../src/api/endpoints/JwksApi.ts", "../src/api/endpoints/JwtTemplatesApi.ts", "../src/api/endpoints/OrganizationApi.ts", "../src/api/endpoints/OAuthApplicationsApi.ts", "../src/api/endpoints/PhoneNumberApi.ts", "../src/api/endpoints/ProxyCheckApi.ts", "../src/api/endpoints/RedirectUrlApi.ts", "../src/api/endpoints/SamlConnectionApi.ts", "../src/api/endpoints/SessionApi.ts", "../src/api/endpoints/SignInTokenApi.ts", "../src/api/endpoints/SignUpApi.ts", "../src/api/endpoints/TestingTokenApi.ts", "../src/api/endpoints/UserApi.ts", "../src/api/endpoints/WaitlistEntryApi.ts", "../src/api/endpoints/WebhookApi.ts", "../src/api/request.ts", "../src/api/resources/AccountlessApplication.ts", "../src/api/resources/ActorToken.ts", "../src/api/resources/AllowlistIdentifier.ts", "../src/api/resources/APIKey.ts", "../src/api/resources/BlocklistIdentifier.ts", "../src/api/resources/Session.ts", "../src/api/resources/Client.ts", "../src/api/resources/CnameTarget.ts", "../src/api/resources/Cookies.ts", "../src/api/resources/DeletedObject.ts", "../src/api/resources/Domain.ts", "../src/api/resources/Email.ts", "../src/api/resources/IdentificationLink.ts", "../src/api/resources/Verification.ts", "../src/api/resources/EmailAddress.ts", "../src/api/resources/ExternalAccount.ts", "../src/api/resources/IdPOAuthAccessToken.ts", "../src/api/resources/Instance.ts", "../src/api/resources/InstanceRestrictions.ts", "../src/api/resources/InstanceSettings.ts", "../src/api/resources/Invitation.ts", "../src/api/resources/JSON.ts", "../src/api/resources/MachineToken.ts", "../src/api/resources/JwtTemplate.ts", "../src/api/resources/OauthAccessToken.ts", "../src/api/resources/OAuthApplication.ts", "../src/api/resources/Organization.ts", "../src/api/resources/OrganizationInvitation.ts", "../src/api/resources/OrganizationMembership.ts", "../src/api/resources/OrganizationSettings.ts", "../src/api/resources/PhoneNumber.ts", "../src/api/resources/ProxyCheck.ts", "../src/api/resources/RedirectUrl.ts", "../src/api/resources/SamlConnection.ts", "../src/api/resources/SamlAccount.ts", "../src/api/resources/SignInTokens.ts", "../src/api/resources/SignUpAttempt.ts", "../src/api/resources/SMSMessage.ts", "../src/api/resources/Token.ts", "../src/api/resources/Web3Wallet.ts", "../src/api/resources/User.ts", "../src/api/resources/WaitlistEntry.ts", "../src/api/resources/Deserializer.ts", "../src/api/factory.ts", "../src/tokens/tokenTypes.ts", "../src/tokens/machine.ts", "../src/tokens/authStatus.ts", "../src/tokens/clerkRequest.ts", "../src/tokens/clerkUrl.ts", "../src/tokens/cookie.ts", "../src/tokens/keys.ts", "../src/tokens/verify.ts", "../src/tokens/handshake.ts", "../src/tokens/organizationMatcher.ts", "../src/tokens/request.ts", "../src/tokens/factory.ts", "../src/util/decorateObjectWithResources.ts", "../src/internal.ts"], "sourcesContent": ["export const API_URL = 'https://api.clerk.com';\nexport const API_VERSION = 'v1';\n\nexport const USER_AGENT = `${PACKAGE_NAME}@${PACKAGE_VERSION}`;\nexport const MAX_CACHE_LAST_UPDATED_AT_SECONDS = 5 * 60;\nexport const SUPPORTED_BAPI_VERSION = '2025-04-10';\n\nconst Attributes = {\n  AuthToken: '__clerkAuthToken',\n  AuthSignature: '__clerkAuthSignature',\n  AuthStatus: '__clerkAuthStatus',\n  AuthReason: '__clerkAuthReason',\n  AuthMessage: '__clerkAuthMessage',\n  ClerkUrl: '__clerkUrl',\n} as const;\n\nconst Cookies = {\n  Session: '__session',\n  Refresh: '__refresh',\n  ClientUat: '__client_uat',\n  Handshake: '__clerk_handshake',\n  DevBrowser: '__clerk_db_jwt',\n  RedirectCount: '__clerk_redirect_count',\n  HandshakeNonce: '__clerk_handshake_nonce',\n} as const;\n\nconst QueryParameters = {\n  ClerkSynced: '__clerk_synced',\n  SuffixedCookies: 'suffixed_cookies',\n  ClerkRedirectUrl: '__clerk_redirect_url',\n  // use the reference to Cookies to indicate that it's the same value\n  DevBrowser: Cookies.DevBrowser,\n  Handshake: Cookies.Handshake,\n  HandshakeHelp: '__clerk_help',\n  LegacyDevBrowser: '__dev_session',\n  HandshakeReason: '__clerk_hs_reason',\n  HandshakeNonce: Cookies.HandshakeNonce,\n} as const;\n\nconst Headers = {\n  Accept: 'accept',\n  AuthMessage: 'x-clerk-auth-message',\n  Authorization: 'authorization',\n  AuthReason: 'x-clerk-auth-reason',\n  AuthSignature: 'x-clerk-auth-signature',\n  AuthStatus: 'x-clerk-auth-status',\n  AuthToken: 'x-clerk-auth-token',\n  CacheControl: 'cache-control',\n  ClerkRedirectTo: 'x-clerk-redirect-to',\n  ClerkRequestData: 'x-clerk-request-data',\n  ClerkUrl: 'x-clerk-clerk-url',\n  CloudFrontForwardedProto: 'cloudfront-forwarded-proto',\n  ContentType: 'content-type',\n  ContentSecurityPolicy: 'content-security-policy',\n  ContentSecurityPolicyReportOnly: 'content-security-policy-report-only',\n  EnableDebug: 'x-clerk-debug',\n  ForwardedHost: 'x-forwarded-host',\n  ForwardedPort: 'x-forwarded-port',\n  ForwardedProto: 'x-forwarded-proto',\n  Host: 'host',\n  Location: 'location',\n  Nonce: 'x-nonce',\n  Origin: 'origin',\n  Referrer: 'referer',\n  SecFetchDest: 'sec-fetch-dest',\n  UserAgent: 'user-agent',\n  ReportingEndpoints: 'reporting-endpoints',\n} as const;\n\nconst ContentTypes = {\n  Json: 'application/json',\n} as const;\n\n/**\n * @internal\n */\nexport const constants = {\n  Attributes,\n  Cookies,\n  Headers,\n  ContentTypes,\n  QueryParameters,\n} as const;\n\nexport type Constants = typeof constants;\n", "import { buildAccountsBaseUrl } from '@clerk/shared/buildAccountsBaseUrl';\nimport type { SessionStatusClaim } from '@clerk/types';\n\nimport { constants } from './constants';\nimport { errorThrower, parsePublishableKey } from './util/shared';\n\nconst buildUrl = (\n  _baseUrl: string | URL,\n  _targetUrl: string | URL,\n  _returnBackUrl?: string | URL | null,\n  _devBrowserToken?: string | null,\n) => {\n  if (_baseUrl === '') {\n    return legacyBuildUrl(_targetUrl.toString(), _returnBackUrl?.toString());\n  }\n\n  const baseUrl = new URL(_baseUrl);\n  const returnBackUrl = _returnBackUrl ? new URL(_returnBackUrl, baseUrl) : undefined;\n  const res = new URL(_targetUrl, baseUrl);\n\n  if (returnBackUrl) {\n    res.searchParams.set('redirect_url', returnBackUrl.toString());\n  }\n  // For cross-origin redirects, we need to pass the dev browser token for URL session syncing\n  if (_devBrowserToken && baseUrl.hostname !== res.hostname) {\n    res.searchParams.set(constants.QueryParameters.DevBrowser, _devBrowserToken);\n  }\n  return res.toString();\n};\n\n/**\n * In v5, we deprecated the top-level redirectToSignIn and redirectToSignUp functions\n * in favor of the new auth().redirectToSignIn helpers\n * In order to allow for a smooth transition, we need to support the legacy redirectToSignIn for now\n * as we will remove it in v6.\n * In order to make sure that the legacy function works as expected, we will use legacyBuildUrl\n * to build the url if baseUrl is not provided (which is the case for legacy redirectToSignIn)\n * This function can be safely removed when we remove the legacy redirectToSignIn function\n */\nconst legacyBuildUrl = (targetUrl: string, redirectUrl?: string) => {\n  let url;\n  if (!targetUrl.startsWith('http')) {\n    if (!redirectUrl || !redirectUrl.startsWith('http')) {\n      throw new Error('destination url or return back url should be an absolute path url!');\n    }\n\n    const baseURL = new URL(redirectUrl);\n    url = new URL(targetUrl, baseURL.origin);\n  } else {\n    url = new URL(targetUrl);\n  }\n\n  if (redirectUrl) {\n    url.searchParams.set('redirect_url', redirectUrl);\n  }\n\n  return url.toString();\n};\n\ntype RedirectAdapter<RedirectReturn> = (url: string) => RedirectReturn;\ntype RedirectToParams = { returnBackUrl?: string | URL | null };\nexport type RedirectFun<ReturnType> = (params?: RedirectToParams) => ReturnType;\n\n/**\n * @internal\n */\ntype CreateRedirect = <ReturnType>(params: {\n  publishableKey: string;\n  devBrowserToken?: string;\n  redirectAdapter: RedirectAdapter<ReturnType>;\n  baseUrl: URL | string;\n  signInUrl?: URL | string;\n  signUpUrl?: URL | string;\n  sessionStatus?: SessionStatusClaim | null;\n}) => {\n  redirectToSignIn: RedirectFun<ReturnType>;\n  redirectToSignUp: RedirectFun<ReturnType>;\n};\n\nexport const createRedirect: CreateRedirect = params => {\n  const { publishableKey, redirectAdapter, signInUrl, signUpUrl, baseUrl, sessionStatus } = params;\n  const parsedPublishableKey = parsePublishableKey(publishableKey);\n  const frontendApi = parsedPublishableKey?.frontendApi;\n  const isDevelopment = parsedPublishableKey?.instanceType === 'development';\n  const accountsBaseUrl = buildAccountsBaseUrl(frontendApi);\n  const hasPendingStatus = sessionStatus === 'pending';\n\n  const redirectToTasks = (url: string | URL, { returnBackUrl }: RedirectToParams) => {\n    return redirectAdapter(\n      buildUrl(baseUrl, `${url}/tasks`, returnBackUrl, isDevelopment ? params.devBrowserToken : null),\n    );\n  };\n\n  const redirectToSignUp = ({ returnBackUrl }: RedirectToParams = {}) => {\n    if (!signUpUrl && !accountsBaseUrl) {\n      errorThrower.throwMissingPublishableKeyError();\n    }\n\n    const accountsSignUpUrl = `${accountsBaseUrl}/sign-up`;\n\n    // Allows redirection to SignInOrUp path\n    function buildSignUpUrl(signIn: string | URL | undefined) {\n      if (!signIn) {\n        return;\n      }\n      const url = new URL(signIn, baseUrl);\n      url.pathname = `${url.pathname}/create`;\n      return url.toString();\n    }\n\n    const targetUrl = signUpUrl || buildSignUpUrl(signInUrl) || accountsSignUpUrl;\n\n    if (hasPendingStatus) {\n      return redirectToTasks(targetUrl, { returnBackUrl });\n    }\n\n    return redirectAdapter(buildUrl(baseUrl, targetUrl, returnBackUrl, isDevelopment ? params.devBrowserToken : null));\n  };\n\n  const redirectToSignIn = ({ returnBackUrl }: RedirectToParams = {}) => {\n    if (!signInUrl && !accountsBaseUrl) {\n      errorThrower.throwMissingPublishableKeyError();\n    }\n\n    const accountsSignInUrl = `${accountsBaseUrl}/sign-in`;\n    const targetUrl = signInUrl || accountsSignInUrl;\n\n    if (hasPendingStatus) {\n      return redirectToTasks(targetUrl, { returnBackUrl });\n    }\n\n    return redirectAdapter(buildUrl(baseUrl, targetUrl, returnBackUrl, isDevelopment ? params.devBrowserToken : null));\n  };\n\n  return { redirectToSignUp, redirectToSignIn };\n};\n", "export function mergePreDefinedOptions<T extends Record<string, any>>(preDefinedOptions: T, options: Partial<T>): T {\n  return Object.keys(preDefinedOptions).reduce(\n    (obj: T, key: string) => {\n      return { ...obj, [key]: options[key] || obj[key] };\n    },\n    { ...preDefinedOptions },\n  );\n}\n", "import { parsePublishable<PERSON>ey } from './shared';\n\nexport function assertValidSecretKey(val: unknown): asserts val is string {\n  if (!val || typeof val !== 'string') {\n    throw Error('Missing Clerk Secret Key. Go to https://dashboard.clerk.com and get your key for your instance.');\n  }\n\n  //TODO: Check if the key is invalid and throw error\n}\n\nexport function assertValidPublishableKey(val: unknown): asserts val is string {\n  parsePublishableKey(val as string | undefined, { fatal: true });\n}\n", "import type { Jwt } from '@clerk/types';\n\nimport { constants } from '../constants';\nimport { decodeJwt } from '../jwt/verifyJwt';\nimport { runtime } from '../runtime';\nimport { assertValidPublishableKey } from '../util/optionsAssertions';\nimport { getCookieSuffix, getSuffixedCookieName, parsePublishableKey } from '../util/shared';\nimport type { ClerkRequest } from './clerkRequest';\nimport type { AuthenticateRequestOptions } from './types';\n\ninterface AuthenticateContext extends AuthenticateRequestOptions {\n  // header-based values\n  tokenInHeader: string | undefined;\n  origin: string | undefined;\n  host: string | undefined;\n  forwardedHost: string | undefined;\n  forwardedProto: string | undefined;\n  referrer: string | undefined;\n  userAgent: string | undefined;\n  secFetchDest: string | undefined;\n  accept: string | undefined;\n  // cookie-based values\n  sessionTokenInCookie: string | undefined;\n  refreshTokenInCookie: string | undefined;\n  clientUat: number;\n  // handshake-related values\n  devBrowserToken: string | undefined;\n  handshakeNonce: string | undefined;\n  handshakeToken: string | undefined;\n  handshakeRedirectLoopCounter: number;\n\n  // url derived from headers\n  clerkUrl: URL;\n  // enforce existence of the following props\n  publishableKey: string;\n  instanceType: string;\n  frontendApi: string;\n}\n\n/**\n * All data required to authenticate a request.\n * This is the data we use to decide whether a request\n * is in a signed in or signed out state or if we need\n * to perform a handshake.\n */\nclass AuthenticateContext implements AuthenticateContext {\n  /**\n   * Retrieves the session token from either the cookie or the header.\n   *\n   * @returns {string | undefined} The session token if available, otherwise undefined.\n   */\n  public get sessionToken(): string | undefined {\n    return this.sessionTokenInCookie || this.tokenInHeader;\n  }\n\n  public constructor(\n    private cookieSuffix: string,\n    private clerkRequest: ClerkRequest,\n    options: AuthenticateRequestOptions,\n  ) {\n    // Even though the options are assigned to this later in this function\n    // we set the publishableKey here because it is being used in cookies/headers/handshake-values\n    // as part of getMultipleAppsCookie\n    this.initPublishableKeyValues(options);\n    this.initHeaderValues();\n    // initCookieValues should be used before initHandshakeValues because it depends on suffixedCookies\n    this.initCookieValues();\n    this.initHandshakeValues();\n    Object.assign(this, options);\n    this.clerkUrl = this.clerkRequest.clerkUrl;\n  }\n\n  public usesSuffixedCookies(): boolean {\n    const suffixedClientUat = this.getSuffixedCookie(constants.Cookies.ClientUat);\n    const clientUat = this.getCookie(constants.Cookies.ClientUat);\n    const suffixedSession = this.getSuffixedCookie(constants.Cookies.Session) || '';\n    const session = this.getCookie(constants.Cookies.Session) || '';\n\n    // In the case of malformed session cookies (eg missing the iss claim), we should\n    // use the un-suffixed cookies to return signed-out state instead of triggering\n    // handshake\n    if (session && !this.tokenHasIssuer(session)) {\n      return false;\n    }\n\n    // If there's a token in un-suffixed, and it doesn't belong to this\n    // instance, then we must trust suffixed\n    if (session && !this.tokenBelongsToInstance(session)) {\n      return true;\n    }\n\n    // If there are no suffixed cookies use un-suffixed\n    if (!suffixedClientUat && !suffixedSession) {\n      return false;\n    }\n\n    const { data: sessionData } = decodeJwt(session);\n    const sessionIat = sessionData?.payload.iat || 0;\n    const { data: suffixedSessionData } = decodeJwt(suffixedSession);\n    const suffixedSessionIat = suffixedSessionData?.payload.iat || 0;\n\n    // Both indicate signed in, but un-suffixed is newer\n    // Trust un-suffixed because it's newer\n    if (suffixedClientUat !== '0' && clientUat !== '0' && sessionIat > suffixedSessionIat) {\n      return false;\n    }\n\n    // Suffixed indicates signed out, but un-suffixed indicates signed in\n    // Trust un-suffixed because it gets set with both new and old clerk.js,\n    // so we can assume it's newer\n    if (suffixedClientUat === '0' && clientUat !== '0') {\n      return false;\n    }\n\n    // Suffixed indicates signed in, un-suffixed indicates signed out\n    // This is the tricky one\n\n    // In production, suffixed_uat should be set reliably, since it's\n    // set by FAPI and not clerk.js. So in the scenario where a developer\n    // downgrades, the state will look like this:\n    // - un-suffixed session cookie: empty\n    // - un-suffixed uat: 0\n    // - suffixed session cookie: (possibly filled, possibly empty)\n    // - suffixed uat: 0\n\n    // Our SDK honors client_uat over the session cookie, so we don't\n    // need a special case for production. We can rely on suffixed,\n    // and the fact that the suffixed uat is set properly means and\n    // suffixed session cookie will be ignored.\n\n    // The important thing to make sure we have a test that confirms\n    // the user ends up as signed out in this scenario, and the suffixed\n    // session cookie is ignored\n\n    // In development, suffixed_uat is not set reliably, since it's done\n    // by clerk.js. If the developer downgrades to a pinned version of\n    // clerk.js, the suffixed uat will no longer be updated\n\n    // The best we can do is look to see if the suffixed token is expired.\n    // This means that, if a developer downgrades, and then immediately\n    // signs out, all in the span of 1 minute, then they will inadvertently\n    // remain signed in for the rest of that minute. This is a known\n    // limitation of the strategy but seems highly unlikely.\n    if (this.instanceType !== 'production') {\n      const isSuffixedSessionExpired = this.sessionExpired(suffixedSessionData);\n      if (suffixedClientUat !== '0' && clientUat === '0' && isSuffixedSessionExpired) {\n        return false;\n      }\n    }\n\n    // If a suffixed session cookie exists but the corresponding client_uat cookie is missing, fallback to using\n    // unsuffixed cookies.\n    // This handles the scenario where an app has been deployed using an SDK version that supports suffixed\n    // cookies, but FAPI for its Clerk instance has the feature disabled (eg: if we need to temporarily disable the feature).\n    if (!suffixedClientUat && suffixedSession) {\n      return false;\n    }\n\n    return true;\n  }\n\n  private initPublishableKeyValues(options: AuthenticateRequestOptions) {\n    assertValidPublishableKey(options.publishableKey);\n    this.publishableKey = options.publishableKey;\n\n    const pk = parsePublishableKey(this.publishableKey, {\n      fatal: true,\n      proxyUrl: options.proxyUrl,\n      domain: options.domain,\n      isSatellite: options.isSatellite,\n    });\n    this.instanceType = pk.instanceType;\n    this.frontendApi = pk.frontendApi;\n  }\n\n  private initHeaderValues() {\n    this.tokenInHeader = this.parseAuthorizationHeader(this.getHeader(constants.Headers.Authorization));\n    this.origin = this.getHeader(constants.Headers.Origin);\n    this.host = this.getHeader(constants.Headers.Host);\n    this.forwardedHost = this.getHeader(constants.Headers.ForwardedHost);\n    this.forwardedProto =\n      this.getHeader(constants.Headers.CloudFrontForwardedProto) || this.getHeader(constants.Headers.ForwardedProto);\n    this.referrer = this.getHeader(constants.Headers.Referrer);\n    this.userAgent = this.getHeader(constants.Headers.UserAgent);\n    this.secFetchDest = this.getHeader(constants.Headers.SecFetchDest);\n    this.accept = this.getHeader(constants.Headers.Accept);\n  }\n\n  private initCookieValues() {\n    // suffixedCookies needs to be set first because it's used in getMultipleAppsCookie\n    this.sessionTokenInCookie = this.getSuffixedOrUnSuffixedCookie(constants.Cookies.Session);\n    this.refreshTokenInCookie = this.getSuffixedCookie(constants.Cookies.Refresh);\n    this.clientUat = Number.parseInt(this.getSuffixedOrUnSuffixedCookie(constants.Cookies.ClientUat) || '') || 0;\n  }\n\n  private initHandshakeValues() {\n    this.devBrowserToken =\n      this.getQueryParam(constants.QueryParameters.DevBrowser) ||\n      this.getSuffixedOrUnSuffixedCookie(constants.Cookies.DevBrowser);\n    // Using getCookie since we don't suffix the handshake token cookie\n    this.handshakeToken =\n      this.getQueryParam(constants.QueryParameters.Handshake) || this.getCookie(constants.Cookies.Handshake);\n    this.handshakeRedirectLoopCounter = Number(this.getCookie(constants.Cookies.RedirectCount)) || 0;\n    this.handshakeNonce =\n      this.getQueryParam(constants.QueryParameters.HandshakeNonce) || this.getCookie(constants.Cookies.HandshakeNonce);\n  }\n\n  private getQueryParam(name: string) {\n    return this.clerkRequest.clerkUrl.searchParams.get(name);\n  }\n\n  private getHeader(name: string) {\n    return this.clerkRequest.headers.get(name) || undefined;\n  }\n\n  private getCookie(name: string) {\n    return this.clerkRequest.cookies.get(name) || undefined;\n  }\n\n  private getSuffixedCookie(name: string) {\n    return this.getCookie(getSuffixedCookieName(name, this.cookieSuffix)) || undefined;\n  }\n\n  private getSuffixedOrUnSuffixedCookie(cookieName: string) {\n    if (this.usesSuffixedCookies()) {\n      return this.getSuffixedCookie(cookieName);\n    }\n    return this.getCookie(cookieName);\n  }\n\n  private parseAuthorizationHeader(authorizationHeader: string | undefined | null): string | undefined {\n    if (!authorizationHeader) {\n      return undefined;\n    }\n\n    const [scheme, token] = authorizationHeader.split(' ', 2);\n\n    if (!token) {\n      // No scheme specified, treat the entire value as the token\n      return scheme;\n    }\n\n    if (scheme === 'Bearer') {\n      return token;\n    }\n\n    // Skip all other schemes\n    return undefined;\n  }\n\n  private tokenHasIssuer(token: string): boolean {\n    const { data, errors } = decodeJwt(token);\n    if (errors) {\n      return false;\n    }\n    return !!data.payload.iss;\n  }\n\n  private tokenBelongsToInstance(token: string): boolean {\n    if (!token) {\n      return false;\n    }\n\n    const { data, errors } = decodeJwt(token);\n    if (errors) {\n      return false;\n    }\n    const tokenIssuer = data.payload.iss.replace(/https?:\\/\\//gi, '');\n    return this.frontendApi === tokenIssuer;\n  }\n\n  private sessionExpired(jwt: Jwt | undefined): boolean {\n    return !!jwt && jwt?.payload.exp <= (Date.now() / 1000) >> 0;\n  }\n}\n\nexport type { AuthenticateContext };\n\nexport const createAuthenticateContext = async (\n  clerkRequest: ClerkRequest,\n  options: AuthenticateRequestOptions,\n): Promise<AuthenticateContext> => {\n  const cookieSuffix = options.publishableKey\n    ? await getCookieSuffix(options.publishableKey, runtime.crypto.subtle)\n    : '';\n  return new AuthenticateContext(cookieSuffix, clerkRequest, options);\n};\n", "import { createCheckAuthorization } from '@clerk/shared/authorization';\nimport { __experimental_JWTPayloadToAuthObjectProperties } from '@clerk/shared/jwtPayloadParser';\nimport type {\n  CheckAuthorizationFromSessionClaims,\n  Jwt,\n  JwtPayload,\n  PendingSessionOptions,\n  ServerGetToken,\n  ServerGetTokenOptions,\n  SessionStatusClaim,\n  SharedSignedInAuthObjectProperties,\n} from '@clerk/types';\n\nimport type { APIKey, CreateBackendApiOptions, IdPOAuthAccessToken, MachineToken } from '../api';\nimport { createBackendApiClient } from '../api';\nimport { isTokenTypeAccepted } from '../internal';\nimport type { AuthenticateContext } from './authenticateContext';\nimport { isMachineTokenType } from './machine';\nimport type { MachineTokenType, SessionTokenType } from './tokenTypes';\nimport { TokenType } from './tokenTypes';\nimport type { AuthenticateRequestOptions, MachineAuthType } from './types';\n\n/**\n * @inline\n */\ntype AuthObjectDebugData = Record<string, any>;\n/**\n * @inline\n */\ntype AuthObjectDebug = () => AuthObjectDebugData;\n\ntype Claims = Record<string, any>;\n\n/**\n * @internal\n */\nexport type SignedInAuthObjectOptions = CreateBackendApiOptions & {\n  token: string;\n};\n\n/**\n * @internal\n */\nexport type SignedInAuthObject = SharedSignedInAuthObjectProperties & {\n  /**\n   * The allowed token type.\n   */\n  tokenType: SessionTokenType;\n  /**\n   * A function that gets the current user's [session token](https://clerk.com/docs/backend-requests/resources/session-tokens) or a [custom JWT template](https://clerk.com/docs/backend-requests/jwt-templates).\n   */\n  getToken: ServerGetToken;\n  /**\n   * A function that checks if the user has an organization role or custom permission.\n   */\n  has: CheckAuthorizationFromSessionClaims;\n  /**\n   * Used to help debug issues when using Clerk in development.\n   */\n  debug: AuthObjectDebug;\n  isAuthenticated: true;\n};\n\n/**\n * @internal\n */\nexport type SignedOutAuthObject = {\n  sessionClaims: null;\n  sessionId: null;\n  sessionStatus: SessionStatusClaim | null;\n  actor: null;\n  tokenType: SessionTokenType;\n  userId: null;\n  orgId: null;\n  orgRole: null;\n  orgSlug: null;\n  orgPermissions: null;\n  factorVerificationAge: null;\n  getToken: ServerGetToken;\n  has: CheckAuthorizationFromSessionClaims;\n  debug: AuthObjectDebug;\n  isAuthenticated: false;\n};\n\n/**\n * Extended properties specific to each machine token type.\n * While all machine token types share common properties (id, name, subject, etc),\n * this type defines the additional properties that are unique to each token type.\n *\n * @template TAuthenticated - Whether the machine object is authenticated or not\n */\ntype MachineObjectExtendedProperties<TAuthenticated extends boolean> = {\n  api_key: TAuthenticated extends true\n    ?\n        | { name: string; claims: Claims | null; userId: string; orgId: null }\n        | { name: string; claims: Claims | null; userId: null; orgId: string }\n    : { name: null; claims: null; userId: null; orgId: null };\n  machine_token: {\n    name: TAuthenticated extends true ? string : null;\n    claims: TAuthenticated extends true ? Claims | null : null;\n    machineId: TAuthenticated extends true ? string : null;\n  };\n  oauth_token: {\n    userId: TAuthenticated extends true ? string : null;\n    clientId: TAuthenticated extends true ? string : null;\n  };\n};\n\n/**\n * @internal\n *\n * Uses `T extends any` to create a distributive conditional type.\n * This ensures that union types like `'api_key' | 'oauth_token'` are processed\n * individually, creating proper discriminated unions where each token type\n * gets its own distinct properties (e.g., oauth_token won't have claims).\n */\nexport type AuthenticatedMachineObject<T extends MachineTokenType = MachineTokenType> = T extends any\n  ? {\n      id: string;\n      subject: string;\n      scopes: string[];\n      getToken: () => Promise<string>;\n      has: CheckAuthorizationFromSessionClaims;\n      debug: AuthObjectDebug;\n      tokenType: T;\n      isAuthenticated: true;\n    } & MachineObjectExtendedProperties<true>[T]\n  : never;\n\n/**\n * @internal\n *\n * Uses `T extends any` to create a distributive conditional type.\n * This ensures that union types like `'api_key' | 'oauth_token'` are processed\n * individually, creating proper discriminated unions where each token type\n * gets its own distinct properties (e.g., oauth_token won't have claims).\n */\nexport type UnauthenticatedMachineObject<T extends MachineTokenType = MachineTokenType> = T extends any\n  ? {\n      id: null;\n      subject: null;\n      scopes: null;\n      getToken: () => Promise<null>;\n      has: CheckAuthorizationFromSessionClaims;\n      debug: AuthObjectDebug;\n      tokenType: T;\n      isAuthenticated: false;\n    } & MachineObjectExtendedProperties<false>[T]\n  : never;\n\nexport type InvalidTokenAuthObject = {\n  isAuthenticated: false;\n  tokenType: null;\n  getToken: () => Promise<null>;\n  has: () => false;\n  debug: AuthObjectDebug;\n};\n\n/**\n * @interface\n */\nexport type AuthObject =\n  | SignedInAuthObject\n  | SignedOutAuthObject\n  | AuthenticatedMachineObject\n  | UnauthenticatedMachineObject\n  | InvalidTokenAuthObject;\n\nconst createDebug = (data: AuthObjectDebugData | undefined) => {\n  return () => {\n    const res = { ...data };\n    res.secretKey = (res.secretKey || '').substring(0, 7);\n    res.jwtKey = (res.jwtKey || '').substring(0, 7);\n    return { ...res };\n  };\n};\n\n/**\n * @internal\n */\nexport function signedInAuthObject(\n  authenticateContext: Partial<AuthenticateContext>,\n  sessionToken: string,\n  sessionClaims: JwtPayload,\n): SignedInAuthObject {\n  const { actor, sessionId, sessionStatus, userId, orgId, orgRole, orgSlug, orgPermissions, factorVerificationAge } =\n    __experimental_JWTPayloadToAuthObjectProperties(sessionClaims);\n  const apiClient = createBackendApiClient(authenticateContext);\n  const getToken = createGetToken({\n    sessionId,\n    sessionToken,\n    fetcher: async (...args) => (await apiClient.sessions.getToken(...args)).jwt,\n  });\n  return {\n    tokenType: TokenType.SessionToken,\n    actor,\n    sessionClaims,\n    sessionId,\n    sessionStatus,\n    userId,\n    orgId,\n    orgRole,\n    orgSlug,\n    orgPermissions,\n    factorVerificationAge,\n    getToken,\n    has: createCheckAuthorization({\n      orgId,\n      orgRole,\n      orgPermissions,\n      userId,\n      factorVerificationAge,\n      features: (sessionClaims.fea as string) || '',\n      plans: (sessionClaims.pla as string) || '',\n    }),\n    debug: createDebug({ ...authenticateContext, sessionToken }),\n    isAuthenticated: true,\n  };\n}\n\n/**\n * @internal\n */\nexport function signedOutAuthObject(\n  debugData?: AuthObjectDebugData,\n  initialSessionStatus?: SessionStatusClaim,\n): SignedOutAuthObject {\n  return {\n    tokenType: TokenType.SessionToken,\n    sessionClaims: null,\n    sessionId: null,\n    sessionStatus: initialSessionStatus ?? null,\n    userId: null,\n    actor: null,\n    orgId: null,\n    orgRole: null,\n    orgSlug: null,\n    orgPermissions: null,\n    factorVerificationAge: null,\n    getToken: () => Promise.resolve(null),\n    has: () => false,\n    debug: createDebug(debugData),\n    isAuthenticated: false,\n  };\n}\n\n/**\n * @internal\n */\nexport function authenticatedMachineObject<T extends MachineTokenType>(\n  tokenType: T,\n  token: string,\n  verificationResult: MachineAuthType,\n  debugData?: AuthObjectDebugData,\n): AuthenticatedMachineObject<T> {\n  const baseObject = {\n    id: verificationResult.id,\n    subject: verificationResult.subject,\n    getToken: () => Promise.resolve(token),\n    has: () => false,\n    debug: createDebug(debugData),\n    isAuthenticated: true,\n  };\n\n  // Type assertions are safe here since we know the verification result type matches the tokenType.\n  // We need these assertions because TS can't infer the specific type\n  // just from the tokenType discriminator.\n\n  switch (tokenType) {\n    case TokenType.ApiKey: {\n      const result = verificationResult as APIKey;\n      return {\n        ...baseObject,\n        tokenType,\n        name: result.name,\n        claims: result.claims,\n        scopes: result.scopes,\n        userId: result.subject.startsWith('user_') ? result.subject : null,\n        orgId: result.subject.startsWith('org_') ? result.subject : null,\n      } as unknown as AuthenticatedMachineObject<T>;\n    }\n    case TokenType.MachineToken: {\n      const result = verificationResult as MachineToken;\n      return {\n        ...baseObject,\n        tokenType,\n        name: result.name,\n        claims: result.claims,\n        scopes: result.scopes,\n        machineId: result.subject,\n      } as unknown as AuthenticatedMachineObject<T>;\n    }\n    case TokenType.OAuthToken: {\n      const result = verificationResult as IdPOAuthAccessToken;\n      return {\n        ...baseObject,\n        tokenType,\n        scopes: result.scopes,\n        userId: result.subject,\n        clientId: result.clientId,\n      } as unknown as AuthenticatedMachineObject<T>;\n    }\n    default:\n      throw new Error(`Invalid token type: ${tokenType}`);\n  }\n}\n\n/**\n * @internal\n */\nexport function unauthenticatedMachineObject<T extends MachineTokenType>(\n  tokenType: T,\n  debugData?: AuthObjectDebugData,\n): UnauthenticatedMachineObject<T> {\n  const baseObject = {\n    id: null,\n    subject: null,\n    scopes: null,\n    has: () => false,\n    getToken: () => Promise.resolve(null),\n    debug: createDebug(debugData),\n    isAuthenticated: false,\n  };\n\n  switch (tokenType) {\n    case TokenType.ApiKey: {\n      return {\n        ...baseObject,\n        tokenType,\n        name: null,\n        claims: null,\n        scopes: null,\n        userId: null,\n        orgId: null,\n      } as unknown as UnauthenticatedMachineObject<T>;\n    }\n    case TokenType.MachineToken: {\n      return {\n        ...baseObject,\n        tokenType,\n        name: null,\n        claims: null,\n        scopes: null,\n        machineId: null,\n      } as unknown as UnauthenticatedMachineObject<T>;\n    }\n    case TokenType.OAuthToken: {\n      return {\n        ...baseObject,\n        tokenType,\n        scopes: null,\n        userId: null,\n        clientId: null,\n      } as unknown as UnauthenticatedMachineObject<T>;\n    }\n    default:\n      throw new Error(`Invalid token type: ${tokenType}`);\n  }\n}\n\n/**\n * @internal\n */\nexport function invalidTokenAuthObject(): InvalidTokenAuthObject {\n  return {\n    isAuthenticated: false,\n    tokenType: null,\n    getToken: () => Promise.resolve(null),\n    has: () => false,\n    debug: () => ({}),\n  };\n}\n\n/**\n * Auth objects moving through the server -> client boundary need to be serializable\n * as we need to ensure that they can be transferred via the network as pure strings.\n * Some frameworks like Remix or Next (/pages dir only) handle this serialization by simply\n * ignoring any non-serializable keys, however Nextjs /app directory is stricter and\n * throws an error if a non-serializable value is found.\n *\n * @internal\n */\nexport const makeAuthObjectSerializable = <T extends Record<string, unknown>>(obj: T): T => {\n  // remove any non-serializable props from the returned object\n\n  const { debug, getToken, has, ...rest } = obj as unknown as AuthObject;\n  return rest as unknown as T;\n};\n\ntype TokenFetcher = (sessionId: string, template: string) => Promise<string>;\n\ntype CreateGetToken = (params: { sessionId: string; sessionToken: string; fetcher: TokenFetcher }) => ServerGetToken;\n\nconst createGetToken: CreateGetToken = params => {\n  const { fetcher, sessionToken, sessionId } = params || {};\n\n  return async (options: ServerGetTokenOptions = {}) => {\n    if (!sessionId) {\n      return null;\n    }\n\n    if (options.template) {\n      return fetcher(sessionId, options.template);\n    }\n\n    return sessionToken;\n  };\n};\n\n/**\n * @internal\n */\nexport const getAuthObjectFromJwt = (\n  jwt: Jwt,\n  { treatPendingAsSignedOut = true, ...options }: PendingSessionOptions & Partial<AuthenticateContext>,\n) => {\n  const authObject = signedInAuthObject(options, jwt.raw.text, jwt.payload);\n\n  if (treatPendingAsSignedOut && authObject.sessionStatus === 'pending') {\n    return signedOutAuthObject(options, authObject.sessionStatus);\n  }\n\n  return authObject;\n};\n\n/**\n * @internal\n * Returns an auth object matching the requested token type(s).\n *\n * If the parsed token type does not match any in acceptsToken, returns:\n *   - an unauthenticated machine object for the first machine token type in acceptsToken (if present), or\n *   - a signed-out session object otherwise.\n *\n * This ensures the returned object always matches the developer's intent.\n */\nexport function getAuthObjectForAcceptedToken({\n  authObject,\n  acceptsToken = TokenType.SessionToken,\n}: {\n  authObject: AuthObject;\n  acceptsToken: AuthenticateRequestOptions['acceptsToken'];\n}): AuthObject {\n  if (acceptsToken === 'any') {\n    return authObject;\n  }\n\n  if (Array.isArray(acceptsToken)) {\n    if (!isTokenTypeAccepted(authObject.tokenType, acceptsToken)) {\n      // If the token is not in the accepted array, return invalid token auth object\n      return invalidTokenAuthObject();\n    }\n    return authObject;\n  }\n\n  // Single value: Intent based\n  if (!isTokenTypeAccepted(authObject.tokenType, acceptsToken)) {\n    if (isMachineTokenType(acceptsToken)) {\n      return unauthenticatedMachineObject(acceptsToken, authObject.debug);\n    }\n    return signedOutAuthObject(authObject.debug);\n  }\n\n  return authObject;\n}\n", "const SEPARATOR = '/';\nconst MULTIPLE_SEPARATOR_REGEX = new RegExp('(?<!:)' + SEPARATOR + '{1,}', 'g');\n\ntype PathString = string | null | undefined;\n\nexport function joinPaths(...args: PathString[]): string {\n  return args\n    .filter(p => p)\n    .join(SEPARATOR)\n    .replace(MULTIPLE_SEPARATOR_REGEX, SEPARATOR);\n}\n", "import type { RequestFunction } from '../request';\n\nexport abstract class AbstractAPI {\n  constructor(protected request: RequestFunction) {}\n\n  protected requireId(id: string) {\n    if (!id) {\n      throw new Error('A valid resource ID is required.');\n    }\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { ActorToken } from '../resources/ActorToken';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/actor_tokens';\n\ntype ActorTokenActorCreateParams = {\n  /**\n   * The ID of the actor.\n   */\n  sub: string;\n  /**\n   * Additional properties of the actor.\n   */\n  additionalProperties?: { [k: string]: any };\n};\n\ntype ActorTokenCreateParams = {\n  /**\n   * The ID of the user being impersonated.\n   */\n  userId: string;\n  /**\n   * The actor payload. It needs to include a sub property which should contain the ID of the actor.\n   *\n   * @remarks\n   * This whole payload will be also included in the JWT session token.\n   */\n  actor: ActorTokenActorCreateParams;\n  /**\n   * Optional parameter to specify the life duration of the actor token in seconds.\n   *\n   * @remarks\n   * By default, the duration is 1 hour.\n   */\n  expiresInSeconds?: number | undefined;\n  /**\n   * The maximum duration that the session which will be created by the generated actor token should last.\n   *\n   * @remarks\n   * By default, the duration of a session created via an actor token, lasts 30 minutes.\n   */\n  sessionMaxDurationInSeconds?: number | undefined;\n};\n\nexport class ActorTokenAPI extends AbstractAPI {\n  public async create(params: ActorTokenCreateParams) {\n    return this.request<ActorToken>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async revoke(actorTokenId: string) {\n    this.requireId(actorTokenId);\n    return this.request<ActorToken>({\n      method: 'POST',\n      path: joinPaths(basePath, actorTokenId, 'revoke'),\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { AccountlessApplication } from '../resources/AccountlessApplication';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/accountless_applications';\n\nexport class AccountlessApplicationAPI extends AbstractAPI {\n  public async createAccountlessApplication() {\n    return this.request<AccountlessApplication>({\n      method: 'POST',\n      path: basePath,\n    });\n  }\n\n  public async completeAccountlessApplicationOnboarding() {\n    return this.request<AccountlessApplication>({\n      method: 'POST',\n      path: joinPaths(basePath, 'complete'),\n    });\n  }\n}\n", "import type { ClerkPaginationRequest } from '@clerk/types';\n\nimport { joinPaths } from '../../util/path';\nimport type { AllowlistIdentifier } from '../resources/AllowlistIdentifier';\nimport type { DeletedObject } from '../resources/DeletedObject';\nimport type { PaginatedResourceResponse } from '../resources/Deserializer';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/allowlist_identifiers';\n\ntype AllowlistIdentifierCreateParams = {\n  identifier: string;\n  notify: boolean;\n};\n\nexport class AllowlistIdentifierAPI extends AbstractAPI {\n  public async getAllowlistIdentifierList(params: ClerkPaginationRequest = {}) {\n    return this.request<PaginatedResourceResponse<AllowlistIdentifier[]>>({\n      method: 'GET',\n      path: basePath,\n      queryParams: { ...params, paginated: true },\n    });\n  }\n\n  public async createAllowlistIdentifier(params: AllowlistIdentifierCreateParams) {\n    return this.request<AllowlistIdentifier>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async deleteAllowlistIdentifier(allowlistIdentifierId: string) {\n    this.requireId(allowlistIdentifierId);\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, allowlistIdentifierId),\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { APIKey } from '../resources/APIKey';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/api_keys';\n\nexport class APIKeysAPI extends AbstractAPI {\n  async verifySecret(secret: string) {\n    return this.request<APIKey>({\n      method: 'POST',\n      path: joinPaths(basePath, 'verify'),\n      bodyParams: { secret },\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/beta_features';\n\ntype ChangeDomainParams = {\n  /**\n   * The new home URL of the production instance e.g. https://www.example.com\n   */\n  homeUrl?: string;\n  /**\n   * Whether this is a domain for a secondary app, meaning that any subdomain\n   * provided is significant and will be stored as part of the domain. This is\n   * useful for supporting multiple apps (one primary and multiple secondaries)\n   * on the same root domain (eTLD+1).\n   */\n  isSecondary?: boolean;\n};\n\nexport class BetaFeaturesAPI extends AbstractAPI {\n  /**\n   * Change the domain of a production instance.\n   *\n   * Changing the domain requires updating the DNS records accordingly, deploying new SSL certificates,\n   * updating your Social Connection's redirect URLs and setting the new keys in your code.\n   *\n   * @remarks\n   * WARNING: Changing your domain will invalidate all current user sessions (i.e. users will be logged out).\n   *          Also, while your application is being deployed, a small downtime is expected to occur.\n   */\n  public async changeDomain(params: ChangeDomainParams) {\n    return this.request<void>({\n      method: 'POST',\n      path: joinPaths(basePath, 'change_domain'),\n      bodyParams: params,\n    });\n  }\n}\n", "import type { ClerkPaginationRequest } from '@clerk/types';\n\nimport { joinPaths } from '../../util/path';\nimport type { BlocklistIdentifier } from '../resources/BlocklistIdentifier';\nimport type { DeletedObject } from '../resources/DeletedObject';\nimport type { PaginatedResourceResponse } from '../resources/Deserializer';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/blocklist_identifiers';\n\ntype BlocklistIdentifierCreateParams = {\n  identifier: string;\n};\n\nexport class BlocklistIdentifierAPI extends AbstractAPI {\n  public async getBlocklistIdentifierList(params: ClerkPaginationRequest = {}) {\n    return this.request<PaginatedResourceResponse<BlocklistIdentifier[]>>({\n      method: 'GET',\n      path: basePath,\n      queryParams: params,\n    });\n  }\n\n  public async createBlocklistIdentifier(params: BlocklistIdentifierCreateParams) {\n    return this.request<BlocklistIdentifier>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async deleteBlocklistIdentifier(blocklistIdentifierId: string) {\n    this.requireId(blocklistIdentifierId);\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, blocklistIdentifierId),\n    });\n  }\n}\n", "import type { ClerkPaginationRequest } from '@clerk/types';\n\nimport { joinPaths } from '../../util/path';\nimport type { Client } from '../resources/Client';\nimport type { PaginatedResourceResponse } from '../resources/Deserializer';\nimport type { HandshakePayload } from '../resources/HandshakePayload';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/clients';\n\ntype GetHandshakePayloadParams = {\n  nonce: string;\n};\n\nexport class ClientAPI extends AbstractAPI {\n  public async getClientList(params: ClerkPaginationRequest = {}) {\n    return this.request<PaginatedResourceResponse<Client[]>>({\n      method: 'GET',\n      path: basePath,\n      queryParams: { ...params, paginated: true },\n    });\n  }\n\n  public async getClient(clientId: string) {\n    this.requireId(clientId);\n    return this.request<Client>({\n      method: 'GET',\n      path: joinPaths(basePath, clientId),\n    });\n  }\n\n  public verifyClient(token: string) {\n    return this.request<Client>({\n      method: 'POST',\n      path: joinPaths(basePath, 'verify'),\n      bodyParams: { token },\n    });\n  }\n\n  public async getHandshakePayload(queryParams: GetHandshakePayloadParams) {\n    return this.request<HandshakePayload>({\n      method: 'GET',\n      path: joinPaths(basePath, 'handshake_payload'),\n      queryParams,\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { DeletedObject } from '../resources/DeletedObject';\nimport type { PaginatedResourceResponse } from '../resources/Deserializer';\nimport type { Domain } from '../resources/Domain';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/domains';\n\ntype AddDomainParams = {\n  /**\n   * The new domain name. For development instances, can contain the port, i.e myhostname:3000. For production instances, must be a valid FQDN, i.e mysite.com. Cannot contain protocol scheme.\n   */\n  name: string;\n  /**\n   * Marks the new domain as satellite. Only true is accepted at the moment.\n   */\n  is_satellite: boolean;\n  /**\n   * The full URL of the proxy which will forward requests to the Clerk Frontend API for this domain. Applicable only to production instances.\n   */\n  proxy_url?: string | null;\n};\n\ntype UpdateDomainParams = Partial<Pick<AddDomainParams, 'name' | 'proxy_url'>> & {\n  /**\n   * The ID of the domain that will be updated.\n   */\n  domainId: string;\n  /**\n   * Whether this is a domain for a secondary app, meaning that any subdomain provided is significant\n   * and will be stored as part of the domain. This is useful for supporting multiple apps\n   * (one primary and multiple secondaries) on the same root domain (eTLD+1).\n   */\n  is_secondary?: boolean | null;\n};\n\nexport class DomainAPI extends AbstractAPI {\n  public async list() {\n    return this.request<PaginatedResourceResponse<Domain[]>>({\n      method: 'GET',\n      path: basePath,\n    });\n  }\n\n  public async add(params: AddDomainParams) {\n    return this.request<Domain>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async update(params: UpdateDomainParams) {\n    const { domainId, ...bodyParams } = params;\n\n    this.requireId(domainId);\n\n    return this.request<Domain>({\n      method: 'PATCH',\n      path: joinPaths(basePath, domainId),\n      bodyParams: bodyParams,\n    });\n  }\n\n  /**\n   * Deletes a satellite domain for the instance.\n   * It is currently not possible to delete the instance's primary domain.\n   */\n  public async delete(satelliteDomainId: string) {\n    return this.deleteDomain(satelliteDomainId);\n  }\n\n  /**\n   * @deprecated Use `delete` instead\n   */\n  public async deleteDomain(satelliteDomainId: string) {\n    this.requireId(satelliteDomainId);\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, satelliteDomainId),\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { DeletedObject, EmailAddress } from '../resources';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/email_addresses';\n\ntype CreateEmailAddressParams = {\n  userId: string;\n  emailAddress: string;\n  verified?: boolean;\n  primary?: boolean;\n};\n\ntype UpdateEmailAddressParams = {\n  verified?: boolean;\n  primary?: boolean;\n};\n\nexport class EmailAddressAPI extends AbstractAPI {\n  public async getEmailAddress(emailAddressId: string) {\n    this.requireId(emailAddressId);\n\n    return this.request<EmailAddress>({\n      method: 'GET',\n      path: joinPaths(basePath, emailAddressId),\n    });\n  }\n\n  public async createEmailAddress(params: CreateEmailAddressParams) {\n    return this.request<EmailAddress>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async updateEmailAddress(emailAddressId: string, params: UpdateEmailAddressParams = {}) {\n    this.requireId(emailAddressId);\n\n    return this.request<EmailAddress>({\n      method: 'PATCH',\n      path: joinPaths(basePath, emailAddressId),\n      bodyParams: params,\n    });\n  }\n\n  public async deleteEmailAddress(emailAddressId: string) {\n    this.requireId(emailAddressId);\n\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, emailAddressId),\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { IdPOAuthAccessToken } from '../resources';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/oauth_applications/access_tokens';\n\nexport class IdPOAuthAccessToken<PERSON>pi extends AbstractAPI {\n  async verifyAccessToken(accessToken: string) {\n    return this.request<IdPOAuthAccessToken>({\n      method: 'POST',\n      path: joinPaths(basePath, 'verify'),\n      bodyParams: { access_token: accessToken },\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { Instance } from '../resources/Instance';\nimport type { InstanceRestrictions } from '../resources/InstanceRestrictions';\nimport type { OrganizationSettings } from '../resources/OrganizationSettings';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/instance';\n\ntype UpdateParams = {\n  /**\n   * Toggles test mode for this instance, allowing the use of test email addresses and phone numbers.\n   *\n   * @remarks Defaults to true for development instances.\n   */\n  testMode?: boolean | null | undefined;\n  /**\n   * Whether the instance should be using the HIBP service to check passwords for breaches\n   */\n  hibp?: boolean | null | undefined;\n  /**\n   * The \"enhanced_email_deliverability\" feature will send emails from \"<EMAIL>\" instead of your domain.\n   *\n   * @remarks This can be helpful if you do not have a high domain reputation.\n   */\n  enhancedEmailDeliverability?: boolean | null | undefined;\n  supportEmail?: string | null | undefined;\n  clerkJsVersion?: string | null | undefined;\n  developmentOrigin?: string | null | undefined;\n  /**\n   * For browser-like stacks such as browser extensions, Electron, or Capacitor.js the instance allowed origins need to be updated with the request origin value.\n   *\n   * @remarks For Chrome extensions popup, background, or service worker pages the origin is chrome-extension://extension_uiid. For Electron apps the default origin is http://localhost:3000. For Capacitor, the origin is capacitor://localhost.\n   */\n  allowedOrigins?: Array<string> | undefined;\n  /**\n   * Whether the instance should use URL-based session syncing in development mode (i.e. without third-party cookies).\n   */\n  urlBasedSessionSyncing?: boolean | null | undefined;\n};\n\ntype UpdateRestrictionsParams = {\n  allowlist?: boolean | null | undefined;\n  blocklist?: boolean | null | undefined;\n  blockEmailSubaddresses?: boolean | null | undefined;\n  blockDisposableEmailDomains?: boolean | null | undefined;\n  ignoreDotsForGmailAddresses?: boolean | null | undefined;\n};\n\ntype UpdateOrganizationSettingsParams = {\n  enabled?: boolean | null | undefined;\n  maxAllowedMemberships?: number | null | undefined;\n  adminDeleteEnabled?: boolean | null | undefined;\n  domainsEnabled?: boolean | null | undefined;\n  /**\n   * Specifies which [enrollment modes](https://clerk.com/docs/organizations/verified-domains#enrollment-mode) to enable for your Organization Domains.\n   *\n   * @remarks Supported modes are 'automatic_invitation' & 'automatic_suggestion'.\n   */\n  domainsEnrollmentModes?: Array<string> | undefined;\n  /**\n   * Specifies what the default organization role is for an organization creator.\n   */\n  creatorRoleId?: string | null | undefined;\n  /**\n   * Specifies what the default organization role is for the organization domains.\n   */\n  domainsDefaultRoleId?: string | null | undefined;\n};\n\nexport class InstanceAPI extends AbstractAPI {\n  public async get() {\n    return this.request<Instance>({\n      method: 'GET',\n      path: basePath,\n    });\n  }\n\n  public async update(params: UpdateParams) {\n    return this.request<void>({\n      method: 'PATCH',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async updateRestrictions(params: UpdateRestrictionsParams) {\n    return this.request<InstanceRestrictions>({\n      method: 'PATCH',\n      path: joinPaths(basePath, 'restrictions'),\n      bodyParams: params,\n    });\n  }\n\n  public async updateOrganizationSettings(params: UpdateOrganizationSettingsParams) {\n    return this.request<OrganizationSettings>({\n      method: 'PATCH',\n      path: joinPaths(basePath, 'organization_settings'),\n      bodyParams: params,\n    });\n  }\n}\n", "import type { ClerkPaginationRequest } from '@clerk/types';\n\nimport { joinPaths } from '../../util/path';\nimport type { PaginatedResourceResponse } from '../resources/Deserializer';\nimport type { InvitationStatus } from '../resources/Enums';\nimport type { Invitation } from '../resources/Invitation';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/invitations';\n\ntype CreateParams = {\n  emailAddress: string;\n  redirectUrl?: string;\n  publicMetadata?: UserPublicMetadata;\n  notify?: boolean;\n  ignoreExisting?: boolean;\n};\n\ntype GetInvitationListParams = ClerkPaginationRequest<{\n  /**\n   * Filters invitations based on their status.\n   *\n   * @example\n   * Get all revoked invitations\n   * ```ts\n   * import { createClerkClient } from '@clerk/backend';\n   * const clerkClient = createClerkClient(...)\n   * await clerkClient.invitations.getInvitationList({ status: 'revoked' })\n   * ```\n   */\n  status?: InvitationStatus;\n  /**\n   * Filters invitations based on `email_address` or `id`.\n   *\n   * @example\n   * Get all invitations for a specific email address\n   * ```ts\n   * import { createClerkClient } from '@clerk/backend';\n   * const clerkClient = createClerkClient(...)\n   * await clerkClient.invitations.getInvitationList({ query: '<EMAIL>' })\n   * ```\n   */\n  query?: string;\n}>;\n\nexport class InvitationAPI extends AbstractAPI {\n  public async getInvitationList(params: GetInvitationListParams = {}) {\n    return this.request<PaginatedResourceResponse<Invitation[]>>({\n      method: 'GET',\n      path: basePath,\n      queryParams: { ...params, paginated: true },\n    });\n  }\n\n  public async createInvitation(params: CreateParams) {\n    return this.request<Invitation>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async revokeInvitation(invitationId: string) {\n    this.requireId(invitationId);\n    return this.request<Invitation>({\n      method: 'POST',\n      path: joinPaths(basePath, invitationId, 'revoke'),\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { MachineToken } from '../resources/MachineToken';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/m2m_tokens';\n\nexport class MachineTokensApi extends AbstractAPI {\n  async verifySecret(secret: string) {\n    return this.request<MachineToken>({\n      method: 'POST',\n      path: joinPaths(basePath, 'verify'),\n      bodyParams: { secret },\n    });\n  }\n}\n", "import type { JwksJSON } from '../resources/JSON';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/jwks';\n\nexport class JwksAPI extends AbstractAPI {\n  public async getJwks() {\n    return this.request<JwksJSON>({\n      method: 'GET',\n      path: basePath,\n    });\n  }\n}\n", "import type { ClerkPaginationRequest } from '@clerk/types';\nimport { joinPaths } from 'src/util/path';\n\nimport type { DeletedObject, JwtTemplate } from '../resources';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/jwt_templates';\n\ntype Claims = object;\n\ntype CreateJWTTemplateParams = {\n  /**\n   * JWT template name\n   */\n  name: string;\n  /**\n   * JWT template claims in JSON format\n   */\n  claims: Claims;\n  /**\n   * JWT token lifetime\n   */\n  lifetime?: number | null | undefined;\n  /**\n   * JWT token allowed clock skew\n   */\n  allowedClockSkew?: number | null | undefined;\n  /**\n   * Whether a custom signing key/algorithm is also provided for this template\n   */\n  customSigningKey?: boolean | undefined;\n  /**\n   * The custom signing algorithm to use when minting JWTs. Required if `custom_signing_key` is `true`.\n   */\n  signingAlgorithm?: string | null | undefined;\n  /**\n   * The custom signing private key to use when minting JWTs. Required if `custom_signing_key` is `true`.\n   */\n  signingKey?: string | null | undefined;\n};\n\ntype UpdateJWTTemplateParams = CreateJWTTemplateParams & {\n  /**\n   * JWT template ID\n   */\n  templateId: string;\n};\n\nexport class JwtTemplatesApi extends AbstractAPI {\n  public async list(params: ClerkPaginationRequest = {}) {\n    return this.request<JwtTemplate[]>({\n      method: 'GET',\n      path: basePath,\n      queryParams: { ...params, paginated: true },\n    });\n  }\n\n  public async get(templateId: string) {\n    this.requireId(templateId);\n\n    return this.request<JwtTemplate>({\n      method: 'GET',\n      path: joinPaths(basePath, templateId),\n    });\n  }\n\n  public async create(params: CreateJWTTemplateParams) {\n    return this.request<JwtTemplate>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async update(params: UpdateJWTTemplateParams) {\n    const { templateId, ...bodyParams } = params;\n\n    this.requireId(templateId);\n    return this.request<JwtTemplate>({\n      method: 'PATCH',\n      path: joinPaths(basePath, templateId),\n      bodyParams,\n    });\n  }\n\n  public async delete(templateId: string) {\n    this.requireId(templateId);\n\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, templateId),\n    });\n  }\n}\n", "import type { ClerkPaginationRequest, OrganizationEnrollmentMode } from '@clerk/types';\n\nimport { runtime } from '../../runtime';\nimport { joinPaths } from '../../util/path';\nimport type {\n  Organization,\n  OrganizationDomain,\n  OrganizationInvitation,\n  OrganizationInvitationStatus,\n  OrganizationMembership,\n} from '../resources';\nimport type { PaginatedResourceResponse } from '../resources/Deserializer';\nimport type { OrganizationMembershipRole } from '../resources/Enums';\nimport { AbstractAPI } from './AbstractApi';\nimport type { WithSign } from './util-types';\n\nconst basePath = '/organizations';\n\ntype MetadataParams<TPublic = OrganizationPublicMetadata, TPrivate = OrganizationPrivateMetadata> = {\n  publicMetadata?: TPublic;\n  privateMetadata?: TPrivate;\n};\n\ntype GetOrganizationListParams = ClerkPaginationRequest<{\n  includeMembersCount?: boolean;\n  query?: string;\n  orderBy?: WithSign<'name' | 'created_at' | 'members_count'>;\n  organizationId?: string[];\n}>;\n\ntype CreateParams = {\n  name: string;\n  slug?: string;\n  /* The User id for the user creating the organization. The user will become an administrator for the organization. */\n  createdBy?: string;\n  maxAllowedMemberships?: number;\n} & MetadataParams;\n\ntype GetOrganizationParams = ({ organizationId: string } | { slug: string }) & {\n  includeMembersCount?: boolean;\n};\n\ntype UpdateParams = {\n  name?: string;\n  slug?: string;\n  maxAllowedMemberships?: number;\n} & MetadataParams;\n\ntype UpdateLogoParams = {\n  file: Blob | File;\n  uploaderUserId?: string;\n};\n\ntype UpdateMetadataParams = MetadataParams;\n\ntype GetOrganizationMembershipListParams = ClerkPaginationRequest<{\n  organizationId: string;\n\n  /**\n   * Sorts organizations memberships by phone_number, email_address, created_at, first_name, last_name or username.\n   * By prepending one of those values with + or -, we can choose to sort in ascending (ASC) or descending (DESC) order.\n   */\n  orderBy?: WithSign<'phone_number' | 'email_address' | 'created_at' | 'first_name' | 'last_name' | 'username'>;\n\n  /**\n   * Returns users with the user ids specified. For each user id, the `+` and `-` can be\n   * prepended to the id, which denote whether the respective user id should be included or\n   * excluded from the result set. Accepts up to 100 user ids. Any user ids not found are ignored.\n   */\n  userId?: string[];\n\n  /* Returns users with the specified email addresses. Accepts up to 100 email addresses. Any email addresses not found are ignored. */\n  emailAddress?: string[];\n\n  /* Returns users with the specified phone numbers. Accepts up to 100 phone numbers. Any phone numbers not found are ignored. */\n  phoneNumber?: string[];\n\n  /* Returns users with the specified usernames. Accepts up to 100 usernames. Any usernames not found are ignored. */\n  username?: string[];\n\n  /* Returns users with the specified web3 wallet addresses. Accepts up to 100 web3 wallet addresses. Any web3 wallet addressed not found are ignored. */\n  web3Wallet?: string[];\n\n  /* Returns users with the specified roles. Accepts up to 100 roles. Any roles not found are ignored. */\n  role?: OrganizationMembershipRole[];\n\n  /**\n   * Returns users that match the given query.\n   * For possible matches, we check the email addresses, phone numbers, usernames, web3 wallets, user ids, first and last names.\n   * The query value doesn't need to match the exact value you are looking for, it is capable of partial matches as well.\n   */\n  query?: string;\n\n  /**\n   * Returns users with emails that match the given query, via case-insensitive partial match.\n   * For example, `email_address_query=ello` will match a user with the email `<EMAIL>`.\n   */\n  emailAddressQuery?: string;\n\n  /**\n   * Returns users with phone numbers that match the given query, via case-insensitive partial match.\n   * For example, `phone_number_query=555` will match a user with the phone number `+1555xxxxxxx`.\n   */\n  phoneNumberQuery?: string;\n\n  /**\n   * Returns users with usernames that match the given query, via case-insensitive partial match.\n   * For example, `username_query=CoolUser` will match a user with the username `SomeCoolUser`.\n   */\n  usernameQuery?: string;\n\n  /* Returns users with names that match the given query, via case-insensitive partial match. */\n  nameQuery?: string;\n\n  /**\n   * Returns users whose last session activity was before the given date (with millisecond precision).\n   * Example: use 1700690400000 to retrieve users whose last session activity was before 2023-11-23.\n   */\n  lastActiveAtBefore?: number;\n  /**\n   * Returns users whose last session activity was after the given date (with millisecond precision).\n   * Example: use 1700690400000 to retrieve users whose last session activity was after 2023-11-23.\n   */\n  lastActiveAtAfter?: number;\n\n  /**\n   * Returns users who have been created before the given date (with millisecond precision).\n   * Example: use 1730160000000 to retrieve users who have been created before 2024-10-29.\n   */\n  createdAtBefore?: number;\n\n  /**\n   * Returns users who have been created after the given date (with millisecond precision).\n   * Example: use 1730160000000 to retrieve users who have been created after 2024-10-29.\n   */\n  createdAtAfter?: number;\n}>;\n\ntype GetInstanceOrganizationMembershipListParams = ClerkPaginationRequest<{\n  /**\n   * Sorts organizations memberships by phone_number, email_address, created_at, first_name, last_name or username.\n   * By prepending one of those values with + or -, we can choose to sort in ascending (ASC) or descending (DESC) order.\n   */\n  orderBy?: WithSign<'phone_number' | 'email_address' | 'created_at' | 'first_name' | 'last_name' | 'username'>;\n}>;\n\ntype CreateOrganizationMembershipParams = {\n  organizationId: string;\n  userId: string;\n  role: OrganizationMembershipRole;\n};\n\ntype UpdateOrganizationMembershipParams = CreateOrganizationMembershipParams;\n\ntype UpdateOrganizationMembershipMetadataParams = {\n  organizationId: string;\n  userId: string;\n} & MetadataParams<OrganizationMembershipPublicMetadata>;\n\ntype DeleteOrganizationMembershipParams = {\n  organizationId: string;\n  userId: string;\n};\n\ntype CreateOrganizationInvitationParams = {\n  organizationId: string;\n  inviterUserId?: string;\n  emailAddress: string;\n  role: OrganizationMembershipRole;\n  redirectUrl?: string;\n  publicMetadata?: OrganizationInvitationPublicMetadata;\n};\n\ntype CreateBulkOrganizationInvitationParams = Array<{\n  inviterUserId?: string;\n  emailAddress: string;\n  role: OrganizationMembershipRole;\n  redirectUrl?: string;\n  publicMetadata?: OrganizationInvitationPublicMetadata;\n}>;\n\ntype GetOrganizationInvitationListParams = ClerkPaginationRequest<{\n  organizationId: string;\n  status?: OrganizationInvitationStatus[];\n}>;\n\ntype GetOrganizationInvitationParams = {\n  organizationId: string;\n  invitationId: string;\n};\n\ntype RevokeOrganizationInvitationParams = {\n  organizationId: string;\n  invitationId: string;\n  requestingUserId?: string;\n};\n\ntype GetOrganizationDomainListParams = {\n  organizationId: string;\n  limit?: number;\n  offset?: number;\n};\n\ntype CreateOrganizationDomainParams = {\n  organizationId: string;\n  name: string;\n  enrollmentMode: OrganizationEnrollmentMode;\n  verified?: boolean;\n};\n\ntype UpdateOrganizationDomainParams = {\n  organizationId: string;\n  domainId: string;\n} & Partial<CreateOrganizationDomainParams>;\n\ntype DeleteOrganizationDomainParams = {\n  organizationId: string;\n  domainId: string;\n};\n\nexport class OrganizationAPI extends AbstractAPI {\n  public async getOrganizationList(params?: GetOrganizationListParams) {\n    return this.request<PaginatedResourceResponse<Organization[]>>({\n      method: 'GET',\n      path: basePath,\n      queryParams: params,\n    });\n  }\n\n  public async createOrganization(params: CreateParams) {\n    return this.request<Organization>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async getOrganization(params: GetOrganizationParams) {\n    const { includeMembersCount } = params;\n    const organizationIdOrSlug = 'organizationId' in params ? params.organizationId : params.slug;\n    this.requireId(organizationIdOrSlug);\n\n    return this.request<Organization>({\n      method: 'GET',\n      path: joinPaths(basePath, organizationIdOrSlug),\n      queryParams: {\n        includeMembersCount,\n      },\n    });\n  }\n\n  public async updateOrganization(organizationId: string, params: UpdateParams) {\n    this.requireId(organizationId);\n    return this.request<Organization>({\n      method: 'PATCH',\n      path: joinPaths(basePath, organizationId),\n      bodyParams: params,\n    });\n  }\n\n  public async updateOrganizationLogo(organizationId: string, params: UpdateLogoParams) {\n    this.requireId(organizationId);\n\n    const formData = new runtime.FormData();\n    formData.append('file', params?.file);\n    if (params?.uploaderUserId) {\n      formData.append('uploader_user_id', params?.uploaderUserId);\n    }\n\n    return this.request<Organization>({\n      method: 'PUT',\n      path: joinPaths(basePath, organizationId, 'logo'),\n      formData,\n    });\n  }\n\n  public async deleteOrganizationLogo(organizationId: string) {\n    this.requireId(organizationId);\n\n    return this.request<Organization>({\n      method: 'DELETE',\n      path: joinPaths(basePath, organizationId, 'logo'),\n    });\n  }\n\n  public async updateOrganizationMetadata(organizationId: string, params: UpdateMetadataParams) {\n    this.requireId(organizationId);\n\n    return this.request<Organization>({\n      method: 'PATCH',\n      path: joinPaths(basePath, organizationId, 'metadata'),\n      bodyParams: params,\n    });\n  }\n\n  public async deleteOrganization(organizationId: string) {\n    return this.request<Organization>({\n      method: 'DELETE',\n      path: joinPaths(basePath, organizationId),\n    });\n  }\n\n  public async getOrganizationMembershipList(params: GetOrganizationMembershipListParams) {\n    const { organizationId, ...queryParams } = params;\n    this.requireId(organizationId);\n\n    return this.request<PaginatedResourceResponse<OrganizationMembership[]>>({\n      method: 'GET',\n      path: joinPaths(basePath, organizationId, 'memberships'),\n      queryParams,\n    });\n  }\n\n  public async getInstanceOrganizationMembershipList(params: GetInstanceOrganizationMembershipListParams) {\n    return this.request<PaginatedResourceResponse<OrganizationMembership[]>>({\n      method: 'GET',\n      path: '/organization_memberships',\n      queryParams: params,\n    });\n  }\n\n  public async createOrganizationMembership(params: CreateOrganizationMembershipParams) {\n    const { organizationId, ...bodyParams } = params;\n    this.requireId(organizationId);\n\n    return this.request<OrganizationMembership>({\n      method: 'POST',\n      path: joinPaths(basePath, organizationId, 'memberships'),\n      bodyParams,\n    });\n  }\n\n  public async updateOrganizationMembership(params: UpdateOrganizationMembershipParams) {\n    const { organizationId, userId, ...bodyParams } = params;\n    this.requireId(organizationId);\n\n    return this.request<OrganizationMembership>({\n      method: 'PATCH',\n      path: joinPaths(basePath, organizationId, 'memberships', userId),\n      bodyParams,\n    });\n  }\n\n  public async updateOrganizationMembershipMetadata(params: UpdateOrganizationMembershipMetadataParams) {\n    const { organizationId, userId, ...bodyParams } = params;\n\n    return this.request<OrganizationMembership>({\n      method: 'PATCH',\n      path: joinPaths(basePath, organizationId, 'memberships', userId, 'metadata'),\n      bodyParams,\n    });\n  }\n\n  public async deleteOrganizationMembership(params: DeleteOrganizationMembershipParams) {\n    const { organizationId, userId } = params;\n    this.requireId(organizationId);\n\n    return this.request<OrganizationMembership>({\n      method: 'DELETE',\n      path: joinPaths(basePath, organizationId, 'memberships', userId),\n    });\n  }\n\n  public async getOrganizationInvitationList(params: GetOrganizationInvitationListParams) {\n    const { organizationId, ...queryParams } = params;\n    this.requireId(organizationId);\n\n    return this.request<PaginatedResourceResponse<OrganizationInvitation[]>>({\n      method: 'GET',\n      path: joinPaths(basePath, organizationId, 'invitations'),\n      queryParams,\n    });\n  }\n\n  public async createOrganizationInvitation(params: CreateOrganizationInvitationParams) {\n    const { organizationId, ...bodyParams } = params;\n    this.requireId(organizationId);\n\n    return this.request<OrganizationInvitation>({\n      method: 'POST',\n      path: joinPaths(basePath, organizationId, 'invitations'),\n      bodyParams,\n    });\n  }\n\n  public async createOrganizationInvitationBulk(\n    organizationId: string,\n    params: CreateBulkOrganizationInvitationParams,\n  ) {\n    this.requireId(organizationId);\n\n    return this.request<OrganizationInvitation[]>({\n      method: 'POST',\n      path: joinPaths(basePath, organizationId, 'invitations', 'bulk'),\n      bodyParams: params,\n    });\n  }\n\n  public async getOrganizationInvitation(params: GetOrganizationInvitationParams) {\n    const { organizationId, invitationId } = params;\n    this.requireId(organizationId);\n    this.requireId(invitationId);\n\n    return this.request<OrganizationInvitation>({\n      method: 'GET',\n      path: joinPaths(basePath, organizationId, 'invitations', invitationId),\n    });\n  }\n\n  public async revokeOrganizationInvitation(params: RevokeOrganizationInvitationParams) {\n    const { organizationId, invitationId, ...bodyParams } = params;\n    this.requireId(organizationId);\n\n    return this.request<OrganizationInvitation>({\n      method: 'POST',\n      path: joinPaths(basePath, organizationId, 'invitations', invitationId, 'revoke'),\n      bodyParams,\n    });\n  }\n\n  public async getOrganizationDomainList(params: GetOrganizationDomainListParams) {\n    const { organizationId, ...queryParams } = params;\n    this.requireId(organizationId);\n\n    return this.request<PaginatedResourceResponse<OrganizationDomain[]>>({\n      method: 'GET',\n      path: joinPaths(basePath, organizationId, 'domains'),\n      queryParams,\n    });\n  }\n\n  public async createOrganizationDomain(params: CreateOrganizationDomainParams) {\n    const { organizationId, ...bodyParams } = params;\n    this.requireId(organizationId);\n\n    return this.request<OrganizationDomain>({\n      method: 'POST',\n      path: joinPaths(basePath, organizationId, 'domains'),\n      bodyParams: {\n        ...bodyParams,\n        verified: bodyParams.verified ?? true,\n      },\n    });\n  }\n\n  public async updateOrganizationDomain(params: UpdateOrganizationDomainParams) {\n    const { organizationId, domainId, ...bodyParams } = params;\n    this.requireId(organizationId);\n    this.requireId(domainId);\n\n    return this.request<OrganizationDomain>({\n      method: 'PATCH',\n      path: joinPaths(basePath, organizationId, 'domains', domainId),\n      bodyParams,\n    });\n  }\n\n  public async deleteOrganizationDomain(params: DeleteOrganizationDomainParams) {\n    const { organizationId, domainId } = params;\n    this.requireId(organizationId);\n    this.requireId(domainId);\n\n    return this.request<OrganizationDomain>({\n      method: 'DELETE',\n      path: joinPaths(basePath, organizationId, 'domains', domainId),\n    });\n  }\n}\n", "import type { ClerkPaginationRequest } from '@clerk/types';\n\nimport { joinPaths } from '../../util/path';\nimport type { DeletedObject } from '../resources';\nimport type { PaginatedResourceResponse } from '../resources/Deserializer';\nimport type { OAuthApplication } from '../resources/OAuthApplication';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/oauth_applications';\n\ntype CreateOAuthApplicationParams = {\n  /**\n   * The name of the new OAuth application.\n   *\n   * @remarks Max length: 256\n   */\n  name: string;\n  /**\n   * An array of redirect URIs of the new OAuth application\n   */\n  redirectUris?: Array<string> | null | undefined;\n  /**\n   * Define the allowed scopes for the new OAuth applications that dictate the user payload of the OAuth user info endpoint. Available scopes are `profile`, `email`, `public_metadata`, `private_metadata`. Provide the requested scopes as a string, separated by spaces.\n   */\n  scopes?: string | null | undefined;\n  /**\n   * If true, this client is public and you can use the Proof Key of Code Exchange (PKCE) flow.\n   */\n  public?: boolean | null | undefined;\n};\n\ntype UpdateOAuthApplicationParams = CreateOAuthApplicationParams & {\n  /**\n   * The ID of the OAuth application to update\n   */\n  oauthApplicationId: string;\n};\n\nexport class OAuthApplicationsApi extends AbstractAPI {\n  public async list(params: ClerkPaginationRequest = {}) {\n    return this.request<PaginatedResourceResponse<OAuthApplication[]>>({\n      method: 'GET',\n      path: basePath,\n      queryParams: params,\n    });\n  }\n\n  public async get(oauthApplicationId: string) {\n    this.requireId(oauthApplicationId);\n\n    return this.request<OAuthApplication>({\n      method: 'GET',\n      path: joinPaths(basePath, oauthApplicationId),\n    });\n  }\n\n  public async create(params: CreateOAuthApplicationParams) {\n    return this.request<OAuthApplication>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async update(params: UpdateOAuthApplicationParams) {\n    const { oauthApplicationId, ...bodyParams } = params;\n\n    this.requireId(oauthApplicationId);\n\n    return this.request<OAuthApplication>({\n      method: 'PATCH',\n      path: joinPaths(basePath, oauthApplicationId),\n      bodyParams,\n    });\n  }\n\n  public async delete(oauthApplicationId: string) {\n    this.requireId(oauthApplicationId);\n\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, oauthApplicationId),\n    });\n  }\n\n  public async rotateSecret(oauthApplicationId: string) {\n    this.requireId(oauthApplicationId);\n\n    return this.request<OAuthApplication>({\n      method: 'POST',\n      path: joinPaths(basePath, oauthApplicationId, 'rotate_secret'),\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { DeletedObject, PhoneNumber } from '../resources';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/phone_numbers';\n\ntype CreatePhoneNumberParams = {\n  userId: string;\n  phoneNumber: string;\n  verified?: boolean;\n  primary?: boolean;\n  reservedForSecondFactor?: boolean;\n};\n\ntype UpdatePhoneNumberParams = {\n  verified?: boolean;\n  primary?: boolean;\n  reservedForSecondFactor?: boolean;\n};\n\nexport class PhoneNumberAPI extends AbstractAPI {\n  public async getPhoneNumber(phoneNumberId: string) {\n    this.requireId(phoneNumberId);\n\n    return this.request<PhoneNumber>({\n      method: 'GET',\n      path: joinPaths(basePath, phoneNumberId),\n    });\n  }\n\n  public async createPhoneNumber(params: CreatePhoneNumberParams) {\n    return this.request<PhoneNumber>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async updatePhoneNumber(phoneNumberId: string, params: UpdatePhoneNumberParams = {}) {\n    this.requireId(phoneNumberId);\n\n    return this.request<PhoneNumber>({\n      method: 'PATCH',\n      path: joinPaths(basePath, phoneNumberId),\n      bodyParams: params,\n    });\n  }\n\n  public async deletePhoneNumber(phoneNumberId: string) {\n    this.requireId(phoneNumberId);\n\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, phoneNumberId),\n    });\n  }\n}\n", "import type { ProxyCheck } from '../resources';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/proxy_checks';\n\ntype VerifyParams = {\n  domainId: string;\n  proxyUrl: string;\n};\n\nexport class ProxyCheckAPI extends AbstractAPI {\n  public async verify(params: VerifyParams) {\n    return this.request<ProxyCheck>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { PaginatedResourceResponse } from '../resources/Deserializer';\nimport type { RedirectUrl } from '../resources/RedirectUrl';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/redirect_urls';\n\ntype CreateRedirectUrlParams = {\n  url: string;\n};\n\nexport class RedirectUrlAPI extends AbstractAPI {\n  public async getRedirectUrlList() {\n    return this.request<PaginatedResourceResponse<RedirectUrl[]>>({\n      method: 'GET',\n      path: basePath,\n      queryParams: { paginated: true },\n    });\n  }\n\n  public async getRedirectUrl(redirectUrlId: string) {\n    this.requireId(redirectUrlId);\n    return this.request<RedirectUrl>({\n      method: 'GET',\n      path: joinPaths(basePath, redirectUrlId),\n    });\n  }\n\n  public async createRedirectUrl(params: CreateRedirectUrlParams) {\n    return this.request<RedirectUrl>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async deleteRedirectUrl(redirectUrlId: string) {\n    this.requireId(redirectUrlId);\n    return this.request<RedirectUrl>({\n      method: 'DELETE',\n      path: joinPaths(basePath, redirectUrlId),\n    });\n  }\n}\n", "import type { SamlIdpSlug } from '@clerk/types';\n\nimport { joinPaths } from '../../util/path';\nimport type { SamlConnection } from '../resources';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/saml_connections';\n\ntype SamlConnectionListParams = {\n  limit?: number;\n  offset?: number;\n};\ntype CreateSamlConnectionParams = {\n  name: string;\n  provider: SamlIdpSlug;\n  domain: string;\n  organizationId?: string;\n  idpEntityId?: string;\n  idpSsoUrl?: string;\n  idpCertificate?: string;\n  idpMetadataUrl?: string;\n  idpMetadata?: string;\n  attributeMapping?: {\n    emailAddress?: string;\n    firstName?: string;\n    lastName?: string;\n    userId?: string;\n  };\n};\n\ntype UpdateSamlConnectionParams = {\n  name?: string;\n  provider?: SamlIdpSlug;\n  domain?: string;\n  organizationId?: string;\n  idpEntityId?: string;\n  idpSsoUrl?: string;\n  idpCertificate?: string;\n  idpMetadataUrl?: string;\n  idpMetadata?: string;\n  attributeMapping?: {\n    emailAddress?: string;\n    firstName?: string;\n    lastName?: string;\n    userId?: string;\n  };\n  active?: boolean;\n  syncUserAttributes?: boolean;\n  allowSubdomains?: boolean;\n  allowIdpInitiated?: boolean;\n};\n\nexport class SamlConnectionAPI extends AbstractAPI {\n  public async getSamlConnectionList(params: SamlConnectionListParams = {}) {\n    return this.request<SamlConnection[]>({\n      method: 'GET',\n      path: basePath,\n      queryParams: params,\n    });\n  }\n\n  public async createSamlConnection(params: CreateSamlConnectionParams) {\n    return this.request<SamlConnection>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async getSamlConnection(samlConnectionId: string) {\n    this.requireId(samlConnectionId);\n    return this.request<SamlConnection>({\n      method: 'GET',\n      path: joinPaths(basePath, samlConnectionId),\n    });\n  }\n\n  public async updateSamlConnection(samlConnectionId: string, params: UpdateSamlConnectionParams = {}) {\n    this.requireId(samlConnectionId);\n\n    return this.request<SamlConnection>({\n      method: 'PATCH',\n      path: joinPaths(basePath, samlConnectionId),\n      bodyParams: params,\n    });\n  }\n  public async deleteSamlConnection(samlConnectionId: string) {\n    this.requireId(samlConnectionId);\n    return this.request<SamlConnection>({\n      method: 'DELETE',\n      path: joinPaths(basePath, samlConnectionId),\n    });\n  }\n}\n", "import type { ClerkPaginationRequest, SessionStatus } from '@clerk/types';\n\nimport { joinPaths } from '../../util/path';\nimport type { Cookies } from '../resources/Cookies';\nimport type { PaginatedResourceResponse } from '../resources/Deserializer';\nimport type { Session } from '../resources/Session';\nimport type { Token } from '../resources/Token';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/sessions';\n\ntype SessionListParams = ClerkPaginationRequest<{\n  clientId?: string;\n  userId?: string;\n  status?: SessionStatus;\n}>;\n\ntype RefreshTokenParams = {\n  expired_token: string;\n  refresh_token: string;\n  request_origin: string;\n  request_originating_ip?: string;\n  request_headers?: Record<string, string[]>;\n  suffixed_cookies?: boolean;\n  format?: 'token' | 'cookie';\n};\n\ntype CreateSessionParams = {\n  userId: string;\n};\n\nexport class SessionAPI extends AbstractAPI {\n  public async getSessionList(params: SessionListParams = {}) {\n    return this.request<PaginatedResourceResponse<Session[]>>({\n      method: 'GET',\n      path: basePath,\n      queryParams: { ...params, paginated: true },\n    });\n  }\n\n  public async getSession(sessionId: string) {\n    this.requireId(sessionId);\n    return this.request<Session>({\n      method: 'GET',\n      path: joinPaths(basePath, sessionId),\n    });\n  }\n\n  public async createSession(params: CreateSessionParams) {\n    return this.request<Session>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async revokeSession(sessionId: string) {\n    this.requireId(sessionId);\n    return this.request<Session>({\n      method: 'POST',\n      path: joinPaths(basePath, sessionId, 'revoke'),\n    });\n  }\n\n  public async verifySession(sessionId: string, token: string) {\n    this.requireId(sessionId);\n    return this.request<Session>({\n      method: 'POST',\n      path: joinPaths(basePath, sessionId, 'verify'),\n      bodyParams: { token },\n    });\n  }\n\n  public async getToken(sessionId: string, template: string) {\n    this.requireId(sessionId);\n    return this.request<Token>({\n      method: 'POST',\n      path: joinPaths(basePath, sessionId, 'tokens', template || ''),\n    });\n  }\n\n  public async refreshSession(sessionId: string, params: RefreshTokenParams & { format: 'token' }): Promise<Token>;\n  public async refreshSession(sessionId: string, params: RefreshTokenParams & { format: 'cookie' }): Promise<Cookies>;\n  public async refreshSession(sessionId: string, params: RefreshTokenParams): Promise<Token>;\n  public async refreshSession(sessionId: string, params: RefreshTokenParams): Promise<Token | Cookies> {\n    this.requireId(sessionId);\n    const { suffixed_cookies, ...restParams } = params;\n    return this.request({\n      method: 'POST',\n      path: joinPaths(basePath, sessionId, 'refresh'),\n      bodyParams: restParams,\n      queryParams: { suffixed_cookies },\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { SignInToken } from '../resources/SignInTokens';\nimport { AbstractAPI } from './AbstractApi';\n\ntype CreateSignInTokensParams = {\n  userId: string;\n  expiresInSeconds: number;\n};\n\nconst basePath = '/sign_in_tokens';\n\nexport class SignInTokenAPI extends AbstractAPI {\n  public async createSignInToken(params: CreateSignInTokensParams) {\n    return this.request<SignInToken>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async revokeSignInToken(signInTokenId: string) {\n    this.requireId(signInTokenId);\n    return this.request<SignInToken>({\n      method: 'POST',\n      path: joinPaths(basePath, signInTokenId, 'revoke'),\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { SignUpAttempt } from '../resources/SignUpAttempt';\nimport { AbstractAPI } from './AbstractApi';\n\ntype UpdateSignUpParams = {\n  signUpAttemptId: string;\n  externalId?: string | null;\n  customAction?: boolean | null;\n};\n\nconst basePath = '/sign_ups';\n\nexport class SignUpAPI extends AbstractAPI {\n  public async get(signUpAttemptId: string) {\n    this.requireId(signUpAttemptId);\n\n    return this.request<SignUpAttempt>({\n      method: 'GET',\n      path: joinPaths(basePath, signUpAttemptId),\n    });\n  }\n\n  public async update(params: UpdateSignUpParams) {\n    const { signUpAttemptId, ...bodyParams } = params;\n\n    return this.request<SignUpAttempt>({\n      method: 'PATCH',\n      path: joinPaths(basePath, signUpAttemptId),\n      bodyParams,\n    });\n  }\n}\n", "import type { TestingToken } from '../resources/TestingToken';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/testing_tokens';\n\nexport class TestingTokenAPI extends AbstractAPI {\n  public async createTestingToken() {\n    return this.request<TestingToken>({\n      method: 'POST',\n      path: basePath,\n    });\n  }\n}\n", "import type { ClerkPaginationRequest, OAuthProvider, OrganizationInvitationStatus } from '@clerk/types';\n\nimport { runtime } from '../../runtime';\nimport { joinPaths } from '../../util/path';\nimport { deprecated } from '../../util/shared';\nimport type {\n  DeletedObject,\n  OauthAccessToken,\n  OrganizationInvitation,\n  OrganizationMembership,\n  User,\n} from '../resources';\nimport type { PaginatedResourceResponse } from '../resources/Deserializer';\nimport { AbstractAPI } from './AbstractApi';\nimport type { WithSign } from './util-types';\n\nconst basePath = '/users';\n\ntype UserCountParams = {\n  emailAddress?: string[];\n  phoneNumber?: string[];\n  username?: string[];\n  web3Wallet?: string[];\n  query?: string;\n  userId?: string[];\n  externalId?: string[];\n};\n\ntype UserListParams = ClerkPaginationRequest<\n  UserCountParams & {\n    orderBy?: WithSign<\n      | 'created_at'\n      | 'updated_at'\n      | 'email_address'\n      | 'web3wallet'\n      | 'first_name'\n      | 'last_name'\n      | 'phone_number'\n      | 'username'\n      | 'last_active_at'\n      | 'last_sign_in_at'\n    >;\n    last_active_at_since?: number;\n    organizationId?: string[];\n  }\n>;\n\ntype UserMetadataParams = {\n  publicMetadata?: UserPublicMetadata;\n  privateMetadata?: UserPrivateMetadata;\n  unsafeMetadata?: UserUnsafeMetadata;\n};\n\ntype PasswordHasher =\n  | 'argon2i'\n  | 'argon2id'\n  | 'awscognito'\n  | 'bcrypt'\n  | 'bcrypt_sha256_django'\n  | 'md5'\n  | 'pbkdf2_sha256'\n  | 'pbkdf2_sha256_django'\n  | 'pbkdf2_sha1'\n  | 'phpass'\n  | 'scrypt_firebase'\n  | 'scrypt_werkzeug'\n  | 'sha256'\n  | 'md5_phpass'\n  | 'ldap_ssha';\n\ntype UserPasswordHashingParams = {\n  passwordDigest: string;\n  passwordHasher: PasswordHasher;\n};\n\ntype CreateUserParams = {\n  externalId?: string;\n  emailAddress?: string[];\n  phoneNumber?: string[];\n  username?: string;\n  password?: string;\n  firstName?: string;\n  lastName?: string;\n  skipPasswordChecks?: boolean;\n  skipPasswordRequirement?: boolean;\n  skipLegalChecks?: boolean;\n  legalAcceptedAt?: Date;\n  totpSecret?: string;\n  backupCodes?: string[];\n  createdAt?: Date;\n} & UserMetadataParams &\n  (UserPasswordHashingParams | object);\n\ntype UpdateUserParams = {\n  /** The first name to assign to the user. */\n  firstName?: string;\n\n  /** The last name of the user. */\n  lastName?: string;\n\n  /** The username to give to the user. It must be unique across your instance. */\n  username?: string;\n\n  /** The plaintext password to give the user. Must be at least 8 characters long, and can not be in any list of hacked passwords. */\n  password?: string;\n\n  /** Set it to true if you're updating the user's password and want to skip any password policy settings check. This parameter can only be used when providing a password. */\n  skipPasswordChecks?: boolean;\n\n  /** Set to true to sign out the user from all their active sessions once their password is updated. This parameter can only be used when providing a password. */\n  signOutOfOtherSessions?: boolean;\n\n  /** The ID of the email address to set as primary. It must be verified, and present on the current user. */\n  primaryEmailAddressID?: string;\n\n  /** If set to true, the user will be notified that their primary email address has changed. By default, no notification is sent. */\n  notifyPrimaryEmailAddressChanged?: boolean;\n\n  /** The ID of the phone number to set as primary. It must be verified, and present on the current user. */\n  primaryPhoneNumberID?: string;\n\n  /** The ID of the web3 wallets to set as primary. It must be verified, and present on the current user. */\n  primaryWeb3WalletID?: string;\n\n  /** The ID of the image to set as the user's profile image */\n  profileImageID?: string;\n\n  /**\n   * In case TOTP is configured on the instance, you can provide the secret to enable it on the specific user without the need to reset it.\n   * Please note that currently the supported options are:\n   * - Period: 30 seconds\n   * - Code length: 6 digits\n   * - Algorithm: SHA1\n   */\n  totpSecret?: string;\n\n  /** If Backup Codes are configured on the instance, you can provide them to enable it on the specific user without the need to reset them. You must provide the backup codes in plain format or the corresponding bcrypt digest. */\n  backupCodes?: string[];\n\n  /** The ID of the user as used in your external systems or your previous authentication solution. Must be unique across your instance. */\n  externalId?: string;\n\n  /** A custom timestamp denoting when the user signed up to the application, specified in RFC3339 format (e.g. 2012-10-20T07:15:20.902Z). */\n  createdAt?: Date;\n\n  /** When set to true all legal checks are skipped. It is not recommended to skip legal checks unless you are migrating a user to Clerk. */\n  skipLegalChecks?: boolean;\n\n  /** A custom timestamp denoting when the user accepted legal requirements, specified in RFC3339 format (e.g. 2012-10-20T07:15:20.902Z). */\n  legalAcceptedAt?: Date;\n\n  /** If true, the user can delete themselves with the Frontend API. */\n  deleteSelfEnabled?: boolean;\n\n  /** If true, the user can create organizations with the Frontend API. */\n  createOrganizationEnabled?: boolean;\n\n  /** The maximum number of organizations the user can create. 0 means unlimited. */\n  createOrganizationsLimit?: number;\n} & UserMetadataParams &\n  (UserPasswordHashingParams | object);\n\ntype GetOrganizationMembershipListParams = ClerkPaginationRequest<{\n  userId: string;\n}>;\n\ntype GetOrganizationInvitationListParams = ClerkPaginationRequest<{\n  userId: string;\n  status?: OrganizationInvitationStatus;\n}>;\n\ntype VerifyPasswordParams = {\n  userId: string;\n  password: string;\n};\n\ntype VerifyTOTPParams = {\n  userId: string;\n  code: string;\n};\n\ntype DeleteUserPasskeyParams = {\n  userId: string;\n  passkeyIdentificationId: string;\n};\n\ntype DeleteWeb3WalletParams = {\n  userId: string;\n  web3WalletIdentificationId: string;\n};\n\ntype DeleteUserExternalAccountParams = {\n  userId: string;\n  externalAccountId: string;\n};\n\ntype UserID = {\n  userId: string;\n};\n\nexport class UserAPI extends AbstractAPI {\n  public async getUserList(params: UserListParams = {}) {\n    const { limit, offset, orderBy, ...userCountParams } = params;\n    // TODO(dimkl): Temporary change to populate totalCount using a 2nd BAPI call to /users/count endpoint\n    // until we update the /users endpoint to be paginated in a next BAPI version.\n    // In some edge cases the data.length != totalCount due to a creation of a user between the 2 api responses\n    const [data, totalCount] = await Promise.all([\n      this.request<User[]>({\n        method: 'GET',\n        path: basePath,\n        queryParams: params,\n      }),\n      this.getCount(userCountParams),\n    ]);\n    return { data, totalCount } as PaginatedResourceResponse<User[]>;\n  }\n\n  public async getUser(userId: string) {\n    this.requireId(userId);\n    return this.request<User>({\n      method: 'GET',\n      path: joinPaths(basePath, userId),\n    });\n  }\n\n  public async createUser(params: CreateUserParams) {\n    return this.request<User>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async updateUser(userId: string, params: UpdateUserParams = {}) {\n    this.requireId(userId);\n\n    return this.request<User>({\n      method: 'PATCH',\n      path: joinPaths(basePath, userId),\n      bodyParams: params,\n    });\n  }\n\n  public async updateUserProfileImage(userId: string, params: { file: Blob | File }) {\n    this.requireId(userId);\n\n    const formData = new runtime.FormData();\n    formData.append('file', params?.file);\n\n    return this.request<User>({\n      method: 'POST',\n      path: joinPaths(basePath, userId, 'profile_image'),\n      formData,\n    });\n  }\n\n  public async updateUserMetadata(userId: string, params: UserMetadataParams) {\n    this.requireId(userId);\n\n    return this.request<User>({\n      method: 'PATCH',\n      path: joinPaths(basePath, userId, 'metadata'),\n      bodyParams: params,\n    });\n  }\n\n  public async deleteUser(userId: string) {\n    this.requireId(userId);\n    return this.request<User>({\n      method: 'DELETE',\n      path: joinPaths(basePath, userId),\n    });\n  }\n\n  public async getCount(params: UserCountParams = {}) {\n    return this.request<number>({\n      method: 'GET',\n      path: joinPaths(basePath, 'count'),\n      queryParams: params,\n    });\n  }\n\n  /** @deprecated Use `getUserOauthAccessToken` without the `oauth_` provider prefix . */\n  public async getUserOauthAccessToken(\n    userId: string,\n    provider: `oauth_${OAuthProvider}`,\n  ): Promise<PaginatedResourceResponse<OauthAccessToken[]>>;\n  public async getUserOauthAccessToken(\n    userId: string,\n    provider: OAuthProvider,\n  ): Promise<PaginatedResourceResponse<OauthAccessToken[]>>;\n  public async getUserOauthAccessToken(userId: string, provider: `oauth_${OAuthProvider}` | OAuthProvider) {\n    this.requireId(userId);\n    const hasPrefix = provider.startsWith('oauth_');\n    const _provider = hasPrefix ? provider : `oauth_${provider}`;\n\n    if (hasPrefix) {\n      deprecated(\n        'getUserOauthAccessToken(userId, provider)',\n        'Remove the `oauth_` prefix from the `provider` argument.',\n      );\n    }\n\n    return this.request<PaginatedResourceResponse<OauthAccessToken[]>>({\n      method: 'GET',\n      path: joinPaths(basePath, userId, 'oauth_access_tokens', _provider),\n      queryParams: { paginated: true },\n    });\n  }\n\n  public async disableUserMFA(userId: string) {\n    this.requireId(userId);\n    return this.request<UserID>({\n      method: 'DELETE',\n      path: joinPaths(basePath, userId, 'mfa'),\n    });\n  }\n\n  public async getOrganizationMembershipList(params: GetOrganizationMembershipListParams) {\n    const { userId, limit, offset } = params;\n    this.requireId(userId);\n\n    return this.request<PaginatedResourceResponse<OrganizationMembership[]>>({\n      method: 'GET',\n      path: joinPaths(basePath, userId, 'organization_memberships'),\n      queryParams: { limit, offset },\n    });\n  }\n\n  public async getOrganizationInvitationList(params: GetOrganizationInvitationListParams) {\n    const { userId, ...queryParams } = params;\n    this.requireId(userId);\n\n    return this.request<PaginatedResourceResponse<OrganizationInvitation[]>>({\n      method: 'GET',\n      path: joinPaths(basePath, userId, 'organization_invitations'),\n      queryParams,\n    });\n  }\n\n  public async verifyPassword(params: VerifyPasswordParams) {\n    const { userId, password } = params;\n    this.requireId(userId);\n\n    return this.request<{ verified: true }>({\n      method: 'POST',\n      path: joinPaths(basePath, userId, 'verify_password'),\n      bodyParams: { password },\n    });\n  }\n\n  public async verifyTOTP(params: VerifyTOTPParams) {\n    const { userId, code } = params;\n    this.requireId(userId);\n\n    return this.request<{ verified: true; code_type: 'totp' }>({\n      method: 'POST',\n      path: joinPaths(basePath, userId, 'verify_totp'),\n      bodyParams: { code },\n    });\n  }\n\n  public async banUser(userId: string) {\n    this.requireId(userId);\n    return this.request<User>({\n      method: 'POST',\n      path: joinPaths(basePath, userId, 'ban'),\n    });\n  }\n\n  public async unbanUser(userId: string) {\n    this.requireId(userId);\n    return this.request<User>({\n      method: 'POST',\n      path: joinPaths(basePath, userId, 'unban'),\n    });\n  }\n\n  public async lockUser(userId: string) {\n    this.requireId(userId);\n    return this.request<User>({\n      method: 'POST',\n      path: joinPaths(basePath, userId, 'lock'),\n    });\n  }\n\n  public async unlockUser(userId: string) {\n    this.requireId(userId);\n    return this.request<User>({\n      method: 'POST',\n      path: joinPaths(basePath, userId, 'unlock'),\n    });\n  }\n\n  public async deleteUserProfileImage(userId: string) {\n    this.requireId(userId);\n    return this.request<User>({\n      method: 'DELETE',\n      path: joinPaths(basePath, userId, 'profile_image'),\n    });\n  }\n\n  public async deleteUserPasskey(params: DeleteUserPasskeyParams) {\n    this.requireId(params.userId);\n    this.requireId(params.passkeyIdentificationId);\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, params.userId, 'passkeys', params.passkeyIdentificationId),\n    });\n  }\n\n  public async deleteUserWeb3Wallet(params: DeleteWeb3WalletParams) {\n    this.requireId(params.userId);\n    this.requireId(params.web3WalletIdentificationId);\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, params.userId, 'web3_wallets', params.web3WalletIdentificationId),\n    });\n  }\n\n  public async deleteUserExternalAccount(params: DeleteUserExternalAccountParams) {\n    this.requireId(params.userId);\n    this.requireId(params.externalAccountId);\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, params.userId, 'external_accounts', params.externalAccountId),\n    });\n  }\n\n  public async deleteUserBackupCodes(userId: string) {\n    this.requireId(userId);\n    return this.request<UserID>({\n      method: 'DELETE',\n      path: joinPaths(basePath, userId, 'backup_code'),\n    });\n  }\n\n  public async deleteUserTOTP(userId: string) {\n    this.requireId(userId);\n    return this.request<UserID>({\n      method: 'DELETE',\n      path: joinPaths(basePath, userId, 'totp'),\n    });\n  }\n}\n", "import type { ClerkPaginationRequest } from '@clerk/types';\n\nimport type { PaginatedResourceResponse } from '../resources/Deserializer';\nimport type { WaitlistEntryStatus } from '../resources/Enums';\nimport type { WaitlistEntry } from '../resources/WaitlistEntry';\nimport { AbstractAPI } from './AbstractApi';\nimport type { WithSign } from './util-types';\n\nconst basePath = '/waitlist_entries';\n\ntype WaitlistEntryListParams = ClerkPaginationRequest<{\n  /**\n   * Filter waitlist entries by `email_address` or `id`\n   */\n  query?: string;\n  status?: WaitlistEntryStatus;\n  orderBy?: WithSign<'created_at' | 'invited_at' | 'email_address'>;\n}>;\n\ntype WaitlistEntryCreateParams = {\n  emailAddress: string;\n  notify?: boolean;\n};\n\nexport class WaitlistEntryAPI extends AbstractAPI {\n  public async list(params: WaitlistEntryListParams = {}) {\n    return this.request<PaginatedResourceResponse<WaitlistEntry>>({\n      method: 'GET',\n      path: basePath,\n      queryParams: params,\n    });\n  }\n\n  public async create(params: WaitlistEntryCreateParams) {\n    return this.request<WaitlistEntry>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { WebhooksSvixJSON } from '../resources/JSON';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/webhooks';\n\nexport class WebhookAPI extends Abstract<PERSON>I {\n  public async createSvixApp() {\n    return this.request<WebhooksSvixJSON>({\n      method: 'POST',\n      path: joinPaths(basePath, 'svix'),\n    });\n  }\n\n  public async generateSvixAuthURL() {\n    return this.request<WebhooksSvixJSON>({\n      method: 'POST',\n      path: joinPaths(basePath, 'svix_url'),\n    });\n  }\n\n  public async deleteSvixApp() {\n    return this.request<void>({\n      method: 'DELETE',\n      path: joinPaths(basePath, 'svix'),\n    });\n  }\n}\n", "import { ClerkAPIResponseError, parseError } from '@clerk/shared/error';\nimport type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON> } from '@clerk/types';\nimport snakecaseKeys from 'snakecase-keys';\n\nimport { API_URL, API_VERSION, constants, SUPPORTED_BAPI_VERSION, USER_AGENT } from '../constants';\nimport { runtime } from '../runtime';\nimport { assertValidSecretKey } from '../util/optionsAssertions';\nimport { joinPaths } from '../util/path';\nimport { deserialize } from './resources/Deserializer';\n\nexport type ClerkBackendApiRequestOptions = {\n  method: 'GET' | 'POST' | 'PATCH' | 'DELETE' | 'PUT';\n  queryParams?: Record<string, unknown>;\n  headerParams?: Record<string, string>;\n  bodyParams?: Record<string, unknown> | Array<Record<string, unknown>>;\n  formData?: FormData;\n} & (\n  | {\n      url: string;\n      path?: string;\n    }\n  | {\n      url?: string;\n      path: string;\n    }\n);\n\nexport type ClerkBackendApiResponse<T> =\n  | {\n      data: T;\n      errors: null;\n      totalCount?: number;\n    }\n  | {\n      data: null;\n      errors: ClerkAPIError[];\n      totalCount?: never;\n      clerkTraceId?: string;\n      status?: number;\n      statusText?: string;\n      retryAfter?: number;\n    };\n\nexport type RequestFunction = ReturnType<typeof buildRequest>;\n\ntype BuildRequestOptions = {\n  /* Secret Key */\n  secretKey?: string;\n  /* Backend API URL */\n  apiUrl?: string;\n  /* Backend API version */\n  apiVersion?: string;\n  /* Library/SDK name */\n  userAgent?: string;\n  /**\n   * Allow requests without specifying a secret key. In most cases this should be set to `false`.\n   * @default true\n   */\n  requireSecretKey?: boolean;\n};\n\nexport function buildRequest(options: BuildRequestOptions) {\n  const requestFn = async <T>(requestOptions: ClerkBackendApiRequestOptions): Promise<ClerkBackendApiResponse<T>> => {\n    const {\n      secretKey,\n      requireSecretKey = true,\n      apiUrl = API_URL,\n      apiVersion = API_VERSION,\n      userAgent = USER_AGENT,\n    } = options;\n    const { path, method, queryParams, headerParams, bodyParams, formData } = requestOptions;\n\n    if (requireSecretKey) {\n      assertValidSecretKey(secretKey);\n    }\n\n    const url = joinPaths(apiUrl, apiVersion, path);\n\n    // Build final URL with search parameters\n    const finalUrl = new URL(url);\n\n    if (queryParams) {\n      // Snakecase query parameters\n      const snakecasedQueryParams = snakecaseKeys({ ...queryParams });\n\n      // Support array values for queryParams such as { foo: [42, 43] }\n      for (const [key, val] of Object.entries(snakecasedQueryParams)) {\n        if (val) {\n          [val].flat().forEach(v => finalUrl.searchParams.append(key, v as string));\n        }\n      }\n    }\n\n    // Build headers\n    const headers: Record<string, any> = {\n      'Clerk-API-Version': SUPPORTED_BAPI_VERSION,\n      'User-Agent': userAgent,\n      ...headerParams,\n    };\n\n    if (secretKey) {\n      headers.Authorization = `Bearer ${secretKey}`;\n    }\n\n    let res: Response | undefined;\n    try {\n      if (formData) {\n        res = await runtime.fetch(finalUrl.href, {\n          method,\n          headers,\n          body: formData,\n        });\n      } else {\n        // Enforce application/json for all non form-data requests\n        headers['Content-Type'] = 'application/json';\n\n        const buildBody = () => {\n          const hasBody = method !== 'GET' && bodyParams && Object.keys(bodyParams).length > 0;\n          if (!hasBody) {\n            return null;\n          }\n\n          const formatKeys = (object: Parameters<typeof snakecaseKeys>[0]) => snakecaseKeys(object, { deep: false });\n\n          return {\n            body: JSON.stringify(Array.isArray(bodyParams) ? bodyParams.map(formatKeys) : formatKeys(bodyParams)),\n          };\n        };\n\n        res = await runtime.fetch(finalUrl.href, {\n          method,\n          headers,\n          ...buildBody(),\n        });\n      }\n\n      // TODO: Parse JSON or Text response based on a response header\n      const isJSONResponse =\n        res?.headers && res.headers?.get(constants.Headers.ContentType) === constants.ContentTypes.Json;\n      const responseBody = await (isJSONResponse ? res.json() : res.text());\n\n      if (!res.ok) {\n        return {\n          data: null,\n          errors: parseErrors(responseBody),\n          status: res?.status,\n          statusText: res?.statusText,\n          clerkTraceId: getTraceId(responseBody, res?.headers),\n          retryAfter: getRetryAfter(res?.headers),\n        };\n      }\n\n      return {\n        ...deserialize<T>(responseBody),\n        errors: null,\n      };\n    } catch (err) {\n      if (err instanceof Error) {\n        return {\n          data: null,\n          errors: [\n            {\n              code: 'unexpected_error',\n              message: err.message || 'Unexpected error',\n            },\n          ],\n          clerkTraceId: getTraceId(err, res?.headers),\n        };\n      }\n\n      return {\n        data: null,\n        errors: parseErrors(err),\n        status: res?.status,\n        statusText: res?.statusText,\n        clerkTraceId: getTraceId(err, res?.headers),\n        retryAfter: getRetryAfter(res?.headers),\n      };\n    }\n  };\n\n  return withLegacyRequestReturn(requestFn);\n}\n\n// Returns either clerk_trace_id if present in response json, otherwise defaults to CF-Ray header\n// If the request failed before receiving a response, returns undefined\nfunction getTraceId(data: unknown, headers?: Headers): string {\n  if (data && typeof data === 'object' && 'clerk_trace_id' in data && typeof data.clerk_trace_id === 'string') {\n    return data.clerk_trace_id;\n  }\n\n  const cfRay = headers?.get('cf-ray');\n  return cfRay || '';\n}\n\nfunction getRetryAfter(headers?: Headers): number | undefined {\n  const retryAfter = headers?.get('Retry-After');\n  if (!retryAfter) return;\n\n  const value = parseInt(retryAfter, 10);\n  if (isNaN(value)) return;\n\n  return value;\n}\n\nfunction parseErrors(data: unknown): ClerkAPIError[] {\n  if (!!data && typeof data === 'object' && 'errors' in data) {\n    const errors = data.errors as ClerkAPIErrorJSON[];\n    return errors.length > 0 ? errors.map(parseError) : [];\n  }\n  return [];\n}\n\ntype LegacyRequestFunction = <T>(requestOptions: ClerkBackendApiRequestOptions) => Promise<T>;\n\n// TODO(dimkl): Will be probably be dropped in next major version\nfunction withLegacyRequestReturn(cb: any): LegacyRequestFunction {\n  return async (...args) => {\n    const { data, errors, totalCount, status, statusText, clerkTraceId, retryAfter } = await cb(...args);\n    if (errors) {\n      // instead of passing `data: errors`, we have set the `error.errors` because\n      // the errors returned from callback is already parsed and passing them as `data`\n      // will not be able to assign them to the instance\n      const error = new ClerkAPIResponseError(statusText || '', {\n        data: [],\n        status,\n        clerkTraceId,\n        retryAfter,\n      });\n      error.errors = errors;\n      throw error;\n    }\n\n    if (typeof totalCount !== 'undefined') {\n      return { data, totalCount };\n    }\n\n    return data;\n  };\n}\n", "import type { AccountlessApplicationJSON } from './JSON';\n\nexport class AccountlessApplication {\n  constructor(\n    readonly publishableKey: string,\n    readonly secretKey: string,\n    readonly claimUrl: string,\n    readonly apiKeysUrl: string,\n  ) {}\n\n  static fromJSON(data: AccountlessApplicationJSON): AccountlessApplication {\n    return new AccountlessApplication(data.publishable_key, data.secret_key, data.claim_url, data.api_keys_url);\n  }\n}\n", "import type { ActorTokenStatus } from './Enums';\nimport type { ActorTokenJSON } from './JSON';\n\nexport class ActorToken {\n  constructor(\n    readonly id: string,\n    readonly status: ActorTokenStatus,\n    readonly userId: string,\n    readonly actor: Record<string, unknown> | null,\n    readonly token: string | null | undefined,\n    readonly url: string | null | undefined,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n  ) {}\n\n  static fromJSON(data: ActorTokenJSON): ActorToken {\n    return new ActorToken(\n      data.id,\n      data.status,\n      data.user_id,\n      data.actor,\n      data.token,\n      data.url,\n      data.created_at,\n      data.updated_at,\n    );\n  }\n}\n", "import type { AllowlistIdentifierType } from './Enums';\nimport type { AllowlistIdentifierJSON } from './JSON';\n\n/**\n * The Backend `AllowlistIdentifier` object represents an identifier that has been added to the allowlist of your application. The Backend `AllowlistIdentifier` object is used in the [Backend API](https://clerk.com/docs/reference/backend-api/tag/Allow-list-Block-list#operation/ListAllowlistIdentifiers) and is not directly accessible from the Frontend API.\n */\nexport class AllowlistIdentifier {\n  constructor(\n    /**\n     * A unique ID for the allowlist identifier.\n     */\n    readonly id: string,\n    /**\n     * The [identifier](https://clerk.com/docs/authentication/configuration/sign-up-sign-in-options#identifiers) that was added to the allowlist.\n     */\n    readonly identifier: string,\n    /**\n     * The type of the allowlist identifier.\n     */\n    readonly identifierType: AllowlistIdentifierType,\n    /**\n     * The date when the allowlist identifier was first created.\n     */\n    readonly createdAt: number,\n    /**\n     * The date when the allowlist identifier was last updated.\n     */\n    readonly updatedAt: number,\n    /**\n     * The ID of the instance that this allowlist identifier belongs to.\n     */\n    readonly instanceId?: string,\n    /**\n     * The ID of the invitation sent to the identifier.\n     */\n    readonly invitationId?: string,\n  ) {}\n\n  static fromJSON(data: AllowlistIdentifierJSON): AllowlistIdentifier {\n    return new AllowlistIdentifier(\n      data.id,\n      data.identifier,\n      data.identifier_type,\n      data.created_at,\n      data.updated_at,\n      data.instance_id,\n      data.invitation_id,\n    );\n  }\n}\n", "import type { <PERSON><PERSON>eyJSON } from './JSON';\n\nexport class <PERSON><PERSON>ey {\n  constructor(\n    readonly id: string,\n    readonly type: string,\n    readonly name: string,\n    readonly subject: string,\n    readonly scopes: string[],\n    readonly claims: Record<string, any> | null,\n    readonly revoked: boolean,\n    readonly revocationReason: string | null,\n    readonly expired: boolean,\n    readonly expiration: number | null,\n    readonly createdBy: string | null,\n    readonly description: string | null,\n    readonly lastUsedAt: number | null,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n  ) {}\n\n  static fromJSON(data: APIKeyJSON) {\n    return new APIKey(\n      data.id,\n      data.type,\n      data.name,\n      data.subject,\n      data.scopes,\n      data.claims,\n      data.revoked,\n      data.revocation_reason,\n      data.expired,\n      data.expiration,\n      data.created_by,\n      data.description,\n      data.last_used_at,\n      data.created_at,\n      data.updated_at,\n    );\n  }\n}\n", "import type { BlocklistIdentifierType } from './Enums';\nimport type { BlocklistIdentifierJSON } from './JSON';\n\nexport class BlocklistIdentifier {\n  constructor(\n    readonly id: string,\n    readonly identifier: string,\n    readonly identifierType: BlocklistIdentifierType,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n    readonly instanceId?: string,\n  ) {}\n\n  static fromJSON(data: BlocklistIdentifierJSON): BlocklistIdentifier {\n    return new BlocklistIdentifier(\n      data.id,\n      data.identifier,\n      data.identifier_type,\n      data.created_at,\n      data.updated_at,\n      data.instance_id,\n    );\n  }\n}\n", "import type { SessionActivityJSO<PERSON>, SessionJSON } from './JSON';\n\n/**\n * The Backend `SessionActivity` object models the activity of a user session, capturing details such as the device type, browser information, and geographical location.\n */\nexport class SessionActivity {\n  constructor(\n    /**\n     * The unique identifier for the session activity record.\n     */\n    readonly id: string,\n    /**\n     * Will be set to `true` if the session activity came from a mobile device. Set to `false` otherwise.\n     */\n    readonly isMobile: boolean,\n    /**\n     * The IP address from which this session activity originated.\n     */\n    readonly ipAddress?: string,\n    /**\n     * The city from which this session activity occurred. Resolved by IP address geo-location.\n     */\n    readonly city?: string,\n    /**\n     * The country from which this session activity occurred. Resolved by IP address geo-location.\n     */\n    readonly country?: string,\n    /**\n     * The version of the browser from which this session activity occurred.\n     */\n    readonly browserVersion?: string,\n    /**\n     * The name of the browser from which this session activity occurred.\n     */\n    readonly browserName?: string,\n    /**\n     * The type of the device which was used in this session activity.\n     */\n    readonly deviceType?: string,\n  ) {}\n\n  static fromJSON(data: SessionActivityJSON): SessionActivity {\n    return new SessionActivity(\n      data.id,\n      data.is_mobile,\n      data.ip_address,\n      data.city,\n      data.country,\n      data.browser_version,\n      data.browser_name,\n      data.device_type,\n    );\n  }\n}\n\n/**\n * The Backend `Session` object is similar to the [`Session`](https://clerk.com/docs/references/javascript/session) object as it is an abstraction over an HTTP session and models the period of information exchange between a user and the server. However, the Backend `Session` object is different as it is used in the [Backend API](https://clerk.com/docs/reference/backend-api/tag/Sessions#operation/GetSessionList) and is not directly accessible from the Frontend API.\n */\nexport class Session {\n  constructor(\n    /**\n     * The unique identifier for the `Session`.\n     */\n    readonly id: string,\n    /**\n     * The ID of the client associated with the `Session`.\n     */\n    readonly clientId: string,\n    /**\n     * The ID of the user associated with the `Session`.\n     */\n    readonly userId: string,\n    /**\n     * The current state of the `Session`.\n     */\n    readonly status: string,\n    /**\n     * The time the session was last active on the [`Client`](https://clerk.com/docs/references/backend/types/backend-client).\n     */\n    readonly lastActiveAt: number,\n    /**\n     * The date when the `Session` will expire.\n     */\n    readonly expireAt: number,\n    /**\n     * The date when the `Session` will be abandoned.\n     */\n    readonly abandonAt: number,\n    /**\n     * The date when the `Session` was first created.\n     */\n    readonly createdAt: number,\n    /**\n     * The date when the `Session` was last updated.\n     */\n    readonly updatedAt: number,\n    /**\n     * The ID of the last active organization.\n     */\n    readonly lastActiveOrganizationId?: string,\n    /**\n     * An object that provides additional information about this session, focused around user activity data.\n     */\n    readonly latestActivity?: SessionActivity,\n    /**\n     * The JWT actor for the session. Holds identifier for the user that is impersonating the current user. Read more about [impersonation](https://clerk.com/docs/users/user-impersonation).\n     */\n    readonly actor: Record<string, unknown> | null = null,\n  ) {}\n\n  static fromJSON(data: SessionJSON): Session {\n    return new Session(\n      data.id,\n      data.client_id,\n      data.user_id,\n      data.status,\n      data.last_active_at,\n      data.expire_at,\n      data.abandon_at,\n      data.created_at,\n      data.updated_at,\n      data.last_active_organization_id,\n      data.latest_activity && SessionActivity.fromJSON(data.latest_activity),\n      data.actor,\n    );\n  }\n}\n", "import type { <PERSON><PERSON><PERSON>SON } from './JSON';\nimport { Session } from './Session';\n\n/**\n * The Backend `Client` object is similar to the [`Client`](https://clerk.com/docs/references/javascript/client) object as it holds information about the authenticated sessions in the current device. However, the Backend `Client` object is different from the `Client` object in that it is used in the [Backend API](https://clerk.com/docs/reference/backend-api/tag/Clients#operation/GetClient) and is not directly accessible from the Frontend API.\n */\nexport class Client {\n  constructor(\n    /**\n     * The unique identifier for the `Client`.\n     */\n    readonly id: string,\n    /**\n     * An array of [Session](https://clerk.com/docs/references/backend/types/backend-session){{ target: '_blank' }} IDs associated with the `Client`.\n     */\n    readonly sessionIds: string[],\n    /**\n     * An array of [Session](https://clerk.com/docs/references/backend/types/backend-session){{ target: '_blank' }} objects associated with the `Client`.\n     */\n    readonly sessions: Session[],\n    /**\n     * The ID of the [`SignIn`](https://clerk.com/docs/references/javascript/sign-in){{ target: '_blank' }}.\n     */\n    readonly signInId: string | null,\n    /**\n     * The ID of the [`SignUp`](https://clerk.com/docs/references/javascript/sign-up){{ target: '_blank' }}.\n     */\n    readonly signUpId: string | null,\n    /**\n     * The ID of the last active [Session](https://clerk.com/docs/references/backend/types/backend-session).\n     */\n    readonly lastActiveSessionId: string | null,\n    /**\n     * The date when the `Client` was first created.\n     */\n    readonly createdAt: number,\n    /**\n     * The date when the `Client` was last updated.\n     */\n    readonly updatedAt: number,\n  ) {}\n\n  static fromJSON(data: ClientJSON): Client {\n    return new Client(\n      data.id,\n      data.session_ids,\n      data.sessions.map(x => Session.fromJSON(x)),\n      data.sign_in_id,\n      data.sign_up_id,\n      data.last_active_session_id,\n      data.created_at,\n      data.updated_at,\n    );\n  }\n}\n", "import type { CnameTargetJSON } from './JSON';\n\nexport class CnameTarget {\n  constructor(\n    readonly host: string,\n    readonly value: string,\n    readonly required: boolean,\n  ) {}\n\n  static fromJSON(data: CnameTargetJSON): CnameTarget {\n    return new CnameTarget(data.host, data.value, data.required);\n  }\n}\n", "import type { CookiesJSON } from './JSON';\n\nexport class Cookies {\n  constructor(readonly cookies: string[]) {}\n\n  static fromJSON(data: CookiesJSON): Cookies {\n    return new Cookies(data.cookies);\n  }\n}\n", "import type { DeletedObjectJSON } from './JSON';\n\nexport class DeletedObject {\n  constructor(\n    readonly object: string,\n    readonly id: string | null,\n    readonly slug: string | null,\n    readonly deleted: boolean,\n  ) {}\n\n  static fromJSON(data: DeletedObjectJSON) {\n    return new DeletedObject(data.object, data.id || null, data.slug || null, data.deleted);\n  }\n}\n", "import { CnameTarget } from './CnameTarget';\nimport type { DomainJSON } from './JSON';\n\nexport class Domain {\n  constructor(\n    readonly id: string,\n    readonly name: string,\n    readonly isSatellite: boolean,\n    readonly frontendApiUrl: string,\n    readonly developmentOrigin: string,\n    readonly cnameTargets: CnameTarget[],\n    readonly accountsPortalUrl?: string | null,\n    readonly proxyUrl?: string | null,\n  ) {}\n\n  static fromJSON(data: DomainJSON): Domain {\n    return new Domain(\n      data.id,\n      data.name,\n      data.is_satellite,\n      data.frontend_api_url,\n      data.development_origin,\n      data.cname_targets && data.cname_targets.map(x => CnameTarget.fromJSON(x)),\n      data.accounts_portal_url,\n      data.proxy_url,\n    );\n  }\n}\n", "import type { EmailJSON } from './JSON';\n\nexport class Email {\n  constructor(\n    readonly id: string,\n    readonly fromEmailName: string,\n    readonly emailAddressId: string | null,\n    readonly toEmailAddress?: string,\n    readonly subject?: string,\n    readonly body?: string,\n    readonly bodyPlain?: string | null,\n    readonly status?: string,\n    readonly slug?: string | null,\n    readonly data?: Record<string, any> | null,\n    readonly deliveredByClerk?: boolean,\n  ) {}\n\n  static fromJSON(data: EmailJSON): Email {\n    return new Email(\n      data.id,\n      data.from_email_name,\n      data.email_address_id,\n      data.to_email_address,\n      data.subject,\n      data.body,\n      data.body_plain,\n      data.status,\n      data.slug,\n      data.data,\n      data.delivered_by_clerk,\n    );\n  }\n}\n", "import type { IdentificationLinkJSON } from './JSON';\n\n/**\n * Contains information about any identifications that might be linked to the email address.\n */\nexport class IdentificationLink {\n  constructor(\n    /**\n     * The unique identifier for the identification link.\n     */\n    readonly id: string,\n    /**\n     * The type of the identification link, e.g., `\"email_address\"`, `\"phone_number\"`, etc.\n     */\n    readonly type: string,\n  ) {}\n\n  static fromJSON(data: IdentificationLinkJSON): IdentificationLink {\n    return new IdentificationLink(data.id, data.type);\n  }\n}\n", "import type { VerificationStatus } from '@clerk/types';\n\nimport type { OrganizationDomainVerificationJSON, VerificationJSON } from './JSON';\n\n/**\n * The Backend `Verification` object describes the state of the verification process of a sign-in or sign-up attempt.\n */\nexport class Verification {\n  constructor(\n    /**\n     * The state of the verification.\n     *\n     * <ul>\n     *  <li>`unverified`: The verification has not been verified yet.</li>\n     *  <li>`verified`: The verification has been verified.</li>\n     *  <li>`transferable`: The verification is transferable to between sign-in and sign-up flows.</li>\n     *  <li>`failed`: The verification has failed.</li>\n     *  <li>`expired`: The verification has expired.</li>\n     * </ul>\n     */\n    readonly status: VerificationStatus,\n    /**\n     * The strategy pertaining to the parent sign-up or sign-in attempt.\n     */\n    readonly strategy: string,\n    /**\n     * The redirect URL for an external verification.\n     */\n    readonly externalVerificationRedirectURL: URL | null = null,\n    /**\n     * The number of attempts related to the verification.\n     */\n    readonly attempts: number | null = null,\n    /**\n     * The time the verification will expire at.\n     */\n    readonly expireAt: number | null = null,\n    /**\n     * The [nonce](https://en.wikipedia.org/wiki/Cryptographic_nonce) pertaining to the verification.\n     */\n    readonly nonce: string | null = null,\n    /**\n     * The message that will be presented to the user's Web3 wallet for signing during authentication. This follows the [Sign-In with Ethereum (SIWE) protocol format](https://docs.login.xyz/general-information/siwe-overview/eip-4361#example-message-to-be-signed), which typically includes details like the requesting service, wallet address, terms acceptance, nonce, timestamp, and any additional resources.\n     */\n    readonly message: string | null = null,\n  ) {}\n\n  static fromJSON(data: VerificationJSON): Verification {\n    return new Verification(\n      data.status,\n      data.strategy,\n      data.external_verification_redirect_url ? new URL(data.external_verification_redirect_url) : null,\n      data.attempts,\n      data.expire_at,\n      data.nonce,\n    );\n  }\n}\n\nexport class OrganizationDomainVerification {\n  constructor(\n    readonly status: string,\n    readonly strategy: string,\n    readonly attempts: number | null = null,\n    readonly expireAt: number | null = null,\n  ) {}\n\n  static fromJSON(data: OrganizationDomainVerificationJSON): OrganizationDomainVerification {\n    return new OrganizationDomainVerification(data.status, data.strategy, data.attempts, data.expires_at);\n  }\n}\n", "import { IdentificationLink } from './IdentificationLink';\nimport type { EmailAddressJSON } from './JSON';\nimport { Verification } from './Verification';\n\n/**\n * The Backend `EmailAddress` object is a model around an email address. Email addresses are one of the [identifiers](https://clerk.com/docs/authentication/configuration/sign-up-sign-in-options#identifiers) used to provide identification for users.\n *\n * Email addresses must be **verified** to ensure that they are assigned to their rightful owners. The `EmailAddress` object holds all necessary state around the verification process.\n *\n * For implementation examples for adding and verifying email addresses, see the [email link custom flow](https://clerk.com/docs/custom-flows/email-links) and [email code custom flow](https://clerk.com/docs/custom-flows/add-email) guides.\n */\nexport class EmailAddress {\n  constructor(\n    /**\n     * The unique identifier for the email address.\n     */\n    readonly id: string,\n    /**\n     * The value of the email address.\n     */\n    readonly emailAddress: string,\n    /**\n     * An object holding information on the verification of the email address.\n     */\n    readonly verification: Verification | null,\n    /**\n     * An array of objects containing information about any identifications that might be linked to the email address.\n     */\n    readonly linkedTo: IdentificationLink[],\n  ) {}\n\n  static fromJSON(data: EmailAddressJSON): EmailAddress {\n    return new EmailAddress(\n      data.id,\n      data.email_address,\n      data.verification && Verification.fromJSON(data.verification),\n      data.linked_to.map(link => IdentificationLink.fromJSON(link)),\n    );\n  }\n}\n", "import type { ExternalAccountJSON } from './JSON';\nimport { Verification } from './Verification';\n\n/**\n * The Backend `ExternalAccount` object is a model around an identification obtained by an external provider (e.g. a social provider such as Google).\n *\n * External account must be verified, so that you can make sure they can be assigned to their rightful owners. The `ExternalAccount` object holds all necessary state around the verification process.\n */\nexport class ExternalAccount {\n  constructor(\n    /**\n     * The unique identifier for this external account.\n     */\n    readonly id: string,\n    /**\n     * The provider name (e.g., `google`).\n     */\n    readonly provider: string,\n    /**\n     * The identification with which this external account is associated.\n     */\n    readonly identificationId: string,\n    /**\n     * The unique ID of the user in the provider.\n     */\n    readonly externalId: string,\n    /**\n     * The scopes that the user has granted access to.\n     */\n    readonly approvedScopes: string,\n    /**\n     * The user's email address.\n     */\n    readonly emailAddress: string,\n    /**\n     * The user's first name.\n     */\n    readonly firstName: string,\n    /**\n     * The user's last name.\n     */\n    readonly lastName: string,\n    /**\n     * The user's image URL.\n     */\n    readonly imageUrl: string,\n    /**\n     * The user's username.\n     */\n    readonly username: string | null,\n    /**\n     * The phone number related to this specific external account.\n     */\n    readonly phoneNumber: string | null,\n    /**\n     * Metadata that can be read from the Frontend API and Backend API and can be set only from the Backend API.\n     */\n    readonly publicMetadata: Record<string, unknown> | null = {},\n    /**\n     * A descriptive label to differentiate multiple external accounts of the same user for the same provider.\n     */\n    readonly label: string | null,\n    /**\n     * An object holding information on the verification of this external account.\n     */\n    readonly verification: Verification | null,\n  ) {}\n\n  static fromJSON(data: ExternalAccountJSON): ExternalAccount {\n    return new ExternalAccount(\n      data.id,\n      data.provider,\n      data.identification_id,\n      data.provider_user_id,\n      data.approved_scopes,\n      data.email_address,\n      data.first_name,\n      data.last_name,\n      data.image_url || '',\n      data.username,\n      data.phone_number,\n      data.public_metadata,\n      data.label,\n      data.verification && Verification.fromJSON(data.verification),\n    );\n  }\n}\n", "import type { IdPOAuthAccessTokenJSON } from './JSON';\n\nexport class IdPOAuthAccessToken {\n  constructor(\n    readonly id: string,\n    readonly clientId: string,\n    readonly type: string,\n    readonly subject: string,\n    readonly scopes: string[],\n    readonly revoked: boolean,\n    readonly revocationReason: string | null,\n    readonly expired: boolean,\n    readonly expiration: number | null,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n  ) {}\n\n  static fromJSON(data: IdPOAuthAccessTokenJSON) {\n    return new IdPOAuthAccessToken(\n      data.id,\n      data.client_id,\n      data.type,\n      data.subject,\n      data.scopes,\n      data.revoked,\n      data.revocation_reason,\n      data.expired,\n      data.expiration,\n      data.created_at,\n      data.updated_at,\n    );\n  }\n}\n", "import type { InstanceJSON } from './JSON';\n\nexport class Instance {\n  constructor(\n    readonly id: string,\n    readonly environmentType: string,\n    readonly allowedOrigins: Array<string> | null,\n  ) {}\n\n  static fromJSON(data: InstanceJSON): Instance {\n    return new Instance(data.id, data.environment_type, data.allowed_origins);\n  }\n}\n", "import type { InstanceRestrictionsJSON } from './JSON';\n\nexport class InstanceRestrictions {\n  constructor(\n    readonly allowlist: boolean,\n    readonly blocklist: boolean,\n    readonly blockEmailSubaddresses: boolean,\n    readonly blockDisposableEmailDomains: boolean,\n    readonly ignoreDotsForGmailAddresses: boolean,\n  ) {}\n\n  static fromJSON(data: InstanceRestrictionsJSON): InstanceRestrictions {\n    return new InstanceRestrictions(\n      data.allowlist,\n      data.blocklist,\n      data.block_email_subaddresses,\n      data.block_disposable_email_domains,\n      data.ignore_dots_for_gmail_addresses,\n    );\n  }\n}\n", "import type { InstanceSettingsJSON } from './JSON';\n\nexport class InstanceSettings {\n  constructor(\n    readonly id?: string | undefined,\n    readonly restrictedToAllowlist?: boolean | undefined,\n    readonly fromEmailAddress?: string | undefined,\n    readonly progressiveSignUp?: boolean | undefined,\n    readonly enhancedEmailDeliverability?: boolean | undefined,\n  ) {}\n\n  static fromJSON(data: InstanceSettingsJSON): InstanceSettings {\n    return new InstanceSettings(\n      data.id,\n      data.restricted_to_allowlist,\n      data.from_email_address,\n      data.progressive_sign_up,\n      data.enhanced_email_deliverability,\n    );\n  }\n}\n", "import type { InvitationStatus } from './Enums';\nimport type { InvitationJSON } from './JSON';\n\n/**\n * The Backend `Invitation` object represents an invitation to join your application.\n */\nexport class Invitation {\n  private _raw: InvitationJSON | null = null;\n\n  public get raw(): InvitationJSON | null {\n    return this._raw;\n  }\n\n  constructor(\n    /**\n     * The unique identifier for the `Invitation`.\n     */\n    readonly id: string,\n    /**\n     * The email address that the invitation was sent to.\n     */\n    readonly emailAddress: string,\n    /**\n     * [Metadata](https://clerk.com/docs/references/javascript/types/metadata#user-public-metadata){{ target: '_blank' }} that can be read from the Frontend API and [Backend API](https://clerk.com/docs/reference/backend-api){{ target: '_blank' }} and can be set only from the Backend API. Once the user accepts the invitation and signs up, these metadata will end up in the user's public metadata.\n     */\n    readonly publicMetadata: Record<string, unknown> | null,\n    /**\n     * The date when the `Invitation` was first created.\n     */\n    readonly createdAt: number,\n    /**\n     * The date when the `Invitation` was last updated.\n     */\n    readonly updatedAt: number,\n    /**\n     * The status of the `Invitation`.\n     */\n    readonly status: InvitationStatus,\n    /**\n     * The URL that the user can use to accept the invitation.\n     */\n    readonly url?: string,\n    /**\n     * Whether the `Invitation` has been revoked.\n     */\n    readonly revoked?: boolean,\n  ) {}\n\n  static fromJSON(data: InvitationJSON): Invitation {\n    const res = new Invitation(\n      data.id,\n      data.email_address,\n      data.public_metadata,\n      data.created_at,\n      data.updated_at,\n      data.status,\n      data.url,\n      data.revoked,\n    );\n    res._raw = data;\n    return res;\n  }\n}\n", "import type { SignUpStatus, VerificationStatus } from '@clerk/types';\n\nimport type {\n  ActorTokenStatus,\n  AllowlistIdentifierType,\n  BlocklistIdentifierType,\n  DomainsEnrollmentModes,\n  InvitationStatus,\n  OrganizationDomainVerificationStatus,\n  OrganizationDomainVerificationStrategy,\n  OrganizationEnrollmentMode,\n  OrganizationInvitationStatus,\n  OrganizationMembershipRole,\n  SignInStatus,\n  SignUpVerificationNextAction,\n  WaitlistEntryStatus,\n} from './Enums';\n\nexport const ObjectType = {\n  AccountlessApplication: 'accountless_application',\n  ActorToken: 'actor_token',\n  AllowlistIdentifier: 'allowlist_identifier',\n  ApiKey: 'api_key',\n  BlocklistIdentifier: 'blocklist_identifier',\n  Client: 'client',\n  Cookies: 'cookies',\n  Domain: 'domain',\n  Email: 'email',\n  EmailAddress: 'email_address',\n  ExternalAccount: 'external_account',\n  FacebookAccount: 'facebook_account',\n  GoogleAccount: 'google_account',\n  Instance: 'instance',\n  InstanceRestrictions: 'instance_restrictions',\n  InstanceSettings: 'instance_settings',\n  Invitation: 'invitation',\n  MachineToken: 'machine_to_machine_token',\n  JwtTemplate: 'jwt_template',\n  OauthAccessToken: 'oauth_access_token',\n  IdpOAuthAccessToken: 'clerk_idp_oauth_access_token',\n  OAuthApplication: 'oauth_application',\n  Organization: 'organization',\n  OrganizationDomain: 'organization_domain',\n  OrganizationInvitation: 'organization_invitation',\n  OrganizationMembership: 'organization_membership',\n  OrganizationSettings: 'organization_settings',\n  PhoneNumber: 'phone_number',\n  ProxyCheck: 'proxy_check',\n  RedirectUrl: 'redirect_url',\n  SamlAccount: 'saml_account',\n  SamlConnection: 'saml_connection',\n  Session: 'session',\n  SignInAttempt: 'sign_in_attempt',\n  SignInToken: 'sign_in_token',\n  SignUpAttempt: 'sign_up_attempt',\n  SmsMessage: 'sms_message',\n  User: 'user',\n  WaitlistEntry: 'waitlist_entry',\n  Web3Wallet: 'web3_wallet',\n  Token: 'token',\n  TotalCount: 'total_count',\n  TestingToken: 'testing_token',\n  Role: 'role',\n  Permission: 'permission',\n} as const;\n\nexport type ObjectType = (typeof ObjectType)[keyof typeof ObjectType];\n\nexport interface ClerkResourceJSON {\n  /**\n   * The type of the resource.\n   */\n  object: ObjectType;\n  /**\n   * The unique identifier for the resource.\n   */\n  id: string;\n}\n\nexport interface CookiesJSON {\n  object: typeof ObjectType.Cookies;\n  cookies: string[];\n}\n\nexport interface TokenJSON {\n  object: typeof ObjectType.Token;\n  jwt: string;\n}\n\nexport interface AccountlessApplicationJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.AccountlessApplication;\n  publishable_key: string;\n  secret_key: string;\n  claim_url: string;\n  api_keys_url: string;\n}\n\nexport interface ActorTokenJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.ActorToken;\n  id: string;\n  status: ActorTokenStatus;\n  user_id: string;\n  actor: Record<string, unknown> | null;\n  token?: string | null;\n  url?: string | null;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface AllowlistIdentifierJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.AllowlistIdentifier;\n  identifier: string;\n  identifier_type: AllowlistIdentifierType;\n  instance_id?: string;\n  invitation_id?: string;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface BlocklistIdentifierJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.BlocklistIdentifier;\n  identifier: string;\n  identifier_type: BlocklistIdentifierType;\n  instance_id?: string;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface ClientJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.Client;\n  session_ids: string[];\n  sessions: SessionJSON[];\n  sign_in_id: string | null;\n  sign_up_id: string | null;\n  last_active_session_id: string | null;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface CnameTargetJSON {\n  host: string;\n  value: string;\n  /**\n   * Denotes whether this CNAME target is required to be set in order for the domain to be considered deployed.\n   */\n  required: boolean;\n}\n\nexport interface DomainJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.Domain;\n  id: string;\n  name: string;\n  is_satellite: boolean;\n  frontend_api_url: string;\n  /**\n   * null for satellite domains\n   */\n  accounts_portal_url?: string | null;\n  proxy_url?: string;\n  development_origin: string;\n  cname_targets: CnameTargetJSON[];\n}\n\nexport interface EmailJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.Email;\n  slug?: string | null;\n  from_email_name: string;\n  to_email_address?: string;\n  email_address_id: string | null;\n  user_id?: string | null;\n  subject?: string;\n  body?: string;\n  body_plain?: string | null;\n  status?: string;\n  data?: Record<string, any> | null;\n  delivered_by_clerk: boolean;\n}\n\nexport interface EmailAddressJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.EmailAddress;\n  email_address: string;\n  verification: VerificationJSON | null;\n  linked_to: IdentificationLinkJSON[];\n}\n\nexport interface ExternalAccountJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.ExternalAccount;\n  provider: string;\n  identification_id: string;\n  provider_user_id: string;\n  approved_scopes: string;\n  email_address: string;\n  first_name: string;\n  last_name: string;\n  image_url?: string;\n  username: string | null;\n  phone_number: string | null;\n  public_metadata?: Record<string, unknown> | null;\n  label: string | null;\n  verification: VerificationJSON | null;\n}\n\nexport interface JwksJSON {\n  keys?: JwksKeyJSON[];\n}\n\nexport interface JwksKeyJSON {\n  use: string;\n  kty: string;\n  kid: string;\n  alg: string;\n  n: string;\n  e: string;\n}\n\nexport interface JwtTemplateJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.JwtTemplate;\n  id: string;\n  name: string;\n  claims: object;\n  lifetime: number;\n  allowed_clock_skew: number;\n  custom_signing_key: boolean;\n  signing_algorithm: string;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface SamlAccountJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.SamlAccount;\n  provider: string;\n  provider_user_id: string | null;\n  active: boolean;\n  email_address: string;\n  first_name: string;\n  last_name: string;\n  verification: VerificationJSON | null;\n  saml_connection: SamlAccountConnectionJSON | null;\n}\n\nexport interface IdentificationLinkJSON extends ClerkResourceJSON {\n  type: string;\n}\n\nexport interface OrganizationSettingsJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.OrganizationSettings;\n  enabled: boolean;\n  max_allowed_memberships: number;\n  max_allowed_roles: number;\n  max_allowed_permissions: number;\n  creator_role: string;\n  admin_delete_enabled: boolean;\n  domains_enabled: boolean;\n  domains_enrollment_modes: Array<DomainsEnrollmentModes>;\n  domains_default_role: string;\n}\n\nexport interface InstanceJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.Instance;\n  id: string;\n  environment_type: string;\n  allowed_origins: Array<string> | null;\n}\n\nexport interface InstanceRestrictionsJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.InstanceRestrictions;\n  allowlist: boolean;\n  blocklist: boolean;\n  block_email_subaddresses: boolean;\n  block_disposable_email_domains: boolean;\n  ignore_dots_for_gmail_addresses: boolean;\n}\n\nexport interface InstanceSettingsJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.InstanceSettings;\n  id: string;\n  restricted_to_allowlist: boolean;\n  from_email_address: string;\n  progressive_sign_up: boolean;\n  enhanced_email_deliverability: boolean;\n}\n\nexport interface InvitationJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.Invitation;\n  email_address: string;\n  public_metadata: Record<string, unknown> | null;\n  revoked?: boolean;\n  status: InvitationStatus;\n  url?: string;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface OauthAccessTokenJSON {\n  external_account_id: string;\n  object: typeof ObjectType.OauthAccessToken;\n  token: string;\n  provider: string;\n  public_metadata: Record<string, unknown>;\n  label: string | null;\n  // Only set in OAuth 2.0 tokens\n  scopes?: string[];\n  // Only set in OAuth 1.0 tokens\n  token_secret?: string;\n  expires_at?: number;\n}\n\nexport interface OAuthApplicationJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.OAuthApplication;\n  id: string;\n  instance_id: string;\n  name: string;\n  client_id: string;\n  public: boolean;\n  scopes: string;\n  redirect_uris: Array<string>;\n  authorize_url: string;\n  token_fetch_url: string;\n  user_info_url: string;\n  discovery_url: string;\n  token_introspection_url: string;\n  created_at: number;\n  updated_at: number;\n  client_secret?: string;\n}\n\nexport interface OrganizationJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.Organization;\n  name: string;\n  slug: string;\n  image_url?: string;\n  has_image: boolean;\n  members_count?: number;\n  pending_invitations_count?: number;\n  max_allowed_memberships: number;\n  admin_delete_enabled: boolean;\n  public_metadata: OrganizationPublicMetadata | null;\n  private_metadata?: OrganizationPrivateMetadata;\n  created_by?: string;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface OrganizationDomainJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.OrganizationDomain;\n  id: string;\n  name: string;\n  organization_id: string;\n  enrollment_mode: OrganizationEnrollmentMode;\n  verification: OrganizationDomainVerificationJSON | null;\n  affiliation_email_address: string | null;\n  created_at: number;\n  updated_at: number;\n  total_pending_invitations: number;\n  total_pending_suggestions: number;\n}\n\nexport interface OrganizationDomainVerificationJSON {\n  status: OrganizationDomainVerificationStatus;\n  strategy: OrganizationDomainVerificationStrategy;\n  attempts: number;\n  expires_at: number;\n}\n\nexport interface OrganizationInvitationJSON extends ClerkResourceJSON {\n  email_address: string;\n  role: OrganizationMembershipRole;\n  role_name: string;\n  organization_id: string;\n  public_organization_data?: PublicOrganizationDataJSON | null;\n  status?: OrganizationInvitationStatus;\n  public_metadata: OrganizationInvitationPublicMetadata;\n  private_metadata: OrganizationInvitationPrivateMetadata;\n  url: string | null;\n  created_at: number;\n  updated_at: number;\n  expires_at: number;\n}\n\n/**\n * @interface\n */\nexport interface PublicOrganizationDataJSON extends ClerkResourceJSON {\n  /**\n   * The name of the organization.\n   */\n  name: string;\n  /**\n   * The slug of the organization.\n   */\n  slug: string;\n  /**\n   * Holds the default organization profile image. Compatible with Clerk's [Image Optimization](https://clerk.com/docs/guides/image-optimization).\n   */\n  image_url?: string;\n  /**\n   * Whether the organization has a profile image.\n   */\n  has_image: boolean;\n}\n\nexport interface OrganizationMembershipJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.OrganizationMembership;\n  public_metadata: OrganizationMembershipPublicMetadata;\n  private_metadata?: OrganizationMembershipPrivateMetadata;\n  role: OrganizationMembershipRole;\n  permissions: string[];\n  created_at: number;\n  updated_at: number;\n  organization: OrganizationJSON;\n  public_user_data: OrganizationMembershipPublicUserDataJSON;\n}\n\nexport interface OrganizationMembershipPublicUserDataJSON {\n  identifier: string;\n  first_name: string | null;\n  last_name: string | null;\n  image_url: string;\n  has_image: boolean;\n  user_id: string;\n}\n\nexport interface PhoneNumberJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.PhoneNumber;\n  phone_number: string;\n  reserved_for_second_factor: boolean;\n  default_second_factor: boolean;\n  reserved: boolean;\n  verification: VerificationJSON | null;\n  linked_to: IdentificationLinkJSON[];\n  backup_codes: string[];\n}\n\nexport type ProxyCheckJSON = {\n  object: typeof ObjectType.ProxyCheck;\n  id: string;\n  domain_id: string;\n  last_run_at: number | null;\n  proxy_url: string;\n  successful: boolean;\n  created_at: number;\n  updated_at: number;\n};\n\nexport interface RedirectUrlJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.RedirectUrl;\n  url: string;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface SessionActivityJSON extends ClerkResourceJSON {\n  id: string;\n  device_type?: string;\n  is_mobile: boolean;\n  browser_name?: string;\n  browser_version?: string;\n  ip_address?: string;\n  city?: string;\n  country?: string;\n}\n\nexport interface SessionJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.Session;\n  client_id: string;\n  user_id: string;\n  status: string;\n  last_active_organization_id?: string;\n  actor: Record<string, unknown> | null;\n  latest_activity?: SessionActivityJSON;\n  last_active_at: number;\n  expire_at: number;\n  abandon_at: number;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface SignInJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.SignInToken;\n  status: SignInStatus;\n  identifier: string;\n  created_session_id: string | null;\n}\n\nexport interface SignInTokenJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.SignInToken;\n  user_id: string;\n  token: string;\n  status: 'pending' | 'accepted' | 'revoked';\n  url: string;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface SignUpJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.SignUpAttempt;\n  id: string;\n  status: SignUpStatus;\n  required_fields: string[];\n  optional_fields: string[];\n  missing_fields: string[];\n  unverified_fields: string[];\n  verifications: SignUpVerificationsJSON;\n  username: string | null;\n  email_address: string | null;\n  phone_number: string | null;\n  web3_wallet: string | null;\n  password_enabled: boolean;\n  first_name: string | null;\n  last_name: string | null;\n  public_metadata?: Record<string, unknown> | null;\n  unsafe_metadata?: Record<string, unknown> | null;\n  custom_action: boolean;\n  external_id: string | null;\n  created_session_id: string | null;\n  created_user_id: string | null;\n  abandon_at: number | null;\n  legal_accepted_at: number | null;\n\n  /**\n   * @deprecated Please use `verifications.external_account` instead\n   */\n  external_account: object | null;\n}\n\nexport interface SignUpVerificationsJSON {\n  email_address: SignUpVerificationJSON;\n  phone_number: SignUpVerificationJSON;\n  web3_wallet: SignUpVerificationJSON;\n  external_account: VerificationJSON;\n}\n\nexport interface SignUpVerificationJSON {\n  next_action: SignUpVerificationNextAction;\n  supported_strategies: string[];\n}\n\nexport interface SMSMessageJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.SmsMessage;\n  from_phone_number: string;\n  to_phone_number: string;\n  phone_number_id: string | null;\n  user_id?: string;\n  message: string;\n  status: string;\n  slug?: string | null;\n  data?: Record<string, any> | null;\n  delivered_by_clerk: boolean;\n}\n\nexport interface UserJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.User;\n  username: string | null;\n  first_name: string | null;\n  last_name: string | null;\n  image_url: string;\n  has_image: boolean;\n  primary_email_address_id: string | null;\n  primary_phone_number_id: string | null;\n  primary_web3_wallet_id: string | null;\n  password_enabled: boolean;\n  two_factor_enabled: boolean;\n  totp_enabled: boolean;\n  backup_code_enabled: boolean;\n  email_addresses: EmailAddressJSON[];\n  phone_numbers: PhoneNumberJSON[];\n  web3_wallets: Web3WalletJSON[];\n  organization_memberships: OrganizationMembershipJSON[] | null;\n  external_accounts: ExternalAccountJSON[];\n  saml_accounts: SamlAccountJSON[];\n  password_last_updated_at: number | null;\n  public_metadata: UserPublicMetadata;\n  private_metadata: UserPrivateMetadata;\n  unsafe_metadata: UserUnsafeMetadata;\n  external_id: string | null;\n  last_sign_in_at: number | null;\n  banned: boolean;\n  locked: boolean;\n  lockout_expires_in_seconds: number | null;\n  verification_attempts_remaining: number | null;\n  created_at: number;\n  updated_at: number;\n  last_active_at: number | null;\n  create_organization_enabled: boolean;\n  create_organizations_limit: number | null;\n  delete_self_enabled: boolean;\n  legal_accepted_at: number | null;\n}\n\nexport interface VerificationJSON extends ClerkResourceJSON {\n  status: VerificationStatus;\n  strategy: string;\n  attempts: number | null;\n  expire_at: number | null;\n  verified_at_client?: string;\n  external_verification_redirect_url?: string | null;\n  nonce?: string | null;\n  message?: string | null;\n}\n\nexport interface WaitlistEntryJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.WaitlistEntry;\n  id: string;\n  status: WaitlistEntryStatus;\n  email_address: string;\n  invitation: InvitationJSON | null;\n  is_locked: boolean;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface Web3WalletJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.Web3Wallet;\n  web3_wallet: string;\n  verification: VerificationJSON | null;\n}\n\nexport interface DeletedObjectJSON {\n  object: string;\n  id?: string;\n  slug?: string;\n  deleted: boolean;\n}\n\nexport interface PaginatedResponseJSON {\n  data: object[];\n  total_count?: number;\n}\n\nexport interface SamlConnectionJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.SamlConnection;\n  name: string;\n  domain: string;\n  organization_id: string | null;\n  idp_entity_id: string;\n  idp_sso_url: string;\n  idp_certificate: string;\n  idp_metadata_url: string;\n  idp_metadata: string;\n  acs_url: string;\n  sp_entity_id: string;\n  sp_metadata_url: string;\n  active: boolean;\n  provider: string;\n  user_count: number;\n  sync_user_attributes: boolean;\n  allow_subdomains: boolean;\n  allow_idp_initiated: boolean;\n  created_at: number;\n  updated_at: number;\n  attribute_mapping: AttributeMappingJSON;\n}\n\nexport interface AttributeMappingJSON {\n  user_id: string;\n  email_address: string;\n  first_name: string;\n  last_name: string;\n}\n\nexport interface TestingTokenJSON {\n  object: typeof ObjectType.TestingToken;\n  token: string;\n  expires_at: number;\n}\n\nexport interface RoleJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.Role;\n  key: string;\n  name: string;\n  description: string;\n  permissions: PermissionJSON[];\n  is_creator_eligible: boolean;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface PermissionJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.Permission;\n  key: string;\n  name: string;\n  description: string;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface SamlAccountConnectionJSON extends ClerkResourceJSON {\n  id: string;\n  name: string;\n  domain: string;\n  active: boolean;\n  provider: string;\n  sync_user_attributes: boolean;\n  allow_subdomains: boolean;\n  allow_idp_initiated: boolean;\n  disable_additional_identifications: boolean;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface MachineTokenJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.MachineToken;\n  name: string;\n  subject: string;\n  scopes: string[];\n  claims: Record<string, any> | null;\n  revoked: boolean;\n  revocation_reason: string | null;\n  expired: boolean;\n  expiration: number | null;\n  created_by: string | null;\n  creation_reason: string | null;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface APIKeyJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.ApiKey;\n  type: string;\n  name: string;\n  subject: string;\n  scopes: string[];\n  claims: Record<string, any> | null;\n  revoked: boolean;\n  revocation_reason: string | null;\n  expired: boolean;\n  expiration: number | null;\n  created_by: string | null;\n  description: string | null;\n  last_used_at: number | null;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface IdPOAuthAccessTokenJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.IdpOAuthAccessToken;\n  client_id: string;\n  type: string;\n  subject: string;\n  scopes: string[];\n  revoked: boolean;\n  revocation_reason: string | null;\n  expired: boolean;\n  expiration: number | null;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface WebhooksSvixJSON {\n  svix_url: string;\n}\n", "import type { MachineTokenJSON } from './JSON';\n\nexport class MachineToken {\n  constructor(\n    readonly id: string,\n    readonly name: string,\n    readonly subject: string,\n    readonly scopes: string[],\n    readonly claims: Record<string, any> | null,\n    readonly revoked: boolean,\n    readonly revocationReason: string | null,\n    readonly expired: boolean,\n    readonly expiration: number | null,\n    readonly createdBy: string | null,\n    readonly creationReason: string | null,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n  ) {}\n\n  static fromJSON(data: MachineTokenJSON) {\n    return new MachineToken(\n      data.id,\n      data.name,\n      data.subject,\n      data.scopes,\n      data.claims,\n      data.revoked,\n      data.revocation_reason,\n      data.expired,\n      data.expiration,\n      data.created_by,\n      data.creation_reason,\n      data.created_at,\n      data.updated_at,\n    );\n  }\n}\n", "import type { JwtTemplateJSON } from './JSON';\n\nexport class JwtTemplate {\n  constructor(\n    readonly id: string,\n    readonly name: string,\n    readonly claims: object,\n    readonly lifetime: number,\n    readonly allowedClockSkew: number,\n    readonly customSigningKey: boolean,\n    readonly signingAlgorithm: string,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n  ) {}\n\n  static fromJSON(data: JwtTemplateJSON): JwtTemplate {\n    return new JwtTemplate(\n      data.id,\n      data.name,\n      data.claims,\n      data.lifetime,\n      data.allowed_clock_skew,\n      data.custom_signing_key,\n      data.signing_algorithm,\n      data.created_at,\n      data.updated_at,\n    );\n  }\n}\n", "import type { OauthAccessTokenJSON } from './JSON';\n\nexport class OauthAccessToken {\n  constructor(\n    readonly externalAccountId: string,\n    readonly provider: string,\n    readonly token: string,\n    readonly publicMetadata: Record<string, unknown> = {},\n    readonly label: string,\n    readonly scopes?: string[],\n    readonly tokenSecret?: string,\n    readonly expiresAt?: number,\n  ) {}\n\n  static fromJSON(data: OauthAccessTokenJSON) {\n    return new OauthAccessToken(\n      data.external_account_id,\n      data.provider,\n      data.token,\n      data.public_metadata,\n      data.label || '',\n      data.scopes,\n      data.token_secret,\n      data.expires_at,\n    );\n  }\n}\n", "import type { OAuthApplicationJSON } from './JSON';\n\nexport class OAuthApplication {\n  constructor(\n    readonly id: string,\n    readonly instanceId: string,\n    readonly name: string,\n    readonly clientId: string,\n    readonly isPublic: boolean, // NOTE: `public` is reserved\n    readonly scopes: string,\n    readonly redirectUris: Array<string>,\n    readonly authorizeUrl: string,\n    readonly tokenFetchUrl: string,\n    readonly userInfoUrl: string,\n    readonly discoveryUrl: string,\n    readonly tokenIntrospectionUrl: string,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n    readonly clientSecret?: string,\n  ) {}\n\n  static fromJSON(data: OAuthApplicationJSON) {\n    return new OAuthApplication(\n      data.id,\n      data.instance_id,\n      data.name,\n      data.client_id,\n      data.public,\n      data.scopes,\n      data.redirect_uris,\n      data.authorize_url,\n      data.token_fetch_url,\n      data.user_info_url,\n      data.discovery_url,\n      data.token_introspection_url,\n      data.created_at,\n      data.updated_at,\n      data.client_secret,\n    );\n  }\n}\n", "import type { OrganizationJSON } from './JSON';\n\n/**\n * The Backend `Organization` object is similar to the [`Organization`](https://clerk.com/docs/references/javascript/organization) object as it holds information about an organization, as well as methods for managing it. However, the Backend `Organization` object is different in that it is used in the [Backend API](https://clerk.com/docs/reference/backend-api/tag/Organizations#operation/ListOrganizations){{ target: '_blank' }} and is not directly accessible from the Frontend API.\n */\nexport class Organization {\n  private _raw: OrganizationJSON | null = null;\n\n  public get raw(): OrganizationJSON | null {\n    return this._raw;\n  }\n\n  constructor(\n    /**\n     * The unique identifier for the organization.\n     */\n    readonly id: string,\n    /**\n     * The name of the organization.\n     */\n    readonly name: string,\n    /**\n     * The URL-friendly identifier of the user's active organization. If supplied, it must be unique for the instance.\n     */\n    readonly slug: string,\n    /**\n     * Holds the organization's logo. Compatible with Clerk's [Image Optimization](https://clerk.com/docs/guides/image-optimization).\n     */\n    readonly imageUrl: string,\n    /**\n     * Whether the organization has an image.\n     */\n    readonly hasImage: boolean,\n    /**\n     * The date when the organization was first created.\n     */\n    readonly createdAt: number,\n    /**\n     * The date when the organization was last updated.\n     */\n    readonly updatedAt: number,\n    /**\n     * Metadata that can be read from the Frontend API and [Backend API](https://clerk.com/docs/reference/backend-api){{ target: '_blank' }} and can be set only from the Backend API.\n     */\n    readonly publicMetadata: OrganizationPublicMetadata | null = {},\n    /**\n     * Metadata that can be read and set only from the [Backend API](https://clerk.com/docs/reference/backend-api){{ target: '_blank' }}.\n     */\n    readonly privateMetadata: OrganizationPrivateMetadata = {},\n    /**\n     * The maximum number of memberships allowed in the organization.\n     */\n    readonly maxAllowedMemberships: number,\n    /**\n     * Whether the organization allows admins to delete users.\n     */\n    readonly adminDeleteEnabled: boolean,\n    /**\n     * The number of members in the organization.\n     */\n    readonly membersCount?: number,\n    /**\n     * The ID of the user who created the organization.\n     */\n    readonly createdBy?: string,\n  ) {}\n\n  static fromJSON(data: OrganizationJSON): Organization {\n    const res = new Organization(\n      data.id,\n      data.name,\n      data.slug,\n      data.image_url || '',\n      data.has_image,\n      data.created_at,\n      data.updated_at,\n      data.public_metadata,\n      data.private_metadata,\n      data.max_allowed_memberships,\n      data.admin_delete_enabled,\n      data.members_count,\n      data.created_by,\n    );\n    res._raw = data;\n    return res;\n  }\n}\n", "import type { OrganizationInvitationStatus, OrganizationMembershipRole } from './Enums';\nimport type { OrganizationInvitationJSON, PublicOrganizationDataJSON } from './JSON';\n\n/**\n * The Backend `OrganizationInvitation` object is similar to the [`OrganizationInvitation`](https://clerk.com/docs/references/javascript/types/organization-invitation) object as it's the model around an organization invitation. However, the Backend `OrganizationInvitation` object is different in that it's used in the [Backend API](https://clerk.com/docs/reference/backend-api/tag/Organization-Invitations#operation/CreateOrganizationInvitation){{ target: '_blank' }} and is not directly accessible from the Frontend API.\n */\nexport class OrganizationInvitation {\n  private _raw: OrganizationInvitationJSON | null = null;\n\n  public get raw(): OrganizationInvitationJSON | null {\n    return this._raw;\n  }\n\n  constructor(\n    /**\n     * The unique identifier for the `OrganizationInvitation`.\n     */\n    readonly id: string,\n    /**\n     * The email address of the user who is invited to the [`Organization`](https://clerk.com/docs/references/backend/types/backend-organization).\n     */\n    readonly emailAddress: string,\n    /**\n     * The role of the invited user.\n     */\n    readonly role: OrganizationMembershipRole,\n    /**\n     * The name of the role of the invited user.\n     */\n    readonly roleName: string,\n    /**\n     * The ID of the [`Organization`](https://clerk.com/docs/references/backend/types/backend-organization) that the user is invited to.\n     */\n    readonly organizationId: string,\n    /**\n     * The date when the invitation was first created.\n     */\n    readonly createdAt: number,\n    /**\n     * The date when the invitation was last updated.\n     */\n    readonly updatedAt: number,\n    /**\n     * The date when the invitation expires.\n     */\n    readonly expiresAt: number,\n    /**\n     * The URL that the user can use to accept the invitation.\n     */\n    readonly url: string | null,\n    /**\n     * The status of the invitation.\n     */\n    readonly status?: OrganizationInvitationStatus,\n    /**\n     * Metadata that can be read from the Frontend API and [Backend API](https://clerk.com/docs/reference/backend-api){{ target: '_blank' }} and can be set only from the Backend API.\n     */\n    readonly publicMetadata: OrganizationInvitationPublicMetadata = {},\n    /**\n     * Metadata that can be read and set only from the [Backend API](https://clerk.com/docs/reference/backend-api){{ target: '_blank' }}.\n     */\n    readonly privateMetadata: OrganizationInvitationPrivateMetadata = {},\n    /**\n     * Public data about the organization that the user is invited to.\n     */\n    readonly publicOrganizationData?: PublicOrganizationDataJSON | null,\n  ) {}\n\n  static fromJSON(data: OrganizationInvitationJSON) {\n    const res = new OrganizationInvitation(\n      data.id,\n      data.email_address,\n      data.role,\n      data.role_name,\n      data.organization_id,\n      data.created_at,\n      data.updated_at,\n      data.expires_at,\n      data.url,\n      data.status,\n      data.public_metadata,\n      data.private_metadata,\n      data.public_organization_data,\n    );\n    res._raw = data;\n    return res;\n  }\n}\n", "import { Organization } from '../resources';\nimport type { OrganizationMembershipRole } from './Enums';\nimport type { OrganizationMembershipJSON, OrganizationMembershipPublicUserDataJSON } from './JSON';\n\n/**\n * The Backend `OrganizationMembership` object is similar to the [`OrganizationMembership`](https://clerk.com/docs/references/javascript/types/organization-membership) object as it's the model around an organization membership entity and describes the relationship between users and organizations. However, the Backend `OrganizationMembership` object is different in that it's used in the [Backend API](https://clerk.com/docs/reference/backend-api/tag/Organization-Memberships#operation/CreateOrganizationMembership){{ target: '_blank' }} and is not directly accessible from the Frontend API.\n */\nexport class OrganizationMembership {\n  private _raw: OrganizationMembershipJSON | null = null;\n\n  public get raw(): OrganizationMembershipJSON | null {\n    return this._raw;\n  }\n\n  constructor(\n    /**\n     * The unique identifier for the membership.\n     */\n    readonly id: string,\n    /**\n     * The role of the user.\n     */\n    readonly role: OrganizationMembershipRole,\n    /**\n     * The permissions granted to the user in the organization.\n     */\n    readonly permissions: string[],\n    /**\n     * Metadata that can be read from the Frontend API and [Backend API](https://clerk.com/docs/reference/backend-api){{ target: '_blank' }} and can be set only from the Backend API.\n     */\n    readonly publicMetadata: OrganizationMembershipPublicMetadata = {},\n    /**\n     * Metadata that can be read and set only from the [Backend API](https://clerk.com/docs/reference/backend-api){{ target: '_blank' }}.\n     */\n    readonly privateMetadata: OrganizationMembershipPrivateMetadata = {},\n    /**\n     * The date when the membership was first created.\n     */\n    readonly createdAt: number,\n    /**\n     * The date when the membership was last updated.\n     */\n    readonly updatedAt: number,\n    /**\n     * The organization that the user is a member of.\n     */\n    readonly organization: Organization,\n    /**\n     * Public information about the user that this membership belongs to.\n     */\n    readonly publicUserData?: OrganizationMembershipPublicUserData | null,\n  ) {}\n\n  static fromJSON(data: OrganizationMembershipJSON) {\n    const res = new OrganizationMembership(\n      data.id,\n      data.role,\n      data.permissions,\n      data.public_metadata,\n      data.private_metadata,\n      data.created_at,\n      data.updated_at,\n      Organization.fromJSON(data.organization),\n      OrganizationMembershipPublicUserData.fromJSON(data.public_user_data),\n    );\n    res._raw = data;\n    return res;\n  }\n}\n\n/**\n * @class\n */\nexport class OrganizationMembershipPublicUserData {\n  constructor(\n    /**\n     * The [identifier](https://clerk.com/docs/authentication/configuration/sign-up-sign-in-options#identifiers) of the user.\n     */\n    readonly identifier: string,\n    /**\n     * The first name of the user.\n     */\n    readonly firstName: string | null,\n    /**\n     * The last name of the user.\n     */\n    readonly lastName: string | null,\n    /**\n     * Holds the default avatar or user's uploaded profile image. Compatible with Clerk's [Image Optimization](https://clerk.com/docs/guides/image-optimization).\n     */\n    readonly imageUrl: string,\n    /**\n     * Whether the user has a profile picture.\n     */\n    readonly hasImage: boolean,\n    /**\n     * The ID of the user that this public data belongs to.\n     */\n    readonly userId: string,\n  ) {}\n\n  static fromJSON(data: OrganizationMembershipPublicUserDataJSON) {\n    return new OrganizationMembershipPublicUserData(\n      data.identifier,\n      data.first_name,\n      data.last_name,\n      data.image_url,\n      data.has_image,\n      data.user_id,\n    );\n  }\n}\n", "import type { DomainsEnrollmentModes } from './Enums';\nimport type { OrganizationSettingsJSON } from './JSON';\n\nexport class OrganizationSettings {\n  constructor(\n    readonly enabled: boolean,\n    readonly maxAllowedMemberships: number,\n    readonly maxAllowedRoles: number,\n    readonly maxAllowedPermissions: number,\n    readonly creatorRole: string,\n    readonly adminDeleteEnabled: boolean,\n    readonly domainsEnabled: boolean,\n    readonly domainsEnrollmentModes: Array<DomainsEnrollmentModes>,\n    readonly domainsDefaultRole: string,\n  ) {}\n\n  static fromJSON(data: OrganizationSettingsJSON): OrganizationSettings {\n    return new OrganizationSettings(\n      data.enabled,\n      data.max_allowed_memberships,\n      data.max_allowed_roles,\n      data.max_allowed_permissions,\n      data.creator_role,\n      data.admin_delete_enabled,\n      data.domains_enabled,\n      data.domains_enrollment_modes,\n      data.domains_default_role,\n    );\n  }\n}\n", "import { IdentificationLink } from './IdentificationLink';\nimport type { PhoneNumberJSON } from './JSON';\nimport { Verification } from './Verification';\n\n/**\n * The Backend `PhoneNumber` object describes a phone number. Phone numbers can be used as a proof of identification for users, or simply as a means of contacting users.\n *\n * Phone numbers must be **verified** to ensure that they can be assigned to their rightful owners. The `PhoneNumber` object holds all the necessary state around the verification process.\n *\n * Finally, phone numbers can be used as part of [multi-factor authentication](https://clerk.com/docs/authentication/configuration/sign-up-sign-in-options#multi-factor-authentication). During sign in, users can opt in to an extra verification step where they will receive an SMS message with a one-time code. This code must be entered to complete the sign in process.\n */\nexport class PhoneNumber {\n  constructor(\n    /**\n     * The unique identifier for this phone number.\n     */\n    readonly id: string,\n    /**\n     * The value of this phone number, in [E.164 format](https://en.wikipedia.org/wiki/E.164).\n     */\n    readonly phoneNumber: string,\n    /**\n     * Set to `true` if this phone number is reserved for multi-factor authentication (2FA). Set to `false` otherwise.\n     */\n    readonly reservedForSecondFactor: boolean,\n    /**\n     * Set to `true` if this phone number is the default second factor. Set to `false` otherwise. A user must have exactly one default second factor, if multi-factor authentication (2FA) is enabled.\n     */\n    readonly defaultSecondFactor: boolean,\n    /**\n     * An object holding information on the verification of this phone number.\n     */\n    readonly verification: Verification | null,\n    /**\n     * An object containing information about any other identification that might be linked to this phone number.\n     */\n    readonly linkedTo: IdentificationLink[],\n  ) {}\n\n  static fromJSON(data: PhoneNumberJSON): PhoneNumber {\n    return new PhoneNumber(\n      data.id,\n      data.phone_number,\n      data.reserved_for_second_factor,\n      data.default_second_factor,\n      data.verification && Verification.fromJSON(data.verification),\n      data.linked_to.map(link => IdentificationLink.fromJSON(link)),\n    );\n  }\n}\n", "import type { Proxy<PERSON>heckJSON } from './JSON';\n\nexport class Proxy<PERSON>heck {\n  constructor(\n    readonly id: string,\n    readonly domainId: string,\n    readonly lastRunAt: number | null,\n    readonly proxyUrl: string,\n    readonly successful: boolean,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n  ) {}\n\n  static fromJSON(data: ProxyCheckJSON): ProxyCheck {\n    return new ProxyCheck(\n      data.id,\n      data.domain_id,\n      data.last_run_at,\n      data.proxy_url,\n      data.successful,\n      data.created_at,\n      data.updated_at,\n    );\n  }\n}\n", "import type { RedirectUrlJSON } from './JSON';\n\n/**\n * Redirect URLs are whitelisted URLs that facilitate secure authentication flows in native applications (e.g. React Native, Expo). In these contexts, Clerk ensures that security-critical nonces are passed only to the whitelisted URLs.\n\nThe Backend `RedirectUrl` object represents a redirect URL in your application. This object is used in the Backend API.\n */\nexport class RedirectUrl {\n  constructor(\n    /**\n     * The unique identifier for the redirect URL.\n     */\n    readonly id: string,\n    /**\n     * The full URL value prefixed with `https://` or a custom scheme.\n     * @example https://my-app.com/oauth-callback\n     * @example my-app://oauth-callback\n     */\n    readonly url: string,\n    /**\n     * The date when the redirect URL was first created.\n     */\n    readonly createdAt: number,\n    /**\n     * The date when the redirect URL was last updated.\n     */\n    readonly updatedAt: number,\n  ) {}\n\n  static fromJSON(data: RedirectUrlJSON): RedirectUrl {\n    return new RedirectUrl(data.id, data.url, data.created_at, data.updated_at);\n  }\n}\n", "import type { Attribute<PERSON>appingJSON, SamlAccountConnectionJSON, SamlConnectionJSON } from './JSON';\n\n/**\n * The Backend `SamlConnection` object holds information about a SAML connection for an organization.\n */\nexport class SamlConnection {\n  constructor(\n    /**\n     * The unique identifier for the connection.\n     */\n    readonly id: string,\n    /**\n     * The name to use as a label for the connection.\n     */\n    readonly name: string,\n    /**\n     * The domain of your organization. Sign in flows using an email with this domain will use the connection.\n     */\n    readonly domain: string,\n    /**\n     * The organization ID of the organization.\n     */\n    readonly organizationId: string | null,\n    /**\n     * The Entity ID as provided by the Identity Provider (IdP).\n     */\n    readonly idpEntityId: string | null,\n    /**\n     * The Single-Sign On URL as provided by the Identity Provider (IdP).\n     */\n    readonly idpSsoUrl: string | null,\n    /**\n     * The X.509 certificate as provided by the Identity Provider (IdP).\n     */\n    readonly idpCertificate: string | null,\n    /**\n     * The URL which serves the Identity Provider (IdP) metadata. If present, it takes priority over the corresponding individual properties.\n     */\n    readonly idpMetadataUrl: string | null,\n    /**\n     * The XML content of the Identity Provider (IdP) metadata file. If present, it takes priority over the corresponding individual properties.\n     */\n    readonly idpMetadata: string | null,\n    /**\n     * The Assertion Consumer Service (ACS) URL of the connection.\n     */\n    readonly acsUrl: string,\n    /**\n     * The Entity ID as provided by the Service Provider (Clerk).\n     */\n    readonly spEntityId: string,\n    /**\n     * The metadata URL as provided by the Service Provider (Clerk).\n     */\n    readonly spMetadataUrl: string,\n    /**\n     * Indicates whether the connection is active or not.\n     */\n    readonly active: boolean,\n    /**\n     * The Identity Provider (IdP) of the connection.\n     */\n    readonly provider: string,\n    /**\n     * The number of users associated with the connection.\n     */\n    readonly userCount: number,\n    /**\n     * Indicates whether the connection syncs user attributes between the Service Provider (SP) and Identity Provider (IdP) or not.\n     */\n    readonly syncUserAttributes: boolean,\n    /**\n     * Indicates whether users with an email address subdomain are allowed to use this connection in order to authenticate or not.\n     */\n    readonly allowSubdomains: boolean,\n    /**\n     * Indicates whether the connection allows Identity Provider (IdP) initiated flows or not.\n     */\n    readonly allowIdpInitiated: boolean,\n    /**\n     * The date when the connection was first created.\n     */\n    readonly createdAt: number,\n    /**\n     * The date when the SAML connection was last updated.\n     */\n    readonly updatedAt: number,\n    /**\n     * Defines the attribute name mapping between the Identity Provider (IdP) and Clerk's [`User`](https://clerk.com/docs/references/javascript/user) properties.\n     */\n    readonly attributeMapping: AttributeMapping,\n  ) {}\n  static fromJSON(data: SamlConnectionJSON): SamlConnection {\n    return new SamlConnection(\n      data.id,\n      data.name,\n      data.domain,\n      data.organization_id,\n      data.idp_entity_id,\n      data.idp_sso_url,\n      data.idp_certificate,\n      data.idp_metadata_url,\n      data.idp_metadata,\n      data.acs_url,\n      data.sp_entity_id,\n      data.sp_metadata_url,\n      data.active,\n      data.provider,\n      data.user_count,\n      data.sync_user_attributes,\n      data.allow_subdomains,\n      data.allow_idp_initiated,\n      data.created_at,\n      data.updated_at,\n      data.attribute_mapping && AttributeMapping.fromJSON(data.attribute_mapping),\n    );\n  }\n}\n\nexport class SamlAccountConnection {\n  constructor(\n    readonly id: string,\n    readonly name: string,\n    readonly domain: string,\n    readonly active: boolean,\n    readonly provider: string,\n    readonly syncUserAttributes: boolean,\n    readonly allowSubdomains: boolean,\n    readonly allowIdpInitiated: boolean,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n  ) {}\n  static fromJSON(data: SamlAccountConnectionJSON): SamlAccountConnection {\n    return new SamlAccountConnection(\n      data.id,\n      data.name,\n      data.domain,\n      data.active,\n      data.provider,\n      data.sync_user_attributes,\n      data.allow_subdomains,\n      data.allow_idp_initiated,\n      data.created_at,\n      data.updated_at,\n    );\n  }\n}\n\nclass AttributeMapping {\n  constructor(\n    /**\n     * The user ID attribute name.\n     */\n    readonly userId: string,\n    /**\n     * The email address attribute name.\n     */\n    readonly emailAddress: string,\n    /**\n     * The first name attribute name.\n     */\n    readonly firstName: string,\n    /**\n     * The last name attribute name.\n     */\n    readonly lastName: string,\n  ) {}\n\n  static fromJSON(data: AttributeMappingJSON): AttributeMapping {\n    return new AttributeMapping(data.user_id, data.email_address, data.first_name, data.last_name);\n  }\n}\n", "import type { SamlA<PERSON>untJSON } from './JSON';\nimport { SamlAccountConnection } from './SamlConnection';\nimport { Verification } from './Verification';\n\n/**\n * The Backend `SamlAccount` object describes a SAML account.\n */\nexport class SamlAccount {\n  constructor(\n    /**\n     * The unique identifier for the SAML account.\n     */\n    readonly id: string,\n    /**\n     * The provider of the SAML account.\n     */\n    readonly provider: string,\n    /**\n     * The user's ID as used in the provider.\n     */\n    readonly providerUserId: string | null,\n    /**\n     * A boolean that indicates whether the SAML account is active.\n     */\n    readonly active: boolean,\n    /**\n     * The email address of the SAML account.\n     */\n    readonly emailAddress: string,\n    /**\n     * The first name of the SAML account.\n     */\n    readonly firstName: string,\n    /**\n     * The last name of the SAML account.\n     */\n    readonly lastName: string,\n    /**\n     * The verification of the SAML account.\n     */\n    readonly verification: Verification | null,\n    /**\n     * The SAML connection of the SAML account.\n     */\n    readonly samlConnection: SamlAccountConnection | null,\n  ) {}\n\n  static fromJSON(data: SamlAccountJSON): SamlAccount {\n    return new SamlAccount(\n      data.id,\n      data.provider,\n      data.provider_user_id,\n      data.active,\n      data.email_address,\n      data.first_name,\n      data.last_name,\n      data.verification && Verification.fromJSON(data.verification),\n      data.saml_connection && SamlAccountConnection.fromJSON(data.saml_connection),\n    );\n  }\n}\n", "import type { SignInTokenJSON } from './JSON';\n\nexport class SignInToken {\n  constructor(\n    readonly id: string,\n    readonly userId: string,\n    readonly token: string,\n    readonly status: string,\n    readonly url: string,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n  ) {}\n\n  static fromJSON(data: SignInTokenJSON): SignInToken {\n    return new SignInToken(data.id, data.user_id, data.token, data.status, data.url, data.created_at, data.updated_at);\n  }\n}\n", "import type { SignUpStatus } from '@clerk/types';\n\nimport type { SignUpVerificationNextAction } from './Enums';\nimport type { SignUpJSON, SignUpVerificationJSON, SignUpVerificationsJSON } from './JSON';\n\nexport class SignUpAttemptVerification {\n  constructor(\n    readonly nextAction: SignUpVerificationNextAction,\n    readonly supportedStrategies: string[],\n  ) {}\n\n  static fromJSON(data: SignUpVerificationJSON): SignUpAttemptVerification {\n    return new SignUpAttemptVerification(data.next_action, data.supported_strategies);\n  }\n}\n\nexport class SignUpAttemptVerifications {\n  constructor(\n    readonly emailAddress: SignUpAttemptVerification | null,\n    readonly phoneNumber: SignUpAttemptVerification | null,\n    readonly web3Wallet: SignUpAttemptVerification | null,\n    readonly externalAccount: object | null,\n  ) {}\n\n  static fromJSON(data: SignUpVerificationsJSON): SignUpAttemptVerifications {\n    return new SignUpAttemptVerifications(\n      data.email_address && SignUpAttemptVerification.fromJSON(data.email_address),\n      data.phone_number && SignUpAttemptVerification.fromJSON(data.phone_number),\n      data.web3_wallet && SignUpAttemptVerification.fromJSON(data.web3_wallet),\n      data.external_account,\n    );\n  }\n}\n\nexport class SignUpAttempt {\n  constructor(\n    readonly id: string,\n    readonly status: SignUpStatus,\n    readonly requiredFields: string[],\n    readonly optionalFields: string[],\n    readonly missingFields: string[],\n    readonly unverifiedFields: string[],\n    readonly verifications: SignUpAttemptVerifications | null,\n    readonly username: string | null,\n    readonly emailAddress: string | null,\n    readonly phoneNumber: string | null,\n    readonly web3Wallet: string | null,\n    readonly passwordEnabled: boolean,\n    readonly firstName: string | null,\n    readonly lastName: string | null,\n    readonly customAction: boolean,\n    readonly externalId: string | null,\n    readonly createdSessionId: string | null,\n    readonly createdUserId: string | null,\n    readonly abandonAt: number | null,\n    readonly legalAcceptedAt: number | null,\n    readonly publicMetadata?: Record<string, unknown> | null,\n    readonly unsafeMetadata?: Record<string, unknown> | null,\n  ) {}\n\n  static fromJSON(data: SignUpJSON): SignUpAttempt {\n    return new SignUpAttempt(\n      data.id,\n      data.status,\n      data.required_fields,\n      data.optional_fields,\n      data.missing_fields,\n      data.unverified_fields,\n      data.verifications ? SignUpAttemptVerifications.fromJSON(data.verifications) : null,\n      data.username,\n      data.email_address,\n      data.phone_number,\n      data.web3_wallet,\n      data.password_enabled,\n      data.first_name,\n      data.last_name,\n      data.custom_action,\n      data.external_id,\n      data.created_session_id,\n      data.created_user_id,\n      data.abandon_at,\n      data.legal_accepted_at,\n      data.public_metadata,\n      data.unsafe_metadata,\n    );\n  }\n}\n", "import type { SMSMessageJSON } from './JSON';\n\nexport class SMSMessage {\n  constructor(\n    readonly id: string,\n    readonly fromPhoneNumber: string,\n    readonly toPhoneNumber: string,\n    readonly message: string,\n    readonly status: string,\n    readonly phoneNumberId: string | null,\n    readonly data?: Record<string, any> | null,\n  ) {}\n\n  static fromJSON(data: SMSMessageJSON): SMSMessage {\n    return new SMSMessage(\n      data.id,\n      data.from_phone_number,\n      data.to_phone_number,\n      data.message,\n      data.status,\n      data.phone_number_id,\n      data.data,\n    );\n  }\n}\n", "import type { TokenJSON } from './JSON';\n\nexport class Token {\n  constructor(readonly jwt: string) {}\n\n  static fromJSON(data: TokenJSON): Token {\n    return new Token(data.jwt);\n  }\n}\n", "import type { Web3WalletJSON } from './JSON';\nimport { Verification } from './Verification';\n\n/**\n * The Backend `Web3Wallet` object describes a Web3 wallet address. The address can be used as a proof of identification for users.\n *\n * Web3 addresses must be verified to ensure that they can be assigned to their rightful owners. The verification is completed via Web3 wallet browser extensions, such as [Metamask](https://metamask.io/), [Coinbase Wallet](https://www.coinbase.com/wallet), and [OKX Wallet](https://www.okx.com/help/section/faq-web3-wallet). The `Web3Wallet3` object holds all the necessary state around the verification process.\n */\nexport class Web3Wallet {\n  constructor(\n    /**\n     * The unique ID for the Web3 wallet.\n     */\n    readonly id: string,\n    /**\n     * The Web3 wallet address, made up of 0x + 40 hexadecimal characters.\n     */\n    readonly web3Wallet: string,\n    /**\n     * An object holding information on the verification of this Web3 wallet.\n     */\n    readonly verification: Verification | null,\n  ) {}\n\n  static fromJSON(data: Web3WalletJSON): Web3Wallet {\n    return new Web3Wallet(data.id, data.web3_wallet, data.verification && Verification.fromJSON(data.verification));\n  }\n}\n", "import { EmailAddress } from './EmailAddress';\nimport { ExternalAccount } from './ExternalAccount';\nimport type { ExternalAccountJSON, SamlAccountJSON, UserJSON } from './JSON';\nimport { PhoneNumber } from './PhoneNumber';\nimport { SamlAccount } from './SamlAccount';\nimport { Web3Wallet } from './Web3Wallet';\n\n/**\n * The Backend `User` object is similar to the `User` object as it holds information about a user of your application, such as their unique identifier, name, email addresses, phone numbers, and more. However, the Backend `User` object is different from the `User` object in that it is used in the [Backend API](https://clerk.com/docs/reference/backend-api/tag/Users#operation/GetUser){{ target: '_blank' }} and is not directly accessible from the Frontend API.\n */\nexport class User {\n  private _raw: UserJSON | null = null;\n\n  public get raw(): UserJSON | null {\n    return this._raw;\n  }\n\n  constructor(\n    /**\n     * The unique identifier for the user.\n     */\n    readonly id: string,\n    /**\n     * A boolean indicating whether the user has a password on their account.\n     */\n    readonly passwordEnabled: boolean,\n    /**\n     * A boolean indicating whether the user has enabled TOTP by generating a TOTP secret and verifying it via an authenticator app.\n     */\n    readonly totpEnabled: boolean,\n    /**\n     * A boolean indicating whether the user has enabled Backup codes.\n     */\n    readonly backupCodeEnabled: boolean,\n    /**\n     * A boolean indicating whether the user has enabled two-factor authentication.\n     */\n    readonly twoFactorEnabled: boolean,\n    /**\n     * A boolean indicating whether the user is banned or not.\n     */\n    readonly banned: boolean,\n    /**\n     * A boolean indicating whether the user is banned or not.\n     */\n    readonly locked: boolean,\n    /**\n     * The date when the user was first created.\n     */\n    readonly createdAt: number,\n    /**\n     * The date when the user was last updated.\n     */\n    readonly updatedAt: number,\n    /**\n     * The URL of the user's profile image.\n     */\n    readonly imageUrl: string,\n    /**\n     * A getter boolean to check if the user has uploaded an image or one was copied from OAuth. Returns `false` if Clerk is displaying an avatar for the user.\n     */\n    readonly hasImage: boolean,\n    /**\n     * The ID for the `EmailAddress` that the user has set as primary.\n     */\n    readonly primaryEmailAddressId: string | null,\n    /**\n     * The ID for the `PhoneNumber` that the user has set as primary.\n     */\n    readonly primaryPhoneNumberId: string | null,\n    /**\n     * The ID for the [`Web3Wallet`](https://clerk.com/docs/references/backend/types/backend-web3-wallet) that the user signed up with.\n     */\n    readonly primaryWeb3WalletId: string | null,\n    /**\n     * The date when the user last signed in. May be empty if the user has never signed in.\n     */\n    readonly lastSignInAt: number | null,\n    /**\n     * The ID of the user as used in your external systems. Must be unique across your instance.\n     */\n    readonly externalId: string | null,\n    /**\n     * The user's username.\n     */\n    readonly username: string | null,\n    /**\n     * The user's first name.\n     */\n    readonly firstName: string | null,\n    /**\n     * The user's last name.\n     */\n    readonly lastName: string | null,\n    /**\n     * Metadata that can be read from the Frontend API and [Backend API](https://clerk.com/docs/reference/backend-api){{ target: '_blank' }} and can be set only from the Backend API.\n     */\n    readonly publicMetadata: UserPublicMetadata = {},\n    /**\n     * Metadata that can be read and set only from the [Backend API](https://clerk.com/docs/reference/backend-api){{ target: '_blank' }}.\n     */\n    readonly privateMetadata: UserPrivateMetadata = {},\n    /**\n     * Metadata that can be read and set from the Frontend API. It's considered unsafe because it can be modified from the frontend.\n     */\n    readonly unsafeMetadata: UserUnsafeMetadata = {},\n    /**\n     * An array of all the `EmailAddress` objects associated with the user. Includes the primary.\n     */\n    readonly emailAddresses: EmailAddress[] = [],\n    /**\n     * An array of all the `PhoneNumber` objects associated with the user. Includes the primary.\n     */\n    readonly phoneNumbers: PhoneNumber[] = [],\n    /**\n     * An array of all the `Web3Wallet` objects associated with the user. Includes the primary.\n     */\n    readonly web3Wallets: Web3Wallet[] = [],\n    /**\n     * An array of all the `ExternalAccount` objects associated with the user via OAuth. **Note**: This includes both verified & unverified external accounts.\n     */\n    readonly externalAccounts: ExternalAccount[] = [],\n    /**\n     * An array of all the `SamlAccount` objects associated with the user via SAML.\n     */\n    readonly samlAccounts: SamlAccount[] = [],\n    /**\n     * Date when the user was last active.\n     */\n    readonly lastActiveAt: number | null,\n    /**\n     * A boolean indicating whether the organization creation is enabled for the user or not.\n     */\n    readonly createOrganizationEnabled: boolean,\n    /**\n     * An integer indicating the number of organizations that can be created by the user. If the value is `0`, then the user can create unlimited organizations. Default is `null`.\n     */\n    readonly createOrganizationsLimit: number | null = null,\n    /**\n     * A boolean indicating whether the user can delete their own account.\n     */\n    readonly deleteSelfEnabled: boolean,\n    /**\n     * The unix timestamp of when the user accepted the legal requirements. `null` if [**Require express consent to legal documents**](https://clerk.com/docs/authentication/configuration/legal-compliance) is not enabled.\n     */\n    readonly legalAcceptedAt: number | null,\n  ) {}\n\n  static fromJSON(data: UserJSON): User {\n    const res = new User(\n      data.id,\n      data.password_enabled,\n      data.totp_enabled,\n      data.backup_code_enabled,\n      data.two_factor_enabled,\n      data.banned,\n      data.locked,\n      data.created_at,\n      data.updated_at,\n      data.image_url,\n      data.has_image,\n      data.primary_email_address_id,\n      data.primary_phone_number_id,\n      data.primary_web3_wallet_id,\n      data.last_sign_in_at,\n      data.external_id,\n      data.username,\n      data.first_name,\n      data.last_name,\n      data.public_metadata,\n      data.private_metadata,\n      data.unsafe_metadata,\n      (data.email_addresses || []).map(x => EmailAddress.fromJSON(x)),\n      (data.phone_numbers || []).map(x => PhoneNumber.fromJSON(x)),\n      (data.web3_wallets || []).map(x => Web3Wallet.fromJSON(x)),\n      (data.external_accounts || []).map((x: ExternalAccountJSON) => ExternalAccount.fromJSON(x)),\n      (data.saml_accounts || []).map((x: SamlAccountJSON) => SamlAccount.fromJSON(x)),\n      data.last_active_at,\n      data.create_organization_enabled,\n      data.create_organizations_limit,\n      data.delete_self_enabled,\n      data.legal_accepted_at,\n    );\n    res._raw = data;\n    return res;\n  }\n\n  /**\n   * The primary email address of the user.\n   */\n  get primaryEmailAddress() {\n    return this.emailAddresses.find(({ id }) => id === this.primaryEmailAddressId) ?? null;\n  }\n\n  /**\n   * The primary phone number of the user.\n   */\n  get primaryPhoneNumber() {\n    return this.phoneNumbers.find(({ id }) => id === this.primaryPhoneNumberId) ?? null;\n  }\n\n  /**\n   * The primary web3 wallet of the user.\n   */\n  get primaryWeb3Wallet() {\n    return this.web3Wallets.find(({ id }) => id === this.primaryWeb3WalletId) ?? null;\n  }\n\n  /**\n   * The full name of the user.\n   */\n  get fullName() {\n    return [this.firstName, this.lastName].join(' ').trim() || null;\n  }\n}\n", "import type { WaitlistEntryStatus } from './Enums';\nimport { Invitation } from './Invitation';\nimport type { WaitlistEntryJSON } from './JSON';\n\nexport class WaitlistEntry {\n  constructor(\n    readonly id: string,\n    readonly emailAddress: string,\n    readonly status: WaitlistEntryStatus,\n    readonly invitation: Invitation | null,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n    readonly isLocked?: boolean,\n  ) {}\n\n  static fromJSON(data: WaitlistEntryJSON): WaitlistEntry {\n    return new WaitlistEntry(\n      data.id,\n      data.email_address,\n      data.status,\n      data.invitation && Invitation.fromJSON(data.invitation),\n      data.created_at,\n      data.updated_at,\n      data.is_locked,\n    );\n  }\n}\n", "import {\n  ActorToken,\n  AllowlistIdentifier,\n  APIKey,\n  BlocklistIdentifier,\n  Client,\n  Cookies,\n  DeletedObject,\n  Domain,\n  Email,\n  EmailAddress,\n  IdPOAuthAccessToken,\n  Instance,\n  InstanceRestrictions,\n  InstanceSettings,\n  Invitation,\n  JwtTemplate,\n  MachineToken,\n  OauthAccessToken,\n  OAuthApplication,\n  Organization,\n  OrganizationInvitation,\n  OrganizationMembership,\n  OrganizationSettings,\n  PhoneNumber,\n  ProxyCheck,\n  RedirectUrl,\n  SamlConnection,\n  Session,\n  SignInToken,\n  SignUpAttempt,\n  SMSMessage,\n  Token,\n  User,\n} from '.';\nimport { AccountlessApplication } from './AccountlessApplication';\nimport type { PaginatedResponseJSON } from './JSON';\nimport { ObjectType } from './JSON';\nimport { WaitlistEntry } from './WaitlistEntry';\n\ntype ResourceResponse<T> = {\n  /**\n   * An array that contains the fetched data.\n   */\n  data: T;\n};\n\n/**\n * An interface that describes the response of a method that returns a paginated list of resources.\n *\n * If the promise resolves, you will get back the [properties](#properties) listed below. `data` will be an array of the resource type you requested. You can use the `totalCount` property to determine how many total items exist remotely.\n *\n * Some methods that return this type allow pagination with the `limit` and `offset` parameters, in which case the first 10 items will be returned by default. For methods such as [`getAllowlistIdentifierList()`](https://clerk.com/docs/references/backend/allowlist/get-allowlist-identifier-list), which do not take a `limit` or `offset`, all items will be returned.\n *\n * If the promise is rejected, you will receive a `ClerkAPIResponseError` or network error.\n *\n * @interface\n */\nexport type PaginatedResourceResponse<T> = ResourceResponse<T> & {\n  /**\n   * The total count of data that exist remotely.\n   */\n  totalCount: number;\n};\n\nexport function deserialize<U = any>(payload: unknown): PaginatedResourceResponse<U> | ResourceResponse<U> {\n  let data, totalCount: number | undefined;\n\n  if (Array.isArray(payload)) {\n    const data = payload.map(item => jsonToObject(item)) as U;\n    return { data };\n  } else if (isPaginated(payload)) {\n    data = payload.data.map(item => jsonToObject(item)) as U;\n    totalCount = payload.total_count;\n\n    return { data, totalCount };\n  } else {\n    return { data: jsonToObject(payload) };\n  }\n}\n\nfunction isPaginated(payload: unknown): payload is PaginatedResponseJSON {\n  if (!payload || typeof payload !== 'object' || !('data' in payload)) {\n    return false;\n  }\n\n  return Array.isArray(payload.data) && payload.data !== undefined;\n}\n\nfunction getCount(item: PaginatedResponseJSON) {\n  return item.total_count;\n}\n\n// TODO: Revise response deserialization\nfunction jsonToObject(item: any): any {\n  // Special case: DeletedObject\n  // TODO: Improve this check\n  if (typeof item !== 'string' && 'object' in item && 'deleted' in item) {\n    return DeletedObject.fromJSON(item);\n  }\n\n  switch (item.object) {\n    case ObjectType.AccountlessApplication:\n      return AccountlessApplication.fromJSON(item);\n    case ObjectType.ActorToken:\n      return ActorToken.fromJSON(item);\n    case ObjectType.AllowlistIdentifier:\n      return AllowlistIdentifier.fromJSON(item);\n    case ObjectType.ApiKey:\n      return APIKey.fromJSON(item);\n    case ObjectType.BlocklistIdentifier:\n      return BlocklistIdentifier.fromJSON(item);\n    case ObjectType.Client:\n      return Client.fromJSON(item);\n    case ObjectType.Cookies:\n      return Cookies.fromJSON(item);\n    case ObjectType.Domain:\n      return Domain.fromJSON(item);\n    case ObjectType.EmailAddress:\n      return EmailAddress.fromJSON(item);\n    case ObjectType.Email:\n      return Email.fromJSON(item);\n    case ObjectType.IdpOAuthAccessToken:\n      return IdPOAuthAccessToken.fromJSON(item);\n    case ObjectType.Instance:\n      return Instance.fromJSON(item);\n    case ObjectType.InstanceRestrictions:\n      return InstanceRestrictions.fromJSON(item);\n    case ObjectType.InstanceSettings:\n      return InstanceSettings.fromJSON(item);\n    case ObjectType.Invitation:\n      return Invitation.fromJSON(item);\n    case ObjectType.JwtTemplate:\n      return JwtTemplate.fromJSON(item);\n    case ObjectType.MachineToken:\n      return MachineToken.fromJSON(item);\n    case ObjectType.OauthAccessToken:\n      return OauthAccessToken.fromJSON(item);\n    case ObjectType.OAuthApplication:\n      return OAuthApplication.fromJSON(item);\n    case ObjectType.Organization:\n      return Organization.fromJSON(item);\n    case ObjectType.OrganizationInvitation:\n      return OrganizationInvitation.fromJSON(item);\n    case ObjectType.OrganizationMembership:\n      return OrganizationMembership.fromJSON(item);\n    case ObjectType.OrganizationSettings:\n      return OrganizationSettings.fromJSON(item);\n    case ObjectType.PhoneNumber:\n      return PhoneNumber.fromJSON(item);\n    case ObjectType.ProxyCheck:\n      return ProxyCheck.fromJSON(item);\n    case ObjectType.RedirectUrl:\n      return RedirectUrl.fromJSON(item);\n    case ObjectType.SamlConnection:\n      return SamlConnection.fromJSON(item);\n    case ObjectType.SignInToken:\n      return SignInToken.fromJSON(item);\n    case ObjectType.SignUpAttempt:\n      return SignUpAttempt.fromJSON(item);\n    case ObjectType.Session:\n      return Session.fromJSON(item);\n    case ObjectType.SmsMessage:\n      return SMSMessage.fromJSON(item);\n    case ObjectType.Token:\n      return Token.fromJSON(item);\n    case ObjectType.TotalCount:\n      return getCount(item);\n    case ObjectType.User:\n      return User.fromJSON(item);\n    case ObjectType.WaitlistEntry:\n      return WaitlistEntry.fromJSON(item);\n    default:\n      return item;\n  }\n}\n", "import {\n  AccountlessApplicationAP<PERSON>,\n  ActorTokenAPI,\n  AllowlistIdentifierAPI,\n  APIKeysAPI,\n  BetaFeaturesAPI,\n  BlocklistIdentifierAPI,\n  ClientAPI,\n  DomainAPI,\n  EmailAddressAPI,\n  IdPOAuthAccessTokenApi,\n  InstanceAPI,\n  InvitationAPI,\n  JwksAPI,\n  JwtTemplatesApi,\n  MachineTokensApi,\n  OAuthApplicationsApi,\n  OrganizationAPI,\n  PhoneNumberAPI,\n  ProxyCheckAPI,\n  RedirectUrlAPI,\n  SamlConnectionAPI,\n  SessionAPI,\n  SignInTokenAPI,\n  SignUpAPI,\n  TestingTokenAPI,\n  UserAPI,\n  WaitlistEntryAPI,\n  WebhookAPI,\n} from './endpoints';\nimport { buildRequest } from './request';\n\nexport type CreateBackendApiOptions = Parameters<typeof buildRequest>[0];\n\nexport type ApiClient = ReturnType<typeof createBackendApiClient>;\n\nexport function createBackendApiClient(options: CreateBackendApiOptions) {\n  const request = buildRequest(options);\n\n  return {\n    __experimental_accountlessApplications: new AccountlessApplicationAPI(\n      buildRequest({ ...options, requireSecretKey: false }),\n    ),\n    actorTokens: new ActorTokenAPI(request),\n    allowlistIdentifiers: new AllowlistIdentifierAPI(request),\n    betaFeatures: new BetaFeaturesAPI(request),\n    blocklistIdentifiers: new BlocklistIdentifierAPI(request),\n    clients: new ClientAPI(request),\n    domains: new DomainAPI(request),\n    emailAddresses: new EmailAddressAPI(request),\n    instance: new InstanceAPI(request),\n    invitations: new InvitationAPI(request),\n    // Using \"/\" instead of an actual version since they're bapi-proxy endpoints.\n    // bapi-proxy connects directly to C1 without URL versioning,\n    // while API versioning is handled through the Clerk-API-Version header.\n    machineTokens: new MachineTokensApi(\n      buildRequest({\n        ...options,\n        apiVersion: '/',\n      }),\n    ),\n    idPOAuthAccessToken: new IdPOAuthAccessTokenApi(\n      buildRequest({\n        ...options,\n        apiVersion: '/',\n      }),\n    ),\n    apiKeys: new APIKeysAPI(\n      buildRequest({\n        ...options,\n        apiVersion: '/',\n      }),\n    ),\n    jwks: new JwksAPI(request),\n    jwtTemplates: new JwtTemplatesApi(request),\n    oauthApplications: new OAuthApplicationsApi(request),\n    organizations: new OrganizationAPI(request),\n    phoneNumbers: new PhoneNumberAPI(request),\n    proxyChecks: new ProxyCheckAPI(request),\n    redirectUrls: new RedirectUrlAPI(request),\n    samlConnections: new SamlConnectionAPI(request),\n    sessions: new SessionAPI(request),\n    signInTokens: new SignInTokenAPI(request),\n    signUps: new SignUpAPI(request),\n    testingTokens: new TestingTokenAPI(request),\n    users: new UserAPI(request),\n    waitlistEntries: new WaitlistEntryAPI(request),\n    webhooks: new WebhookAPI(request),\n  };\n}\n", "export const TokenType = {\n  SessionToken: 'session_token',\n  Api<PERSON><PERSON>: 'api_key',\n  MachineToken: 'machine_token',\n  OAuthToken: 'oauth_token',\n} as const;\n\n/**\n * @inline\n */\nexport type TokenType = (typeof TokenType)[keyof typeof TokenType];\n\n/**\n * @inline\n */\nexport type SessionTokenType = typeof TokenType.SessionToken;\n/**\n * @inline\n */\nexport type MachineTokenType = Exclude<TokenType, SessionTokenType>;\n", "import type { AuthenticateRequestOptions } from '../tokens/types';\nimport type { MachineTokenType } from './tokenTypes';\nimport { TokenType } from './tokenTypes';\n\nexport const M2M_TOKEN_PREFIX = 'mt_';\nexport const OAUTH_TOKEN_PREFIX = 'oat_';\nexport const API_KEY_PREFIX = 'ak_';\n\nconst MACHINE_TOKEN_PREFIXES = [M2M_TOKEN_PREFIX, OAUTH_TOKEN_PREFIX, API_KEY_PREFIX] as const;\n\n/**\n * Checks if a token is a machine token by looking at its prefix.\n *\n * @remarks\n * In the future, this will support custom prefixes that can be prepended to the base prefixes\n * (e.g. \"org_a_m2m_\", \"org_a_oauth_access_\", \"org_a_api_key_\")\n *\n * @param token - The token string to check\n * @returns true if the token starts with a recognized machine token prefix\n */\nexport function isMachineTokenByPrefix(token: string): boolean {\n  return MACHINE_TOKEN_PREFIXES.some(prefix => token.startsWith(prefix));\n}\n\n/**\n * Gets the specific type of machine token based on its prefix.\n *\n * @remarks\n * In the future, this will support custom prefixes that can be prepended to the base prefixes\n * (e.g. \"org_a_m2m_\", \"org_a_oauth_access_\", \"org_a_api_key_\")\n *\n * @param token - The token string to check\n * @returns The specific MachineTokenType\n * @throws Error if the token doesn't match any known machine token prefix\n */\nexport function getMachineTokenType(token: string): MachineTokenType {\n  if (token.startsWith(M2M_TOKEN_PREFIX)) {\n    return TokenType.MachineToken;\n  }\n\n  if (token.startsWith(OAUTH_TOKEN_PREFIX)) {\n    return TokenType.OAuthToken;\n  }\n\n  if (token.startsWith(API_KEY_PREFIX)) {\n    return TokenType.ApiKey;\n  }\n\n  throw new Error('Unknown machine token type');\n}\n\n/**\n * Check if a token type is accepted given a requested token type or list of token types.\n *\n * @param tokenType - The token type to check (can be null if the token is invalid)\n * @param acceptsToken - The requested token type or list of token types\n * @returns true if the token type is accepted\n */\nexport const isTokenTypeAccepted = (\n  tokenType: TokenType | null,\n  acceptsToken: NonNullable<AuthenticateRequestOptions['acceptsToken']>,\n): boolean => {\n  if (!tokenType) {\n    return false;\n  }\n\n  if (acceptsToken === 'any') {\n    return true;\n  }\n\n  const tokenTypes = Array.isArray(acceptsToken) ? acceptsToken : [acceptsToken];\n  return tokenTypes.includes(tokenType);\n};\n\n/**\n * Checks if a token type string is a machine token type (api_key, machine_token, or oauth_token).\n *\n * @param type - The token type string to check\n * @returns true if the type is a machine token type\n */\nexport function isMachineTokenType(type: string): type is MachineTokenType {\n  return type === TokenType.ApiKey || type === TokenType.MachineToken || type === TokenType.OAuthToken;\n}\n", "import type { JwtPayload, PendingSessionOptions } from '@clerk/types';\n\nimport { constants } from '../constants';\nimport type { TokenVerificationErrorReason } from '../errors';\nimport type { AuthenticateContext } from './authenticateContext';\nimport type {\n  AuthenticatedMachineObject,\n  SignedInAuthObject,\n  SignedOutAuthObject,\n  UnauthenticatedMachineObject,\n} from './authObjects';\nimport {\n  authenticatedMachineObject,\n  signedInAuthObject,\n  signedOutAuthObject,\n  unauthenticatedMachineObject,\n} from './authObjects';\nimport type { MachineTokenType, SessionTokenType } from './tokenTypes';\nimport { TokenType } from './tokenTypes';\nimport type { MachineAuthType } from './types';\n\nexport const AuthStatus = {\n  SignedIn: 'signed-in',\n  SignedOut: 'signed-out',\n  Handshake: 'handshake',\n} as const;\n\nexport type AuthStatus = (typeof AuthStatus)[keyof typeof AuthStatus];\n\ntype ToAuth<T extends TokenType, Authenticated extends boolean> = T extends SessionTokenType\n  ? Authenticated extends true\n    ? (opts?: PendingSessionOptions) => SignedInAuthObject\n    : () => SignedOutAuthObject\n  : Authenticated extends true\n    ? () => AuthenticatedMachineObject<Exclude<T, SessionTokenType>>\n    : () => UnauthenticatedMachineObject<Exclude<T, SessionTokenType>>;\n\nexport type AuthenticatedState<T extends TokenType = SessionTokenType> = {\n  status: typeof AuthStatus.SignedIn;\n  reason: null;\n  message: null;\n  proxyUrl?: string;\n  publishableKey: string;\n  isSatellite: boolean;\n  domain: string;\n  signInUrl: string;\n  signUpUrl: string;\n  afterSignInUrl: string;\n  afterSignUpUrl: string;\n  /**\n   * @deprecated Use `isAuthenticated` instead.\n   */\n  isSignedIn: true;\n  isAuthenticated: true;\n  headers: Headers;\n  token: string;\n  tokenType: T;\n  toAuth: ToAuth<T, true>;\n};\n\nexport type UnauthenticatedState<T extends TokenType = SessionTokenType> = {\n  status: typeof AuthStatus.SignedOut;\n  reason: AuthReason;\n  message: string;\n  proxyUrl?: string;\n  publishableKey: string;\n  isSatellite: boolean;\n  domain: string;\n  signInUrl: string;\n  signUpUrl: string;\n  afterSignInUrl: string;\n  afterSignUpUrl: string;\n  /**\n   * @deprecated Use `isAuthenticated` instead.\n   */\n  isSignedIn: false;\n  isAuthenticated: false;\n  tokenType: T;\n  headers: Headers;\n  token: null;\n  toAuth: ToAuth<T, false>;\n};\n\nexport type HandshakeState = Omit<UnauthenticatedState<SessionTokenType>, 'status' | 'toAuth' | 'tokenType'> & {\n  tokenType: SessionTokenType;\n  status: typeof AuthStatus.Handshake;\n  headers: Headers;\n  toAuth: () => null;\n};\n\n/**\n * @deprecated Use AuthenticatedState instead\n */\nexport type SignedInState = AuthenticatedState<SessionTokenType>;\n\n/**\n * @deprecated Use UnauthenticatedState instead\n */\nexport type SignedOutState = UnauthenticatedState<SessionTokenType>;\n\nexport const AuthErrorReason = {\n  ClientUATWithoutSessionToken: 'client-uat-but-no-session-token',\n  DevBrowserMissing: 'dev-browser-missing',\n  DevBrowserSync: 'dev-browser-sync',\n  PrimaryRespondsToSyncing: 'primary-responds-to-syncing',\n  SatelliteCookieNeedsSyncing: 'satellite-needs-syncing',\n  SessionTokenAndUATMissing: 'session-token-and-uat-missing',\n  SessionTokenMissing: 'session-token-missing',\n  SessionTokenExpired: 'session-token-expired',\n  SessionTokenIATBeforeClientUAT: 'session-token-iat-before-client-uat',\n  SessionTokenNBF: 'session-token-nbf',\n  SessionTokenIatInTheFuture: 'session-token-iat-in-the-future',\n  SessionTokenWithoutClientUAT: 'session-token-but-no-client-uat',\n  ActiveOrganizationMismatch: 'active-organization-mismatch',\n  TokenTypeMismatch: 'token-type-mismatch',\n  UnexpectedError: 'unexpected-error',\n} as const;\n\nexport type AuthErrorReason = (typeof AuthErrorReason)[keyof typeof AuthErrorReason];\n\nexport type AuthReason = AuthErrorReason | TokenVerificationErrorReason;\n\nexport type RequestState<T extends TokenType = SessionTokenType> =\n  | AuthenticatedState<T>\n  | UnauthenticatedState<T>\n  | (T extends SessionTokenType ? HandshakeState : never);\n\ntype BaseSignedInParams = {\n  authenticateContext: AuthenticateContext;\n  headers?: Headers;\n  token: string;\n  tokenType: TokenType;\n};\n\ntype SignedInParams =\n  | (BaseSignedInParams & { tokenType: SessionTokenType; sessionClaims: JwtPayload })\n  | (BaseSignedInParams & { tokenType: MachineTokenType; machineData: MachineAuthType });\n\nexport function signedIn<T extends TokenType>(params: SignedInParams & { tokenType: T }): AuthenticatedState<T> {\n  const { authenticateContext, headers = new Headers(), token } = params;\n\n  const toAuth = (({ treatPendingAsSignedOut = true } = {}) => {\n    if (params.tokenType === TokenType.SessionToken) {\n      const { sessionClaims } = params as { sessionClaims: JwtPayload };\n      const authObject = signedInAuthObject(authenticateContext, token, sessionClaims);\n\n      if (treatPendingAsSignedOut && authObject.sessionStatus === 'pending') {\n        return signedOutAuthObject(undefined, authObject.sessionStatus);\n      }\n\n      return authObject;\n    }\n\n    const { machineData } = params as { machineData: MachineAuthType };\n    return authenticatedMachineObject(params.tokenType, token, machineData, authenticateContext);\n  }) as ToAuth<T, true>;\n\n  return {\n    status: AuthStatus.SignedIn,\n    reason: null,\n    message: null,\n    proxyUrl: authenticateContext.proxyUrl || '',\n    publishableKey: authenticateContext.publishableKey || '',\n    isSatellite: authenticateContext.isSatellite || false,\n    domain: authenticateContext.domain || '',\n    signInUrl: authenticateContext.signInUrl || '',\n    signUpUrl: authenticateContext.signUpUrl || '',\n    afterSignInUrl: authenticateContext.afterSignInUrl || '',\n    afterSignUpUrl: authenticateContext.afterSignUpUrl || '',\n    isSignedIn: true,\n    isAuthenticated: true,\n    tokenType: params.tokenType,\n    toAuth,\n    headers,\n    token,\n  };\n}\n\ntype SignedOutParams = Omit<BaseSignedInParams, 'token'> & {\n  reason: AuthReason;\n  message?: string;\n};\n\nexport function signedOut<T extends TokenType>(params: SignedOutParams & { tokenType: T }): UnauthenticatedState<T> {\n  const { authenticateContext, headers = new Headers(), reason, message = '', tokenType } = params;\n\n  const toAuth = (() => {\n    if (tokenType === TokenType.SessionToken) {\n      return signedOutAuthObject({ ...authenticateContext, status: AuthStatus.SignedOut, reason, message });\n    }\n\n    return unauthenticatedMachineObject(tokenType, { reason, message, headers });\n  }) as ToAuth<T, false>;\n\n  return withDebugHeaders({\n    status: AuthStatus.SignedOut,\n    reason,\n    message,\n    proxyUrl: authenticateContext.proxyUrl || '',\n    publishableKey: authenticateContext.publishableKey || '',\n    isSatellite: authenticateContext.isSatellite || false,\n    domain: authenticateContext.domain || '',\n    signInUrl: authenticateContext.signInUrl || '',\n    signUpUrl: authenticateContext.signUpUrl || '',\n    afterSignInUrl: authenticateContext.afterSignInUrl || '',\n    afterSignUpUrl: authenticateContext.afterSignUpUrl || '',\n    isSignedIn: false,\n    isAuthenticated: false,\n    tokenType,\n    toAuth,\n    headers,\n    token: null,\n  });\n}\n\nexport function handshake(\n  authenticateContext: AuthenticateContext,\n  reason: AuthReason,\n  message = '',\n  headers: Headers,\n): HandshakeState {\n  return withDebugHeaders({\n    status: AuthStatus.Handshake,\n    reason,\n    message,\n    publishableKey: authenticateContext.publishableKey || '',\n    isSatellite: authenticateContext.isSatellite || false,\n    domain: authenticateContext.domain || '',\n    proxyUrl: authenticateContext.proxyUrl || '',\n    signInUrl: authenticateContext.signInUrl || '',\n    signUpUrl: authenticateContext.signUpUrl || '',\n    afterSignInUrl: authenticateContext.afterSignInUrl || '',\n    afterSignUpUrl: authenticateContext.afterSignUpUrl || '',\n    isSignedIn: false,\n    isAuthenticated: false,\n    tokenType: TokenType.SessionToken,\n    toAuth: () => null,\n    headers,\n    token: null,\n  });\n}\n\nconst withDebugHeaders = <T extends { headers: Headers; message?: string; reason?: AuthReason; status?: AuthStatus }>(\n  requestState: T,\n): T => {\n  const headers = new Headers(requestState.headers || {});\n\n  if (requestState.message) {\n    try {\n      headers.set(constants.Headers.AuthMessage, requestState.message);\n    } catch {\n      // headers.set can throw if unicode strings are passed to it. In this case, simply do nothing\n    }\n  }\n\n  if (requestState.reason) {\n    try {\n      headers.set(constants.Headers.AuthReason, requestState.reason);\n    } catch {\n      /* empty */\n    }\n  }\n\n  if (requestState.status) {\n    try {\n      headers.set(constants.Headers.AuthStatus, requestState.status);\n    } catch {\n      /* empty */\n    }\n  }\n\n  requestState.headers = headers;\n\n  return requestState;\n};\n", "import { parse } from 'cookie';\n\nimport { constants } from '../constants';\nimport type { ClerkUrl } from './clerkUrl';\nimport { createClerkUrl } from './clerkUrl';\n\n/**\n * A class that extends the native Request class,\n * adds cookies helpers and a normalised clerkUrl that is constructed by using the values found\n * in req.headers so it is able to work reliably when the app is running behind a proxy server.\n */\nclass ClerkRequest extends Request {\n  readonly clerkUrl: ClerkUrl;\n  readonly cookies: Map<string, string | undefined>;\n\n  public constructor(input: ClerkRequest | Request | RequestInfo, init?: RequestInit) {\n    // The usual way to duplicate a request object is to\n    // pass the original request object to the Request constructor\n    // both as the `input` and `init` parameters, eg: super(req, req)\n    // However, this fails in certain environments like Vercel Edge Runtime\n    // when a framework like Remix polyfills the global Request object.\n    // This happens because `undici` performs the following instanceof check\n    // which, instead of testing against the global Request object, tests against\n    // the Request class defined in the same file (local Request class).\n    // For more details, please refer to:\n    // https://github.com/nodejs/undici/issues/2155\n    // https://github.com/nodejs/undici/blob/7153a1c78d51840bbe16576ce353e481c3934701/lib/fetch/request.js#L854\n    const url = typeof input !== 'string' && 'url' in input ? input.url : String(input);\n    super(url, init || typeof input === 'string' ? undefined : input);\n    this.clerkUrl = this.deriveUrlFromHeaders(this);\n    this.cookies = this.parseCookies(this);\n  }\n\n  public toJSON() {\n    return {\n      url: this.clerkUrl.href,\n      method: this.method,\n      headers: JSON.stringify(Object.fromEntries(this.headers)),\n      clerkUrl: this.clerkUrl.toString(),\n      cookies: JSON.stringify(Object.fromEntries(this.cookies)),\n    };\n  }\n\n  /**\n   * Used to fix request.url using the x-forwarded-* headers\n   * TODO add detailed description of the issues this solves\n   */\n  private deriveUrlFromHeaders(req: Request) {\n    const initialUrl = new URL(req.url);\n    const forwardedProto = req.headers.get(constants.Headers.ForwardedProto);\n    const forwardedHost = req.headers.get(constants.Headers.ForwardedHost);\n    const host = req.headers.get(constants.Headers.Host);\n    const protocol = initialUrl.protocol;\n\n    const resolvedHost = this.getFirstValueFromHeader(forwardedHost) ?? host;\n    const resolvedProtocol = this.getFirstValueFromHeader(forwardedProto) ?? protocol?.replace(/[:/]/, '');\n    const origin = resolvedHost && resolvedProtocol ? `${resolvedProtocol}://${resolvedHost}` : initialUrl.origin;\n\n    if (origin === initialUrl.origin) {\n      return createClerkUrl(initialUrl);\n    }\n    return createClerkUrl(initialUrl.pathname + initialUrl.search, origin);\n  }\n\n  private getFirstValueFromHeader(value?: string | null) {\n    return value?.split(',')[0];\n  }\n\n  private parseCookies(req: Request) {\n    const cookiesRecord = parse(this.decodeCookieValue(req.headers.get('cookie') || ''));\n    return new Map(Object.entries(cookiesRecord));\n  }\n\n  private decodeCookieValue(str: string) {\n    return str ? str.replace(/(%[0-9A-Z]{2})+/g, decodeURIComponent) : str;\n  }\n}\n\nexport const createClerkRequest = (...args: ConstructorParameters<typeof ClerkRequest>): ClerkRequest => {\n  return args[0] instanceof ClerkRequest ? args[0] : new ClerkRequest(...args);\n};\n\nexport type { ClerkRequest };\n", "class ClerkUrl extends URL {\n  public isCrossOrigin(other: URL | string) {\n    return this.origin !== new URL(other.toString()).origin;\n  }\n}\n\nexport type WithClerkUrl<T> = T & {\n  /**\n   * When a NextJs app is hosted on a platform different from Vercel\n   * or inside a container (Netlify, Fly.io, AWS Amplify, docker etc),\n   * req.url is always set to `localhost:3000` instead of the actual host of the app.\n   *\n   * The `authMiddleware` uses the value of the available req.headers in order to construct\n   * and use the correct url internally. This url is then exposed as `experimental_clerkUrl`,\n   * intended to be used within `beforeAuth` and `afterAuth` if needed.\n   */\n  clerkUrl: ClerkUrl;\n};\n\nexport const createClerkUrl = (...args: ConstructorParameters<typeof ClerkUrl>): ClerkUrl => {\n  return new ClerkUrl(...args);\n};\n\nexport type { ClerkUrl };\n", "export const getCookieName = (cookieDirective: string): string => {\n  return cookieDirective.split(';')[0]?.split('=')[0];\n};\n\nexport const getCookieValue = (cookieDirective: string): string => {\n  return cookieDirective.split(';')[0]?.split('=')[1];\n};\n", "import {\n  API_URL,\n  API_VERSION,\n  MAX_CACHE_LAST_UPDATED_AT_SECONDS,\n  SUPPORTED_BAPI_VERSION,\n  USER_AGENT,\n} from '../constants';\nimport {\n  TokenVerificationError,\n  TokenVerificationErrorAction,\n  TokenVerificationErrorCode,\n  TokenVerificationErrorReason,\n} from '../errors';\nimport { runtime } from '../runtime';\nimport { joinPaths } from '../util/path';\nimport { retry } from '../util/shared';\n\ntype JsonWebKeyWithKid = JsonWebKey & { kid: string };\n\ntype JsonWebKeyCache = Record<string, JsonWebKeyWithKid>;\n\nlet cache: JsonWebKeyCache = {};\nlet lastUpdatedAt = 0;\n\nfunction getFromCache(kid: string) {\n  return cache[kid];\n}\n\nfunction getCacheValues() {\n  return Object.values(cache);\n}\n\nfunction setInCache(jwk: JsonWebKeyWithKid, shouldExpire = true) {\n  cache[jwk.kid] = jwk;\n  lastUpdatedAt = shouldExpire ? Date.now() : -1;\n}\n\nconst LocalJwkKid = 'local';\nconst PEM_HEADER = '-----BEGIN PUBLIC KEY-----';\nconst PEM_TRAILER = '-----END PUBLIC KEY-----';\nconst RSA_PREFIX = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA';\nconst RSA_SUFFIX = 'IDAQAB';\n\n/**\n *\n * Loads a local PEM key usually from process.env and transform it to JsonWebKey format.\n * The result is also cached on the module level to avoid unnecessary computations in subsequent invocations.\n *\n * @param {string} localKey\n * @returns {JsonWebKey} key\n */\nexport function loadClerkJWKFromLocal(localKey?: string): JsonWebKey {\n  if (!getFromCache(LocalJwkKid)) {\n    if (!localKey) {\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.SetClerkJWTKey,\n        message: 'Missing local JWK.',\n        reason: TokenVerificationErrorReason.LocalJWKMissing,\n      });\n    }\n\n    const modulus = localKey\n      .replace(/\\r\\n|\\n|\\r/g, '')\n      .replace(PEM_HEADER, '')\n      .replace(PEM_TRAILER, '')\n      .replace(RSA_PREFIX, '')\n      .replace(RSA_SUFFIX, '')\n      .replace(/\\+/g, '-')\n      .replace(/\\//g, '_');\n\n    // JWK https://datatracker.ietf.org/doc/html/rfc7517\n    setInCache(\n      {\n        kid: 'local',\n        kty: 'RSA',\n        alg: 'RS256',\n        n: modulus,\n        e: 'AQAB',\n      },\n      false, // local key never expires in cache\n    );\n  }\n\n  return getFromCache(LocalJwkKid);\n}\n\n/**\n * @internal\n */\nexport type LoadClerkJWKFromRemoteOptions = {\n  /**\n   * @internal\n   */\n  kid: string;\n  /**\n   * @deprecated This cache TTL will be removed in the next major version. Specifying a cache TTL is a no-op.\n   */\n  jwksCacheTtlInMs?: number;\n  /**\n   * A flag to ignore the JWKS cache and always fetch JWKS before each JWT verification.\n   */\n  skipJwksCache?: boolean;\n  /**\n   * The Clerk Secret Key from the [**API keys**](https://dashboard.clerk.com/last-active?path=api-keys) page in the Clerk Dashboard.\n   */\n  secretKey?: string;\n  /**\n   * The [Clerk Backend API](https://clerk.com/docs/reference/backend-api){{ target: '_blank' }} endpoint.\n   * @default 'https://api.clerk.com'\n   */\n  apiUrl?: string;\n  /**\n   * The version passed to the Clerk API.\n   * @default 'v1'\n   */\n  apiVersion?: string;\n};\n\n/**\n *\n * Loads a key from JWKS retrieved from the well-known Frontend API endpoint of the issuer.\n * The result is also cached on the module level to avoid network requests in subsequent invocations.\n * The cache lasts up to 5 minutes.\n *\n * @param {Object} options\n * @param {string} options.kid - The id of the key that the JWT was signed with\n * @param {string} options.alg - The algorithm of the JWT\n * @returns {JsonWebKey} key\n */\nexport async function loadClerkJWKFromRemote({\n  secretKey,\n  apiUrl = API_URL,\n  apiVersion = API_VERSION,\n  kid,\n  skipJwksCache,\n}: LoadClerkJWKFromRemoteOptions): Promise<JsonWebKey> {\n  if (skipJwksCache || cacheHasExpired() || !getFromCache(kid)) {\n    if (!secretKey) {\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.ContactSupport,\n        message: 'Failed to load JWKS from Clerk Backend or Frontend API.',\n        reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad,\n      });\n    }\n    const fetcher = () => fetchJWKSFromBAPI(apiUrl, secretKey, apiVersion) as Promise<{ keys: JsonWebKeyWithKid[] }>;\n    const { keys } = await retry(fetcher);\n\n    if (!keys || !keys.length) {\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.ContactSupport,\n        message: 'The JWKS endpoint did not contain any signing keys. Contact <EMAIL>.',\n        reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad,\n      });\n    }\n\n    keys.forEach(key => setInCache(key));\n  }\n\n  const jwk = getFromCache(kid);\n\n  if (!jwk) {\n    const cacheValues = getCacheValues();\n    const jwkKeys = cacheValues\n      .map(jwk => jwk.kid)\n      .sort()\n      .join(', ');\n\n    throw new TokenVerificationError({\n      action: `Go to your Dashboard and validate your secret and public keys are correct. ${TokenVerificationErrorAction.ContactSupport} if the issue persists.`,\n      message: `Unable to find a signing key in JWKS that matches the kid='${kid}' of the provided session token. Please make sure that the __session cookie or the HTTP authorization header contain a Clerk-generated session JWT. The following kid is available: ${jwkKeys}`,\n      reason: TokenVerificationErrorReason.JWKKidMismatch,\n    });\n  }\n\n  return jwk;\n}\n\nasync function fetchJWKSFromBAPI(apiUrl: string, key: string, apiVersion: string) {\n  if (!key) {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.SetClerkSecretKey,\n      message:\n        'Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.',\n      reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad,\n    });\n  }\n\n  const url = new URL(apiUrl);\n  url.pathname = joinPaths(url.pathname, apiVersion, '/jwks');\n\n  const response = await runtime.fetch(url.href, {\n    headers: {\n      Authorization: `Bearer ${key}`,\n      'Clerk-API-Version': SUPPORTED_BAPI_VERSION,\n      'Content-Type': 'application/json',\n      'User-Agent': USER_AGENT,\n    },\n  });\n\n  if (!response.ok) {\n    const json = await response.json();\n    const invalidSecretKeyError = getErrorObjectByCode(json?.errors, TokenVerificationErrorCode.InvalidSecretKey);\n\n    if (invalidSecretKeyError) {\n      const reason = TokenVerificationErrorReason.InvalidSecretKey;\n\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.ContactSupport,\n        message: invalidSecretKeyError.message,\n        reason,\n      });\n    }\n\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.ContactSupport,\n      message: `Error loading Clerk JWKS from ${url.href} with code=${response.status}`,\n      reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad,\n    });\n  }\n\n  return response.json();\n}\n\nfunction cacheHasExpired() {\n  // If lastUpdatedAt is -1, it means that we're using a local JWKS and it never expires\n  if (lastUpdatedAt === -1) {\n    return false;\n  }\n\n  // If the cache has expired, clear the value so we don't attempt to make decisions based on stale data\n  const isExpired = Date.now() - lastUpdatedAt >= MAX_CACHE_LAST_UPDATED_AT_SECONDS * 1000;\n\n  if (isExpired) {\n    cache = {};\n  }\n\n  return isExpired;\n}\n\ntype ErrorFields = {\n  message: string;\n  long_message: string;\n  code: string;\n};\n\nconst getErrorObjectByCode = (errors: ErrorFields[], code: string) => {\n  if (!errors) {\n    return null;\n  }\n\n  return errors.find((err: ErrorFields) => err.code === code);\n};\n", "import { isClerkAPIResponseError } from '@clerk/shared/error';\nimport type { JwtPayload } from '@clerk/types';\n\nimport type { <PERSON>Key, IdPOAuthAccessToken, MachineToken } from '../api';\nimport { createBackendApiClient } from '../api/factory';\nimport {\n  MachineTokenVerificationError,\n  MachineTokenVerificationErrorCode,\n  TokenVerificationError,\n  TokenVerificationErrorAction,\n  TokenVerificationErrorReason,\n} from '../errors';\nimport type { VerifyJwtOptions } from '../jwt';\nimport type { JwtReturnType, MachineTokenReturnType } from '../jwt/types';\nimport { decodeJwt, verifyJwt } from '../jwt/verifyJwt';\nimport type { LoadClerkJWKFromRemoteOptions } from './keys';\nimport { loadClerkJWKFromLocal, loadClerkJWKFromRemote } from './keys';\nimport { API_KEY_PREFIX, M2M_TOKEN_PREFIX, OAUTH_TOKEN_PREFIX } from './machine';\nimport type { MachineTokenType } from './tokenTypes';\nimport { TokenType } from './tokenTypes';\n\n/**\n * @interface\n */\nexport type VerifyTokenOptions = Omit<VerifyJwtOptions, 'key'> &\n  Omit<LoadClerkJWKFromRemoteOptions, 'kid'> & {\n    /**\n     * Used to verify the session token in a networkless manner. Supply the PEM public key from the **[**API keys**](https://dashboard.clerk.com/last-active?path=api-keys) page -> Show JWT public key -> PEM Public Key** section in the Clerk Dashboard. **It's recommended to use [the environment variable](https://clerk.com/docs/deployments/clerk-environment-variables) instead.** For more information, refer to [Manual JWT verification](https://clerk.com/docs/backend-requests/manual-jwt).\n     */\n    jwtKey?: string;\n  };\n\n/**\n * > [!WARNING]\n * > This is a lower-level method intended for more advanced use-cases. It's recommended to use [`authenticateRequest()`](https://clerk.com/docs/references/backend/authenticate-request), which fully authenticates a token passed from the `request` object.\n *\n * Verifies a Clerk-generated token signature. Networkless if the `jwtKey` is provided. Otherwise, performs a network call to retrieve the JWKS from the [Backend API](https://clerk.com/docs/reference/backend-api/tag/JWKS#operation/GetJWKS){{ target: '_blank' }}.\n *\n * @param token - The token to verify.\n * @param options - Options for verifying the token.\n *\n * @displayFunctionSignature\n *\n * @paramExtension\n *\n * ### `VerifyTokenOptions`\n *\n * It is recommended to set these options as [environment variables](/docs/deployments/clerk-environment-variables#api-and-sdk-configuration) where possible, and then pass them to the function. For example, you can set the `secretKey` option using the `CLERK_SECRET_KEY` environment variable, and then pass it to the function like this: `createClerkClient({ secretKey: process.env.CLERK_SECRET_KEY })`.\n *\n * > [!WARNING]\n * You must provide either `jwtKey` or `secretKey`.\n *\n * <Typedoc src=\"backend/verify-token-options\" />\n *\n * @example\n *\n * The following example demonstrates how to use the [JavaScript Backend SDK](https://clerk.com/docs/references/backend/overview) to verify the token signature.\n *\n * In the following example:\n *\n * 1. The **JWKS Public Key** from the Clerk Dashboard is set in the environment variable `CLERK_JWT_KEY`.\n * 1. The session token is retrieved from the `__session` cookie or the Authorization header.\n * 1. The token is verified in a networkless manner by passing the `jwtKey` prop.\n * 1. The `authorizedParties` prop is passed to verify that the session token is generated from the expected frontend application.\n * 1. If the token is valid, the response contains the verified token.\n *\n * ```ts\n * import { verifyToken } from '@clerk/backend'\n * import { cookies } from 'next/headers'\n *\n * export async function GET(request: Request) {\n *   const cookieStore = cookies()\n *   const sessToken = cookieStore.get('__session')?.value\n *   const bearerToken = request.headers.get('Authorization')?.replace('Bearer ', '')\n *   const token = sessToken || bearerToken\n *\n *   if (!token) {\n *     return Response.json({ error: 'Token not found. User must sign in.' }, { status: 401 })\n *   }\n *\n *   try {\n *     const verifiedToken = await verifyToken(token, {\n *       jwtKey: process.env.CLERK_JWT_KEY,\n *       authorizedParties: ['http://localhost:3001', 'api.example.com'], // Replace with your authorized parties\n *     })\n *\n *     return Response.json({ verifiedToken })\n *   } catch (error) {\n *     return Response.json({ error: 'Token not verified.' }, { status: 401 })\n *   }\n * }\n * ```\n *\n * If the token is valid, the response will contain a JSON object that looks something like this:\n *\n * ```json\n * {\n *   \"verifiedToken\": {\n *     \"azp\": \"http://localhost:3000\",\n *     \"exp\": **********,\n *     \"iat\": **********,\n *     \"iss\": \"https://magical-marmoset-51.clerk.accounts.dev\",\n *     \"nbf\": **********,\n *     \"sid\": \"sess_2Ro7e2IxrffdqBboq8KfB6eGbIy\",\n *     \"sub\": \"user_2RfWKJREkjKbHZy0Wqa5qrHeAnb\"\n *   }\n * }\n * ```\n */\nexport async function verifyToken(\n  token: string,\n  options: VerifyTokenOptions,\n): Promise<JwtReturnType<JwtPayload, TokenVerificationError>> {\n  const { data: decodedResult, errors } = decodeJwt(token);\n  if (errors) {\n    return { errors };\n  }\n\n  const { header } = decodedResult;\n  const { kid } = header;\n\n  try {\n    let key;\n\n    if (options.jwtKey) {\n      key = loadClerkJWKFromLocal(options.jwtKey);\n    } else if (options.secretKey) {\n      // Fetch JWKS from Backend API using the key\n      key = await loadClerkJWKFromRemote({ ...options, kid });\n    } else {\n      return {\n        errors: [\n          new TokenVerificationError({\n            action: TokenVerificationErrorAction.SetClerkJWTKey,\n            message: 'Failed to resolve JWK during verification.',\n            reason: TokenVerificationErrorReason.JWKFailedToResolve,\n          }),\n        ],\n      };\n    }\n\n    return await verifyJwt(token, { ...options, key });\n  } catch (error) {\n    return { errors: [error as TokenVerificationError] };\n  }\n}\n\n/**\n * Handles errors from Clerk API responses for machine tokens\n * @param tokenType - The type of machine token\n * @param err - The error from the Clerk API\n * @param notFoundMessage - Custom message for 404 errors\n */\nfunction handleClerkAPIError(\n  tokenType: MachineTokenType,\n  err: any,\n  notFoundMessage: string,\n): MachineTokenReturnType<any, MachineTokenVerificationError> {\n  if (isClerkAPIResponseError(err)) {\n    let code: MachineTokenVerificationErrorCode;\n    let message: string;\n\n    switch (err.status) {\n      case 401:\n        code = MachineTokenVerificationErrorCode.InvalidSecretKey;\n        message = err.errors[0]?.message || 'Invalid secret key';\n        break;\n      case 404:\n        code = MachineTokenVerificationErrorCode.TokenInvalid;\n        message = notFoundMessage;\n        break;\n      default:\n        code = MachineTokenVerificationErrorCode.UnexpectedError;\n        message = 'Unexpected error';\n    }\n\n    return {\n      data: undefined,\n      tokenType,\n      errors: [\n        new MachineTokenVerificationError({\n          message,\n          code,\n          status: err.status,\n        }),\n      ],\n    };\n  }\n\n  return {\n    data: undefined,\n    tokenType,\n    errors: [\n      new MachineTokenVerificationError({\n        message: 'Unexpected error',\n        code: MachineTokenVerificationErrorCode.UnexpectedError,\n        status: err.status,\n      }),\n    ],\n  };\n}\n\nasync function verifyMachineToken(\n  secret: string,\n  options: VerifyTokenOptions,\n): Promise<MachineTokenReturnType<MachineToken, MachineTokenVerificationError>> {\n  try {\n    const client = createBackendApiClient(options);\n    const verifiedToken = await client.machineTokens.verifySecret(secret);\n    return { data: verifiedToken, tokenType: TokenType.MachineToken, errors: undefined };\n  } catch (err: any) {\n    return handleClerkAPIError(TokenType.MachineToken, err, 'Machine token not found');\n  }\n}\n\nasync function verifyOAuthToken(\n  accessToken: string,\n  options: VerifyTokenOptions,\n): Promise<MachineTokenReturnType<IdPOAuthAccessToken, MachineTokenVerificationError>> {\n  try {\n    const client = createBackendApiClient(options);\n    const verifiedToken = await client.idPOAuthAccessToken.verifyAccessToken(accessToken);\n    return { data: verifiedToken, tokenType: TokenType.OAuthToken, errors: undefined };\n  } catch (err: any) {\n    return handleClerkAPIError(TokenType.OAuthToken, err, 'OAuth token not found');\n  }\n}\n\nasync function verifyAPIKey(\n  secret: string,\n  options: VerifyTokenOptions,\n): Promise<MachineTokenReturnType<APIKey, MachineTokenVerificationError>> {\n  try {\n    const client = createBackendApiClient(options);\n    const verifiedToken = await client.apiKeys.verifySecret(secret);\n    return { data: verifiedToken, tokenType: TokenType.ApiKey, errors: undefined };\n  } catch (err: any) {\n    return handleClerkAPIError(TokenType.ApiKey, err, 'API key not found');\n  }\n}\n\n/**\n * Verifies any type of machine token by detecting its type from the prefix.\n *\n * @param token - The token to verify (e.g. starts with \"m2m_\", \"oauth_\", \"api_key_\", etc.)\n * @param options - Options including secretKey for BAPI authorization\n */\nexport async function verifyMachineAuthToken(token: string, options: VerifyTokenOptions) {\n  if (token.startsWith(M2M_TOKEN_PREFIX)) {\n    return verifyMachineToken(token, options);\n  }\n  if (token.startsWith(OAUTH_TOKEN_PREFIX)) {\n    return verifyOAuthToken(token, options);\n  }\n  if (token.startsWith(API_KEY_PREFIX)) {\n    return verifyAPIKey(token, options);\n  }\n\n  throw new Error('Unknown machine token type');\n}\n", "import { constants, SUPPORTED_BAPI_VERSION } from '../constants';\nimport { TokenVerificationError, TokenVerificationErrorAction, TokenVerificationErrorReason } from '../errors';\nimport type { VerifyJwtOptions } from '../jwt';\nimport { assertHeaderAlgorithm, assertHeaderType } from '../jwt/assertions';\nimport { decodeJwt, hasValidSignature } from '../jwt/verifyJwt';\nimport type { AuthenticateContext } from './authenticateContext';\nimport type { SignedInState, SignedOutState } from './authStatus';\nimport { AuthErrorReason, signedIn, signedOut } from './authStatus';\nimport { getCookieName, getCookieValue } from './cookie';\nimport { loadClerkJWKFromLocal, loadClerkJWKFromRemote } from './keys';\nimport type { OrganizationMatcher } from './organizationMatcher';\nimport { TokenType } from './tokenTypes';\nimport type { OrganizationSyncOptions, OrganizationSyncTarget } from './types';\nimport type { VerifyTokenOptions } from './verify';\nimport { verifyToken } from './verify';\n\nasync function verifyHandshakeJwt(token: string, { key }: VerifyJwtOptions): Promise<{ handshake: string[] }> {\n  const { data: decoded, errors } = decodeJwt(token);\n  if (errors) {\n    throw errors[0];\n  }\n\n  const { header, payload } = decoded;\n\n  // Header verifications\n  const { typ, alg } = header;\n\n  assertHeaderType(typ);\n  assertHeaderAlgorithm(alg);\n\n  const { data: signatureValid, errors: signatureErrors } = await hasValidSignature(decoded, key);\n  if (signatureErrors) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenVerificationFailed,\n      message: `Error verifying handshake token. ${signatureErrors[0]}`,\n    });\n  }\n\n  if (!signatureValid) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenInvalidSignature,\n      message: 'Handshake signature is invalid.',\n    });\n  }\n\n  return payload as unknown as { handshake: string[] };\n}\n\n/**\n * Similar to our verifyToken flow for Clerk-issued JWTs, but this verification flow is for our signed handshake payload.\n * The handshake payload requires fewer verification steps.\n */\nexport async function verifyHandshakeToken(\n  token: string,\n  options: VerifyTokenOptions,\n): Promise<{ handshake: string[] }> {\n  const { secretKey, apiUrl, apiVersion, jwksCacheTtlInMs, jwtKey, skipJwksCache } = options;\n\n  const { data, errors } = decodeJwt(token);\n  if (errors) {\n    throw errors[0];\n  }\n\n  const { kid } = data.header;\n\n  let key;\n\n  if (jwtKey) {\n    key = loadClerkJWKFromLocal(jwtKey);\n  } else if (secretKey) {\n    // Fetch JWKS from Backend API using the key\n    key = await loadClerkJWKFromRemote({ secretKey, apiUrl, apiVersion, kid, jwksCacheTtlInMs, skipJwksCache });\n  } else {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.SetClerkJWTKey,\n      message: 'Failed to resolve JWK during handshake verification.',\n      reason: TokenVerificationErrorReason.JWKFailedToResolve,\n    });\n  }\n\n  return await verifyHandshakeJwt(token, {\n    key,\n  });\n}\n\nexport class HandshakeService {\n  private readonly authenticateContext: AuthenticateContext;\n  private readonly organizationMatcher: OrganizationMatcher;\n  private readonly options: { organizationSyncOptions?: OrganizationSyncOptions };\n\n  constructor(\n    authenticateContext: AuthenticateContext,\n    options: { organizationSyncOptions?: OrganizationSyncOptions },\n    organizationMatcher: OrganizationMatcher,\n  ) {\n    this.authenticateContext = authenticateContext;\n    this.options = options;\n    this.organizationMatcher = organizationMatcher;\n  }\n\n  /**\n   * Determines if a request is eligible for handshake based on its headers\n   *\n   * Currently, a request is only eligible for a handshake if we can say it's *probably* a request for a document, not a fetch or some other exotic request.\n   * This heuristic should give us a reliable enough signal for browsers that support `Sec-Fetch-Dest` and for those that don't.\n   *\n   * @returns boolean indicating if the request is eligible for handshake\n   */\n  isRequestEligibleForHandshake(): boolean {\n    const { accept, secFetchDest } = this.authenticateContext;\n\n    // NOTE: we could also check sec-fetch-mode === navigate here, but according to the spec, sec-fetch-dest: document should indicate that the request is the data of a user navigation.\n    // Also, we check for 'iframe' because it's the value set when a doc request is made by an iframe.\n    if (secFetchDest === 'document' || secFetchDest === 'iframe') {\n      return true;\n    }\n\n    if (!secFetchDest && accept?.startsWith('text/html')) {\n      return true;\n    }\n\n    return false;\n  }\n\n  /**\n   * Builds the redirect headers for a handshake request\n   * @param reason - The reason for the handshake (e.g. 'session-token-expired')\n   * @returns Headers object containing the Location header for redirect\n   * @throws Error if clerkUrl is missing in authenticateContext\n   */\n  buildRedirectToHandshake(reason: string): Headers {\n    if (!this.authenticateContext?.clerkUrl) {\n      throw new Error('Missing clerkUrl in authenticateContext');\n    }\n\n    const redirectUrl = this.removeDevBrowserFromURL(this.authenticateContext.clerkUrl);\n    const frontendApiNoProtocol = this.authenticateContext.frontendApi.replace(/http(s)?:\\/\\//, '');\n\n    const baseUrl = this.authenticateContext.proxyUrl\n      ? this.authenticateContext.proxyUrl.replace(/\\/$/, '')\n      : `https://${frontendApiNoProtocol}`;\n\n    const url = new URL(`${baseUrl}/v1/client/handshake`);\n    url.searchParams.append('redirect_url', redirectUrl?.href || '');\n    url.searchParams.append('__clerk_api_version', SUPPORTED_BAPI_VERSION);\n    url.searchParams.append(\n      constants.QueryParameters.SuffixedCookies,\n      this.authenticateContext.usesSuffixedCookies().toString(),\n    );\n    url.searchParams.append(constants.QueryParameters.HandshakeReason, reason);\n\n    if (this.authenticateContext.instanceType === 'development' && this.authenticateContext.devBrowserToken) {\n      url.searchParams.append(constants.QueryParameters.DevBrowser, this.authenticateContext.devBrowserToken);\n    }\n\n    const toActivate = this.getOrganizationSyncTarget(this.authenticateContext.clerkUrl, this.organizationMatcher);\n    if (toActivate) {\n      const params = this.getOrganizationSyncQueryParams(toActivate);\n      params.forEach((value, key) => {\n        url.searchParams.append(key, value);\n      });\n    }\n\n    return new Headers({ [constants.Headers.Location]: url.href });\n  }\n\n  /**\n   * Gets cookies from either a handshake nonce or a handshake token\n   * @returns Promise resolving to string array of cookie directives\n   */\n  public async getCookiesFromHandshake(): Promise<string[]> {\n    const cookiesToSet: string[] = [];\n\n    if (this.authenticateContext.handshakeNonce) {\n      try {\n        const handshakePayload = await this.authenticateContext.apiClient?.clients.getHandshakePayload({\n          nonce: this.authenticateContext.handshakeNonce,\n        });\n        if (handshakePayload) {\n          cookiesToSet.push(...handshakePayload.directives);\n        }\n      } catch (error) {\n        console.error('Clerk: HandshakeService: error getting handshake payload:', error);\n      }\n    } else if (this.authenticateContext.handshakeToken) {\n      const handshakePayload = await verifyHandshakeToken(\n        this.authenticateContext.handshakeToken,\n        this.authenticateContext,\n      );\n      if (handshakePayload && Array.isArray(handshakePayload.handshake)) {\n        cookiesToSet.push(...handshakePayload.handshake);\n      }\n    }\n\n    return cookiesToSet;\n  }\n\n  /**\n   * Resolves a handshake request by verifying the handshake token and setting appropriate cookies\n   * @returns Promise resolving to either a SignedInState or SignedOutState\n   * @throws Error if handshake verification fails or if there are issues with the session token\n   */\n  async resolveHandshake(): Promise<SignedInState | SignedOutState> {\n    const headers = new Headers({\n      'Access-Control-Allow-Origin': 'null',\n      'Access-Control-Allow-Credentials': 'true',\n    });\n\n    const cookiesToSet = await this.getCookiesFromHandshake();\n\n    let sessionToken = '';\n    cookiesToSet.forEach((x: string) => {\n      headers.append('Set-Cookie', x);\n      if (getCookieName(x).startsWith(constants.Cookies.Session)) {\n        sessionToken = getCookieValue(x);\n      }\n    });\n\n    if (this.authenticateContext.instanceType === 'development') {\n      const newUrl = new URL(this.authenticateContext.clerkUrl);\n      newUrl.searchParams.delete(constants.QueryParameters.Handshake);\n      newUrl.searchParams.delete(constants.QueryParameters.HandshakeHelp);\n      headers.append(constants.Headers.Location, newUrl.toString());\n      headers.set(constants.Headers.CacheControl, 'no-store');\n    }\n\n    if (sessionToken === '') {\n      return signedOut({\n        tokenType: TokenType.SessionToken,\n        authenticateContext: this.authenticateContext,\n        reason: AuthErrorReason.SessionTokenMissing,\n        message: '',\n        headers,\n      });\n    }\n\n    const { data, errors: [error] = [] } = await verifyToken(sessionToken, this.authenticateContext);\n    if (data) {\n      return signedIn({\n        tokenType: TokenType.SessionToken,\n        authenticateContext: this.authenticateContext,\n        sessionClaims: data,\n        headers,\n        token: sessionToken,\n      });\n    }\n\n    if (\n      this.authenticateContext.instanceType === 'development' &&\n      (error?.reason === TokenVerificationErrorReason.TokenExpired ||\n        error?.reason === TokenVerificationErrorReason.TokenNotActiveYet ||\n        error?.reason === TokenVerificationErrorReason.TokenIatInTheFuture)\n    ) {\n      // Create a new error object with the same properties\n      const developmentError = new TokenVerificationError({\n        action: error.action,\n        message: error.message,\n        reason: error.reason,\n      });\n      // Set the tokenCarrier after construction\n      developmentError.tokenCarrier = 'cookie';\n\n      console.error(\n        `Clerk: Clock skew detected. This usually means that your system clock is inaccurate. Clerk will attempt to account for the clock skew in development.\n\nTo resolve this issue, make sure your system's clock is set to the correct time (e.g. turn off and on automatic time synchronization).\n\n---\n\n${developmentError.getFullMessage()}`,\n      );\n\n      const { data: retryResult, errors: [retryError] = [] } = await verifyToken(sessionToken, {\n        ...this.authenticateContext,\n        clockSkewInMs: 86_400_000,\n      });\n      if (retryResult) {\n        return signedIn({\n          tokenType: TokenType.SessionToken,\n          authenticateContext: this.authenticateContext,\n          sessionClaims: retryResult,\n          headers,\n          token: sessionToken,\n        });\n      }\n\n      throw new Error(retryError?.message || 'Clerk: Handshake retry failed.');\n    }\n\n    throw new Error(error?.message || 'Clerk: Handshake failed.');\n  }\n\n  /**\n   * Handles handshake token verification errors in development mode\n   * @param error - The TokenVerificationError that occurred\n   * @throws Error with a descriptive message about the verification failure\n   */\n  handleTokenVerificationErrorInDevelopment(error: TokenVerificationError): void {\n    // In development, the handshake token is being transferred in the URL as a query parameter, so there is no\n    // possibility of collision with a handshake token of another app running on the same local domain\n    // (etc one app on localhost:3000 and one on localhost:3001).\n    // Therefore, if the handshake token is invalid, it is likely that the user has switched Clerk keys locally.\n    // We make sure to throw a descriptive error message and then stop the handshake flow in every case,\n    // to avoid the possibility of an infinite loop.\n    if (error.reason === TokenVerificationErrorReason.TokenInvalidSignature) {\n      const msg = `Clerk: Handshake token verification failed due to an invalid signature. If you have switched Clerk keys locally, clear your cookies and try again.`;\n      throw new Error(msg);\n    }\n    throw new Error(`Clerk: Handshake token verification failed: ${error.getFullMessage()}.`);\n  }\n\n  /**\n   * Checks if a redirect loop is detected and sets headers to track redirect count\n   * @param headers - The Headers object to modify\n   * @returns boolean indicating if a redirect loop was detected (true) or if the request can proceed (false)\n   */\n  checkAndTrackRedirectLoop(headers: Headers): boolean {\n    if (this.authenticateContext.handshakeRedirectLoopCounter === 3) {\n      return true;\n    }\n\n    const newCounterValue = this.authenticateContext.handshakeRedirectLoopCounter + 1;\n    const cookieName = constants.Cookies.RedirectCount;\n    headers.append('Set-Cookie', `${cookieName}=${newCounterValue}; SameSite=Lax; HttpOnly; Max-Age=3`);\n    return false;\n  }\n\n  private removeDevBrowserFromURL(url: URL): URL {\n    const updatedURL = new URL(url);\n    updatedURL.searchParams.delete(constants.QueryParameters.DevBrowser);\n    updatedURL.searchParams.delete(constants.QueryParameters.LegacyDevBrowser);\n    return updatedURL;\n  }\n\n  private getOrganizationSyncTarget(url: URL, matchers: OrganizationMatcher): OrganizationSyncTarget | null {\n    return matchers.findTarget(url);\n  }\n\n  private getOrganizationSyncQueryParams(toActivate: OrganizationSyncTarget): Map<string, string> {\n    const ret = new Map();\n    if (toActivate.type === 'personalAccount') {\n      ret.set('organization_id', '');\n    }\n    if (toActivate.type === 'organization') {\n      if (toActivate.organizationId) {\n        ret.set('organization_id', toActivate.organizationId);\n      }\n      if (toActivate.organizationSlug) {\n        ret.set('organization_id', toActivate.organizationSlug);\n      }\n    }\n    return ret;\n  }\n}\n", "import type { MatchFunction } from '@clerk/shared/pathToRegexp';\nimport { match } from '@clerk/shared/pathToRegexp';\n\nimport type { OrganizationSyncOptions, OrganizationSyncTarget } from './types';\n\nexport class OrganizationMatcher {\n  private readonly organizationPattern: MatchFunction | null;\n  private readonly personalAccountPattern: MatchFunction | null;\n\n  constructor(options?: OrganizationSyncOptions) {\n    this.organizationPattern = this.createMatcher(options?.organizationPatterns);\n    this.personalAccountPattern = this.createMatcher(options?.personalAccountPatterns);\n  }\n\n  private createMatcher(pattern?: string[]): MatchFunction | null {\n    if (!pattern) return null;\n    try {\n      return match(pattern);\n    } catch (e) {\n      throw new Error(`Invalid pattern \"${pattern}\": ${e}`);\n    }\n  }\n\n  findTarget(url: URL): OrganizationSyncTarget | null {\n    const orgTarget = this.findOrganizationTarget(url);\n    if (orgTarget) return orgTarget;\n\n    return this.findPersonalAccountTarget(url);\n  }\n\n  private findOrganizationTarget(url: URL): OrganizationSyncTarget | null {\n    if (!this.organizationPattern) return null;\n\n    try {\n      const result = this.organizationPattern(url.pathname);\n      if (!result || !('params' in result)) return null;\n\n      const params = result.params as { id?: string; slug?: string };\n      if (params.id) return { type: 'organization', organizationId: params.id };\n      if (params.slug) return { type: 'organization', organizationSlug: params.slug };\n\n      return null;\n    } catch (e) {\n      console.error('Failed to match organization pattern:', e);\n      return null;\n    }\n  }\n\n  private findPersonalAccountTarget(url: URL): OrganizationSyncTarget | null {\n    if (!this.personalAccountPattern) return null;\n\n    try {\n      const result = this.personalAccountPattern(url.pathname);\n      return result ? { type: 'personalAccount' } : null;\n    } catch (e) {\n      console.error('Failed to match personal account pattern:', e);\n      return null;\n    }\n  }\n}\n", "import type { JwtPayload } from '@clerk/types';\n\nimport { constants } from '../constants';\nimport type { TokenCarrier } from '../errors';\nimport { MachineTokenVerificationError, TokenVerificationError, TokenVerificationErrorReason } from '../errors';\nimport { decodeJwt } from '../jwt/verifyJwt';\nimport { assertValidSecretKey } from '../util/optionsAssertions';\nimport { isDevelopmentFromSecretKey } from '../util/shared';\nimport type { AuthenticateContext } from './authenticateContext';\nimport { createAuthenticateContext } from './authenticateContext';\nimport type { SignedInAuthObject } from './authObjects';\nimport type { HandshakeState, RequestState, SignedInState, SignedOutState, UnauthenticatedState } from './authStatus';\nimport { AuthErrorReason, handshake, signedIn, signedOut } from './authStatus';\nimport { createClerkRequest } from './clerkRequest';\nimport { getCookieName, getCookieValue } from './cookie';\nimport { HandshakeService } from './handshake';\nimport { getMachineTokenType, isMachineTokenByPrefix, isTokenTypeAccepted } from './machine';\nimport { OrganizationMatcher } from './organizationMatcher';\nimport type { MachineTokenType, SessionTokenType } from './tokenTypes';\nimport { TokenType } from './tokenTypes';\nimport type { AuthenticateRequestOptions } from './types';\nimport { verifyMachineAuthToken, verifyToken } from './verify';\n\nexport const RefreshTokenErrorReason = {\n  NonEligibleNoCookie: 'non-eligible-no-refresh-cookie',\n  NonEligibleNonGet: 'non-eligible-non-get',\n  InvalidSessionToken: 'invalid-session-token',\n  MissingApiClient: 'missing-api-client',\n  MissingSessionToken: 'missing-session-token',\n  MissingRefreshToken: 'missing-refresh-token',\n  ExpiredSessionTokenDecodeFailed: 'expired-session-token-decode-failed',\n  ExpiredSessionTokenMissingSidClaim: 'expired-session-token-missing-sid-claim',\n  FetchError: 'fetch-error',\n  UnexpectedSDKError: 'unexpected-sdk-error',\n  UnexpectedBAPIError: 'unexpected-bapi-error',\n} as const;\n\nfunction assertSignInUrlExists(signInUrl: string | undefined, key: string): asserts signInUrl is string {\n  if (!signInUrl && isDevelopmentFromSecretKey(key)) {\n    throw new Error(`Missing signInUrl. Pass a signInUrl for dev instances if an app is satellite`);\n  }\n}\n\nfunction assertProxyUrlOrDomain(proxyUrlOrDomain: string | undefined) {\n  if (!proxyUrlOrDomain) {\n    throw new Error(`Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl`);\n  }\n}\n\nfunction assertSignInUrlFormatAndOrigin(_signInUrl: string, origin: string) {\n  let signInUrl: URL;\n  try {\n    signInUrl = new URL(_signInUrl);\n  } catch {\n    throw new Error(`The signInUrl needs to have a absolute url format.`);\n  }\n\n  if (signInUrl.origin === origin) {\n    throw new Error(`The signInUrl needs to be on a different origin than your satellite application.`);\n  }\n}\n\nfunction isRequestEligibleForRefresh(\n  err: TokenVerificationError,\n  authenticateContext: { refreshTokenInCookie?: string },\n  request: Request,\n) {\n  return (\n    err.reason === TokenVerificationErrorReason.TokenExpired &&\n    !!authenticateContext.refreshTokenInCookie &&\n    request.method === 'GET'\n  );\n}\n\nfunction checkTokenTypeMismatch(\n  parsedTokenType: MachineTokenType,\n  acceptsToken: NonNullable<AuthenticateRequestOptions['acceptsToken']>,\n  authenticateContext: AuthenticateContext,\n): UnauthenticatedState<MachineTokenType> | null {\n  const mismatch = !isTokenTypeAccepted(parsedTokenType, acceptsToken);\n  if (mismatch) {\n    return signedOut({\n      tokenType: parsedTokenType,\n      authenticateContext,\n      reason: AuthErrorReason.TokenTypeMismatch,\n    });\n  }\n  return null;\n}\n\nexport interface AuthenticateRequest {\n  /**\n   * @example\n   * clerkClient.authenticateRequest(request, { acceptsToken: ['session_token', 'api_key'] });\n   */\n  <T extends readonly TokenType[]>(\n    request: Request,\n    options: AuthenticateRequestOptions & { acceptsToken: T },\n  ): Promise<RequestState<T[number]>>;\n\n  /**\n   * @example\n   * clerkClient.authenticateRequest(request, { acceptsToken: 'session_token' });\n   */\n  <T extends TokenType>(\n    request: Request,\n    options: AuthenticateRequestOptions & { acceptsToken: T },\n  ): Promise<RequestState<T>>;\n\n  /**\n   * @example\n   * clerkClient.authenticateRequest(request, { acceptsToken: 'any' });\n   */\n  (request: Request, options: AuthenticateRequestOptions & { acceptsToken: 'any' }): Promise<RequestState<TokenType>>;\n\n  /**\n   * @example\n   * clerkClient.authenticateRequest(request);\n   */\n  (request: Request, options?: AuthenticateRequestOptions): Promise<RequestState<SessionTokenType>>;\n}\n\nexport const authenticateRequest: AuthenticateRequest = (async (\n  request: Request,\n  options: AuthenticateRequestOptions,\n): Promise<RequestState<TokenType>> => {\n  const authenticateContext = await createAuthenticateContext(createClerkRequest(request), options);\n  assertValidSecretKey(authenticateContext.secretKey);\n\n  // Default tokenType is session_token for backwards compatibility.\n  const acceptsToken = options.acceptsToken ?? TokenType.SessionToken;\n\n  if (authenticateContext.isSatellite) {\n    assertSignInUrlExists(authenticateContext.signInUrl, authenticateContext.secretKey);\n    if (authenticateContext.signInUrl && authenticateContext.origin) {\n      assertSignInUrlFormatAndOrigin(authenticateContext.signInUrl, authenticateContext.origin);\n    }\n    assertProxyUrlOrDomain(authenticateContext.proxyUrl || authenticateContext.domain);\n  }\n\n  const organizationMatcher = new OrganizationMatcher(options.organizationSyncOptions);\n  const handshakeService = new HandshakeService(\n    authenticateContext,\n    { organizationSyncOptions: options.organizationSyncOptions },\n    organizationMatcher,\n  );\n\n  async function refreshToken(\n    authenticateContext: AuthenticateContext,\n  ): Promise<{ data: string[]; error: null } | { data: null; error: any }> {\n    // To perform a token refresh, apiClient must be defined.\n    if (!options.apiClient) {\n      return {\n        data: null,\n        error: {\n          message: 'An apiClient is needed to perform token refresh.',\n          cause: { reason: RefreshTokenErrorReason.MissingApiClient },\n        },\n      };\n    }\n    const { sessionToken: expiredSessionToken, refreshTokenInCookie: refreshToken } = authenticateContext;\n    if (!expiredSessionToken) {\n      return {\n        data: null,\n        error: {\n          message: 'Session token must be provided.',\n          cause: { reason: RefreshTokenErrorReason.MissingSessionToken },\n        },\n      };\n    }\n    if (!refreshToken) {\n      return {\n        data: null,\n        error: {\n          message: 'Refresh token must be provided.',\n          cause: { reason: RefreshTokenErrorReason.MissingRefreshToken },\n        },\n      };\n    }\n    // The token refresh endpoint requires a sessionId, so we decode that from the expired token.\n    const { data: decodeResult, errors: decodedErrors } = decodeJwt(expiredSessionToken);\n    if (!decodeResult || decodedErrors) {\n      return {\n        data: null,\n        error: {\n          message: 'Unable to decode the expired session token.',\n          cause: { reason: RefreshTokenErrorReason.ExpiredSessionTokenDecodeFailed, errors: decodedErrors },\n        },\n      };\n    }\n\n    if (!decodeResult?.payload?.sid) {\n      return {\n        data: null,\n        error: {\n          message: 'Expired session token is missing the `sid` claim.',\n          cause: { reason: RefreshTokenErrorReason.ExpiredSessionTokenMissingSidClaim },\n        },\n      };\n    }\n\n    try {\n      // Perform the actual token refresh.\n      const response = await options.apiClient.sessions.refreshSession(decodeResult.payload.sid, {\n        format: 'cookie',\n        suffixed_cookies: authenticateContext.usesSuffixedCookies(),\n        expired_token: expiredSessionToken || '',\n        refresh_token: refreshToken || '',\n        request_origin: authenticateContext.clerkUrl.origin,\n        // The refresh endpoint expects headers as Record<string, string[]>, so we need to transform it.\n        request_headers: Object.fromEntries(Array.from(request.headers.entries()).map(([k, v]) => [k, [v]])),\n      });\n      return { data: response.cookies, error: null };\n    } catch (err: any) {\n      if (err?.errors?.length) {\n        if (err.errors[0].code === 'unexpected_error') {\n          return {\n            data: null,\n            error: {\n              message: `Fetch unexpected error`,\n              cause: { reason: RefreshTokenErrorReason.FetchError, errors: err.errors },\n            },\n          };\n        }\n        return {\n          data: null,\n          error: {\n            message: err.errors[0].code,\n            cause: { reason: err.errors[0].code, errors: err.errors },\n          },\n        };\n      } else {\n        return {\n          data: null,\n          error: {\n            message: `Unexpected Server/BAPI error`,\n            cause: { reason: RefreshTokenErrorReason.UnexpectedBAPIError, errors: [err] },\n          },\n        };\n      }\n    }\n  }\n\n  async function attemptRefresh(\n    authenticateContext: AuthenticateContext,\n  ): Promise<\n    | { data: { jwtPayload: JwtPayload; sessionToken: string; headers: Headers }; error: null }\n    | { data: null; error: any }\n  > {\n    const { data: cookiesToSet, error } = await refreshToken(authenticateContext);\n    if (!cookiesToSet || cookiesToSet.length === 0) {\n      return { data: null, error };\n    }\n\n    const headers = new Headers();\n    let sessionToken = '';\n    cookiesToSet.forEach((x: string) => {\n      headers.append('Set-Cookie', x);\n      if (getCookieName(x).startsWith(constants.Cookies.Session)) {\n        sessionToken = getCookieValue(x);\n      }\n    });\n\n    // Since we're going to return a signedIn response, we need to decode the data from the new sessionToken.\n    const { data: jwtPayload, errors } = await verifyToken(sessionToken, authenticateContext);\n    if (errors) {\n      return {\n        data: null,\n        error: {\n          message: `Clerk: unable to verify refreshed session token.`,\n          cause: { reason: RefreshTokenErrorReason.InvalidSessionToken, errors },\n        },\n      };\n    }\n    return { data: { jwtPayload, sessionToken, headers }, error: null };\n  }\n\n  function handleMaybeHandshakeStatus(\n    authenticateContext: AuthenticateContext,\n    reason: string,\n    message: string,\n    headers?: Headers,\n  ): SignedInState | SignedOutState | HandshakeState {\n    if (!handshakeService.isRequestEligibleForHandshake()) {\n      return signedOut({\n        tokenType: TokenType.SessionToken,\n        authenticateContext,\n        reason,\n        message,\n      });\n    }\n\n    // Right now the only usage of passing in different headers is for multi-domain sync, which redirects somewhere else.\n    // In the future if we want to decorate the handshake redirect with additional headers per call we need to tweak this logic.\n    const handshakeHeaders = headers ?? handshakeService.buildRedirectToHandshake(reason);\n\n    // Chrome aggressively caches inactive tabs. If we don't set the header here,\n    // all 307 redirects will be cached and the handshake will end up in an infinite loop.\n    if (handshakeHeaders.get(constants.Headers.Location)) {\n      handshakeHeaders.set(constants.Headers.CacheControl, 'no-store');\n    }\n\n    // Introduce the mechanism to protect for infinite handshake redirect loops\n    // using a cookie and returning true if it's infinite redirect loop or false if we can\n    // proceed with triggering handshake.\n    const isRedirectLoop = handshakeService.checkAndTrackRedirectLoop(handshakeHeaders);\n    if (isRedirectLoop) {\n      const msg = `Clerk: Refreshing the session token resulted in an infinite redirect loop. This usually means that your Clerk instance keys do not match - make sure to copy the correct publishable and secret keys from the Clerk dashboard.`;\n      console.log(msg);\n      return signedOut({\n        tokenType: TokenType.SessionToken,\n        authenticateContext,\n        reason,\n        message,\n      });\n    }\n\n    return handshake(authenticateContext, reason, message, handshakeHeaders);\n  }\n\n  /**\n   * Determines if a handshake must occur to resolve a mismatch between the organization as specified\n   * by the URL (according to the options) and the actual active organization on the session.\n   *\n   * @returns {HandshakeState | SignedOutState | null} - The function can return the following:\n   *   - {HandshakeState}: If a handshake is needed to resolve the mismatched organization.\n   *   - {SignedOutState}: If a handshake is required but cannot be performed.\n   *   - {null}:           If no action is required.\n   */\n  function handleMaybeOrganizationSyncHandshake(\n    authenticateContext: AuthenticateContext,\n    auth: SignedInAuthObject,\n  ): HandshakeState | SignedOutState | null {\n    const organizationSyncTarget = organizationMatcher.findTarget(authenticateContext.clerkUrl);\n    if (!organizationSyncTarget) {\n      return null;\n    }\n    let mustActivate = false;\n    if (organizationSyncTarget.type === 'organization') {\n      // Activate an org by slug?\n      if (organizationSyncTarget.organizationSlug && organizationSyncTarget.organizationSlug !== auth.orgSlug) {\n        mustActivate = true;\n      }\n      // Activate an org by ID?\n      if (organizationSyncTarget.organizationId && organizationSyncTarget.organizationId !== auth.orgId) {\n        mustActivate = true;\n      }\n    }\n    // Activate the personal account?\n    if (organizationSyncTarget.type === 'personalAccount' && auth.orgId) {\n      mustActivate = true;\n    }\n    if (!mustActivate) {\n      return null;\n    }\n    if (authenticateContext.handshakeRedirectLoopCounter > 0) {\n      // We have an organization that needs to be activated, but this isn't our first time redirecting.\n      // This is because we attempted to activate the organization previously, but the organization\n      // must not have been valid (either not found, or not valid for this user), and gave us back\n      // a null organization. We won't re-try the handshake, and leave it to the server component to handle.\n      console.warn(\n        'Clerk: Organization activation handshake loop detected. This is likely due to an invalid organization ID or slug. Skipping organization activation.',\n      );\n      return null;\n    }\n    const handshakeState = handleMaybeHandshakeStatus(\n      authenticateContext,\n      AuthErrorReason.ActiveOrganizationMismatch,\n      '',\n    );\n    if (handshakeState.status !== 'handshake') {\n      // Currently, this is only possible if we're in a redirect loop, but the above check should guard against that.\n      return null;\n    }\n    return handshakeState;\n  }\n\n  async function authenticateRequestWithTokenInHeader() {\n    const { tokenInHeader } = authenticateContext;\n\n    try {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      const { data, errors } = await verifyToken(tokenInHeader!, authenticateContext);\n      if (errors) {\n        throw errors[0];\n      }\n      // use `await` to force this try/catch handle the signedIn invocation\n      return signedIn({\n        tokenType: TokenType.SessionToken,\n        authenticateContext,\n        sessionClaims: data,\n        headers: new Headers(),\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        token: tokenInHeader!,\n      });\n    } catch (err) {\n      return handleSessionTokenError(err, 'header');\n    }\n  }\n\n  async function authenticateRequestWithTokenInCookie() {\n    const hasActiveClient = authenticateContext.clientUat;\n    const hasSessionToken = !!authenticateContext.sessionTokenInCookie;\n    const hasDevBrowserToken = !!authenticateContext.devBrowserToken;\n\n    /**\n     * If we have a handshakeToken, resolve the handshake and attempt to return a definitive signed in or signed out state.\n     */\n    if (authenticateContext.handshakeNonce || authenticateContext.handshakeToken) {\n      try {\n        return await handshakeService.resolveHandshake();\n      } catch (error) {\n        // In production, the handshake token is being transferred as a cookie, so there is a possibility of collision\n        // with a handshake token of another app running on the same etld+1 domain.\n        // For example, if one app is running on sub1.clerk.com and another on sub2.clerk.com, the handshake token\n        // cookie for both apps will be set on etld+1 (clerk.com) so there's a possibility that one app will accidentally\n        // use the handshake token of a different app during the handshake flow.\n        // In this scenario, verification will fail with TokenInvalidSignature. In contrast to the development case,\n        // we need to allow the flow to continue so the app eventually retries another handshake with the correct token.\n        // We need to make sure, however, that we don't allow the flow to continue indefinitely, so we throw an error after X\n        // retries to avoid an infinite loop. An infinite loop can happen if the customer switched Clerk keys for their prod app.\n\n        // Check the handleTokenVerificationErrorInDevelopment method for the development case.\n        if (error instanceof TokenVerificationError && authenticateContext.instanceType === 'development') {\n          handshakeService.handleTokenVerificationErrorInDevelopment(error);\n        } else {\n          console.error('Clerk: unable to resolve handshake:', error);\n        }\n      }\n    }\n    /**\n     * Otherwise, check for \"known unknown\" auth states that we can resolve with a handshake.\n     */\n    if (\n      authenticateContext.instanceType === 'development' &&\n      authenticateContext.clerkUrl.searchParams.has(constants.QueryParameters.DevBrowser)\n    ) {\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.DevBrowserSync, '');\n    }\n\n    const isRequestEligibleForMultiDomainSync =\n      authenticateContext.isSatellite && authenticateContext.secFetchDest === 'document';\n\n    /**\n     * Begin multi-domain sync flows\n     */\n    if (authenticateContext.instanceType === 'production' && isRequestEligibleForMultiDomainSync) {\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.SatelliteCookieNeedsSyncing, '');\n    }\n\n    // Multi-domain development sync flow\n    if (\n      authenticateContext.instanceType === 'development' &&\n      isRequestEligibleForMultiDomainSync &&\n      !authenticateContext.clerkUrl.searchParams.has(constants.QueryParameters.ClerkSynced)\n    ) {\n      // initiate MD sync\n\n      // signInUrl exists, checked at the top of `authenticateRequest`\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      const redirectURL = new URL(authenticateContext.signInUrl!);\n      redirectURL.searchParams.append(\n        constants.QueryParameters.ClerkRedirectUrl,\n        authenticateContext.clerkUrl.toString(),\n      );\n      const headers = new Headers({ [constants.Headers.Location]: redirectURL.toString() });\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.SatelliteCookieNeedsSyncing, '', headers);\n    }\n\n    // Multi-domain development sync flow\n    const redirectUrl = new URL(authenticateContext.clerkUrl).searchParams.get(\n      constants.QueryParameters.ClerkRedirectUrl,\n    );\n\n    if (authenticateContext.instanceType === 'development' && !authenticateContext.isSatellite && redirectUrl) {\n      // Dev MD sync from primary, redirect back to satellite w/ dev browser query param\n      const redirectBackToSatelliteUrl = new URL(redirectUrl);\n\n      if (authenticateContext.devBrowserToken) {\n        redirectBackToSatelliteUrl.searchParams.append(\n          constants.QueryParameters.DevBrowser,\n          authenticateContext.devBrowserToken,\n        );\n      }\n      redirectBackToSatelliteUrl.searchParams.append(constants.QueryParameters.ClerkSynced, 'true');\n\n      const headers = new Headers({ [constants.Headers.Location]: redirectBackToSatelliteUrl.toString() });\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.PrimaryRespondsToSyncing, '', headers);\n    }\n    /**\n     * End multi-domain sync flows\n     */\n\n    if (authenticateContext.instanceType === 'development' && !hasDevBrowserToken) {\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.DevBrowserMissing, '');\n    }\n\n    if (!hasActiveClient && !hasSessionToken) {\n      return signedOut({\n        tokenType: TokenType.SessionToken,\n        authenticateContext,\n        reason: AuthErrorReason.SessionTokenAndUATMissing,\n      });\n    }\n\n    // This can eagerly run handshake since client_uat is SameSite=Strict in dev\n    if (!hasActiveClient && hasSessionToken) {\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.SessionTokenWithoutClientUAT, '');\n    }\n\n    if (hasActiveClient && !hasSessionToken) {\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.ClientUATWithoutSessionToken, '');\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const { data: decodeResult, errors: decodedErrors } = decodeJwt(authenticateContext.sessionTokenInCookie!);\n\n    if (decodedErrors) {\n      return handleSessionTokenError(decodedErrors[0], 'cookie');\n    }\n\n    if (decodeResult.payload.iat < authenticateContext.clientUat) {\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.SessionTokenIATBeforeClientUAT, '');\n    }\n\n    try {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      const { data, errors } = await verifyToken(authenticateContext.sessionTokenInCookie!, authenticateContext);\n      if (errors) {\n        throw errors[0];\n      }\n\n      const signedInRequestState = signedIn({\n        tokenType: TokenType.SessionToken,\n        authenticateContext,\n        sessionClaims: data,\n        headers: new Headers(),\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        token: authenticateContext.sessionTokenInCookie!,\n      });\n\n      const authObject = signedInRequestState.toAuth();\n      // Org sync if necessary\n      if (authObject.userId) {\n        const handshakeRequestState = handleMaybeOrganizationSyncHandshake(authenticateContext, authObject);\n        if (handshakeRequestState) {\n          return handshakeRequestState;\n        }\n      }\n\n      return signedInRequestState;\n    } catch (err) {\n      return handleSessionTokenError(err, 'cookie');\n    }\n\n    // Unreachable\n    return signedOut({\n      tokenType: TokenType.SessionToken,\n      authenticateContext,\n      reason: AuthErrorReason.UnexpectedError,\n    });\n  }\n\n  async function handleSessionTokenError(\n    err: unknown,\n    tokenCarrier: TokenCarrier,\n  ): Promise<SignedInState | SignedOutState | HandshakeState> {\n    if (!(err instanceof TokenVerificationError)) {\n      return signedOut({\n        tokenType: TokenType.SessionToken,\n        authenticateContext,\n        reason: AuthErrorReason.UnexpectedError,\n      });\n    }\n\n    let refreshError: string | null;\n\n    if (isRequestEligibleForRefresh(err, authenticateContext, request)) {\n      const { data, error } = await attemptRefresh(authenticateContext);\n      if (data) {\n        return signedIn({\n          tokenType: TokenType.SessionToken,\n          authenticateContext,\n          sessionClaims: data.jwtPayload,\n          headers: data.headers,\n          token: data.sessionToken,\n        });\n      }\n\n      // If there's any error, simply fallback to the handshake flow including the reason as a query parameter.\n      if (error?.cause?.reason) {\n        refreshError = error.cause.reason;\n      } else {\n        refreshError = RefreshTokenErrorReason.UnexpectedSDKError;\n      }\n    } else {\n      if (request.method !== 'GET') {\n        refreshError = RefreshTokenErrorReason.NonEligibleNonGet;\n      } else if (!authenticateContext.refreshTokenInCookie) {\n        refreshError = RefreshTokenErrorReason.NonEligibleNoCookie;\n      } else {\n        //refresh error is not applicable if token verification error is not 'session-token-expired'\n        refreshError = null;\n      }\n    }\n\n    err.tokenCarrier = tokenCarrier;\n\n    const reasonToHandshake = [\n      TokenVerificationErrorReason.TokenExpired,\n      TokenVerificationErrorReason.TokenNotActiveYet,\n      TokenVerificationErrorReason.TokenIatInTheFuture,\n    ].includes(err.reason);\n\n    if (reasonToHandshake) {\n      return handleMaybeHandshakeStatus(\n        authenticateContext,\n        convertTokenVerificationErrorReasonToAuthErrorReason({ tokenError: err.reason, refreshError }),\n        err.getFullMessage(),\n      );\n    }\n\n    return signedOut({\n      tokenType: TokenType.SessionToken,\n      authenticateContext,\n      reason: err.reason,\n      message: err.getFullMessage(),\n    });\n  }\n\n  function handleMachineError(tokenType: MachineTokenType, err: unknown): UnauthenticatedState<MachineTokenType> {\n    if (!(err instanceof MachineTokenVerificationError)) {\n      return signedOut({\n        tokenType,\n        authenticateContext,\n        reason: AuthErrorReason.UnexpectedError,\n      });\n    }\n\n    return signedOut({\n      tokenType,\n      authenticateContext,\n      reason: err.code,\n      message: err.getFullMessage(),\n    });\n  }\n\n  async function authenticateMachineRequestWithTokenInHeader() {\n    const { tokenInHeader } = authenticateContext;\n    // Use session token error handling if no token in header (default behavior)\n    if (!tokenInHeader) {\n      return handleSessionTokenError(new Error('Missing token in header'), 'header');\n    }\n\n    // Handle case where tokenType is any and the token is not a machine token\n    if (!isMachineTokenByPrefix(tokenInHeader)) {\n      return signedOut({\n        tokenType: acceptsToken as MachineTokenType,\n        authenticateContext,\n        reason: AuthErrorReason.TokenTypeMismatch,\n        message: '',\n      });\n    }\n\n    const parsedTokenType = getMachineTokenType(tokenInHeader);\n    const mismatchState = checkTokenTypeMismatch(parsedTokenType, acceptsToken, authenticateContext);\n    if (mismatchState) {\n      return mismatchState;\n    }\n\n    const { data, tokenType, errors } = await verifyMachineAuthToken(tokenInHeader, authenticateContext);\n    if (errors) {\n      return handleMachineError(tokenType, errors[0]);\n    }\n    return signedIn({\n      tokenType,\n      authenticateContext,\n      machineData: data,\n      token: tokenInHeader,\n    });\n  }\n\n  async function authenticateAnyRequestWithTokenInHeader() {\n    const { tokenInHeader } = authenticateContext;\n    // Use session token error handling if no token in header (default behavior)\n    if (!tokenInHeader) {\n      return handleSessionTokenError(new Error('Missing token in header'), 'header');\n    }\n\n    // Handle as a machine token\n    if (isMachineTokenByPrefix(tokenInHeader)) {\n      const parsedTokenType = getMachineTokenType(tokenInHeader);\n      const mismatchState = checkTokenTypeMismatch(parsedTokenType, acceptsToken, authenticateContext);\n      if (mismatchState) {\n        return mismatchState;\n      }\n\n      const { data, tokenType, errors } = await verifyMachineAuthToken(tokenInHeader, authenticateContext);\n      if (errors) {\n        return handleMachineError(tokenType, errors[0]);\n      }\n\n      return signedIn({\n        tokenType,\n        authenticateContext,\n        machineData: data,\n        token: tokenInHeader,\n      });\n    }\n\n    // Handle as a regular session token\n    const { data, errors } = await verifyToken(tokenInHeader, authenticateContext);\n    if (errors) {\n      return handleSessionTokenError(errors[0], 'header');\n    }\n\n    return signedIn({\n      tokenType: TokenType.SessionToken,\n      authenticateContext,\n      sessionClaims: data,\n      token: tokenInHeader,\n    });\n  }\n\n  if (authenticateContext.tokenInHeader) {\n    if (acceptsToken === 'any') {\n      return authenticateAnyRequestWithTokenInHeader();\n    }\n\n    if (acceptsToken === TokenType.SessionToken) {\n      return authenticateRequestWithTokenInHeader();\n    }\n\n    return authenticateMachineRequestWithTokenInHeader();\n  }\n\n  // Machine requests cannot have the token in the cookie, it must be in header.\n  if (\n    acceptsToken === TokenType.OAuthToken ||\n    acceptsToken === TokenType.ApiKey ||\n    acceptsToken === TokenType.MachineToken\n  ) {\n    return signedOut({\n      tokenType: acceptsToken,\n      authenticateContext,\n      reason: 'No token in header',\n    });\n  }\n\n  return authenticateRequestWithTokenInCookie();\n}) as AuthenticateRequest;\n\n/**\n * @internal\n */\nexport const debugRequestState = (params: RequestState) => {\n  const { isSignedIn, isAuthenticated, proxyUrl, reason, message, publishableKey, isSatellite, domain } = params;\n  return { isSignedIn, isAuthenticated, proxyUrl, reason, message, publishableKey, isSatellite, domain };\n};\n\nconst convertTokenVerificationErrorReasonToAuthErrorReason = ({\n  tokenError,\n  refreshError,\n}: {\n  tokenError: TokenVerificationErrorReason;\n  refreshError: string | null;\n}): string => {\n  switch (tokenError) {\n    case TokenVerificationErrorReason.TokenExpired:\n      return `${AuthErrorReason.SessionTokenExpired}-refresh-${refreshError}`;\n    case TokenVerificationErrorReason.TokenNotActiveYet:\n      return AuthErrorReason.SessionTokenNBF;\n    case TokenVerificationErrorReason.TokenIatInTheFuture:\n      return AuthErrorReason.SessionTokenIatInTheFuture;\n    default:\n      return AuthErrorReason.UnexpectedError;\n  }\n};\n", "import type { ApiClient } from '../api';\nimport { mergePreDefinedOptions } from '../util/mergePreDefinedOptions';\nimport type { AuthenticateRequest } from './request';\nimport { authenticateRequest as authenticateRequestOriginal, debugRequestState } from './request';\nimport type { AuthenticateRequestOptions } from './types';\n\ntype RunTimeOptions = Omit<AuthenticateRequestOptions, 'apiUrl' | 'apiVersion'>;\ntype BuildTimeOptions = Partial<\n  Pick<\n    AuthenticateRequestOptions,\n    | 'apiUrl'\n    | 'apiVersion'\n    | 'audience'\n    | 'domain'\n    | 'isSatellite'\n    | 'jwtKey'\n    | 'proxyUrl'\n    | 'publishableKey'\n    | 'secretKey'\n  >\n>;\n\nconst defaultOptions = {\n  secretKey: '',\n  jwtKey: '',\n  apiUrl: undefined,\n  apiVersion: undefined,\n  proxyUrl: '',\n  publishableKey: '',\n  isSatellite: false,\n  domain: '',\n  audience: '',\n} satisfies BuildTimeOptions;\n\n/**\n * @internal\n */\nexport type CreateAuthenticateRequestOptions = {\n  options: BuildTimeOptions;\n  apiClient: ApiClient;\n};\n\n/**\n * @internal\n */\nexport function createAuthenticateRequest(params: CreateAuthenticateRequestOptions) {\n  const buildTimeOptions = mergePreDefinedOptions(defaultOptions, params.options);\n  const apiClient = params.apiClient;\n\n  const authenticateRequest: AuthenticateRequest = (request: Request, options: RunTimeOptions = {}) => {\n    const { apiUrl, apiVersion } = buildTimeOptions;\n    const runTimeOptions = mergePreDefinedOptions(buildTimeOptions, options);\n    return authenticateRequestOriginal(request, {\n      ...options,\n      ...runTimeOptions,\n      // We should add all the omitted props from options here (eg apiUrl / apiVersion)\n      // to avoid runtime options override them.\n      apiUrl,\n      apiVersion,\n      apiClient,\n    });\n  };\n\n  return {\n    authenticateRequest,\n    debugRequestState,\n  };\n}\n", "import type { CreateBackendApiOptions, Organization, Session, User } from '../api';\nimport { createBackendApiClient } from '../api';\nimport type { AuthObject, SignedInAuthObject, SignedOutAuthObject } from '../tokens/authObjects';\n\ntype DecorateAuthWithResourcesOptions = {\n  loadSession?: boolean;\n  loadUser?: boolean;\n  loadOrganization?: boolean;\n};\n\ntype WithResources<T> = T & {\n  session?: Session | null;\n  user?: User | null;\n  organization?: Organization | null;\n};\n\n/**\n * @internal\n */\nexport const decorateObjectWithResources = async <T extends object>(\n  obj: T,\n  authObj: AuthObject,\n  opts: CreateBackendApiOptions & DecorateAuthWithResourcesOptions,\n): Promise<WithResources<T>> => {\n  const { loadSession, loadUser, loadOrganization } = opts || {};\n  const { userId, sessionId, orgId } = authObj as SignedInAuthObject | SignedOutAuthObject;\n\n  const { sessions, users, organizations } = createBackendApiClient({ ...opts });\n\n  const [sessionResp, userResp, organizationResp] = await Promise.all([\n    loadSession && sessionId ? sessions.getSession(sessionId) : Promise.resolve(undefined),\n    loadUser && userId ? users.getUser(userId) : Promise.resolve(undefined),\n    loadOrganization && orgId ? organizations.getOrganization({ organizationId: orgId }) : Promise.resolve(undefined),\n  ]);\n\n  const resources = stripPrivateDataFromObject({\n    session: sessionResp,\n    user: userResp,\n    organization: organizationResp,\n  });\n  return Object.assign(obj, resources);\n};\n\n/**\n * @internal\n */\nexport function stripPrivateDataFromObject<T extends WithResources<object>>(authObject: T): T {\n  const user = authObject.user ? { ...authObject.user } : authObject.user;\n  const organization = authObject.organization ? { ...authObject.organization } : authObject.organization;\n  prunePrivateMetadata(user);\n  prunePrivateMetadata(organization);\n  return { ...authObject, user, organization };\n}\n\nfunction prunePrivateMetadata(resource?: { private_metadata: any } | { privateMetadata: any } | null) {\n  // Delete sensitive private metadata from resource before rendering in SSR\n  if (resource) {\n    if ('privateMetadata' in resource) {\n      delete resource['privateMetadata'];\n    }\n    if ('private_metadata' in resource) {\n      delete resource['private_metadata'];\n    }\n  }\n\n  return resource;\n}\n", "export { constants } from './constants';\nexport { createRedirect } from './createRedirect';\nexport type { RedirectFun } from './createRedirect';\n\nexport type { CreateAuthenticateRequestOptions } from './tokens/factory';\nexport { createAuthenticateRequest } from './tokens/factory';\n\nexport { debugRequestState } from './tokens/request';\n\nexport type {\n  AuthenticateRequestOptions,\n  OrganizationSyncOptions,\n  InferAuthObjectFromToken,\n  InferAuthObjectFromTokenArray,\n  GetAuthFn,\n} from './tokens/types';\n\nexport { TokenType } from './tokens/tokenTypes';\nexport type { SessionTokenType, MachineTokenType } from './tokens/tokenTypes';\n\nexport type {\n  SignedInAuthObjectOptions,\n  SignedInAuthObject,\n  SignedOutAuthObject,\n  AuthenticatedMachineObject,\n  UnauthenticatedMachineObject,\n} from './tokens/authObjects';\nexport {\n  makeAuthObjectSerializable,\n  signedOutAuthObject,\n  signedInAuthObject,\n  authenticatedMachineObject,\n  unauthenticatedMachineObject,\n  invalidTokenAuthObject,\n  getAuthObjectFromJwt,\n  getAuthObjectForAcceptedToken,\n} from './tokens/authObjects';\n\nexport { AuthStatus } from './tokens/authStatus';\nexport type {\n  RequestState,\n  SignedInState,\n  SignedOutState,\n  AuthenticatedState,\n  UnauthenticatedState,\n} from './tokens/authStatus';\n\nexport { decorateObjectWithResources, stripPrivateDataFromObject } from './util/decorateObjectWithResources';\n\nexport { createClerkRequest } from './tokens/clerkRequest';\nexport type { ClerkRequest } from './tokens/clerkRequest';\n\nexport { reverificationError, reverificationErrorResponse } from '@clerk/shared/authorization-errors';\n\nexport { verifyMachineAuthToken } from './tokens/verify';\n\nexport { isMachineTokenByPrefix, isMachineTokenType, getMachineTokenType, isTokenTypeAccepted } from './tokens/machine';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,IAAM,UAAU;AAChB,IAAM,cAAc;AAEpB,IAAM,aAAa,GAAG,gBAAY,IAAI,OAAe;AACrD,IAAM,oCAAoC,IAAI;AAC9C,IAAM,yBAAyB;AAEtC,IAAM,aAAa;AAAA,EACjB,WAAW;AAAA,EACX,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,UAAU;AACZ;AAEA,IAAM,UAAU;AAAA,EACd,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,gBAAgB;AAClB;AAEA,IAAM,kBAAkB;AAAA,EACtB,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,YAAY,QAAQ;AAAA,EACpB,WAAW,QAAQ;AAAA,EACnB,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,gBAAgB,QAAQ;AAC1B;AAEA,IAAMA,WAAU;AAAA,EACd,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,UAAU;AAAA,EACV,0BAA0B;AAAA,EAC1B,aAAa;AAAA,EACb,uBAAuB;AAAA,EACvB,iCAAiC;AAAA,EACjC,aAAa;AAAA,EACb,eAAe;AAAA,EACf,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,MAAM;AAAA,EACN,UAAU;AAAA,EACV,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,cAAc;AAAA,EACd,WAAW;AAAA,EACX,oBAAoB;AACtB;AAEA,IAAM,eAAe;AAAA,EACnB,MAAM;AACR;AAKO,IAAM,YAAY;AAAA,EACvB;AAAA,EACA;AAAA,EACA,SAAAA;AAAA,EACA;AAAA,EACA;AACF;;;AClFA,SAAS,4BAA4B;AAMrC,IAAM,WAAW,CACf,UACA,YACA,gBACA,qBACG;AACH,MAAI,aAAa,IAAI;AACnB,WAAO,eAAe,WAAW,SAAS,GAAG,gBAAgB,SAAS,CAAC;AAAA,EACzE;AAEA,QAAM,UAAU,IAAI,IAAI,QAAQ;AAChC,QAAM,gBAAgB,iBAAiB,IAAI,IAAI,gBAAgB,OAAO,IAAI;AAC1E,QAAM,MAAM,IAAI,IAAI,YAAY,OAAO;AAEvC,MAAI,eAAe;AACjB,QAAI,aAAa,IAAI,gBAAgB,cAAc,SAAS,CAAC;AAAA,EAC/D;AAEA,MAAI,oBAAoB,QAAQ,aAAa,IAAI,UAAU;AACzD,QAAI,aAAa,IAAI,UAAU,gBAAgB,YAAY,gBAAgB;AAAA,EAC7E;AACA,SAAO,IAAI,SAAS;AACtB;AAWA,IAAM,iBAAiB,CAAC,WAAmB,gBAAyB;AAClE,MAAI;AACJ,MAAI,CAAC,UAAU,WAAW,MAAM,GAAG;AACjC,QAAI,CAAC,eAAe,CAAC,YAAY,WAAW,MAAM,GAAG;AACnD,YAAM,IAAI,MAAM,oEAAoE;AAAA,IACtF;AAEA,UAAM,UAAU,IAAI,IAAI,WAAW;AACnC,UAAM,IAAI,IAAI,WAAW,QAAQ,MAAM;AAAA,EACzC,OAAO;AACL,UAAM,IAAI,IAAI,SAAS;AAAA,EACzB;AAEA,MAAI,aAAa;AACf,QAAI,aAAa,IAAI,gBAAgB,WAAW;AAAA,EAClD;AAEA,SAAO,IAAI,SAAS;AACtB;AAsBO,IAAM,iBAAiC,YAAU;AACtD,QAAM,EAAE,gBAAgB,iBAAiB,WAAW,WAAW,SAAS,cAAc,IAAI;AAC1F,QAAM,uBAAuB,oBAAoB,cAAc;AAC/D,QAAM,cAAc,sBAAsB;AAC1C,QAAM,gBAAgB,sBAAsB,iBAAiB;AAC7D,QAAM,kBAAkB,qBAAqB,WAAW;AACxD,QAAM,mBAAmB,kBAAkB;AAE3C,QAAM,kBAAkB,CAAC,KAAmB,EAAE,cAAc,MAAwB;AAClF,WAAO;AAAA,MACL,SAAS,SAAS,GAAG,GAAG,UAAU,eAAe,gBAAgB,OAAO,kBAAkB,IAAI;AAAA,IAChG;AAAA,EACF;AAEA,QAAM,mBAAmB,CAAC,EAAE,cAAc,IAAsB,CAAC,MAAM;AACrE,QAAI,CAAC,aAAa,CAAC,iBAAiB;AAClC,mBAAa,gCAAgC;AAAA,IAC/C;AAEA,UAAM,oBAAoB,GAAG,eAAe;AAG5C,aAAS,eAAe,QAAkC;AACxD,UAAI,CAAC,QAAQ;AACX;AAAA,MACF;AACA,YAAM,MAAM,IAAI,IAAI,QAAQ,OAAO;AACnC,UAAI,WAAW,GAAG,IAAI,QAAQ;AAC9B,aAAO,IAAI,SAAS;AAAA,IACtB;AAEA,UAAM,YAAY,aAAa,eAAe,SAAS,KAAK;AAE5D,QAAI,kBAAkB;AACpB,aAAO,gBAAgB,WAAW,EAAE,cAAc,CAAC;AAAA,IACrD;AAEA,WAAO,gBAAgB,SAAS,SAAS,WAAW,eAAe,gBAAgB,OAAO,kBAAkB,IAAI,CAAC;AAAA,EACnH;AAEA,QAAM,mBAAmB,CAAC,EAAE,cAAc,IAAsB,CAAC,MAAM;AACrE,QAAI,CAAC,aAAa,CAAC,iBAAiB;AAClC,mBAAa,gCAAgC;AAAA,IAC/C;AAEA,UAAM,oBAAoB,GAAG,eAAe;AAC5C,UAAM,YAAY,aAAa;AAE/B,QAAI,kBAAkB;AACpB,aAAO,gBAAgB,WAAW,EAAE,cAAc,CAAC;AAAA,IACrD;AAEA,WAAO,gBAAgB,SAAS,SAAS,WAAW,eAAe,gBAAgB,OAAO,kBAAkB,IAAI,CAAC;AAAA,EACnH;AAEA,SAAO,EAAE,kBAAkB,iBAAiB;AAC9C;;;ACvIO,SAAS,uBAAsD,mBAAsB,SAAwB;AAClH,SAAO,OAAO,KAAK,iBAAiB,EAAE;AAAA,IACpC,CAAC,KAAQ,QAAgB;AACvB,aAAO,EAAE,GAAG,KAAK,CAAC,GAAG,GAAG,QAAQ,GAAG,KAAK,IAAI,GAAG,EAAE;AAAA,IACnD;AAAA,IACA,EAAE,GAAG,kBAAkB;AAAA,EACzB;AACF;;;ACLO,SAAS,qBAAqB,KAAqC;AACxE,MAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;AACnC,UAAM,MAAM,iGAAiG;AAAA,EAC/G;AAGF;AAEO,SAAS,0BAA0B,KAAqC;AAC7E,sBAAoB,KAA2B,EAAE,OAAO,KAAK,CAAC;AAChE;;;ACiCA,IAAM,sBAAN,MAAyD;AAAA,EAUhD,YACG,cACA,cACR,SACA;AAHQ;AACA;AAMR,SAAK,yBAAyB,OAAO;AACrC,SAAK,iBAAiB;AAEtB,SAAK,iBAAiB;AACtB,SAAK,oBAAoB;AACzB,WAAO,OAAO,MAAM,OAAO;AAC3B,SAAK,WAAW,KAAK,aAAa;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAnBA,IAAW,eAAmC;AAC5C,WAAO,KAAK,wBAAwB,KAAK;AAAA,EAC3C;AAAA,EAmBO,sBAA+B;AACpC,UAAM,oBAAoB,KAAK,kBAAkB,UAAU,QAAQ,SAAS;AAC5E,UAAM,YAAY,KAAK,UAAU,UAAU,QAAQ,SAAS;AAC5D,UAAM,kBAAkB,KAAK,kBAAkB,UAAU,QAAQ,OAAO,KAAK;AAC7E,UAAM,UAAU,KAAK,UAAU,UAAU,QAAQ,OAAO,KAAK;AAK7D,QAAI,WAAW,CAAC,KAAK,eAAe,OAAO,GAAG;AAC5C,aAAO;AAAA,IACT;AAIA,QAAI,WAAW,CAAC,KAAK,uBAAuB,OAAO,GAAG;AACpD,aAAO;AAAA,IACT;AAGA,QAAI,CAAC,qBAAqB,CAAC,iBAAiB;AAC1C,aAAO;AAAA,IACT;AAEA,UAAM,EAAE,MAAM,YAAY,IAAI,UAAU,OAAO;AAC/C,UAAM,aAAa,aAAa,QAAQ,OAAO;AAC/C,UAAM,EAAE,MAAM,oBAAoB,IAAI,UAAU,eAAe;AAC/D,UAAM,qBAAqB,qBAAqB,QAAQ,OAAO;AAI/D,QAAI,sBAAsB,OAAO,cAAc,OAAO,aAAa,oBAAoB;AACrF,aAAO;AAAA,IACT;AAKA,QAAI,sBAAsB,OAAO,cAAc,KAAK;AAClD,aAAO;AAAA,IACT;AA+BA,QAAI,KAAK,iBAAiB,cAAc;AACtC,YAAM,2BAA2B,KAAK,eAAe,mBAAmB;AACxE,UAAI,sBAAsB,OAAO,cAAc,OAAO,0BAA0B;AAC9E,eAAO;AAAA,MACT;AAAA,IACF;AAMA,QAAI,CAAC,qBAAqB,iBAAiB;AACzC,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAAA,EAEQ,yBAAyB,SAAqC;AACpE,8BAA0B,QAAQ,cAAc;AAChD,SAAK,iBAAiB,QAAQ;AAE9B,UAAM,KAAK,oBAAoB,KAAK,gBAAgB;AAAA,MAClD,OAAO;AAAA,MACP,UAAU,QAAQ;AAAA,MAClB,QAAQ,QAAQ;AAAA,MAChB,aAAa,QAAQ;AAAA,IACvB,CAAC;AACD,SAAK,eAAe,GAAG;AACvB,SAAK,cAAc,GAAG;AAAA,EACxB;AAAA,EAEQ,mBAAmB;AACzB,SAAK,gBAAgB,KAAK,yBAAyB,KAAK,UAAU,UAAU,QAAQ,aAAa,CAAC;AAClG,SAAK,SAAS,KAAK,UAAU,UAAU,QAAQ,MAAM;AACrD,SAAK,OAAO,KAAK,UAAU,UAAU,QAAQ,IAAI;AACjD,SAAK,gBAAgB,KAAK,UAAU,UAAU,QAAQ,aAAa;AACnE,SAAK,iBACH,KAAK,UAAU,UAAU,QAAQ,wBAAwB,KAAK,KAAK,UAAU,UAAU,QAAQ,cAAc;AAC/G,SAAK,WAAW,KAAK,UAAU,UAAU,QAAQ,QAAQ;AACzD,SAAK,YAAY,KAAK,UAAU,UAAU,QAAQ,SAAS;AAC3D,SAAK,eAAe,KAAK,UAAU,UAAU,QAAQ,YAAY;AACjE,SAAK,SAAS,KAAK,UAAU,UAAU,QAAQ,MAAM;AAAA,EACvD;AAAA,EAEQ,mBAAmB;AAEzB,SAAK,uBAAuB,KAAK,8BAA8B,UAAU,QAAQ,OAAO;AACxF,SAAK,uBAAuB,KAAK,kBAAkB,UAAU,QAAQ,OAAO;AAC5E,SAAK,YAAY,OAAO,SAAS,KAAK,8BAA8B,UAAU,QAAQ,SAAS,KAAK,EAAE,KAAK;AAAA,EAC7G;AAAA,EAEQ,sBAAsB;AAC5B,SAAK,kBACH,KAAK,cAAc,UAAU,gBAAgB,UAAU,KACvD,KAAK,8BAA8B,UAAU,QAAQ,UAAU;AAEjE,SAAK,iBACH,KAAK,cAAc,UAAU,gBAAgB,SAAS,KAAK,KAAK,UAAU,UAAU,QAAQ,SAAS;AACvG,SAAK,+BAA+B,OAAO,KAAK,UAAU,UAAU,QAAQ,aAAa,CAAC,KAAK;AAC/F,SAAK,iBACH,KAAK,cAAc,UAAU,gBAAgB,cAAc,KAAK,KAAK,UAAU,UAAU,QAAQ,cAAc;AAAA,EACnH;AAAA,EAEQ,cAAc,MAAc;AAClC,WAAO,KAAK,aAAa,SAAS,aAAa,IAAI,IAAI;AAAA,EACzD;AAAA,EAEQ,UAAU,MAAc;AAC9B,WAAO,KAAK,aAAa,QAAQ,IAAI,IAAI,KAAK;AAAA,EAChD;AAAA,EAEQ,UAAU,MAAc;AAC9B,WAAO,KAAK,aAAa,QAAQ,IAAI,IAAI,KAAK;AAAA,EAChD;AAAA,EAEQ,kBAAkB,MAAc;AACtC,WAAO,KAAK,UAAU,sBAAsB,MAAM,KAAK,YAAY,CAAC,KAAK;AAAA,EAC3E;AAAA,EAEQ,8BAA8B,YAAoB;AACxD,QAAI,KAAK,oBAAoB,GAAG;AAC9B,aAAO,KAAK,kBAAkB,UAAU;AAAA,IAC1C;AACA,WAAO,KAAK,UAAU,UAAU;AAAA,EAClC;AAAA,EAEQ,yBAAyB,qBAAoE;AACnG,QAAI,CAAC,qBAAqB;AACxB,aAAO;AAAA,IACT;AAEA,UAAM,CAAC,QAAQ,KAAK,IAAI,oBAAoB,MAAM,KAAK,CAAC;AAExD,QAAI,CAAC,OAAO;AAEV,aAAO;AAAA,IACT;AAEA,QAAI,WAAW,UAAU;AACvB,aAAO;AAAA,IACT;AAGA,WAAO;AAAA,EACT;AAAA,EAEQ,eAAe,OAAwB;AAC7C,UAAM,EAAE,MAAM,OAAO,IAAI,UAAU,KAAK;AACxC,QAAI,QAAQ;AACV,aAAO;AAAA,IACT;AACA,WAAO,CAAC,CAAC,KAAK,QAAQ;AAAA,EACxB;AAAA,EAEQ,uBAAuB,OAAwB;AACrD,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,IACT;AAEA,UAAM,EAAE,MAAM,OAAO,IAAI,UAAU,KAAK;AACxC,QAAI,QAAQ;AACV,aAAO;AAAA,IACT;AACA,UAAM,cAAc,KAAK,QAAQ,IAAI,QAAQ,iBAAiB,EAAE;AAChE,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA,EAEQ,eAAe,KAA+B;AACpD,WAAO,CAAC,CAAC,OAAO,KAAK,QAAQ,OAAQ,KAAK,IAAI,IAAI,OAAS;AAAA,EAC7D;AACF;AAIO,IAAM,4BAA4B,OACvC,cACA,YACiC;AACjC,QAAM,eAAe,QAAQ,iBACzB,MAAM,gBAAgB,QAAQ,gBAAgB,QAAQ,OAAO,MAAM,IACnE;AACJ,SAAO,IAAI,oBAAoB,cAAc,cAAc,OAAO;AACpE;;;AC9RA,SAAS,gCAAgC;AACzC,SAAS,uDAAuD;;;ACDhE,IAAM,YAAY;AAClB,IAAM,2BAA2B,IAAI,OAAO,WAAW,YAAY,QAAQ,GAAG;AAIvE,SAAS,aAAa,MAA4B;AACvD,SAAO,KACJ,OAAO,OAAK,CAAC,EACb,KAAK,SAAS,EACd,QAAQ,0BAA0B,SAAS;AAChD;;;ACRO,IAAe,cAAf,MAA2B;AAAA,EAChC,YAAsB,SAA0B;AAA1B;AAAA,EAA2B;AAAA,EAEvC,UAAU,IAAY;AAC9B,QAAI,CAAC,IAAI;AACP,YAAM,IAAI,MAAM,kCAAkC;AAAA,IACpD;AAAA,EACF;AACF;;;ACNA,IAAM,WAAW;AAyCV,IAAM,gBAAN,cAA4B,YAAY;AAAA,EAC7C,MAAa,OAAO,QAAgC;AAClD,WAAO,KAAK,QAAoB;AAAA,MAC9B,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,OAAO,cAAsB;AACxC,SAAK,UAAU,YAAY;AAC3B,WAAO,KAAK,QAAoB;AAAA,MAC9B,QAAQ;AAAA,MACR,MAAM,UAAU,UAAU,cAAc,QAAQ;AAAA,IAClD,CAAC;AAAA,EACH;AACF;;;ACzDA,IAAMC,YAAW;AAEV,IAAM,4BAAN,cAAwC,YAAY;AAAA,EACzD,MAAa,+BAA+B;AAC1C,WAAO,KAAK,QAAgC;AAAA,MAC1C,QAAQ;AAAA,MACR,MAAMA;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,2CAA2C;AACtD,WAAO,KAAK,QAAgC;AAAA,MAC1C,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,UAAU;AAAA,IACtC,CAAC;AAAA,EACH;AACF;;;ACZA,IAAMC,YAAW;AAOV,IAAM,yBAAN,cAAqC,YAAY;AAAA,EACtD,MAAa,2BAA2B,SAAiC,CAAC,GAAG;AAC3E,WAAO,KAAK,QAA0D;AAAA,MACpE,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,aAAa,EAAE,GAAG,QAAQ,WAAW,KAAK;AAAA,IAC5C,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,0BAA0B,QAAyC;AAC9E,WAAO,KAAK,QAA6B;AAAA,MACvC,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,0BAA0B,uBAA+B;AACpE,SAAK,UAAU,qBAAqB;AACpC,WAAO,KAAK,QAAuB;AAAA,MACjC,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,qBAAqB;AAAA,IACjD,CAAC;AAAA,EACH;AACF;;;ACnCA,IAAMC,YAAW;AAEV,IAAM,aAAN,cAAyB,YAAY;AAAA,EAC1C,MAAM,aAAa,QAAgB;AACjC,WAAO,KAAK,QAAgB;AAAA,MAC1B,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,QAAQ;AAAA,MAClC,YAAY,EAAE,OAAO;AAAA,IACvB,CAAC;AAAA,EACH;AACF;;;ACXA,IAAMC,YAAW;AAgBV,IAAM,kBAAN,cAA8B,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAW/C,MAAa,aAAa,QAA4B;AACpD,WAAO,KAAK,QAAc;AAAA,MACxB,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,eAAe;AAAA,MACzC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;;;AC7BA,IAAMC,YAAW;AAMV,IAAM,yBAAN,cAAqC,YAAY;AAAA,EACtD,MAAa,2BAA2B,SAAiC,CAAC,GAAG;AAC3E,WAAO,KAAK,QAA0D;AAAA,MACpE,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,aAAa;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,0BAA0B,QAAyC;AAC9E,WAAO,KAAK,QAA6B;AAAA,MACvC,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,0BAA0B,uBAA+B;AACpE,SAAK,UAAU,qBAAqB;AACpC,WAAO,KAAK,QAAuB;AAAA,MACjC,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,qBAAqB;AAAA,IACjD,CAAC;AAAA,EACH;AACF;;;AC9BA,IAAMC,YAAW;AAMV,IAAM,YAAN,cAAwB,YAAY;AAAA,EACzC,MAAa,cAAc,SAAiC,CAAC,GAAG;AAC9D,WAAO,KAAK,QAA6C;AAAA,MACvD,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,aAAa,EAAE,GAAG,QAAQ,WAAW,KAAK;AAAA,IAC5C,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,UAAU,UAAkB;AACvC,SAAK,UAAU,QAAQ;AACvB,WAAO,KAAK,QAAgB;AAAA,MAC1B,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,QAAQ;AAAA,IACpC,CAAC;AAAA,EACH;AAAA,EAEO,aAAa,OAAe;AACjC,WAAO,KAAK,QAAgB;AAAA,MAC1B,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,QAAQ;AAAA,MAClC,YAAY,EAAE,MAAM;AAAA,IACtB,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,oBAAoB,aAAwC;AACvE,WAAO,KAAK,QAA0B;AAAA,MACpC,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,mBAAmB;AAAA,MAC7C;AAAA,IACF,CAAC;AAAA,EACH;AACF;;;ACxCA,IAAMC,YAAW;AA8BV,IAAM,YAAN,cAAwB,YAAY;AAAA,EACzC,MAAa,OAAO;AAClB,WAAO,KAAK,QAA6C;AAAA,MACvD,QAAQ;AAAA,MACR,MAAMA;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,IAAI,QAAyB;AACxC,WAAO,KAAK,QAAgB;AAAA,MAC1B,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,OAAO,QAA4B;AAC9C,UAAM,EAAE,UAAU,GAAG,WAAW,IAAI;AAEpC,SAAK,UAAU,QAAQ;AAEvB,WAAO,KAAK,QAAgB;AAAA,MAC1B,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,QAAQ;AAAA,MAClC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAa,OAAO,mBAA2B;AAC7C,WAAO,KAAK,aAAa,iBAAiB;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAKA,MAAa,aAAa,mBAA2B;AACnD,SAAK,UAAU,iBAAiB;AAChC,WAAO,KAAK,QAAuB;AAAA,MACjC,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,iBAAiB;AAAA,IAC7C,CAAC;AAAA,EACH;AACF;;;AC9EA,IAAMC,YAAW;AAcV,IAAM,kBAAN,cAA8B,YAAY;AAAA,EAC/C,MAAa,gBAAgB,gBAAwB;AACnD,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,cAAc;AAAA,IAC1C,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,mBAAmB,QAAkC;AAChE,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,mBAAmB,gBAAwB,SAAmC,CAAC,GAAG;AAC7F,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,cAAc;AAAA,MACxC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,mBAAmB,gBAAwB;AACtD,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAuB;AAAA,MACjC,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,cAAc;AAAA,IAC1C,CAAC;AAAA,EACH;AACF;;;AClDA,IAAMC,aAAW;AAEV,IAAM,yBAAN,cAAqC,YAAY;AAAA,EACtD,MAAM,kBAAkB,aAAqB;AAC3C,WAAO,KAAK,QAA6B;AAAA,MACvC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ;AAAA,MAClC,YAAY,EAAE,cAAc,YAAY;AAAA,IAC1C,CAAC;AAAA,EACH;AACF;;;ACRA,IAAMC,aAAW;AA+DV,IAAM,cAAN,cAA0B,YAAY;AAAA,EAC3C,MAAa,MAAM;AACjB,WAAO,KAAK,QAAkB;AAAA,MAC5B,QAAQ;AAAA,MACR,MAAMA;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,OAAO,QAAsB;AACxC,WAAO,KAAK,QAAc;AAAA,MACxB,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,mBAAmB,QAAkC;AAChE,WAAO,KAAK,QAA8B;AAAA,MACxC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,cAAc;AAAA,MACxC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,2BAA2B,QAA0C;AAChF,WAAO,KAAK,QAA8B;AAAA,MACxC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,uBAAuB;AAAA,MACjD,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;;;AC5FA,IAAMC,aAAW;AAqCV,IAAM,gBAAN,cAA4B,YAAY;AAAA,EAC7C,MAAa,kBAAkB,SAAkC,CAAC,GAAG;AACnE,WAAO,KAAK,QAAiD;AAAA,MAC3D,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,aAAa,EAAE,GAAG,QAAQ,WAAW,KAAK;AAAA,IAC5C,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,iBAAiB,QAAsB;AAClD,WAAO,KAAK,QAAoB;AAAA,MAC9B,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,iBAAiB,cAAsB;AAClD,SAAK,UAAU,YAAY;AAC3B,WAAO,KAAK,QAAoB;AAAA,MAC9B,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,cAAc,QAAQ;AAAA,IAClD,CAAC;AAAA,EACH;AACF;;;ACjEA,IAAMC,aAAW;AAEV,IAAM,mBAAN,cAA+B,YAAY;AAAA,EAChD,MAAM,aAAa,QAAgB;AACjC,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ;AAAA,MAClC,YAAY,EAAE,OAAO;AAAA,IACvB,CAAC;AAAA,EACH;AACF;;;ACXA,IAAMC,aAAW;AAEV,IAAM,UAAN,cAAsB,YAAY;AAAA,EACvC,MAAa,UAAU;AACrB,WAAO,KAAK,QAAkB;AAAA,MAC5B,QAAQ;AAAA,MACR,MAAMA;AAAA,IACR,CAAC;AAAA,EACH;AACF;;;ACNA,IAAMC,aAAW;AA0CV,IAAM,kBAAN,cAA8B,YAAY;AAAA,EAC/C,MAAa,KAAK,SAAiC,CAAC,GAAG;AACrD,WAAO,KAAK,QAAuB;AAAA,MACjC,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,aAAa,EAAE,GAAG,QAAQ,WAAW,KAAK;AAAA,IAC5C,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,IAAI,YAAoB;AACnC,SAAK,UAAU,UAAU;AAEzB,WAAO,KAAK,QAAqB;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,UAAU;AAAA,IACtC,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,OAAO,QAAiC;AACnD,WAAO,KAAK,QAAqB;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,OAAO,QAAiC;AACnD,UAAM,EAAE,YAAY,GAAG,WAAW,IAAI;AAEtC,SAAK,UAAU,UAAU;AACzB,WAAO,KAAK,QAAqB;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,UAAU;AAAA,MACpC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,OAAO,YAAoB;AACtC,SAAK,UAAU,UAAU;AAEzB,WAAO,KAAK,QAAuB;AAAA,MACjC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,UAAU;AAAA,IACtC,CAAC;AAAA,EACH;AACF;;;AC7EA,IAAMC,aAAW;AA4MV,IAAM,kBAAN,cAA8B,YAAY;AAAA,EAC/C,MAAa,oBAAoB,QAAoC;AACnE,WAAO,KAAK,QAAmD;AAAA,MAC7D,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,aAAa;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,mBAAmB,QAAsB;AACpD,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,gBAAgB,QAA+B;AAC1D,UAAM,EAAE,oBAAoB,IAAI;AAChC,UAAM,uBAAuB,oBAAoB,SAAS,OAAO,iBAAiB,OAAO;AACzF,SAAK,UAAU,oBAAoB;AAEnC,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,oBAAoB;AAAA,MAC9C,aAAa;AAAA,QACX;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,mBAAmB,gBAAwB,QAAsB;AAC5E,SAAK,UAAU,cAAc;AAC7B,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,cAAc;AAAA,MACxC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,uBAAuB,gBAAwB,QAA0B;AACpF,SAAK,UAAU,cAAc;AAE7B,UAAM,WAAW,IAAI,QAAQ,SAAS;AACtC,aAAS,OAAO,QAAQ,QAAQ,IAAI;AACpC,QAAI,QAAQ,gBAAgB;AAC1B,eAAS,OAAO,oBAAoB,QAAQ,cAAc;AAAA,IAC5D;AAEA,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,gBAAgB,MAAM;AAAA,MAChD;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,uBAAuB,gBAAwB;AAC1D,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,gBAAgB,MAAM;AAAA,IAClD,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,2BAA2B,gBAAwB,QAA8B;AAC5F,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,gBAAgB,UAAU;AAAA,MACpD,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,mBAAmB,gBAAwB;AACtD,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,cAAc;AAAA,IAC1C,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,8BAA8B,QAA6C;AACtF,UAAM,EAAE,gBAAgB,GAAG,YAAY,IAAI;AAC3C,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAA6D;AAAA,MACvE,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,gBAAgB,aAAa;AAAA,MACvD;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,sCAAsC,QAAqD;AACtG,WAAO,KAAK,QAA6D;AAAA,MACvE,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,aAAa;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,6BAA6B,QAA4C;AACpF,UAAM,EAAE,gBAAgB,GAAG,WAAW,IAAI;AAC1C,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAgC;AAAA,MAC1C,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,gBAAgB,aAAa;AAAA,MACvD;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,6BAA6B,QAA4C;AACpF,UAAM,EAAE,gBAAgB,QAAQ,GAAG,WAAW,IAAI;AAClD,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAgC;AAAA,MAC1C,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,gBAAgB,eAAe,MAAM;AAAA,MAC/D;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,qCAAqC,QAAoD;AACpG,UAAM,EAAE,gBAAgB,QAAQ,GAAG,WAAW,IAAI;AAElD,WAAO,KAAK,QAAgC;AAAA,MAC1C,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,gBAAgB,eAAe,QAAQ,UAAU;AAAA,MAC3E;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,6BAA6B,QAA4C;AACpF,UAAM,EAAE,gBAAgB,OAAO,IAAI;AACnC,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAgC;AAAA,MAC1C,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,gBAAgB,eAAe,MAAM;AAAA,IACjE,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,8BAA8B,QAA6C;AACtF,UAAM,EAAE,gBAAgB,GAAG,YAAY,IAAI;AAC3C,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAA6D;AAAA,MACvE,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,gBAAgB,aAAa;AAAA,MACvD;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,6BAA6B,QAA4C;AACpF,UAAM,EAAE,gBAAgB,GAAG,WAAW,IAAI;AAC1C,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAgC;AAAA,MAC1C,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,gBAAgB,aAAa;AAAA,MACvD;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,iCACX,gBACA,QACA;AACA,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAkC;AAAA,MAC5C,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,gBAAgB,eAAe,MAAM;AAAA,MAC/D,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,0BAA0B,QAAyC;AAC9E,UAAM,EAAE,gBAAgB,aAAa,IAAI;AACzC,SAAK,UAAU,cAAc;AAC7B,SAAK,UAAU,YAAY;AAE3B,WAAO,KAAK,QAAgC;AAAA,MAC1C,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,gBAAgB,eAAe,YAAY;AAAA,IACvE,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,6BAA6B,QAA4C;AACpF,UAAM,EAAE,gBAAgB,cAAc,GAAG,WAAW,IAAI;AACxD,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAgC;AAAA,MAC1C,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,gBAAgB,eAAe,cAAc,QAAQ;AAAA,MAC/E;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,0BAA0B,QAAyC;AAC9E,UAAM,EAAE,gBAAgB,GAAG,YAAY,IAAI;AAC3C,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAyD;AAAA,MACnE,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,gBAAgB,SAAS;AAAA,MACnD;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,yBAAyB,QAAwC;AAC5E,UAAM,EAAE,gBAAgB,GAAG,WAAW,IAAI;AAC1C,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAA4B;AAAA,MACtC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,gBAAgB,SAAS;AAAA,MACnD,YAAY;AAAA,QACV,GAAG;AAAA,QACH,UAAU,WAAW,YAAY;AAAA,MACnC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,yBAAyB,QAAwC;AAC5E,UAAM,EAAE,gBAAgB,UAAU,GAAG,WAAW,IAAI;AACpD,SAAK,UAAU,cAAc;AAC7B,SAAK,UAAU,QAAQ;AAEvB,WAAO,KAAK,QAA4B;AAAA,MACtC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,gBAAgB,WAAW,QAAQ;AAAA,MAC7D;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,yBAAyB,QAAwC;AAC5E,UAAM,EAAE,gBAAgB,SAAS,IAAI;AACrC,SAAK,UAAU,cAAc;AAC7B,SAAK,UAAU,QAAQ;AAEvB,WAAO,KAAK,QAA4B;AAAA,MACtC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,gBAAgB,WAAW,QAAQ;AAAA,IAC/D,CAAC;AAAA,EACH;AACF;;;AC3cA,IAAMC,aAAW;AA8BV,IAAM,uBAAN,cAAmC,YAAY;AAAA,EACpD,MAAa,KAAK,SAAiC,CAAC,GAAG;AACrD,WAAO,KAAK,QAAuD;AAAA,MACjE,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,aAAa;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,IAAI,oBAA4B;AAC3C,SAAK,UAAU,kBAAkB;AAEjC,WAAO,KAAK,QAA0B;AAAA,MACpC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,kBAAkB;AAAA,IAC9C,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,OAAO,QAAsC;AACxD,WAAO,KAAK,QAA0B;AAAA,MACpC,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,OAAO,QAAsC;AACxD,UAAM,EAAE,oBAAoB,GAAG,WAAW,IAAI;AAE9C,SAAK,UAAU,kBAAkB;AAEjC,WAAO,KAAK,QAA0B;AAAA,MACpC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,kBAAkB;AAAA,MAC5C;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,OAAO,oBAA4B;AAC9C,SAAK,UAAU,kBAAkB;AAEjC,WAAO,KAAK,QAAuB;AAAA,MACjC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,kBAAkB;AAAA,IAC9C,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,aAAa,oBAA4B;AACpD,SAAK,UAAU,kBAAkB;AAEjC,WAAO,KAAK,QAA0B;AAAA,MACpC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,oBAAoB,eAAe;AAAA,IAC/D,CAAC;AAAA,EACH;AACF;;;ACzFA,IAAMC,aAAW;AAgBV,IAAM,iBAAN,cAA6B,YAAY;AAAA,EAC9C,MAAa,eAAe,eAAuB;AACjD,SAAK,UAAU,aAAa;AAE5B,WAAO,KAAK,QAAqB;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,aAAa;AAAA,IACzC,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,kBAAkB,QAAiC;AAC9D,WAAO,KAAK,QAAqB;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,kBAAkB,eAAuB,SAAkC,CAAC,GAAG;AAC1F,SAAK,UAAU,aAAa;AAE5B,WAAO,KAAK,QAAqB;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,aAAa;AAAA,MACvC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,kBAAkB,eAAuB;AACpD,SAAK,UAAU,aAAa;AAE5B,WAAO,KAAK,QAAuB;AAAA,MACjC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,aAAa;AAAA,IACzC,CAAC;AAAA,EACH;AACF;;;ACrDA,IAAMC,aAAW;AAOV,IAAM,gBAAN,cAA4B,YAAY;AAAA,EAC7C,MAAa,OAAO,QAAsB;AACxC,WAAO,KAAK,QAAoB;AAAA,MAC9B,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;;;ACbA,IAAMC,aAAW;AAMV,IAAM,iBAAN,cAA6B,YAAY;AAAA,EAC9C,MAAa,qBAAqB;AAChC,WAAO,KAAK,QAAkD;AAAA,MAC5D,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,aAAa,EAAE,WAAW,KAAK;AAAA,IACjC,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,eAAe,eAAuB;AACjD,SAAK,UAAU,aAAa;AAC5B,WAAO,KAAK,QAAqB;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,aAAa;AAAA,IACzC,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,kBAAkB,QAAiC;AAC9D,WAAO,KAAK,QAAqB;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,kBAAkB,eAAuB;AACpD,SAAK,UAAU,aAAa;AAC5B,WAAO,KAAK,QAAqB;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,aAAa;AAAA,IACzC,CAAC;AAAA,EACH;AACF;;;ACrCA,IAAMC,aAAW;AA8CV,IAAM,oBAAN,cAAgC,YAAY;AAAA,EACjD,MAAa,sBAAsB,SAAmC,CAAC,GAAG;AACxE,WAAO,KAAK,QAA0B;AAAA,MACpC,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,aAAa;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,qBAAqB,QAAoC;AACpE,WAAO,KAAK,QAAwB;AAAA,MAClC,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,kBAAkB,kBAA0B;AACvD,SAAK,UAAU,gBAAgB;AAC/B,WAAO,KAAK,QAAwB;AAAA,MAClC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,gBAAgB;AAAA,IAC5C,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,qBAAqB,kBAA0B,SAAqC,CAAC,GAAG;AACnG,SAAK,UAAU,gBAAgB;AAE/B,WAAO,KAAK,QAAwB;AAAA,MAClC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,gBAAgB;AAAA,MAC1C,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EACA,MAAa,qBAAqB,kBAA0B;AAC1D,SAAK,UAAU,gBAAgB;AAC/B,WAAO,KAAK,QAAwB;AAAA,MAClC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,gBAAgB;AAAA,IAC5C,CAAC;AAAA,EACH;AACF;;;ACpFA,IAAMC,aAAW;AAsBV,IAAM,aAAN,cAAyB,YAAY;AAAA,EAC1C,MAAa,eAAe,SAA4B,CAAC,GAAG;AAC1D,WAAO,KAAK,QAA8C;AAAA,MACxD,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,aAAa,EAAE,GAAG,QAAQ,WAAW,KAAK;AAAA,IAC5C,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,WAAW,WAAmB;AACzC,SAAK,UAAU,SAAS;AACxB,WAAO,KAAK,QAAiB;AAAA,MAC3B,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,SAAS;AAAA,IACrC,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,cAAc,QAA6B;AACtD,WAAO,KAAK,QAAiB;AAAA,MAC3B,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,cAAc,WAAmB;AAC5C,SAAK,UAAU,SAAS;AACxB,WAAO,KAAK,QAAiB;AAAA,MAC3B,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,WAAW,QAAQ;AAAA,IAC/C,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,cAAc,WAAmB,OAAe;AAC3D,SAAK,UAAU,SAAS;AACxB,WAAO,KAAK,QAAiB;AAAA,MAC3B,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,WAAW,QAAQ;AAAA,MAC7C,YAAY,EAAE,MAAM;AAAA,IACtB,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,SAAS,WAAmB,UAAkB;AACzD,SAAK,UAAU,SAAS;AACxB,WAAO,KAAK,QAAe;AAAA,MACzB,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,WAAW,UAAU,YAAY,EAAE;AAAA,IAC/D,CAAC;AAAA,EACH;AAAA,EAKA,MAAa,eAAe,WAAmB,QAAsD;AACnG,SAAK,UAAU,SAAS;AACxB,UAAM,EAAE,kBAAkB,GAAG,WAAW,IAAI;AAC5C,WAAO,KAAK,QAAQ;AAAA,MAClB,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,WAAW,SAAS;AAAA,MAC9C,YAAY;AAAA,MACZ,aAAa,EAAE,iBAAiB;AAAA,IAClC,CAAC;AAAA,EACH;AACF;;;ACrFA,IAAMC,aAAW;AAEV,IAAM,iBAAN,cAA6B,YAAY;AAAA,EAC9C,MAAa,kBAAkB,QAAkC;AAC/D,WAAO,KAAK,QAAqB;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,kBAAkB,eAAuB;AACpD,SAAK,UAAU,aAAa;AAC5B,WAAO,KAAK,QAAqB;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,eAAe,QAAQ;AAAA,IACnD,CAAC;AAAA,EACH;AACF;;;ACjBA,IAAMC,aAAW;AAEV,IAAM,YAAN,cAAwB,YAAY;AAAA,EACzC,MAAa,IAAI,iBAAyB;AACxC,SAAK,UAAU,eAAe;AAE9B,WAAO,KAAK,QAAuB;AAAA,MACjC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,eAAe;AAAA,IAC3C,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,OAAO,QAA4B;AAC9C,UAAM,EAAE,iBAAiB,GAAG,WAAW,IAAI;AAE3C,WAAO,KAAK,QAAuB;AAAA,MACjC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,eAAe;AAAA,MACzC;AAAA,IACF,CAAC;AAAA,EACH;AACF;;;AC5BA,IAAMC,aAAW;AAEV,IAAM,kBAAN,cAA8B,YAAY;AAAA,EAC/C,MAAa,qBAAqB;AAChC,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAMA;AAAA,IACR,CAAC;AAAA,EACH;AACF;;;ACIA,IAAMC,aAAW;AAwLV,IAAM,UAAN,cAAsB,YAAY;AAAA,EACvC,MAAa,YAAY,SAAyB,CAAC,GAAG;AACpD,UAAM,EAAE,OAAO,QAAQ,SAAS,GAAG,gBAAgB,IAAI;AAIvD,UAAM,CAAC,MAAM,UAAU,IAAI,MAAM,QAAQ,IAAI;AAAA,MAC3C,KAAK,QAAgB;AAAA,QACnB,QAAQ;AAAA,QACR,MAAMA;AAAA,QACN,aAAa;AAAA,MACf,CAAC;AAAA,MACD,KAAK,SAAS,eAAe;AAAA,IAC/B,CAAC;AACD,WAAO,EAAE,MAAM,WAAW;AAAA,EAC5B;AAAA,EAEA,MAAa,QAAQ,QAAgB;AACnC,SAAK,UAAU,MAAM;AACrB,WAAO,KAAK,QAAc;AAAA,MACxB,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,MAAM;AAAA,IAClC,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,WAAW,QAA0B;AAChD,WAAO,KAAK,QAAc;AAAA,MACxB,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,WAAW,QAAgB,SAA2B,CAAC,GAAG;AACrE,SAAK,UAAU,MAAM;AAErB,WAAO,KAAK,QAAc;AAAA,MACxB,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,MAAM;AAAA,MAChC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,uBAAuB,QAAgB,QAA+B;AACjF,SAAK,UAAU,MAAM;AAErB,UAAM,WAAW,IAAI,QAAQ,SAAS;AACtC,aAAS,OAAO,QAAQ,QAAQ,IAAI;AAEpC,WAAO,KAAK,QAAc;AAAA,MACxB,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ,eAAe;AAAA,MACjD;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,mBAAmB,QAAgB,QAA4B;AAC1E,SAAK,UAAU,MAAM;AAErB,WAAO,KAAK,QAAc;AAAA,MACxB,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ,UAAU;AAAA,MAC5C,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,WAAW,QAAgB;AACtC,SAAK,UAAU,MAAM;AACrB,WAAO,KAAK,QAAc;AAAA,MACxB,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,MAAM;AAAA,IAClC,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,SAAS,SAA0B,CAAC,GAAG;AAClD,WAAO,KAAK,QAAgB;AAAA,MAC1B,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,OAAO;AAAA,MACjC,aAAa;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EAWA,MAAa,wBAAwB,QAAgB,UAAoD;AACvG,SAAK,UAAU,MAAM;AACrB,UAAM,YAAY,SAAS,WAAW,QAAQ;AAC9C,UAAM,YAAY,YAAY,WAAW,SAAS,QAAQ;AAE1D,QAAI,WAAW;AACb;AAAA,QACE;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAEA,WAAO,KAAK,QAAuD;AAAA,MACjE,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ,uBAAuB,SAAS;AAAA,MAClE,aAAa,EAAE,WAAW,KAAK;AAAA,IACjC,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,eAAe,QAAgB;AAC1C,SAAK,UAAU,MAAM;AACrB,WAAO,KAAK,QAAgB;AAAA,MAC1B,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ,KAAK;AAAA,IACzC,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,8BAA8B,QAA6C;AACtF,UAAM,EAAE,QAAQ,OAAO,OAAO,IAAI;AAClC,SAAK,UAAU,MAAM;AAErB,WAAO,KAAK,QAA6D;AAAA,MACvE,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ,0BAA0B;AAAA,MAC5D,aAAa,EAAE,OAAO,OAAO;AAAA,IAC/B,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,8BAA8B,QAA6C;AACtF,UAAM,EAAE,QAAQ,GAAG,YAAY,IAAI;AACnC,SAAK,UAAU,MAAM;AAErB,WAAO,KAAK,QAA6D;AAAA,MACvE,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ,0BAA0B;AAAA,MAC5D;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,eAAe,QAA8B;AACxD,UAAM,EAAE,QAAQ,SAAS,IAAI;AAC7B,SAAK,UAAU,MAAM;AAErB,WAAO,KAAK,QAA4B;AAAA,MACtC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ,iBAAiB;AAAA,MACnD,YAAY,EAAE,SAAS;AAAA,IACzB,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,WAAW,QAA0B;AAChD,UAAM,EAAE,QAAQ,KAAK,IAAI;AACzB,SAAK,UAAU,MAAM;AAErB,WAAO,KAAK,QAA+C;AAAA,MACzD,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ,aAAa;AAAA,MAC/C,YAAY,EAAE,KAAK;AAAA,IACrB,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,QAAQ,QAAgB;AACnC,SAAK,UAAU,MAAM;AACrB,WAAO,KAAK,QAAc;AAAA,MACxB,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ,KAAK;AAAA,IACzC,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,UAAU,QAAgB;AACrC,SAAK,UAAU,MAAM;AACrB,WAAO,KAAK,QAAc;AAAA,MACxB,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ,OAAO;AAAA,IAC3C,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,SAAS,QAAgB;AACpC,SAAK,UAAU,MAAM;AACrB,WAAO,KAAK,QAAc;AAAA,MACxB,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ,MAAM;AAAA,IAC1C,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,WAAW,QAAgB;AACtC,SAAK,UAAU,MAAM;AACrB,WAAO,KAAK,QAAc;AAAA,MACxB,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ,QAAQ;AAAA,IAC5C,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,uBAAuB,QAAgB;AAClD,SAAK,UAAU,MAAM;AACrB,WAAO,KAAK,QAAc;AAAA,MACxB,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ,eAAe;AAAA,IACnD,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,kBAAkB,QAAiC;AAC9D,SAAK,UAAU,OAAO,MAAM;AAC5B,SAAK,UAAU,OAAO,uBAAuB;AAC7C,WAAO,KAAK,QAAuB;AAAA,MACjC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,OAAO,QAAQ,YAAY,OAAO,uBAAuB;AAAA,IACrF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,qBAAqB,QAAgC;AAChE,SAAK,UAAU,OAAO,MAAM;AAC5B,SAAK,UAAU,OAAO,0BAA0B;AAChD,WAAO,KAAK,QAAuB;AAAA,MACjC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,OAAO,QAAQ,gBAAgB,OAAO,0BAA0B;AAAA,IAC5F,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,0BAA0B,QAAyC;AAC9E,SAAK,UAAU,OAAO,MAAM;AAC5B,SAAK,UAAU,OAAO,iBAAiB;AACvC,WAAO,KAAK,QAAuB;AAAA,MACjC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,OAAO,QAAQ,qBAAqB,OAAO,iBAAiB;AAAA,IACxF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,sBAAsB,QAAgB;AACjD,SAAK,UAAU,MAAM;AACrB,WAAO,KAAK,QAAgB;AAAA,MAC1B,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ,aAAa;AAAA,IACjD,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,eAAe,QAAgB;AAC1C,SAAK,UAAU,MAAM;AACrB,WAAO,KAAK,QAAgB;AAAA,MAC1B,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ,MAAM;AAAA,IAC1C,CAAC;AAAA,EACH;AACF;;;ACpbA,IAAMC,aAAW;AAgBV,IAAM,mBAAN,cAA+B,YAAY;AAAA,EAChD,MAAa,KAAK,SAAkC,CAAC,GAAG;AACtD,WAAO,KAAK,QAAkD;AAAA,MAC5D,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,aAAa;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,OAAO,QAAmC;AACrD,WAAO,KAAK,QAAuB;AAAA,MACjC,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;;;ACpCA,IAAMC,aAAW;AAEV,IAAM,aAAN,cAAyB,YAAY;AAAA,EAC1C,MAAa,gBAAgB;AAC3B,WAAO,KAAK,QAA0B;AAAA,MACpC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,MAAM;AAAA,IAClC,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,sBAAsB;AACjC,WAAO,KAAK,QAA0B;AAAA,MACpC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,UAAU;AAAA,IACtC,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,gBAAgB;AAC3B,WAAO,KAAK,QAAc;AAAA,MACxB,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,MAAM;AAAA,IAClC,CAAC;AAAA,EACH;AACF;;;AC3BA,SAAS,uBAAuB,kBAAkB;AAElD,OAAO,mBAAmB;;;ACAnB,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAClC,YACW,gBACA,WACA,UACA,YACT;AAJS;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAA0D;AACxE,WAAO,IAAI,wBAAuB,KAAK,iBAAiB,KAAK,YAAY,KAAK,WAAW,KAAK,YAAY;AAAA,EAC5G;AACF;;;ACVO,IAAM,aAAN,MAAM,YAAW;AAAA,EACtB,YACW,IACA,QACA,QACA,OACA,OACA,KACA,WACA,WACT;AARS;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAkC;AAChD,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;ACrBO,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EAC/B,YAIW,IAIA,YAIA,gBAIA,WAIA,WAIA,YAIA,cACT;AAzBS;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAoD;AAClE,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;AC/CO,IAAM,SAAN,MAAM,QAAO;AAAA,EAClB,YACW,IACA,MACA,MACA,SACA,QACA,QACA,SACA,kBACA,SACA,YACA,WACA,aACA,YACA,WACA,WACT;AAfS;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAkB;AAChC,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;ACrCO,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EAC/B,YACW,IACA,YACA,gBACA,WACA,WACA,YACT;AANS;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAoD;AAClE,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;AClBO,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EAC3B,YAIW,IAIA,UAIA,WAIA,MAIA,SAIA,gBAIA,aAIA,YACT;AA7BS;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAA4C;AAC1D,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;AAKO,IAAM,UAAN,MAAM,SAAQ;AAAA,EACnB,YAIW,IAIA,UAIA,QAIA,QAIA,cAIA,UAIA,WAIA,WAIA,WAIA,0BAIA,gBAIA,QAAwC,MACjD;AA7CS;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAA4B;AAC1C,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,mBAAmB,gBAAgB,SAAS,KAAK,eAAe;AAAA,MACrE,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;ACxHO,IAAM,SAAN,MAAM,QAAO;AAAA,EAClB,YAIW,IAIA,YAIA,UAIA,UAIA,UAIA,qBAIA,WAIA,WACT;AA7BS;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAA0B;AACxC,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,SAAS,IAAI,OAAK,QAAQ,SAAS,CAAC,CAAC;AAAA,MAC1C,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;ACpDO,IAAM,cAAN,MAAM,aAAY;AAAA,EACvB,YACW,MACA,OACA,UACT;AAHS;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAoC;AAClD,WAAO,IAAI,aAAY,KAAK,MAAM,KAAK,OAAO,KAAK,QAAQ;AAAA,EAC7D;AACF;;;ACVO,IAAMC,WAAN,MAAM,SAAQ;AAAA,EACnB,YAAqB,SAAmB;AAAnB;AAAA,EAAoB;AAAA,EAEzC,OAAO,SAAS,MAA4B;AAC1C,WAAO,IAAI,SAAQ,KAAK,OAAO;AAAA,EACjC;AACF;;;ACNO,IAAM,gBAAN,MAAM,eAAc;AAAA,EACzB,YACW,QACA,IACA,MACA,SACT;AAJS;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAyB;AACvC,WAAO,IAAI,eAAc,KAAK,QAAQ,KAAK,MAAM,MAAM,KAAK,QAAQ,MAAM,KAAK,OAAO;AAAA,EACxF;AACF;;;ACVO,IAAM,SAAN,MAAM,QAAO;AAAA,EAClB,YACW,IACA,MACA,aACA,gBACA,mBACA,cACA,mBACA,UACT;AARS;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAA0B;AACxC,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,iBAAiB,KAAK,cAAc,IAAI,OAAK,YAAY,SAAS,CAAC,CAAC;AAAA,MACzE,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;ACzBO,IAAM,QAAN,MAAM,OAAM;AAAA,EACjB,YACW,IACA,eACA,gBACA,gBACA,SACA,MACA,WACA,QACA,MACA,MACA,kBACT;AAXS;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAwB;AACtC,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;AC3BO,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EAC9B,YAIW,IAIA,MACT;AALS;AAIA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAkD;AAChE,WAAO,IAAI,oBAAmB,KAAK,IAAI,KAAK,IAAI;AAAA,EAClD;AACF;;;ACbO,IAAM,eAAN,MAAM,cAAa;AAAA,EACxB,YAYW,QAIA,UAIA,kCAA8C,MAI9C,WAA0B,MAI1B,WAA0B,MAI1B,QAAuB,MAIvB,UAAyB,MAClC;AAzBS;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAsC;AACpD,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,qCAAqC,IAAI,IAAI,KAAK,kCAAkC,IAAI;AAAA,MAC7F,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;AC9CO,IAAM,eAAN,MAAM,cAAa;AAAA,EACxB,YAIW,IAIA,cAIA,cAIA,UACT;AAbS;AAIA;AAIA;AAIA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAsC;AACpD,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,gBAAgB,aAAa,SAAS,KAAK,YAAY;AAAA,MAC5D,KAAK,UAAU,IAAI,UAAQ,mBAAmB,SAAS,IAAI,CAAC;AAAA,IAC9D;AAAA,EACF;AACF;;;AC/BO,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EAC3B,YAIW,IAIA,UAIA,kBAIA,YAIA,gBAIA,cAIA,WAIA,UAIA,UAIA,UAIA,aAIA,iBAAiD,CAAC,GAIlD,OAIA,cACT;AArDS;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAA4C;AAC1D,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,aAAa;AAAA,MAClB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,gBAAgB,aAAa,SAAS,KAAK,YAAY;AAAA,IAC9D;AAAA,EACF;AACF;;;ACpFO,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EAC/B,YACW,IACA,UACA,MACA,SACA,QACA,SACA,kBACA,SACA,YACA,WACA,WACT;AAXS;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAA+B;AAC7C,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;AC9BO,IAAM,WAAN,MAAM,UAAS;AAAA,EACpB,YACW,IACA,iBACA,gBACT;AAHS;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAA8B;AAC5C,WAAO,IAAI,UAAS,KAAK,IAAI,KAAK,kBAAkB,KAAK,eAAe;AAAA,EAC1E;AACF;;;ACVO,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EAChC,YACW,WACA,WACA,wBACA,6BACA,6BACT;AALS;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAsD;AACpE,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;AClBO,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EAC5B,YACW,IACA,uBACA,kBACA,mBACA,6BACT;AALS;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAA8C;AAC5D,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;ACdO,IAAM,aAAN,MAAM,YAAW;AAAA,EAOtB,YAIW,IAIA,cAIA,gBAIA,WAIA,WAIA,QAIA,KAIA,SACT;AA7BS;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAtCX,SAAQ,OAA8B;AAAA,EAuCnC;AAAA,EArCH,IAAW,MAA6B;AACtC,WAAO,KAAK;AAAA,EACd;AAAA,EAqCA,OAAO,SAAS,MAAkC;AAChD,UAAM,MAAM,IAAI;AAAA,MACd,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AACA,QAAI,OAAO;AACX,WAAO;AAAA,EACT;AACF;;;AC5CO,IAAM,aAAa;AAAA,EACxB,wBAAwB;AAAA,EACxB,YAAY;AAAA,EACZ,qBAAqB;AAAA,EACrB,QAAQ;AAAA,EACR,qBAAqB;AAAA,EACrB,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,eAAe;AAAA,EACf,UAAU;AAAA,EACV,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,qBAAqB;AAAA,EACrB,kBAAkB;AAAA,EAClB,cAAc;AAAA,EACd,oBAAoB;AAAA,EACpB,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,SAAS;AAAA,EACT,eAAe;AAAA,EACf,aAAa;AAAA,EACb,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,MAAM;AAAA,EACN,YAAY;AACd;;;AC9DO,IAAM,eAAN,MAAM,cAAa;AAAA,EACxB,YACW,IACA,MACA,SACA,QACA,QACA,SACA,kBACA,SACA,YACA,WACA,gBACA,WACA,WACT;AAbS;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAwB;AACtC,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;AClCO,IAAM,cAAN,MAAM,aAAY;AAAA,EACvB,YACW,IACA,MACA,QACA,UACA,kBACA,kBACA,kBACA,WACA,WACT;AATS;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAoC;AAClD,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;AC1BO,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EAC5B,YACW,mBACA,UACA,OACA,iBAA0C,CAAC,GAC3C,OACA,QACA,aACA,WACT;AARS;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAA4B;AAC1C,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,SAAS;AAAA,MACd,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;ACxBO,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EAC5B,YACW,IACA,YACA,MACA,UACA,UACA,QACA,cACA,cACA,eACA,aACA,cACA,uBACA,WACA,WACA,cACT;AAfS;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAA4B;AAC1C,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;ACnCO,IAAM,eAAN,MAAM,cAAa;AAAA,EAOxB,YAIW,IAIA,MAIA,MAIA,UAIA,UAIA,WAIA,WAIA,iBAAoD,CAAC,GAIrD,kBAA+C,CAAC,GAIhD,uBAIA,oBAIA,cAIA,WACT;AAjDS;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AA1DX,SAAQ,OAAgC;AAAA,EA2DrC;AAAA,EAzDH,IAAW,MAA+B;AACxC,WAAO,KAAK;AAAA,EACd;AAAA,EAyDA,OAAO,SAAS,MAAsC;AACpD,UAAM,MAAM,IAAI;AAAA,MACd,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,aAAa;AAAA,MAClB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AACA,QAAI,OAAO;AACX,WAAO;AAAA,EACT;AACF;;;AChFO,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAOlC,YAIW,IAIA,cAIA,MAIA,UAIA,gBAIA,WAIA,WAIA,WAIA,KAIA,QAIA,iBAAuD,CAAC,GAIxD,kBAAyD,CAAC,GAI1D,wBACT;AAjDS;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AA1DX,SAAQ,OAA0C;AAAA,EA2D/C;AAAA,EAzDH,IAAW,MAAyC;AAClD,WAAO,KAAK;AAAA,EACd;AAAA,EAyDA,OAAO,SAAS,MAAkC;AAChD,UAAM,MAAM,IAAI;AAAA,MACd,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AACA,QAAI,OAAO;AACX,WAAO;AAAA,EACT;AACF;;;AChFO,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAOlC,YAIW,IAIA,MAIA,aAIA,iBAAuD,CAAC,GAIxD,kBAAyD,CAAC,GAI1D,WAIA,WAIA,cAIA,gBACT;AAjCS;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AA1CX,SAAQ,OAA0C;AAAA,EA2C/C;AAAA,EAzCH,IAAW,MAAyC;AAClD,WAAO,KAAK;AAAA,EACd;AAAA,EAyCA,OAAO,SAAS,MAAkC;AAChD,UAAM,MAAM,IAAI;AAAA,MACd,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,aAAa,SAAS,KAAK,YAAY;AAAA,MACvC,qCAAqC,SAAS,KAAK,gBAAgB;AAAA,IACrE;AACA,QAAI,OAAO;AACX,WAAO;AAAA,EACT;AACF;AAKO,IAAM,uCAAN,MAAM,sCAAqC;AAAA,EAChD,YAIW,YAIA,WAIA,UAIA,UAIA,UAIA,QACT;AArBS;AAIA;AAIA;AAIA;AAIA;AAIA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAgD;AAC9D,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;AC5GO,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EAChC,YACW,SACA,uBACA,iBACA,uBACA,aACA,oBACA,gBACA,wBACA,oBACT;AATS;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAsD;AACpE,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;AClBO,IAAM,cAAN,MAAM,aAAY;AAAA,EACvB,YAIW,IAIA,aAIA,yBAIA,qBAIA,cAIA,UACT;AArBS;AAIA;AAIA;AAIA;AAIA;AAIA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAoC;AAClD,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,gBAAgB,aAAa,SAAS,KAAK,YAAY;AAAA,MAC5D,KAAK,UAAU,IAAI,UAAQ,mBAAmB,SAAS,IAAI,CAAC;AAAA,IAC9D;AAAA,EACF;AACF;;;AC/CO,IAAM,aAAN,MAAM,YAAW;AAAA,EACtB,YACW,IACA,UACA,WACA,UACA,YACA,WACA,WACT;AAPS;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAkC;AAChD,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;ACjBO,IAAM,cAAN,MAAM,aAAY;AAAA,EACvB,YAIW,IAMA,KAIA,WAIA,WACT;AAfS;AAMA;AAIA;AAIA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAoC;AAClD,WAAO,IAAI,aAAY,KAAK,IAAI,KAAK,KAAK,KAAK,YAAY,KAAK,UAAU;AAAA,EAC5E;AACF;;;AC3BO,IAAM,iBAAN,MAAM,gBAAe;AAAA,EAC1B,YAIW,IAIA,MAIA,QAIA,gBAIA,aAIA,WAIA,gBAIA,gBAIA,aAIA,QAIA,YAIA,eAIA,QAIA,UAIA,WAIA,oBAIA,iBAIA,mBAIA,WAIA,WAIA,kBACT;AAjFS;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAAA,EACR;AAAA,EACH,OAAO,SAAS,MAA0C;AACxD,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,qBAAqB,iBAAiB,SAAS,KAAK,iBAAiB;AAAA,IAC5E;AAAA,EACF;AACF;AAEO,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EACjC,YACW,IACA,MACA,QACA,QACA,UACA,oBACA,iBACA,mBACA,WACA,WACT;AAVS;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EACH,OAAO,SAAS,MAAwD;AACtE,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;AAEA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAIW,QAIA,cAIA,WAIA,UACT;AAbS;AAIA;AAIA;AAIA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAA8C;AAC5D,WAAO,IAAI,kBAAiB,KAAK,SAAS,KAAK,eAAe,KAAK,YAAY,KAAK,SAAS;AAAA,EAC/F;AACF;;;ACpKO,IAAM,cAAN,MAAM,aAAY;AAAA,EACvB,YAIW,IAIA,UAIA,gBAIA,QAIA,cAIA,WAIA,UAIA,cAIA,gBACT;AAjCS;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAoC;AAClD,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,gBAAgB,aAAa,SAAS,KAAK,YAAY;AAAA,MAC5D,KAAK,mBAAmB,sBAAsB,SAAS,KAAK,eAAe;AAAA,IAC7E;AAAA,EACF;AACF;;;AC1DO,IAAM,cAAN,MAAM,aAAY;AAAA,EACvB,YACW,IACA,QACA,OACA,QACA,KACA,WACA,WACT;AAPS;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAoC;AAClD,WAAO,IAAI,aAAY,KAAK,IAAI,KAAK,SAAS,KAAK,OAAO,KAAK,QAAQ,KAAK,KAAK,KAAK,YAAY,KAAK,UAAU;AAAA,EACnH;AACF;;;ACXO,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EACrC,YACW,YACA,qBACT;AAFS;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAyD;AACvE,WAAO,IAAI,2BAA0B,KAAK,aAAa,KAAK,oBAAoB;AAAA,EAClF;AACF;AAEO,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EACtC,YACW,cACA,aACA,YACA,iBACT;AAJS;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAA2D;AACzE,WAAO,IAAI;AAAA,MACT,KAAK,iBAAiB,0BAA0B,SAAS,KAAK,aAAa;AAAA,MAC3E,KAAK,gBAAgB,0BAA0B,SAAS,KAAK,YAAY;AAAA,MACzE,KAAK,eAAe,0BAA0B,SAAS,KAAK,WAAW;AAAA,MACvE,KAAK;AAAA,IACP;AAAA,EACF;AACF;AAEO,IAAM,gBAAN,MAAM,eAAc;AAAA,EACzB,YACW,IACA,QACA,gBACA,gBACA,eACA,kBACA,eACA,UACA,cACA,aACA,YACA,iBACA,WACA,UACA,cACA,YACA,kBACA,eACA,WACA,iBACA,gBACA,gBACT;AAtBS;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAiC;AAC/C,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,gBAAgB,2BAA2B,SAAS,KAAK,aAAa,IAAI;AAAA,MAC/E,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;ACpFO,IAAM,aAAN,MAAM,YAAW;AAAA,EACtB,YACW,IACA,iBACA,eACA,SACA,QACA,eACA,MACT;AAPS;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAkC;AAChD,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;ACtBO,IAAM,QAAN,MAAM,OAAM;AAAA,EACjB,YAAqB,KAAa;AAAb;AAAA,EAAc;AAAA,EAEnC,OAAO,SAAS,MAAwB;AACtC,WAAO,IAAI,OAAM,KAAK,GAAG;AAAA,EAC3B;AACF;;;ACAO,IAAM,aAAN,MAAM,YAAW;AAAA,EACtB,YAIW,IAIA,YAIA,cACT;AATS;AAIA;AAIA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAkC;AAChD,WAAO,IAAI,YAAW,KAAK,IAAI,KAAK,aAAa,KAAK,gBAAgB,aAAa,SAAS,KAAK,YAAY,CAAC;AAAA,EAChH;AACF;;;ACjBO,IAAM,OAAN,MAAM,MAAK;AAAA,EAOhB,YAIW,IAIA,iBAIA,aAIA,mBAIA,kBAIA,QAIA,QAIA,WAIA,WAIA,UAIA,UAIA,uBAIA,sBAIA,qBAIA,cAIA,YAIA,UAIA,WAIA,UAIA,iBAAqC,CAAC,GAItC,kBAAuC,CAAC,GAIxC,iBAAqC,CAAC,GAItC,iBAAiC,CAAC,GAIlC,eAA8B,CAAC,GAI/B,cAA4B,CAAC,GAI7B,mBAAsC,CAAC,GAIvC,eAA8B,CAAC,GAI/B,cAIA,2BAIA,2BAA0C,MAI1C,mBAIA,iBACT;AA7HS;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAIA;AAtIX,SAAQ,OAAwB;AAAA,EAuI7B;AAAA,EArIH,IAAW,MAAuB;AAChC,WAAO,KAAK;AAAA,EACd;AAAA,EAqIA,OAAO,SAAS,MAAsB;AACpC,UAAM,MAAM,IAAI;AAAA,MACd,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,OACJ,KAAK,mBAAmB,CAAC,GAAG,IAAI,OAAK,aAAa,SAAS,CAAC,CAAC;AAAA,OAC7D,KAAK,iBAAiB,CAAC,GAAG,IAAI,OAAK,YAAY,SAAS,CAAC,CAAC;AAAA,OAC1D,KAAK,gBAAgB,CAAC,GAAG,IAAI,OAAK,WAAW,SAAS,CAAC,CAAC;AAAA,OACxD,KAAK,qBAAqB,CAAC,GAAG,IAAI,CAAC,MAA2B,gBAAgB,SAAS,CAAC,CAAC;AAAA,OACzF,KAAK,iBAAiB,CAAC,GAAG,IAAI,CAAC,MAAuB,YAAY,SAAS,CAAC,CAAC;AAAA,MAC9E,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AACA,QAAI,OAAO;AACX,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,sBAAsB;AACxB,WAAO,KAAK,eAAe,KAAK,CAAC,EAAE,GAAG,MAAM,OAAO,KAAK,qBAAqB,KAAK;AAAA,EACpF;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,qBAAqB;AACvB,WAAO,KAAK,aAAa,KAAK,CAAC,EAAE,GAAG,MAAM,OAAO,KAAK,oBAAoB,KAAK;AAAA,EACjF;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,oBAAoB;AACtB,WAAO,KAAK,YAAY,KAAK,CAAC,EAAE,GAAG,MAAM,OAAO,KAAK,mBAAmB,KAAK;AAAA,EAC/E;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,CAAC,KAAK,WAAW,KAAK,QAAQ,EAAE,KAAK,GAAG,EAAE,KAAK,KAAK;AAAA,EAC7D;AACF;;;AClNO,IAAM,gBAAN,MAAM,eAAc;AAAA,EACzB,YACW,IACA,cACA,QACA,YACA,WACA,WACA,UACT;AAPS;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAwC;AACtD,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,cAAc,WAAW,SAAS,KAAK,UAAU;AAAA,MACtD,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;ACuCO,SAAS,YAAqB,SAAsE;AACzG,MAAI,MAAM;AAEV,MAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,UAAMC,QAAO,QAAQ,IAAI,UAAQ,aAAa,IAAI,CAAC;AACnD,WAAO,EAAE,MAAAA,MAAK;AAAA,EAChB,WAAW,YAAY,OAAO,GAAG;AAC/B,WAAO,QAAQ,KAAK,IAAI,UAAQ,aAAa,IAAI,CAAC;AAClD,iBAAa,QAAQ;AAErB,WAAO,EAAE,MAAM,WAAW;AAAA,EAC5B,OAAO;AACL,WAAO,EAAE,MAAM,aAAa,OAAO,EAAE;AAAA,EACvC;AACF;AAEA,SAAS,YAAY,SAAoD;AACvE,MAAI,CAAC,WAAW,OAAO,YAAY,YAAY,EAAE,UAAU,UAAU;AACnE,WAAO;AAAA,EACT;AAEA,SAAO,MAAM,QAAQ,QAAQ,IAAI,KAAK,QAAQ,SAAS;AACzD;AAEA,SAAS,SAAS,MAA6B;AAC7C,SAAO,KAAK;AACd;AAGA,SAAS,aAAa,MAAgB;AAGpC,MAAI,OAAO,SAAS,YAAY,YAAY,QAAQ,aAAa,MAAM;AACrE,WAAO,cAAc,SAAS,IAAI;AAAA,EACpC;AAEA,UAAQ,KAAK,QAAQ;AAAA,IACnB,KAAK,WAAW;AACd,aAAO,uBAAuB,SAAS,IAAI;AAAA,IAC7C,KAAK,WAAW;AACd,aAAO,WAAW,SAAS,IAAI;AAAA,IACjC,KAAK,WAAW;AACd,aAAO,oBAAoB,SAAS,IAAI;AAAA,IAC1C,KAAK,WAAW;AACd,aAAO,OAAO,SAAS,IAAI;AAAA,IAC7B,KAAK,WAAW;AACd,aAAO,oBAAoB,SAAS,IAAI;AAAA,IAC1C,KAAK,WAAW;AACd,aAAO,OAAO,SAAS,IAAI;AAAA,IAC7B,KAAK,WAAW;AACd,aAAOC,SAAQ,SAAS,IAAI;AAAA,IAC9B,KAAK,WAAW;AACd,aAAO,OAAO,SAAS,IAAI;AAAA,IAC7B,KAAK,WAAW;AACd,aAAO,aAAa,SAAS,IAAI;AAAA,IACnC,KAAK,WAAW;AACd,aAAO,MAAM,SAAS,IAAI;AAAA,IAC5B,KAAK,WAAW;AACd,aAAO,oBAAoB,SAAS,IAAI;AAAA,IAC1C,KAAK,WAAW;AACd,aAAO,SAAS,SAAS,IAAI;AAAA,IAC/B,KAAK,WAAW;AACd,aAAO,qBAAqB,SAAS,IAAI;AAAA,IAC3C,KAAK,WAAW;AACd,aAAO,iBAAiB,SAAS,IAAI;AAAA,IACvC,KAAK,WAAW;AACd,aAAO,WAAW,SAAS,IAAI;AAAA,IACjC,KAAK,WAAW;AACd,aAAO,YAAY,SAAS,IAAI;AAAA,IAClC,KAAK,WAAW;AACd,aAAO,aAAa,SAAS,IAAI;AAAA,IACnC,KAAK,WAAW;AACd,aAAO,iBAAiB,SAAS,IAAI;AAAA,IACvC,KAAK,WAAW;AACd,aAAO,iBAAiB,SAAS,IAAI;AAAA,IACvC,KAAK,WAAW;AACd,aAAO,aAAa,SAAS,IAAI;AAAA,IACnC,KAAK,WAAW;AACd,aAAO,uBAAuB,SAAS,IAAI;AAAA,IAC7C,KAAK,WAAW;AACd,aAAO,uBAAuB,SAAS,IAAI;AAAA,IAC7C,KAAK,WAAW;AACd,aAAO,qBAAqB,SAAS,IAAI;AAAA,IAC3C,KAAK,WAAW;AACd,aAAO,YAAY,SAAS,IAAI;AAAA,IAClC,KAAK,WAAW;AACd,aAAO,WAAW,SAAS,IAAI;AAAA,IACjC,KAAK,WAAW;AACd,aAAO,YAAY,SAAS,IAAI;AAAA,IAClC,KAAK,WAAW;AACd,aAAO,eAAe,SAAS,IAAI;AAAA,IACrC,KAAK,WAAW;AACd,aAAO,YAAY,SAAS,IAAI;AAAA,IAClC,KAAK,WAAW;AACd,aAAO,cAAc,SAAS,IAAI;AAAA,IACpC,KAAK,WAAW;AACd,aAAO,QAAQ,SAAS,IAAI;AAAA,IAC9B,KAAK,WAAW;AACd,aAAO,WAAW,SAAS,IAAI;AAAA,IACjC,KAAK,WAAW;AACd,aAAO,MAAM,SAAS,IAAI;AAAA,IAC5B,KAAK,WAAW;AACd,aAAO,SAAS,IAAI;AAAA,IACtB,KAAK,WAAW;AACd,aAAO,KAAK,SAAS,IAAI;AAAA,IAC3B,KAAK,WAAW;AACd,aAAO,cAAc,SAAS,IAAI;AAAA,IACpC;AACE,aAAO;AAAA,EACX;AACF;;;A3ClHO,SAAS,aAAa,SAA8B;AACzD,QAAM,YAAY,OAAU,mBAAuF;AACjH,UAAM;AAAA,MACJ;AAAA,MACA,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,aAAa;AAAA,MACb,YAAY;AAAA,IACd,IAAI;AACJ,UAAM,EAAE,MAAM,QAAQ,aAAa,cAAc,YAAY,SAAS,IAAI;AAE1E,QAAI,kBAAkB;AACpB,2BAAqB,SAAS;AAAA,IAChC;AAEA,UAAM,MAAM,UAAU,QAAQ,YAAY,IAAI;AAG9C,UAAM,WAAW,IAAI,IAAI,GAAG;AAE5B,QAAI,aAAa;AAEf,YAAM,wBAAwB,cAAc,EAAE,GAAG,YAAY,CAAC;AAG9D,iBAAW,CAAC,KAAK,GAAG,KAAK,OAAO,QAAQ,qBAAqB,GAAG;AAC9D,YAAI,KAAK;AACP,WAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,OAAK,SAAS,aAAa,OAAO,KAAK,CAAW,CAAC;AAAA,QAC1E;AAAA,MACF;AAAA,IACF;AAGA,UAAM,UAA+B;AAAA,MACnC,qBAAqB;AAAA,MACrB,cAAc;AAAA,MACd,GAAG;AAAA,IACL;AAEA,QAAI,WAAW;AACb,cAAQ,gBAAgB,UAAU,SAAS;AAAA,IAC7C;AAEA,QAAI;AACJ,QAAI;AACF,UAAI,UAAU;AACZ,cAAM,MAAM,QAAQ,MAAM,SAAS,MAAM;AAAA,UACvC;AAAA,UACA;AAAA,UACA,MAAM;AAAA,QACR,CAAC;AAAA,MACH,OAAO;AAEL,gBAAQ,cAAc,IAAI;AAE1B,cAAM,YAAY,MAAM;AACtB,gBAAM,UAAU,WAAW,SAAS,cAAc,OAAO,KAAK,UAAU,EAAE,SAAS;AACnF,cAAI,CAAC,SAAS;AACZ,mBAAO;AAAA,UACT;AAEA,gBAAM,aAAa,CAAC,WAAgD,cAAc,QAAQ,EAAE,MAAM,MAAM,CAAC;AAEzG,iBAAO;AAAA,YACL,MAAM,KAAK,UAAU,MAAM,QAAQ,UAAU,IAAI,WAAW,IAAI,UAAU,IAAI,WAAW,UAAU,CAAC;AAAA,UACtG;AAAA,QACF;AAEA,cAAM,MAAM,QAAQ,MAAM,SAAS,MAAM;AAAA,UACvC;AAAA,UACA;AAAA,UACA,GAAG,UAAU;AAAA,QACf,CAAC;AAAA,MACH;AAGA,YAAM,iBACJ,KAAK,WAAW,IAAI,SAAS,IAAI,UAAU,QAAQ,WAAW,MAAM,UAAU,aAAa;AAC7F,YAAM,eAAe,OAAO,iBAAiB,IAAI,KAAK,IAAI,IAAI,KAAK;AAEnE,UAAI,CAAC,IAAI,IAAI;AACX,eAAO;AAAA,UACL,MAAM;AAAA,UACN,QAAQ,YAAY,YAAY;AAAA,UAChC,QAAQ,KAAK;AAAA,UACb,YAAY,KAAK;AAAA,UACjB,cAAc,WAAW,cAAc,KAAK,OAAO;AAAA,UACnD,YAAY,cAAc,KAAK,OAAO;AAAA,QACxC;AAAA,MACF;AAEA,aAAO;AAAA,QACL,GAAG,YAAe,YAAY;AAAA,QAC9B,QAAQ;AAAA,MACV;AAAA,IACF,SAAS,KAAK;AACZ,UAAI,eAAe,OAAO;AACxB,eAAO;AAAA,UACL,MAAM;AAAA,UACN,QAAQ;AAAA,YACN;AAAA,cACE,MAAM;AAAA,cACN,SAAS,IAAI,WAAW;AAAA,YAC1B;AAAA,UACF;AAAA,UACA,cAAc,WAAW,KAAK,KAAK,OAAO;AAAA,QAC5C;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,QAAQ,YAAY,GAAG;AAAA,QACvB,QAAQ,KAAK;AAAA,QACb,YAAY,KAAK;AAAA,QACjB,cAAc,WAAW,KAAK,KAAK,OAAO;AAAA,QAC1C,YAAY,cAAc,KAAK,OAAO;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AAEA,SAAO,wBAAwB,SAAS;AAC1C;AAIA,SAAS,WAAW,MAAe,SAA2B;AAC5D,MAAI,QAAQ,OAAO,SAAS,YAAY,oBAAoB,QAAQ,OAAO,KAAK,mBAAmB,UAAU;AAC3G,WAAO,KAAK;AAAA,EACd;AAEA,QAAM,QAAQ,SAAS,IAAI,QAAQ;AACnC,SAAO,SAAS;AAClB;AAEA,SAAS,cAAc,SAAuC;AAC5D,QAAM,aAAa,SAAS,IAAI,aAAa;AAC7C,MAAI,CAAC,WAAY;AAEjB,QAAM,QAAQ,SAAS,YAAY,EAAE;AACrC,MAAI,MAAM,KAAK,EAAG;AAElB,SAAO;AACT;AAEA,SAAS,YAAY,MAAgC;AACnD,MAAI,CAAC,CAAC,QAAQ,OAAO,SAAS,YAAY,YAAY,MAAM;AAC1D,UAAM,SAAS,KAAK;AACpB,WAAO,OAAO,SAAS,IAAI,OAAO,IAAI,UAAU,IAAI,CAAC;AAAA,EACvD;AACA,SAAO,CAAC;AACV;AAKA,SAAS,wBAAwB,IAAgC;AAC/D,SAAO,UAAU,SAAS;AACxB,UAAM,EAAE,MAAM,QAAQ,YAAY,QAAQ,YAAY,cAAc,WAAW,IAAI,MAAM,GAAG,GAAG,IAAI;AACnG,QAAI,QAAQ;AAIV,YAAM,QAAQ,IAAI,sBAAsB,cAAc,IAAI;AAAA,QACxD,MAAM,CAAC;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,YAAM,SAAS;AACf,YAAM;AAAA,IACR;AAEA,QAAI,OAAO,eAAe,aAAa;AACrC,aAAO,EAAE,MAAM,WAAW;AAAA,IAC5B;AAEA,WAAO;AAAA,EACT;AACF;;;A4C3MO,SAAS,uBAAuB,SAAkC;AACvE,QAAM,UAAU,aAAa,OAAO;AAEpC,SAAO;AAAA,IACL,wCAAwC,IAAI;AAAA,MAC1C,aAAa,EAAE,GAAG,SAAS,kBAAkB,MAAM,CAAC;AAAA,IACtD;AAAA,IACA,aAAa,IAAI,cAAc,OAAO;AAAA,IACtC,sBAAsB,IAAI,uBAAuB,OAAO;AAAA,IACxD,cAAc,IAAI,gBAAgB,OAAO;AAAA,IACzC,sBAAsB,IAAI,uBAAuB,OAAO;AAAA,IACxD,SAAS,IAAI,UAAU,OAAO;AAAA,IAC9B,SAAS,IAAI,UAAU,OAAO;AAAA,IAC9B,gBAAgB,IAAI,gBAAgB,OAAO;AAAA,IAC3C,UAAU,IAAI,YAAY,OAAO;AAAA,IACjC,aAAa,IAAI,cAAc,OAAO;AAAA;AAAA;AAAA;AAAA,IAItC,eAAe,IAAI;AAAA,MACjB,aAAa;AAAA,QACX,GAAG;AAAA,QACH,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AAAA,IACA,qBAAqB,IAAI;AAAA,MACvB,aAAa;AAAA,QACX,GAAG;AAAA,QACH,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AAAA,IACA,SAAS,IAAI;AAAA,MACX,aAAa;AAAA,QACX,GAAG;AAAA,QACH,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AAAA,IACA,MAAM,IAAI,QAAQ,OAAO;AAAA,IACzB,cAAc,IAAI,gBAAgB,OAAO;AAAA,IACzC,mBAAmB,IAAI,qBAAqB,OAAO;AAAA,IACnD,eAAe,IAAI,gBAAgB,OAAO;AAAA,IAC1C,cAAc,IAAI,eAAe,OAAO;AAAA,IACxC,aAAa,IAAI,cAAc,OAAO;AAAA,IACtC,cAAc,IAAI,eAAe,OAAO;AAAA,IACxC,iBAAiB,IAAI,kBAAkB,OAAO;AAAA,IAC9C,UAAU,IAAI,WAAW,OAAO;AAAA,IAChC,cAAc,IAAI,eAAe,OAAO;AAAA,IACxC,SAAS,IAAI,UAAU,OAAO;AAAA,IAC9B,eAAe,IAAI,gBAAgB,OAAO;AAAA,IAC1C,OAAO,IAAI,QAAQ,OAAO;AAAA,IAC1B,iBAAiB,IAAI,iBAAiB,OAAO;AAAA,IAC7C,UAAU,IAAI,WAAW,OAAO;AAAA,EAClC;AACF;;;ACzFO,IAAM,YAAY;AAAA,EACvB,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,YAAY;AACd;;;ACDO,IAAM,mBAAmB;AACzB,IAAM,qBAAqB;AAC3B,IAAM,iBAAiB;AAE9B,IAAM,yBAAyB,CAAC,kBAAkB,oBAAoB,cAAc;AAY7E,SAAS,uBAAuB,OAAwB;AAC7D,SAAO,uBAAuB,KAAK,YAAU,MAAM,WAAW,MAAM,CAAC;AACvE;AAaO,SAAS,oBAAoB,OAAiC;AACnE,MAAI,MAAM,WAAW,gBAAgB,GAAG;AACtC,WAAO,UAAU;AAAA,EACnB;AAEA,MAAI,MAAM,WAAW,kBAAkB,GAAG;AACxC,WAAO,UAAU;AAAA,EACnB;AAEA,MAAI,MAAM,WAAW,cAAc,GAAG;AACpC,WAAO,UAAU;AAAA,EACnB;AAEA,QAAM,IAAI,MAAM,4BAA4B;AAC9C;AASO,IAAM,sBAAsB,CACjC,WACA,iBACY;AACZ,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,EACT;AAEA,MAAI,iBAAiB,OAAO;AAC1B,WAAO;AAAA,EACT;AAEA,QAAM,aAAa,MAAM,QAAQ,YAAY,IAAI,eAAe,CAAC,YAAY;AAC7E,SAAO,WAAW,SAAS,SAAS;AACtC;AAQO,SAAS,mBAAmB,MAAwC;AACzE,SAAO,SAAS,UAAU,UAAU,SAAS,UAAU,gBAAgB,SAAS,UAAU;AAC5F;;;A7EsFA,IAAM,cAAc,CAAC,SAA0C;AAC7D,SAAO,MAAM;AACX,UAAM,MAAM,EAAE,GAAG,KAAK;AACtB,QAAI,aAAa,IAAI,aAAa,IAAI,UAAU,GAAG,CAAC;AACpD,QAAI,UAAU,IAAI,UAAU,IAAI,UAAU,GAAG,CAAC;AAC9C,WAAO,EAAE,GAAG,IAAI;AAAA,EAClB;AACF;AAKO,SAAS,mBACd,qBACA,cACA,eACoB;AACpB,QAAM,EAAE,OAAO,WAAW,eAAe,QAAQ,OAAO,SAAS,SAAS,gBAAgB,sBAAsB,IAC9G,gDAAgD,aAAa;AAC/D,QAAM,YAAY,uBAAuB,mBAAmB;AAC5D,QAAM,WAAW,eAAe;AAAA,IAC9B;AAAA,IACA;AAAA,IACA,SAAS,UAAU,UAAU,MAAM,UAAU,SAAS,SAAS,GAAG,IAAI,GAAG;AAAA,EAC3E,CAAC;AACD,SAAO;AAAA,IACL,WAAW,UAAU;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,yBAAyB;AAAA,MAC5B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAW,cAAc,OAAkB;AAAA,MAC3C,OAAQ,cAAc,OAAkB;AAAA,IAC1C,CAAC;AAAA,IACD,OAAO,YAAY,EAAE,GAAG,qBAAqB,aAAa,CAAC;AAAA,IAC3D,iBAAiB;AAAA,EACnB;AACF;AAKO,SAAS,oBACd,WACA,sBACqB;AACrB,SAAO;AAAA,IACL,WAAW,UAAU;AAAA,IACrB,eAAe;AAAA,IACf,WAAW;AAAA,IACX,eAAe,wBAAwB;AAAA,IACvC,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,gBAAgB;AAAA,IAChB,uBAAuB;AAAA,IACvB,UAAU,MAAM,QAAQ,QAAQ,IAAI;AAAA,IACpC,KAAK,MAAM;AAAA,IACX,OAAO,YAAY,SAAS;AAAA,IAC5B,iBAAiB;AAAA,EACnB;AACF;AAKO,SAAS,2BACd,WACA,OACA,oBACA,WAC+B;AAC/B,QAAM,aAAa;AAAA,IACjB,IAAI,mBAAmB;AAAA,IACvB,SAAS,mBAAmB;AAAA,IAC5B,UAAU,MAAM,QAAQ,QAAQ,KAAK;AAAA,IACrC,KAAK,MAAM;AAAA,IACX,OAAO,YAAY,SAAS;AAAA,IAC5B,iBAAiB;AAAA,EACnB;AAMA,UAAQ,WAAW;AAAA,IACjB,KAAK,UAAU,QAAQ;AACrB,YAAM,SAAS;AACf,aAAO;AAAA,QACL,GAAG;AAAA,QACH;AAAA,QACA,MAAM,OAAO;AAAA,QACb,QAAQ,OAAO;AAAA,QACf,QAAQ,OAAO;AAAA,QACf,QAAQ,OAAO,QAAQ,WAAW,OAAO,IAAI,OAAO,UAAU;AAAA,QAC9D,OAAO,OAAO,QAAQ,WAAW,MAAM,IAAI,OAAO,UAAU;AAAA,MAC9D;AAAA,IACF;AAAA,IACA,KAAK,UAAU,cAAc;AAC3B,YAAM,SAAS;AACf,aAAO;AAAA,QACL,GAAG;AAAA,QACH;AAAA,QACA,MAAM,OAAO;AAAA,QACb,QAAQ,OAAO;AAAA,QACf,QAAQ,OAAO;AAAA,QACf,WAAW,OAAO;AAAA,MACpB;AAAA,IACF;AAAA,IACA,KAAK,UAAU,YAAY;AACzB,YAAM,SAAS;AACf,aAAO;AAAA,QACL,GAAG;AAAA,QACH;AAAA,QACA,QAAQ,OAAO;AAAA,QACf,QAAQ,OAAO;AAAA,QACf,UAAU,OAAO;AAAA,MACnB;AAAA,IACF;AAAA,IACA;AACE,YAAM,IAAI,MAAM,uBAAuB,SAAS,EAAE;AAAA,EACtD;AACF;AAKO,SAAS,6BACd,WACA,WACiC;AACjC,QAAM,aAAa;AAAA,IACjB,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,KAAK,MAAM;AAAA,IACX,UAAU,MAAM,QAAQ,QAAQ,IAAI;AAAA,IACpC,OAAO,YAAY,SAAS;AAAA,IAC5B,iBAAiB;AAAA,EACnB;AAEA,UAAQ,WAAW;AAAA,IACjB,KAAK,UAAU,QAAQ;AACrB,aAAO;AAAA,QACL,GAAG;AAAA,QACH;AAAA,QACA,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,KAAK,UAAU,cAAc;AAC3B,aAAO;AAAA,QACL,GAAG;AAAA,QACH;AAAA,QACA,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,KAAK,UAAU,YAAY;AACzB,aAAO;AAAA,QACL,GAAG;AAAA,QACH;AAAA,QACA,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA;AACE,YAAM,IAAI,MAAM,uBAAuB,SAAS,EAAE;AAAA,EACtD;AACF;AAKO,SAAS,yBAAiD;AAC/D,SAAO;AAAA,IACL,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,UAAU,MAAM,QAAQ,QAAQ,IAAI;AAAA,IACpC,KAAK,MAAM;AAAA,IACX,OAAO,OAAO,CAAC;AAAA,EACjB;AACF;AAWO,IAAM,6BAA6B,CAAoC,QAAc;AAG1F,QAAM,EAAE,OAAO,UAAU,KAAK,GAAG,KAAK,IAAI;AAC1C,SAAO;AACT;AAMA,IAAM,iBAAiC,YAAU;AAC/C,QAAM,EAAE,SAAS,cAAc,UAAU,IAAI,UAAU,CAAC;AAExD,SAAO,OAAO,UAAiC,CAAC,MAAM;AACpD,QAAI,CAAC,WAAW;AACd,aAAO;AAAA,IACT;AAEA,QAAI,QAAQ,UAAU;AACpB,aAAO,QAAQ,WAAW,QAAQ,QAAQ;AAAA,IAC5C;AAEA,WAAO;AAAA,EACT;AACF;AAKO,IAAM,uBAAuB,CAClC,KACA,EAAE,0BAA0B,MAAM,GAAG,QAAQ,MAC1C;AACH,QAAM,aAAa,mBAAmB,SAAS,IAAI,IAAI,MAAM,IAAI,OAAO;AAExE,MAAI,2BAA2B,WAAW,kBAAkB,WAAW;AACrE,WAAO,oBAAoB,SAAS,WAAW,aAAa;AAAA,EAC9D;AAEA,SAAO;AACT;AAYO,SAAS,8BAA8B;AAAA,EAC5C;AAAA,EACA,eAAe,UAAU;AAC3B,GAGe;AACb,MAAI,iBAAiB,OAAO;AAC1B,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,QAAQ,YAAY,GAAG;AAC/B,QAAI,CAAC,oBAAoB,WAAW,WAAW,YAAY,GAAG;AAE5D,aAAO,uBAAuB;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AAGA,MAAI,CAAC,oBAAoB,WAAW,WAAW,YAAY,GAAG;AAC5D,QAAI,mBAAmB,YAAY,GAAG;AACpC,aAAO,6BAA6B,cAAc,WAAW,KAAK;AAAA,IACpE;AACA,WAAO,oBAAoB,WAAW,KAAK;AAAA,EAC7C;AAEA,SAAO;AACT;;;A8E1bO,IAAM,aAAa;AAAA,EACxB,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AACb;AA2EO,IAAM,kBAAkB;AAAA,EAC7B,8BAA8B;AAAA,EAC9B,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,iBAAiB;AAAA,EACjB,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,mBAAmB;AAAA,EACnB,iBAAiB;AACnB;AAsBO,SAAS,SAA8B,QAAkE;AAC9G,QAAM,EAAE,qBAAqB,UAAU,IAAI,QAAQ,GAAG,MAAM,IAAI;AAEhE,QAAM,SAAU,CAAC,EAAE,0BAA0B,KAAK,IAAI,CAAC,MAAM;AAC3D,QAAI,OAAO,cAAc,UAAU,cAAc;AAC/C,YAAM,EAAE,cAAc,IAAI;AAC1B,YAAM,aAAa,mBAAmB,qBAAqB,OAAO,aAAa;AAE/E,UAAI,2BAA2B,WAAW,kBAAkB,WAAW;AACrE,eAAO,oBAAoB,QAAW,WAAW,aAAa;AAAA,MAChE;AAEA,aAAO;AAAA,IACT;AAEA,UAAM,EAAE,YAAY,IAAI;AACxB,WAAO,2BAA2B,OAAO,WAAW,OAAO,aAAa,mBAAmB;AAAA,EAC7F;AAEA,SAAO;AAAA,IACL,QAAQ,WAAW;AAAA,IACnB,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,UAAU,oBAAoB,YAAY;AAAA,IAC1C,gBAAgB,oBAAoB,kBAAkB;AAAA,IACtD,aAAa,oBAAoB,eAAe;AAAA,IAChD,QAAQ,oBAAoB,UAAU;AAAA,IACtC,WAAW,oBAAoB,aAAa;AAAA,IAC5C,WAAW,oBAAoB,aAAa;AAAA,IAC5C,gBAAgB,oBAAoB,kBAAkB;AAAA,IACtD,gBAAgB,oBAAoB,kBAAkB;AAAA,IACtD,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,WAAW,OAAO;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAOO,SAAS,UAA+B,QAAqE;AAClH,QAAM,EAAE,qBAAqB,UAAU,IAAI,QAAQ,GAAG,QAAQ,UAAU,IAAI,UAAU,IAAI;AAE1F,QAAM,SAAU,MAAM;AACpB,QAAI,cAAc,UAAU,cAAc;AACxC,aAAO,oBAAoB,EAAE,GAAG,qBAAqB,QAAQ,WAAW,WAAW,QAAQ,QAAQ,CAAC;AAAA,IACtG;AAEA,WAAO,6BAA6B,WAAW,EAAE,QAAQ,SAAS,QAAQ,CAAC;AAAA,EAC7E;AAEA,SAAO,iBAAiB;AAAA,IACtB,QAAQ,WAAW;AAAA,IACnB;AAAA,IACA;AAAA,IACA,UAAU,oBAAoB,YAAY;AAAA,IAC1C,gBAAgB,oBAAoB,kBAAkB;AAAA,IACtD,aAAa,oBAAoB,eAAe;AAAA,IAChD,QAAQ,oBAAoB,UAAU;AAAA,IACtC,WAAW,oBAAoB,aAAa;AAAA,IAC5C,WAAW,oBAAoB,aAAa;AAAA,IAC5C,gBAAgB,oBAAoB,kBAAkB;AAAA,IACtD,gBAAgB,oBAAoB,kBAAkB;AAAA,IACtD,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,EACT,CAAC;AACH;AAEO,SAAS,UACd,qBACA,QACA,UAAU,IACV,SACgB;AAChB,SAAO,iBAAiB;AAAA,IACtB,QAAQ,WAAW;AAAA,IACnB;AAAA,IACA;AAAA,IACA,gBAAgB,oBAAoB,kBAAkB;AAAA,IACtD,aAAa,oBAAoB,eAAe;AAAA,IAChD,QAAQ,oBAAoB,UAAU;AAAA,IACtC,UAAU,oBAAoB,YAAY;AAAA,IAC1C,WAAW,oBAAoB,aAAa;AAAA,IAC5C,WAAW,oBAAoB,aAAa;AAAA,IAC5C,gBAAgB,oBAAoB,kBAAkB;AAAA,IACtD,gBAAgB,oBAAoB,kBAAkB;AAAA,IACtD,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,WAAW,UAAU;AAAA,IACrB,QAAQ,MAAM;AAAA,IACd;AAAA,IACA,OAAO;AAAA,EACT,CAAC;AACH;AAEA,IAAM,mBAAmB,CACvB,iBACM;AACN,QAAM,UAAU,IAAI,QAAQ,aAAa,WAAW,CAAC,CAAC;AAEtD,MAAI,aAAa,SAAS;AACxB,QAAI;AACF,cAAQ,IAAI,UAAU,QAAQ,aAAa,aAAa,OAAO;AAAA,IACjE,QAAQ;AAAA,IAER;AAAA,EACF;AAEA,MAAI,aAAa,QAAQ;AACvB,QAAI;AACF,cAAQ,IAAI,UAAU,QAAQ,YAAY,aAAa,MAAM;AAAA,IAC/D,QAAQ;AAAA,IAER;AAAA,EACF;AAEA,MAAI,aAAa,QAAQ;AACvB,QAAI;AACF,cAAQ,IAAI,UAAU,QAAQ,YAAY,aAAa,MAAM;AAAA,IAC/D,QAAQ;AAAA,IAER;AAAA,EACF;AAEA,eAAa,UAAU;AAEvB,SAAO;AACT;;;AClRA,SAAS,aAAa;;;ACAtB,IAAM,WAAN,cAAuB,IAAI;AAAA,EAClB,cAAc,OAAqB;AACxC,WAAO,KAAK,WAAW,IAAI,IAAI,MAAM,SAAS,CAAC,EAAE;AAAA,EACnD;AACF;AAeO,IAAM,iBAAiB,IAAI,SAA2D;AAC3F,SAAO,IAAI,SAAS,GAAG,IAAI;AAC7B;;;ADVA,IAAM,eAAN,cAA2B,QAAQ;AAAA,EAI1B,YAAY,OAA6C,MAAoB;AAYlF,UAAM,MAAM,OAAO,UAAU,YAAY,SAAS,QAAQ,MAAM,MAAM,OAAO,KAAK;AAClF,UAAM,KAAK,QAAQ,OAAO,UAAU,WAAW,SAAY,KAAK;AAChE,SAAK,WAAW,KAAK,qBAAqB,IAAI;AAC9C,SAAK,UAAU,KAAK,aAAa,IAAI;AAAA,EACvC;AAAA,EAEO,SAAS;AACd,WAAO;AAAA,MACL,KAAK,KAAK,SAAS;AAAA,MACnB,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK,UAAU,OAAO,YAAY,KAAK,OAAO,CAAC;AAAA,MACxD,UAAU,KAAK,SAAS,SAAS;AAAA,MACjC,SAAS,KAAK,UAAU,OAAO,YAAY,KAAK,OAAO,CAAC;AAAA,IAC1D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMQ,qBAAqB,KAAc;AACzC,UAAM,aAAa,IAAI,IAAI,IAAI,GAAG;AAClC,UAAM,iBAAiB,IAAI,QAAQ,IAAI,UAAU,QAAQ,cAAc;AACvE,UAAM,gBAAgB,IAAI,QAAQ,IAAI,UAAU,QAAQ,aAAa;AACrE,UAAM,OAAO,IAAI,QAAQ,IAAI,UAAU,QAAQ,IAAI;AACnD,UAAM,WAAW,WAAW;AAE5B,UAAM,eAAe,KAAK,wBAAwB,aAAa,KAAK;AACpE,UAAM,mBAAmB,KAAK,wBAAwB,cAAc,KAAK,UAAU,QAAQ,QAAQ,EAAE;AACrG,UAAM,SAAS,gBAAgB,mBAAmB,GAAG,gBAAgB,MAAM,YAAY,KAAK,WAAW;AAEvG,QAAI,WAAW,WAAW,QAAQ;AAChC,aAAO,eAAe,UAAU;AAAA,IAClC;AACA,WAAO,eAAe,WAAW,WAAW,WAAW,QAAQ,MAAM;AAAA,EACvE;AAAA,EAEQ,wBAAwB,OAAuB;AACrD,WAAO,OAAO,MAAM,GAAG,EAAE,CAAC;AAAA,EAC5B;AAAA,EAEQ,aAAa,KAAc;AACjC,UAAM,gBAAgB,MAAM,KAAK,kBAAkB,IAAI,QAAQ,IAAI,QAAQ,KAAK,EAAE,CAAC;AACnF,WAAO,IAAI,IAAI,OAAO,QAAQ,aAAa,CAAC;AAAA,EAC9C;AAAA,EAEQ,kBAAkB,KAAa;AACrC,WAAO,MAAM,IAAI,QAAQ,oBAAoB,kBAAkB,IAAI;AAAA,EACrE;AACF;AAEO,IAAM,qBAAqB,IAAI,SAAmE;AACvG,SAAO,KAAK,CAAC,aAAa,eAAe,KAAK,CAAC,IAAI,IAAI,aAAa,GAAG,IAAI;AAC7E;;;AEhFO,IAAM,gBAAgB,CAAC,oBAAoC;AAChE,SAAO,gBAAgB,MAAM,GAAG,EAAE,CAAC,GAAG,MAAM,GAAG,EAAE,CAAC;AACpD;AAEO,IAAM,iBAAiB,CAAC,oBAAoC;AACjE,SAAO,gBAAgB,MAAM,GAAG,EAAE,CAAC,GAAG,MAAM,GAAG,EAAE,CAAC;AACpD;;;ACeA,IAAI,QAAyB,CAAC;AAC9B,IAAI,gBAAgB;AAEpB,SAAS,aAAa,KAAa;AACjC,SAAO,MAAM,GAAG;AAClB;AAEA,SAAS,iBAAiB;AACxB,SAAO,OAAO,OAAO,KAAK;AAC5B;AAEA,SAAS,WAAW,KAAwB,eAAe,MAAM;AAC/D,QAAM,IAAI,GAAG,IAAI;AACjB,kBAAgB,eAAe,KAAK,IAAI,IAAI;AAC9C;AAEA,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,aAAa;AAUZ,SAAS,sBAAsB,UAA+B;AACnE,MAAI,CAAC,aAAa,WAAW,GAAG;AAC9B,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,uBAAuB;AAAA,QAC/B,QAAQ,6BAA6B;AAAA,QACrC,SAAS;AAAA,QACT,QAAQ,6BAA6B;AAAA,MACvC,CAAC;AAAA,IACH;AAEA,UAAM,UAAU,SACb,QAAQ,eAAe,EAAE,EACzB,QAAQ,YAAY,EAAE,EACtB,QAAQ,aAAa,EAAE,EACvB,QAAQ,YAAY,EAAE,EACtB,QAAQ,YAAY,EAAE,EACtB,QAAQ,OAAO,GAAG,EAClB,QAAQ,OAAO,GAAG;AAGrB;AAAA,MACE;AAAA,QACE,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,MACA;AAAA;AAAA,IACF;AAAA,EACF;AAEA,SAAO,aAAa,WAAW;AACjC;AA6CA,eAAsB,uBAAuB;AAAA,EAC3C;AAAA,EACA,SAAS;AAAA,EACT,aAAa;AAAA,EACb;AAAA,EACA;AACF,GAAuD;AACrD,MAAI,iBAAiB,gBAAgB,KAAK,CAAC,aAAa,GAAG,GAAG;AAC5D,QAAI,CAAC,WAAW;AACd,YAAM,IAAI,uBAAuB;AAAA,QAC/B,QAAQ,6BAA6B;AAAA,QACrC,SAAS;AAAA,QACT,QAAQ,6BAA6B;AAAA,MACvC,CAAC;AAAA,IACH;AACA,UAAM,UAAU,MAAM,kBAAkB,QAAQ,WAAW,UAAU;AACrE,UAAM,EAAE,KAAK,IAAI,MAAM,MAAM,OAAO;AAEpC,QAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ;AACzB,YAAM,IAAI,uBAAuB;AAAA,QAC/B,QAAQ,6BAA6B;AAAA,QACrC,SAAS;AAAA,QACT,QAAQ,6BAA6B;AAAA,MACvC,CAAC;AAAA,IACH;AAEA,SAAK,QAAQ,SAAO,WAAW,GAAG,CAAC;AAAA,EACrC;AAEA,QAAM,MAAM,aAAa,GAAG;AAE5B,MAAI,CAAC,KAAK;AACR,UAAM,cAAc,eAAe;AACnC,UAAM,UAAU,YACb,IAAI,CAAAC,SAAOA,KAAI,GAAG,EAClB,KAAK,EACL,KAAK,IAAI;AAEZ,UAAM,IAAI,uBAAuB;AAAA,MAC/B,QAAQ,8EAA8E,6BAA6B,cAAc;AAAA,MACjI,SAAS,8DAA8D,GAAG,uLAAuL,OAAO;AAAA,MACxQ,QAAQ,6BAA6B;AAAA,IACvC,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAEA,eAAe,kBAAkB,QAAgB,KAAa,YAAoB;AAChF,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,uBAAuB;AAAA,MAC/B,QAAQ,6BAA6B;AAAA,MACrC,SACE;AAAA,MACF,QAAQ,6BAA6B;AAAA,IACvC,CAAC;AAAA,EACH;AAEA,QAAM,MAAM,IAAI,IAAI,MAAM;AAC1B,MAAI,WAAW,UAAU,IAAI,UAAU,YAAY,OAAO;AAE1D,QAAM,WAAW,MAAM,QAAQ,MAAM,IAAI,MAAM;AAAA,IAC7C,SAAS;AAAA,MACP,eAAe,UAAU,GAAG;AAAA,MAC5B,qBAAqB;AAAA,MACrB,gBAAgB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,EACF,CAAC;AAED,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,OAAO,MAAM,SAAS,KAAK;AACjC,UAAM,wBAAwB,qBAAqB,MAAM,QAAQ,2BAA2B,gBAAgB;AAE5G,QAAI,uBAAuB;AACzB,YAAM,SAAS,6BAA6B;AAE5C,YAAM,IAAI,uBAAuB;AAAA,QAC/B,QAAQ,6BAA6B;AAAA,QACrC,SAAS,sBAAsB;AAAA,QAC/B;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,IAAI,uBAAuB;AAAA,MAC/B,QAAQ,6BAA6B;AAAA,MACrC,SAAS,iCAAiC,IAAI,IAAI,cAAc,SAAS,MAAM;AAAA,MAC/E,QAAQ,6BAA6B;AAAA,IACvC,CAAC;AAAA,EACH;AAEA,SAAO,SAAS,KAAK;AACvB;AAEA,SAAS,kBAAkB;AAEzB,MAAI,kBAAkB,IAAI;AACxB,WAAO;AAAA,EACT;AAGA,QAAM,YAAY,KAAK,IAAI,IAAI,iBAAiB,oCAAoC;AAEpF,MAAI,WAAW;AACb,YAAQ,CAAC;AAAA,EACX;AAEA,SAAO;AACT;AAQA,IAAM,uBAAuB,CAAC,QAAuB,SAAiB;AACpE,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AAEA,SAAO,OAAO,KAAK,CAAC,QAAqB,IAAI,SAAS,IAAI;AAC5D;;;AC3PA,SAAS,+BAA+B;AA6GxC,eAAsB,YACpB,OACA,SAC4D;AAC5D,QAAM,EAAE,MAAM,eAAe,OAAO,IAAI,UAAU,KAAK;AACvD,MAAI,QAAQ;AACV,WAAO,EAAE,OAAO;AAAA,EAClB;AAEA,QAAM,EAAE,OAAO,IAAI;AACnB,QAAM,EAAE,IAAI,IAAI;AAEhB,MAAI;AACF,QAAI;AAEJ,QAAI,QAAQ,QAAQ;AAClB,YAAM,sBAAsB,QAAQ,MAAM;AAAA,IAC5C,WAAW,QAAQ,WAAW;AAE5B,YAAM,MAAM,uBAAuB,EAAE,GAAG,SAAS,IAAI,CAAC;AAAA,IACxD,OAAO;AACL,aAAO;AAAA,QACL,QAAQ;AAAA,UACN,IAAI,uBAAuB;AAAA,YACzB,QAAQ,6BAA6B;AAAA,YACrC,SAAS;AAAA,YACT,QAAQ,6BAA6B;AAAA,UACvC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAEA,WAAO,MAAM,UAAU,OAAO,EAAE,GAAG,SAAS,IAAI,CAAC;AAAA,EACnD,SAAS,OAAO;AACd,WAAO,EAAE,QAAQ,CAAC,KAA+B,EAAE;AAAA,EACrD;AACF;AAQA,SAAS,oBACP,WACA,KACA,iBAC4D;AAC5D,MAAI,wBAAwB,GAAG,GAAG;AAChC,QAAI;AACJ,QAAI;AAEJ,YAAQ,IAAI,QAAQ;AAAA,MAClB,KAAK;AACH,eAAO,kCAAkC;AACzC,kBAAU,IAAI,OAAO,CAAC,GAAG,WAAW;AACpC;AAAA,MACF,KAAK;AACH,eAAO,kCAAkC;AACzC,kBAAU;AACV;AAAA,MACF;AACE,eAAO,kCAAkC;AACzC,kBAAU;AAAA,IACd;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA,QAAQ;AAAA,QACN,IAAI,8BAA8B;AAAA,UAChC;AAAA,UACA;AAAA,UACA,QAAQ,IAAI;AAAA,QACd,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,QAAQ;AAAA,MACN,IAAI,8BAA8B;AAAA,QAChC,SAAS;AAAA,QACT,MAAM,kCAAkC;AAAA,QACxC,QAAQ,IAAI;AAAA,MACd,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,eAAe,mBACb,QACA,SAC8E;AAC9E,MAAI;AACF,UAAM,SAAS,uBAAuB,OAAO;AAC7C,UAAM,gBAAgB,MAAM,OAAO,cAAc,aAAa,MAAM;AACpE,WAAO,EAAE,MAAM,eAAe,WAAW,UAAU,cAAc,QAAQ,OAAU;AAAA,EACrF,SAAS,KAAU;AACjB,WAAO,oBAAoB,UAAU,cAAc,KAAK,yBAAyB;AAAA,EACnF;AACF;AAEA,eAAe,iBACb,aACA,SACqF;AACrF,MAAI;AACF,UAAM,SAAS,uBAAuB,OAAO;AAC7C,UAAM,gBAAgB,MAAM,OAAO,oBAAoB,kBAAkB,WAAW;AACpF,WAAO,EAAE,MAAM,eAAe,WAAW,UAAU,YAAY,QAAQ,OAAU;AAAA,EACnF,SAAS,KAAU;AACjB,WAAO,oBAAoB,UAAU,YAAY,KAAK,uBAAuB;AAAA,EAC/E;AACF;AAEA,eAAe,aACb,QACA,SACwE;AACxE,MAAI;AACF,UAAM,SAAS,uBAAuB,OAAO;AAC7C,UAAM,gBAAgB,MAAM,OAAO,QAAQ,aAAa,MAAM;AAC9D,WAAO,EAAE,MAAM,eAAe,WAAW,UAAU,QAAQ,QAAQ,OAAU;AAAA,EAC/E,SAAS,KAAU;AACjB,WAAO,oBAAoB,UAAU,QAAQ,KAAK,mBAAmB;AAAA,EACvE;AACF;AAQA,eAAsB,uBAAuB,OAAe,SAA6B;AACvF,MAAI,MAAM,WAAW,gBAAgB,GAAG;AACtC,WAAO,mBAAmB,OAAO,OAAO;AAAA,EAC1C;AACA,MAAI,MAAM,WAAW,kBAAkB,GAAG;AACxC,WAAO,iBAAiB,OAAO,OAAO;AAAA,EACxC;AACA,MAAI,MAAM,WAAW,cAAc,GAAG;AACpC,WAAO,aAAa,OAAO,OAAO;AAAA,EACpC;AAEA,QAAM,IAAI,MAAM,4BAA4B;AAC9C;;;ACnPA,eAAe,mBAAmB,OAAe,EAAE,IAAI,GAAuD;AAC5G,QAAM,EAAE,MAAM,SAAS,OAAO,IAAI,UAAU,KAAK;AACjD,MAAI,QAAQ;AACV,UAAM,OAAO,CAAC;AAAA,EAChB;AAEA,QAAM,EAAE,QAAQ,QAAQ,IAAI;AAG5B,QAAM,EAAE,KAAK,IAAI,IAAI;AAErB,mBAAiB,GAAG;AACpB,wBAAsB,GAAG;AAEzB,QAAM,EAAE,MAAM,gBAAgB,QAAQ,gBAAgB,IAAI,MAAM,kBAAkB,SAAS,GAAG;AAC9F,MAAI,iBAAiB;AACnB,UAAM,IAAI,uBAAuB;AAAA,MAC/B,QAAQ,6BAA6B;AAAA,MACrC,SAAS,oCAAoC,gBAAgB,CAAC,CAAC;AAAA,IACjE,CAAC;AAAA,EACH;AAEA,MAAI,CAAC,gBAAgB;AACnB,UAAM,IAAI,uBAAuB;AAAA,MAC/B,QAAQ,6BAA6B;AAAA,MACrC,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAMA,eAAsB,qBACpB,OACA,SACkC;AAClC,QAAM,EAAE,WAAW,QAAQ,YAAY,kBAAkB,QAAQ,cAAc,IAAI;AAEnF,QAAM,EAAE,MAAM,OAAO,IAAI,UAAU,KAAK;AACxC,MAAI,QAAQ;AACV,UAAM,OAAO,CAAC;AAAA,EAChB;AAEA,QAAM,EAAE,IAAI,IAAI,KAAK;AAErB,MAAI;AAEJ,MAAI,QAAQ;AACV,UAAM,sBAAsB,MAAM;AAAA,EACpC,WAAW,WAAW;AAEpB,UAAM,MAAM,uBAAuB,EAAE,WAAW,QAAQ,YAAY,KAAK,kBAAkB,cAAc,CAAC;AAAA,EAC5G,OAAO;AACL,UAAM,IAAI,uBAAuB;AAAA,MAC/B,QAAQ,6BAA6B;AAAA,MACrC,SAAS;AAAA,MACT,QAAQ,6BAA6B;AAAA,IACvC,CAAC;AAAA,EACH;AAEA,SAAO,MAAM,mBAAmB,OAAO;AAAA,IACrC;AAAA,EACF,CAAC;AACH;AAEO,IAAM,mBAAN,MAAuB;AAAA,EAK5B,YACE,qBACA,SACA,qBACA;AACA,SAAK,sBAAsB;AAC3B,SAAK,UAAU;AACf,SAAK,sBAAsB;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,gCAAyC;AACvC,UAAM,EAAE,QAAQ,aAAa,IAAI,KAAK;AAItC,QAAI,iBAAiB,cAAc,iBAAiB,UAAU;AAC5D,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,gBAAgB,QAAQ,WAAW,WAAW,GAAG;AACpD,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,yBAAyB,QAAyB;AAChD,QAAI,CAAC,KAAK,qBAAqB,UAAU;AACvC,YAAM,IAAI,MAAM,yCAAyC;AAAA,IAC3D;AAEA,UAAM,cAAc,KAAK,wBAAwB,KAAK,oBAAoB,QAAQ;AAClF,UAAM,wBAAwB,KAAK,oBAAoB,YAAY,QAAQ,iBAAiB,EAAE;AAE9F,UAAM,UAAU,KAAK,oBAAoB,WACrC,KAAK,oBAAoB,SAAS,QAAQ,OAAO,EAAE,IACnD,WAAW,qBAAqB;AAEpC,UAAM,MAAM,IAAI,IAAI,GAAG,OAAO,sBAAsB;AACpD,QAAI,aAAa,OAAO,gBAAgB,aAAa,QAAQ,EAAE;AAC/D,QAAI,aAAa,OAAO,uBAAuB,sBAAsB;AACrE,QAAI,aAAa;AAAA,MACf,UAAU,gBAAgB;AAAA,MAC1B,KAAK,oBAAoB,oBAAoB,EAAE,SAAS;AAAA,IAC1D;AACA,QAAI,aAAa,OAAO,UAAU,gBAAgB,iBAAiB,MAAM;AAEzE,QAAI,KAAK,oBAAoB,iBAAiB,iBAAiB,KAAK,oBAAoB,iBAAiB;AACvG,UAAI,aAAa,OAAO,UAAU,gBAAgB,YAAY,KAAK,oBAAoB,eAAe;AAAA,IACxG;AAEA,UAAM,aAAa,KAAK,0BAA0B,KAAK,oBAAoB,UAAU,KAAK,mBAAmB;AAC7G,QAAI,YAAY;AACd,YAAM,SAAS,KAAK,+BAA+B,UAAU;AAC7D,aAAO,QAAQ,CAAC,OAAO,QAAQ;AAC7B,YAAI,aAAa,OAAO,KAAK,KAAK;AAAA,MACpC,CAAC;AAAA,IACH;AAEA,WAAO,IAAI,QAAQ,EAAE,CAAC,UAAU,QAAQ,QAAQ,GAAG,IAAI,KAAK,CAAC;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAa,0BAA6C;AACxD,UAAM,eAAyB,CAAC;AAEhC,QAAI,KAAK,oBAAoB,gBAAgB;AAC3C,UAAI;AACF,cAAM,mBAAmB,MAAM,KAAK,oBAAoB,WAAW,QAAQ,oBAAoB;AAAA,UAC7F,OAAO,KAAK,oBAAoB;AAAA,QAClC,CAAC;AACD,YAAI,kBAAkB;AACpB,uBAAa,KAAK,GAAG,iBAAiB,UAAU;AAAA,QAClD;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,6DAA6D,KAAK;AAAA,MAClF;AAAA,IACF,WAAW,KAAK,oBAAoB,gBAAgB;AAClD,YAAM,mBAAmB,MAAM;AAAA,QAC7B,KAAK,oBAAoB;AAAA,QACzB,KAAK;AAAA,MACP;AACA,UAAI,oBAAoB,MAAM,QAAQ,iBAAiB,SAAS,GAAG;AACjE,qBAAa,KAAK,GAAG,iBAAiB,SAAS;AAAA,MACjD;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,mBAA4D;AAChE,UAAM,UAAU,IAAI,QAAQ;AAAA,MAC1B,+BAA+B;AAAA,MAC/B,oCAAoC;AAAA,IACtC,CAAC;AAED,UAAM,eAAe,MAAM,KAAK,wBAAwB;AAExD,QAAI,eAAe;AACnB,iBAAa,QAAQ,CAAC,MAAc;AAClC,cAAQ,OAAO,cAAc,CAAC;AAC9B,UAAI,cAAc,CAAC,EAAE,WAAW,UAAU,QAAQ,OAAO,GAAG;AAC1D,uBAAe,eAAe,CAAC;AAAA,MACjC;AAAA,IACF,CAAC;AAED,QAAI,KAAK,oBAAoB,iBAAiB,eAAe;AAC3D,YAAM,SAAS,IAAI,IAAI,KAAK,oBAAoB,QAAQ;AACxD,aAAO,aAAa,OAAO,UAAU,gBAAgB,SAAS;AAC9D,aAAO,aAAa,OAAO,UAAU,gBAAgB,aAAa;AAClE,cAAQ,OAAO,UAAU,QAAQ,UAAU,OAAO,SAAS,CAAC;AAC5D,cAAQ,IAAI,UAAU,QAAQ,cAAc,UAAU;AAAA,IACxD;AAEA,QAAI,iBAAiB,IAAI;AACvB,aAAO,UAAU;AAAA,QACf,WAAW,UAAU;AAAA,QACrB,qBAAqB,KAAK;AAAA,QAC1B,QAAQ,gBAAgB;AAAA,QACxB,SAAS;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,EAAE,MAAM,QAAQ,CAAC,KAAK,IAAI,CAAC,EAAE,IAAI,MAAM,YAAY,cAAc,KAAK,mBAAmB;AAC/F,QAAI,MAAM;AACR,aAAO,SAAS;AAAA,QACd,WAAW,UAAU;AAAA,QACrB,qBAAqB,KAAK;AAAA,QAC1B,eAAe;AAAA,QACf;AAAA,QACA,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAEA,QACE,KAAK,oBAAoB,iBAAiB,kBACzC,OAAO,WAAW,6BAA6B,gBAC9C,OAAO,WAAW,6BAA6B,qBAC/C,OAAO,WAAW,6BAA6B,sBACjD;AAEA,YAAM,mBAAmB,IAAI,uBAAuB;AAAA,QAClD,QAAQ,MAAM;AAAA,QACd,SAAS,MAAM;AAAA,QACf,QAAQ,MAAM;AAAA,MAChB,CAAC;AAED,uBAAiB,eAAe;AAEhC,cAAQ;AAAA,QACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMN,iBAAiB,eAAe,CAAC;AAAA,MAC7B;AAEA,YAAM,EAAE,MAAM,aAAa,QAAQ,CAAC,UAAU,IAAI,CAAC,EAAE,IAAI,MAAM,YAAY,cAAc;AAAA,QACvF,GAAG,KAAK;AAAA,QACR,eAAe;AAAA,MACjB,CAAC;AACD,UAAI,aAAa;AACf,eAAO,SAAS;AAAA,UACd,WAAW,UAAU;AAAA,UACrB,qBAAqB,KAAK;AAAA,UAC1B,eAAe;AAAA,UACf;AAAA,UACA,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AAEA,YAAM,IAAI,MAAM,YAAY,WAAW,gCAAgC;AAAA,IACzE;AAEA,UAAM,IAAI,MAAM,OAAO,WAAW,0BAA0B;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,0CAA0C,OAAqC;AAO7E,QAAI,MAAM,WAAW,6BAA6B,uBAAuB;AACvE,YAAM,MAAM;AACZ,YAAM,IAAI,MAAM,GAAG;AAAA,IACrB;AACA,UAAM,IAAI,MAAM,+CAA+C,MAAM,eAAe,CAAC,GAAG;AAAA,EAC1F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,0BAA0B,SAA2B;AACnD,QAAI,KAAK,oBAAoB,iCAAiC,GAAG;AAC/D,aAAO;AAAA,IACT;AAEA,UAAM,kBAAkB,KAAK,oBAAoB,+BAA+B;AAChF,UAAM,aAAa,UAAU,QAAQ;AACrC,YAAQ,OAAO,cAAc,GAAG,UAAU,IAAI,eAAe,qCAAqC;AAClG,WAAO;AAAA,EACT;AAAA,EAEQ,wBAAwB,KAAe;AAC7C,UAAM,aAAa,IAAI,IAAI,GAAG;AAC9B,eAAW,aAAa,OAAO,UAAU,gBAAgB,UAAU;AACnE,eAAW,aAAa,OAAO,UAAU,gBAAgB,gBAAgB;AACzE,WAAO;AAAA,EACT;AAAA,EAEQ,0BAA0B,KAAU,UAA8D;AACxG,WAAO,SAAS,WAAW,GAAG;AAAA,EAChC;AAAA,EAEQ,+BAA+B,YAAyD;AAC9F,UAAM,MAAM,oBAAI,IAAI;AACpB,QAAI,WAAW,SAAS,mBAAmB;AACzC,UAAI,IAAI,mBAAmB,EAAE;AAAA,IAC/B;AACA,QAAI,WAAW,SAAS,gBAAgB;AACtC,UAAI,WAAW,gBAAgB;AAC7B,YAAI,IAAI,mBAAmB,WAAW,cAAc;AAAA,MACtD;AACA,UAAI,WAAW,kBAAkB;AAC/B,YAAI,IAAI,mBAAmB,WAAW,gBAAgB;AAAA,MACxD;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;;;AChWA,SAAS,aAAa;AAIf,IAAM,sBAAN,MAA0B;AAAA,EAI/B,YAAY,SAAmC;AAC7C,SAAK,sBAAsB,KAAK,cAAc,SAAS,oBAAoB;AAC3E,SAAK,yBAAyB,KAAK,cAAc,SAAS,uBAAuB;AAAA,EACnF;AAAA,EAEQ,cAAc,SAA0C;AAC9D,QAAI,CAAC,QAAS,QAAO;AACrB,QAAI;AACF,aAAO,MAAM,OAAO;AAAA,IACtB,SAAS,GAAG;AACV,YAAM,IAAI,MAAM,oBAAoB,OAAO,MAAM,CAAC,EAAE;AAAA,IACtD;AAAA,EACF;AAAA,EAEA,WAAW,KAAyC;AAClD,UAAM,YAAY,KAAK,uBAAuB,GAAG;AACjD,QAAI,UAAW,QAAO;AAEtB,WAAO,KAAK,0BAA0B,GAAG;AAAA,EAC3C;AAAA,EAEQ,uBAAuB,KAAyC;AACtE,QAAI,CAAC,KAAK,oBAAqB,QAAO;AAEtC,QAAI;AACF,YAAM,SAAS,KAAK,oBAAoB,IAAI,QAAQ;AACpD,UAAI,CAAC,UAAU,EAAE,YAAY,QAAS,QAAO;AAE7C,YAAM,SAAS,OAAO;AACtB,UAAI,OAAO,GAAI,QAAO,EAAE,MAAM,gBAAgB,gBAAgB,OAAO,GAAG;AACxE,UAAI,OAAO,KAAM,QAAO,EAAE,MAAM,gBAAgB,kBAAkB,OAAO,KAAK;AAE9E,aAAO;AAAA,IACT,SAAS,GAAG;AACV,cAAQ,MAAM,yCAAyC,CAAC;AACxD,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEQ,0BAA0B,KAAyC;AACzE,QAAI,CAAC,KAAK,uBAAwB,QAAO;AAEzC,QAAI;AACF,YAAM,SAAS,KAAK,uBAAuB,IAAI,QAAQ;AACvD,aAAO,SAAS,EAAE,MAAM,kBAAkB,IAAI;AAAA,IAChD,SAAS,GAAG;AACV,cAAQ,MAAM,6CAA6C,CAAC;AAC5D,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;ACpCO,IAAM,0BAA0B;AAAA,EACrC,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,kBAAkB;AAAA,EAClB,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,YAAY;AAAA,EACZ,oBAAoB;AAAA,EACpB,qBAAqB;AACvB;AAEA,SAAS,sBAAsB,WAA+B,KAA0C;AACtG,MAAI,CAAC,aAAa,2BAA2B,GAAG,GAAG;AACjD,UAAM,IAAI,MAAM,8EAA8E;AAAA,EAChG;AACF;AAEA,SAAS,uBAAuB,kBAAsC;AACpE,MAAI,CAAC,kBAAkB;AACrB,UAAM,IAAI,MAAM,8FAA8F;AAAA,EAChH;AACF;AAEA,SAAS,+BAA+B,YAAoB,QAAgB;AAC1E,MAAI;AACJ,MAAI;AACF,gBAAY,IAAI,IAAI,UAAU;AAAA,EAChC,QAAQ;AACN,UAAM,IAAI,MAAM,oDAAoD;AAAA,EACtE;AAEA,MAAI,UAAU,WAAW,QAAQ;AAC/B,UAAM,IAAI,MAAM,kFAAkF;AAAA,EACpG;AACF;AAEA,SAAS,4BACP,KACA,qBACA,SACA;AACA,SACE,IAAI,WAAW,6BAA6B,gBAC5C,CAAC,CAAC,oBAAoB,wBACtB,QAAQ,WAAW;AAEvB;AAEA,SAAS,uBACP,iBACA,cACA,qBAC+C;AAC/C,QAAM,WAAW,CAAC,oBAAoB,iBAAiB,YAAY;AACnE,MAAI,UAAU;AACZ,WAAO,UAAU;AAAA,MACf,WAAW;AAAA,MACX;AAAA,MACA,QAAQ,gBAAgB;AAAA,IAC1B,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAkCO,IAAM,sBAA4C,OACvD,SACA,YACqC;AACrC,QAAM,sBAAsB,MAAM,0BAA0B,mBAAmB,OAAO,GAAG,OAAO;AAChG,uBAAqB,oBAAoB,SAAS;AAGlD,QAAM,eAAe,QAAQ,gBAAgB,UAAU;AAEvD,MAAI,oBAAoB,aAAa;AACnC,0BAAsB,oBAAoB,WAAW,oBAAoB,SAAS;AAClF,QAAI,oBAAoB,aAAa,oBAAoB,QAAQ;AAC/D,qCAA+B,oBAAoB,WAAW,oBAAoB,MAAM;AAAA,IAC1F;AACA,2BAAuB,oBAAoB,YAAY,oBAAoB,MAAM;AAAA,EACnF;AAEA,QAAM,sBAAsB,IAAI,oBAAoB,QAAQ,uBAAuB;AACnF,QAAM,mBAAmB,IAAI;AAAA,IAC3B;AAAA,IACA,EAAE,yBAAyB,QAAQ,wBAAwB;AAAA,IAC3D;AAAA,EACF;AAEA,iBAAe,aACbC,sBACuE;AAEvE,QAAI,CAAC,QAAQ,WAAW;AACtB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO,EAAE,QAAQ,wBAAwB,iBAAiB;AAAA,QAC5D;AAAA,MACF;AAAA,IACF;AACA,UAAM,EAAE,cAAc,qBAAqB,sBAAsBC,cAAa,IAAID;AAClF,QAAI,CAAC,qBAAqB;AACxB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO,EAAE,QAAQ,wBAAwB,oBAAoB;AAAA,QAC/D;AAAA,MACF;AAAA,IACF;AACA,QAAI,CAACC,eAAc;AACjB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO,EAAE,QAAQ,wBAAwB,oBAAoB;AAAA,QAC/D;AAAA,MACF;AAAA,IACF;AAEA,UAAM,EAAE,MAAM,cAAc,QAAQ,cAAc,IAAI,UAAU,mBAAmB;AACnF,QAAI,CAAC,gBAAgB,eAAe;AAClC,aAAO;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO,EAAE,QAAQ,wBAAwB,iCAAiC,QAAQ,cAAc;AAAA,QAClG;AAAA,MACF;AAAA,IACF;AAEA,QAAI,CAAC,cAAc,SAAS,KAAK;AAC/B,aAAO;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO,EAAE,QAAQ,wBAAwB,mCAAmC;AAAA,QAC9E;AAAA,MACF;AAAA,IACF;AAEA,QAAI;AAEF,YAAM,WAAW,MAAM,QAAQ,UAAU,SAAS,eAAe,aAAa,QAAQ,KAAK;AAAA,QACzF,QAAQ;AAAA,QACR,kBAAkBD,qBAAoB,oBAAoB;AAAA,QAC1D,eAAe,uBAAuB;AAAA,QACtC,eAAeC,iBAAgB;AAAA,QAC/B,gBAAgBD,qBAAoB,SAAS;AAAA;AAAA,QAE7C,iBAAiB,OAAO,YAAY,MAAM,KAAK,QAAQ,QAAQ,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,MACrG,CAAC;AACD,aAAO,EAAE,MAAM,SAAS,SAAS,OAAO,KAAK;AAAA,IAC/C,SAAS,KAAU;AACjB,UAAI,KAAK,QAAQ,QAAQ;AACvB,YAAI,IAAI,OAAO,CAAC,EAAE,SAAS,oBAAoB;AAC7C,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,OAAO;AAAA,cACL,SAAS;AAAA,cACT,OAAO,EAAE,QAAQ,wBAAwB,YAAY,QAAQ,IAAI,OAAO;AAAA,YAC1E;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,YACL,SAAS,IAAI,OAAO,CAAC,EAAE;AAAA,YACvB,OAAO,EAAE,QAAQ,IAAI,OAAO,CAAC,EAAE,MAAM,QAAQ,IAAI,OAAO;AAAA,UAC1D;AAAA,QACF;AAAA,MACF,OAAO;AACL,eAAO;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,YACL,SAAS;AAAA,YACT,OAAO,EAAE,QAAQ,wBAAwB,qBAAqB,QAAQ,CAAC,GAAG,EAAE;AAAA,UAC9E;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,iBAAe,eACbA,sBAIA;AACA,UAAM,EAAE,MAAM,cAAc,MAAM,IAAI,MAAM,aAAaA,oBAAmB;AAC5E,QAAI,CAAC,gBAAgB,aAAa,WAAW,GAAG;AAC9C,aAAO,EAAE,MAAM,MAAM,MAAM;AAAA,IAC7B;AAEA,UAAM,UAAU,IAAI,QAAQ;AAC5B,QAAI,eAAe;AACnB,iBAAa,QAAQ,CAAC,MAAc;AAClC,cAAQ,OAAO,cAAc,CAAC;AAC9B,UAAI,cAAc,CAAC,EAAE,WAAW,UAAU,QAAQ,OAAO,GAAG;AAC1D,uBAAe,eAAe,CAAC;AAAA,MACjC;AAAA,IACF,CAAC;AAGD,UAAM,EAAE,MAAM,YAAY,OAAO,IAAI,MAAM,YAAY,cAAcA,oBAAmB;AACxF,QAAI,QAAQ;AACV,aAAO;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO,EAAE,QAAQ,wBAAwB,qBAAqB,OAAO;AAAA,QACvE;AAAA,MACF;AAAA,IACF;AACA,WAAO,EAAE,MAAM,EAAE,YAAY,cAAc,QAAQ,GAAG,OAAO,KAAK;AAAA,EACpE;AAEA,WAAS,2BACPA,sBACA,QACA,SACA,SACiD;AACjD,QAAI,CAAC,iBAAiB,8BAA8B,GAAG;AACrD,aAAO,UAAU;AAAA,QACf,WAAW,UAAU;AAAA,QACrB,qBAAAA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAIA,UAAM,mBAAmB,WAAW,iBAAiB,yBAAyB,MAAM;AAIpF,QAAI,iBAAiB,IAAI,UAAU,QAAQ,QAAQ,GAAG;AACpD,uBAAiB,IAAI,UAAU,QAAQ,cAAc,UAAU;AAAA,IACjE;AAKA,UAAM,iBAAiB,iBAAiB,0BAA0B,gBAAgB;AAClF,QAAI,gBAAgB;AAClB,YAAM,MAAM;AACZ,cAAQ,IAAI,GAAG;AACf,aAAO,UAAU;AAAA,QACf,WAAW,UAAU;AAAA,QACrB,qBAAAA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAEA,WAAO,UAAUA,sBAAqB,QAAQ,SAAS,gBAAgB;AAAA,EACzE;AAWA,WAAS,qCACPA,sBACA,MACwC;AACxC,UAAM,yBAAyB,oBAAoB,WAAWA,qBAAoB,QAAQ;AAC1F,QAAI,CAAC,wBAAwB;AAC3B,aAAO;AAAA,IACT;AACA,QAAI,eAAe;AACnB,QAAI,uBAAuB,SAAS,gBAAgB;AAElD,UAAI,uBAAuB,oBAAoB,uBAAuB,qBAAqB,KAAK,SAAS;AACvG,uBAAe;AAAA,MACjB;AAEA,UAAI,uBAAuB,kBAAkB,uBAAuB,mBAAmB,KAAK,OAAO;AACjG,uBAAe;AAAA,MACjB;AAAA,IACF;AAEA,QAAI,uBAAuB,SAAS,qBAAqB,KAAK,OAAO;AACnE,qBAAe;AAAA,IACjB;AACA,QAAI,CAAC,cAAc;AACjB,aAAO;AAAA,IACT;AACA,QAAIA,qBAAoB,+BAA+B,GAAG;AAKxD,cAAQ;AAAA,QACN;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB;AAAA,MACrBA;AAAA,MACA,gBAAgB;AAAA,MAChB;AAAA,IACF;AACA,QAAI,eAAe,WAAW,aAAa;AAEzC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAEA,iBAAe,uCAAuC;AACpD,UAAM,EAAE,cAAc,IAAI;AAE1B,QAAI;AAEF,YAAM,EAAE,MAAM,OAAO,IAAI,MAAM,YAAY,eAAgB,mBAAmB;AAC9E,UAAI,QAAQ;AACV,cAAM,OAAO,CAAC;AAAA,MAChB;AAEA,aAAO,SAAS;AAAA,QACd,WAAW,UAAU;AAAA,QACrB;AAAA,QACA,eAAe;AAAA,QACf,SAAS,IAAI,QAAQ;AAAA;AAAA,QAErB,OAAO;AAAA,MACT,CAAC;AAAA,IACH,SAAS,KAAK;AACZ,aAAO,wBAAwB,KAAK,QAAQ;AAAA,IAC9C;AAAA,EACF;AAEA,iBAAe,uCAAuC;AACpD,UAAM,kBAAkB,oBAAoB;AAC5C,UAAM,kBAAkB,CAAC,CAAC,oBAAoB;AAC9C,UAAM,qBAAqB,CAAC,CAAC,oBAAoB;AAKjD,QAAI,oBAAoB,kBAAkB,oBAAoB,gBAAgB;AAC5E,UAAI;AACF,eAAO,MAAM,iBAAiB,iBAAiB;AAAA,MACjD,SAAS,OAAO;AAYd,YAAI,iBAAiB,0BAA0B,oBAAoB,iBAAiB,eAAe;AACjG,2BAAiB,0CAA0C,KAAK;AAAA,QAClE,OAAO;AACL,kBAAQ,MAAM,uCAAuC,KAAK;AAAA,QAC5D;AAAA,MACF;AAAA,IACF;AAIA,QACE,oBAAoB,iBAAiB,iBACrC,oBAAoB,SAAS,aAAa,IAAI,UAAU,gBAAgB,UAAU,GAClF;AACA,aAAO,2BAA2B,qBAAqB,gBAAgB,gBAAgB,EAAE;AAAA,IAC3F;AAEA,UAAM,sCACJ,oBAAoB,eAAe,oBAAoB,iBAAiB;AAK1E,QAAI,oBAAoB,iBAAiB,gBAAgB,qCAAqC;AAC5F,aAAO,2BAA2B,qBAAqB,gBAAgB,6BAA6B,EAAE;AAAA,IACxG;AAGA,QACE,oBAAoB,iBAAiB,iBACrC,uCACA,CAAC,oBAAoB,SAAS,aAAa,IAAI,UAAU,gBAAgB,WAAW,GACpF;AAKA,YAAM,cAAc,IAAI,IAAI,oBAAoB,SAAU;AAC1D,kBAAY,aAAa;AAAA,QACvB,UAAU,gBAAgB;AAAA,QAC1B,oBAAoB,SAAS,SAAS;AAAA,MACxC;AACA,YAAM,UAAU,IAAI,QAAQ,EAAE,CAAC,UAAU,QAAQ,QAAQ,GAAG,YAAY,SAAS,EAAE,CAAC;AACpF,aAAO,2BAA2B,qBAAqB,gBAAgB,6BAA6B,IAAI,OAAO;AAAA,IACjH;AAGA,UAAM,cAAc,IAAI,IAAI,oBAAoB,QAAQ,EAAE,aAAa;AAAA,MACrE,UAAU,gBAAgB;AAAA,IAC5B;AAEA,QAAI,oBAAoB,iBAAiB,iBAAiB,CAAC,oBAAoB,eAAe,aAAa;AAEzG,YAAM,6BAA6B,IAAI,IAAI,WAAW;AAEtD,UAAI,oBAAoB,iBAAiB;AACvC,mCAA2B,aAAa;AAAA,UACtC,UAAU,gBAAgB;AAAA,UAC1B,oBAAoB;AAAA,QACtB;AAAA,MACF;AACA,iCAA2B,aAAa,OAAO,UAAU,gBAAgB,aAAa,MAAM;AAE5F,YAAM,UAAU,IAAI,QAAQ,EAAE,CAAC,UAAU,QAAQ,QAAQ,GAAG,2BAA2B,SAAS,EAAE,CAAC;AACnG,aAAO,2BAA2B,qBAAqB,gBAAgB,0BAA0B,IAAI,OAAO;AAAA,IAC9G;AAKA,QAAI,oBAAoB,iBAAiB,iBAAiB,CAAC,oBAAoB;AAC7E,aAAO,2BAA2B,qBAAqB,gBAAgB,mBAAmB,EAAE;AAAA,IAC9F;AAEA,QAAI,CAAC,mBAAmB,CAAC,iBAAiB;AACxC,aAAO,UAAU;AAAA,QACf,WAAW,UAAU;AAAA,QACrB;AAAA,QACA,QAAQ,gBAAgB;AAAA,MAC1B,CAAC;AAAA,IACH;AAGA,QAAI,CAAC,mBAAmB,iBAAiB;AACvC,aAAO,2BAA2B,qBAAqB,gBAAgB,8BAA8B,EAAE;AAAA,IACzG;AAEA,QAAI,mBAAmB,CAAC,iBAAiB;AACvC,aAAO,2BAA2B,qBAAqB,gBAAgB,8BAA8B,EAAE;AAAA,IACzG;AAGA,UAAM,EAAE,MAAM,cAAc,QAAQ,cAAc,IAAI,UAAU,oBAAoB,oBAAqB;AAEzG,QAAI,eAAe;AACjB,aAAO,wBAAwB,cAAc,CAAC,GAAG,QAAQ;AAAA,IAC3D;AAEA,QAAI,aAAa,QAAQ,MAAM,oBAAoB,WAAW;AAC5D,aAAO,2BAA2B,qBAAqB,gBAAgB,gCAAgC,EAAE;AAAA,IAC3G;AAEA,QAAI;AAEF,YAAM,EAAE,MAAM,OAAO,IAAI,MAAM,YAAY,oBAAoB,sBAAuB,mBAAmB;AACzG,UAAI,QAAQ;AACV,cAAM,OAAO,CAAC;AAAA,MAChB;AAEA,YAAM,uBAAuB,SAAS;AAAA,QACpC,WAAW,UAAU;AAAA,QACrB;AAAA,QACA,eAAe;AAAA,QACf,SAAS,IAAI,QAAQ;AAAA;AAAA,QAErB,OAAO,oBAAoB;AAAA,MAC7B,CAAC;AAED,YAAM,aAAa,qBAAqB,OAAO;AAE/C,UAAI,WAAW,QAAQ;AACrB,cAAM,wBAAwB,qCAAqC,qBAAqB,UAAU;AAClG,YAAI,uBAAuB;AACzB,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACT,SAAS,KAAK;AACZ,aAAO,wBAAwB,KAAK,QAAQ;AAAA,IAC9C;AAGA,WAAO,UAAU;AAAA,MACf,WAAW,UAAU;AAAA,MACrB;AAAA,MACA,QAAQ,gBAAgB;AAAA,IAC1B,CAAC;AAAA,EACH;AAEA,iBAAe,wBACb,KACA,cAC0D;AAC1D,QAAI,EAAE,eAAe,yBAAyB;AAC5C,aAAO,UAAU;AAAA,QACf,WAAW,UAAU;AAAA,QACrB;AAAA,QACA,QAAQ,gBAAgB;AAAA,MAC1B,CAAC;AAAA,IACH;AAEA,QAAI;AAEJ,QAAI,4BAA4B,KAAK,qBAAqB,OAAO,GAAG;AAClE,YAAM,EAAE,MAAM,MAAM,IAAI,MAAM,eAAe,mBAAmB;AAChE,UAAI,MAAM;AACR,eAAO,SAAS;AAAA,UACd,WAAW,UAAU;AAAA,UACrB;AAAA,UACA,eAAe,KAAK;AAAA,UACpB,SAAS,KAAK;AAAA,UACd,OAAO,KAAK;AAAA,QACd,CAAC;AAAA,MACH;AAGA,UAAI,OAAO,OAAO,QAAQ;AACxB,uBAAe,MAAM,MAAM;AAAA,MAC7B,OAAO;AACL,uBAAe,wBAAwB;AAAA,MACzC;AAAA,IACF,OAAO;AACL,UAAI,QAAQ,WAAW,OAAO;AAC5B,uBAAe,wBAAwB;AAAA,MACzC,WAAW,CAAC,oBAAoB,sBAAsB;AACpD,uBAAe,wBAAwB;AAAA,MACzC,OAAO;AAEL,uBAAe;AAAA,MACjB;AAAA,IACF;AAEA,QAAI,eAAe;AAEnB,UAAM,oBAAoB;AAAA,MACxB,6BAA6B;AAAA,MAC7B,6BAA6B;AAAA,MAC7B,6BAA6B;AAAA,IAC/B,EAAE,SAAS,IAAI,MAAM;AAErB,QAAI,mBAAmB;AACrB,aAAO;AAAA,QACL;AAAA,QACA,qDAAqD,EAAE,YAAY,IAAI,QAAQ,aAAa,CAAC;AAAA,QAC7F,IAAI,eAAe;AAAA,MACrB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA,MACf,WAAW,UAAU;AAAA,MACrB;AAAA,MACA,QAAQ,IAAI;AAAA,MACZ,SAAS,IAAI,eAAe;AAAA,IAC9B,CAAC;AAAA,EACH;AAEA,WAAS,mBAAmB,WAA6B,KAAsD;AAC7G,QAAI,EAAE,eAAe,gCAAgC;AACnD,aAAO,UAAU;AAAA,QACf;AAAA,QACA;AAAA,QACA,QAAQ,gBAAgB;AAAA,MAC1B,CAAC;AAAA,IACH;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA,QAAQ,IAAI;AAAA,MACZ,SAAS,IAAI,eAAe;AAAA,IAC9B,CAAC;AAAA,EACH;AAEA,iBAAe,8CAA8C;AAC3D,UAAM,EAAE,cAAc,IAAI;AAE1B,QAAI,CAAC,eAAe;AAClB,aAAO,wBAAwB,IAAI,MAAM,yBAAyB,GAAG,QAAQ;AAAA,IAC/E;AAGA,QAAI,CAAC,uBAAuB,aAAa,GAAG;AAC1C,aAAO,UAAU;AAAA,QACf,WAAW;AAAA,QACX;AAAA,QACA,QAAQ,gBAAgB;AAAA,QACxB,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,UAAM,kBAAkB,oBAAoB,aAAa;AACzD,UAAM,gBAAgB,uBAAuB,iBAAiB,cAAc,mBAAmB;AAC/F,QAAI,eAAe;AACjB,aAAO;AAAA,IACT;AAEA,UAAM,EAAE,MAAM,WAAW,OAAO,IAAI,MAAM,uBAAuB,eAAe,mBAAmB;AACnG,QAAI,QAAQ;AACV,aAAO,mBAAmB,WAAW,OAAO,CAAC,CAAC;AAAA,IAChD;AACA,WAAO,SAAS;AAAA,MACd;AAAA,MACA;AAAA,MACA,aAAa;AAAA,MACb,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AAEA,iBAAe,0CAA0C;AACvD,UAAM,EAAE,cAAc,IAAI;AAE1B,QAAI,CAAC,eAAe;AAClB,aAAO,wBAAwB,IAAI,MAAM,yBAAyB,GAAG,QAAQ;AAAA,IAC/E;AAGA,QAAI,uBAAuB,aAAa,GAAG;AACzC,YAAM,kBAAkB,oBAAoB,aAAa;AACzD,YAAM,gBAAgB,uBAAuB,iBAAiB,cAAc,mBAAmB;AAC/F,UAAI,eAAe;AACjB,eAAO;AAAA,MACT;AAEA,YAAM,EAAE,MAAAE,OAAM,WAAW,QAAAC,QAAO,IAAI,MAAM,uBAAuB,eAAe,mBAAmB;AACnG,UAAIA,SAAQ;AACV,eAAO,mBAAmB,WAAWA,QAAO,CAAC,CAAC;AAAA,MAChD;AAEA,aAAO,SAAS;AAAA,QACd;AAAA,QACA;AAAA,QACA,aAAaD;AAAA,QACb,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAGA,UAAM,EAAE,MAAM,OAAO,IAAI,MAAM,YAAY,eAAe,mBAAmB;AAC7E,QAAI,QAAQ;AACV,aAAO,wBAAwB,OAAO,CAAC,GAAG,QAAQ;AAAA,IACpD;AAEA,WAAO,SAAS;AAAA,MACd,WAAW,UAAU;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,MACf,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AAEA,MAAI,oBAAoB,eAAe;AACrC,QAAI,iBAAiB,OAAO;AAC1B,aAAO,wCAAwC;AAAA,IACjD;AAEA,QAAI,iBAAiB,UAAU,cAAc;AAC3C,aAAO,qCAAqC;AAAA,IAC9C;AAEA,WAAO,4CAA4C;AAAA,EACrD;AAGA,MACE,iBAAiB,UAAU,cAC3B,iBAAiB,UAAU,UAC3B,iBAAiB,UAAU,cAC3B;AACA,WAAO,UAAU;AAAA,MACf,WAAW;AAAA,MACX;AAAA,MACA,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AAEA,SAAO,qCAAqC;AAC9C;AAKO,IAAM,oBAAoB,CAAC,WAAyB;AACzD,QAAM,EAAE,YAAY,iBAAiB,UAAU,QAAQ,SAAS,gBAAgB,aAAa,OAAO,IAAI;AACxG,SAAO,EAAE,YAAY,iBAAiB,UAAU,QAAQ,SAAS,gBAAgB,aAAa,OAAO;AACvG;AAEA,IAAM,uDAAuD,CAAC;AAAA,EAC5D;AAAA,EACA;AACF,MAGc;AACZ,UAAQ,YAAY;AAAA,IAClB,KAAK,6BAA6B;AAChC,aAAO,GAAG,gBAAgB,mBAAmB,YAAY,YAAY;AAAA,IACvE,KAAK,6BAA6B;AAChC,aAAO,gBAAgB;AAAA,IACzB,KAAK,6BAA6B;AAChC,aAAO,gBAAgB;AAAA,IACzB;AACE,aAAO,gBAAgB;AAAA,EAC3B;AACF;;;ACnvBA,IAAM,iBAAiB;AAAA,EACrB,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,UAAU;AACZ;AAaO,SAAS,0BAA0B,QAA0C;AAClF,QAAM,mBAAmB,uBAAuB,gBAAgB,OAAO,OAAO;AAC9E,QAAM,YAAY,OAAO;AAEzB,QAAME,uBAA2C,CAAC,SAAkB,UAA0B,CAAC,MAAM;AACnG,UAAM,EAAE,QAAQ,WAAW,IAAI;AAC/B,UAAM,iBAAiB,uBAAuB,kBAAkB,OAAO;AACvE,WAAO,oBAA4B,SAAS;AAAA,MAC1C,GAAG;AAAA,MACH,GAAG;AAAA;AAAA;AAAA,MAGH;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO;AAAA,IACL,qBAAAA;AAAA,IACA;AAAA,EACF;AACF;;;AChDO,IAAM,8BAA8B,OACzC,KACA,SACA,SAC8B;AAC9B,QAAM,EAAE,aAAa,UAAU,iBAAiB,IAAI,QAAQ,CAAC;AAC7D,QAAM,EAAE,QAAQ,WAAW,MAAM,IAAI;AAErC,QAAM,EAAE,UAAU,OAAO,cAAc,IAAI,uBAAuB,EAAE,GAAG,KAAK,CAAC;AAE7E,QAAM,CAAC,aAAa,UAAU,gBAAgB,IAAI,MAAM,QAAQ,IAAI;AAAA,IAClE,eAAe,YAAY,SAAS,WAAW,SAAS,IAAI,QAAQ,QAAQ,MAAS;AAAA,IACrF,YAAY,SAAS,MAAM,QAAQ,MAAM,IAAI,QAAQ,QAAQ,MAAS;AAAA,IACtE,oBAAoB,QAAQ,cAAc,gBAAgB,EAAE,gBAAgB,MAAM,CAAC,IAAI,QAAQ,QAAQ,MAAS;AAAA,EAClH,CAAC;AAED,QAAM,YAAY,2BAA2B;AAAA,IAC3C,SAAS;AAAA,IACT,MAAM;AAAA,IACN,cAAc;AAAA,EAChB,CAAC;AACD,SAAO,OAAO,OAAO,KAAK,SAAS;AACrC;AAKO,SAAS,2BAA4D,YAAkB;AAC5F,QAAM,OAAO,WAAW,OAAO,EAAE,GAAG,WAAW,KAAK,IAAI,WAAW;AACnE,QAAM,eAAe,WAAW,eAAe,EAAE,GAAG,WAAW,aAAa,IAAI,WAAW;AAC3F,uBAAqB,IAAI;AACzB,uBAAqB,YAAY;AACjC,SAAO,EAAE,GAAG,YAAY,MAAM,aAAa;AAC7C;AAEA,SAAS,qBAAqB,UAAwE;AAEpG,MAAI,UAAU;AACZ,QAAI,qBAAqB,UAAU;AACjC,aAAO,SAAS,iBAAiB;AAAA,IACnC;AACA,QAAI,sBAAsB,UAAU;AAClC,aAAO,SAAS,kBAAkB;AAAA,IACpC;AAAA,EACF;AAEA,SAAO;AACT;;;ACdA,SAAS,qBAAqB,mCAAmC;", "names": ["Headers", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "Cookies", "data", "Cookies", "jwk", "authenticateContext", "refreshToken", "data", "errors", "authenticateRequest"]}