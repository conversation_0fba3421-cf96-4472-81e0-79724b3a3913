{"name": "@clerk/backend", "version": "2.1.0", "description": "Clerk Backend SDK - REST Client for Backend API & JWT verification utilities", "homepage": "https://clerk.com/", "bugs": {"url": "https://github.com/clerk/javascript/issues"}, "repository": {"type": "git", "url": "git+https://github.com/clerk/javascript.git", "directory": "packages/backend"}, "license": "MIT", "imports": {"#crypto": {"edge-light": "./dist/runtime/browser/crypto.mjs", "worker": "./dist/runtime/browser/crypto.mjs", "browser": "./dist/runtime/browser/crypto.mjs", "node": {"require": "./dist/runtime/node/crypto.js", "import": "./dist/runtime/node/crypto.mjs"}, "default": "./dist/runtime/browser/crypto.mjs"}}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "./errors": {"import": {"types": "./dist/errors.d.ts", "default": "./dist/errors.mjs"}, "require": {"types": "./dist/errors.d.ts", "default": "./dist/errors.js"}}, "./internal": {"import": {"types": "./dist/internal.d.ts", "default": "./dist/internal.mjs"}, "require": {"types": "./dist/internal.d.ts", "default": "./dist/internal.js"}}, "./jwt": {"import": {"types": "./dist/jwt/index.d.ts", "default": "./dist/jwt/index.mjs"}, "require": {"types": "./dist/jwt/index.d.ts", "default": "./dist/jwt/index.js"}}, "./webhooks": {"import": {"types": "./dist/webhooks.d.ts", "default": "./dist/webhooks.mjs"}, "require": {"types": "./dist/webhooks.d.ts", "default": "./dist/webhooks.js"}}, "./package.json": "./package.json"}, "main": "./dist/index.js", "files": ["dist", "errors", "internal", "jwt", "webhooks"], "dependencies": {"cookie": "1.0.2", "snakecase-keys": "8.0.1", "tslib": "2.8.1", "@clerk/shared": "^3.9.7", "@clerk/types": "^4.60.1"}, "devDependencies": {"@edge-runtime/vm": "5.0.0", "msw": "2.8.7", "npm-run-all": "^4.1.5", "vitest-environment-miniflare": "2.14.4"}, "engines": {"node": ">=18.17.0"}, "publishConfig": {"access": "public"}, "scripts": {"build": "tsup", "build:declarations": "tsc -p tsconfig.declarations.json", "build:lib": "tsup --env.NODE_ENV production", "build:runtime": "cpy 'src/runtime/**/*.{mjs,js,cjs}' dist/runtime", "clean": "rimraf ./dist", "dev": "tsup --watch", "dev:publish": "pnpm dev -- --env.publish", "lint": "eslint src", "lint:attw": "attw --pack . --profile node16 --ignore-rules false-cjs", "lint:publint": "publint", "publish:local": "pnpm yalc push --replace --sig", "test": "run-s test:node test:edge-runtime test:cloudflare-miniflare", "test:cloudflare-miniflare": "vitest run --environment miniflare", "test:edge-runtime": "vitest run --environment edge-runtime", "test:node": "vitest run --environment node", "test:watch": "run-s test:watch:node test:watch:edge-runtime test:watch:cloudflare-miniflare", "test:watch:cloudflare-miniflare": "vitest watch --environment miniflare", "test:watch:edge-runtime": "vitest watch --environment edge-runtime", "test:watch:node": "vitest watch --environment node"}}