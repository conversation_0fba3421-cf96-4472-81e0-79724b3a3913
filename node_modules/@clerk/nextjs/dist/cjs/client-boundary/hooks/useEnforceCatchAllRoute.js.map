{"version": 3, "sources": ["../../../../src/client-boundary/hooks/useEnforceCatchAllRoute.tsx"], "sourcesContent": ["import { isProductionEnvironment } from '@clerk/shared/utils';\nimport type { RoutingStrategy } from '@clerk/types';\nimport React from 'react';\n\nimport { useSession } from '../hooks';\nimport { usePagesRouter } from './usePagesRouter';\n\n/**\n * This ugly hook  enforces that the Clerk components are mounted in a catch-all route\n * For pages router, we can parse the pathname we get from the useRouter hook\n * For app router, there is no reliable way to do the same check right now, so we\n * fire a request to a path under window.location.href and we check whether the path\n * exists or not\n */\nexport const useEnforceCatchAllRoute = (\n  component: string,\n  path: string,\n  routing?: RoutingStrategy,\n  requireSessionBeforeCheck = true,\n) => {\n  const ref = React.useRef(0);\n  const { pagesRouter } = usePagesRouter();\n  const { session, isLoaded } = useSession();\n\n  // This check does not break the rules of hooks\n  // as the condition will remain the same for the whole app lifecycle\n  if (isProductionEnvironment()) {\n    return;\n  }\n\n  React.useEffect(() => {\n    if (!isLoaded || (routing && routing !== 'path')) {\n      return;\n    }\n\n    // For components that require an active session, like UserProfile\n    // we should not enforce the catch-all route if there is no session\n    // because these components are usually protected by the middleware\n    // and if the check runs before the session is available, it will fail\n    // even if the route is a catch-all route, as the check request will result\n    // in a 404 because of auth.protect();\n    if (requireSessionBeforeCheck && !session) {\n      return;\n    }\n\n    const ac = new AbortController();\n    const error = () => {\n      const correctPath = pagesRouter ? `${path}/[[...index]].tsx` : `${path}/[[...rest]]/page.tsx`;\n      throw new Error(\n        `\nClerk: The <${component}/> component is not configured correctly. The most likely reasons for this error are:\n\n1. The \"${path}\" route is not a catch-all route.\nIt is recommended to convert this route to a catch-all route, eg: \"${correctPath}\". Alternatively, you can update the <${component}/> component to use hash-based routing by setting the \"routing\" prop to \"hash\".\n\n2. The <${component}/> component is mounted in a catch-all route, but all routes under \"${path}\" are protected by the middleware.\nTo resolve this, ensure that the middleware does not protect the catch-all route or any of its children. If you are using the \"createRouteMatcher\" helper, consider adding \"(.*)\" to the end of the route pattern, eg: \"${path}(.*)\". For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#create-route-matcher\n`,\n      );\n    };\n\n    if (pagesRouter) {\n      if (!pagesRouter.pathname.match(/\\[\\[\\.\\.\\..+]]/)) {\n        error();\n      }\n    } else {\n      const check = async () => {\n        // make sure to run this as soon as possible\n        // but don't run again when strict mode is enabled\n        ref.current++;\n        if (ref.current > 1) {\n          return;\n        }\n        let res;\n        try {\n          const url = `${window.location.origin}${\n            window.location.pathname\n          }/${component}_clerk_catchall_check_${Date.now()}`;\n          res = await fetch(url, { signal: ac.signal });\n        } catch {\n          // no op\n        }\n        if (res?.status === 404) {\n          error();\n        }\n      };\n      void check();\n    }\n\n    return () => {\n      // make sure to run this as soon as possible\n      // but don't run again when strict mode is enabled\n      if (ref.current > 1) {\n        ac.abort();\n      }\n    };\n  }, [isLoaded]);\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAwC;AAExC,mBAAkB;AAElB,mBAA2B;AAC3B,4BAA+B;AASxB,MAAM,0BAA0B,CACrC,WACA,MACA,SACA,4BAA4B,SACzB;AACH,QAAM,MAAM,aAAAA,QAAM,OAAO,CAAC;AAC1B,QAAM,EAAE,YAAY,QAAI,sCAAe;AACvC,QAAM,EAAE,SAAS,SAAS,QAAI,yBAAW;AAIzC,UAAI,sCAAwB,GAAG;AAC7B;AAAA,EACF;AAEA,eAAAA,QAAM,UAAU,MAAM;AACpB,QAAI,CAAC,YAAa,WAAW,YAAY,QAAS;AAChD;AAAA,IACF;AAQA,QAAI,6BAA6B,CAAC,SAAS;AACzC;AAAA,IACF;AAEA,UAAM,KAAK,IAAI,gBAAgB;AAC/B,UAAM,QAAQ,MAAM;AAClB,YAAM,cAAc,cAAc,GAAG,IAAI,sBAAsB,GAAG,IAAI;AACtE,YAAM,IAAI;AAAA,QACR;AAAA,cACM,SAAS;AAAA;AAAA,UAEb,IAAI;AAAA,qEACuD,WAAW,yCAAyC,SAAS;AAAA;AAAA,UAExH,SAAS,uEAAuE,IAAI;AAAA,0NAC4H,IAAI;AAAA;AAAA,MAExN;AAAA,IACF;AAEA,QAAI,aAAa;AACf,UAAI,CAAC,YAAY,SAAS,MAAM,gBAAgB,GAAG;AACjD,cAAM;AAAA,MACR;AAAA,IACF,OAAO;AACL,YAAM,QAAQ,YAAY;AAGxB,YAAI;AACJ,YAAI,IAAI,UAAU,GAAG;AACnB;AAAA,QACF;AACA,YAAI;AACJ,YAAI;AACF,gBAAM,MAAM,GAAG,OAAO,SAAS,MAAM,GACnC,OAAO,SAAS,QAClB,IAAI,SAAS,yBAAyB,KAAK,IAAI,CAAC;AAChD,gBAAM,MAAM,MAAM,KAAK,EAAE,QAAQ,GAAG,OAAO,CAAC;AAAA,QAC9C,QAAQ;AAAA,QAER;AACA,aAAI,2BAAK,YAAW,KAAK;AACvB,gBAAM;AAAA,QACR;AAAA,MACF;AACA,WAAK,MAAM;AAAA,IACb;AAEA,WAAO,MAAM;AAGX,UAAI,IAAI,UAAU,GAAG;AACnB,WAAG,MAAM;AAAA,MACX;AAAA,IACF;AAAA,EACF,GAAG,CAAC,QAAQ,CAAC;AACf;", "names": ["React"]}