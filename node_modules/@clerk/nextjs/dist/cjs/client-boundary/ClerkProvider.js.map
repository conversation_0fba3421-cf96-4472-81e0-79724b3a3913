{"version": 3, "sources": ["../../../src/client-boundary/ClerkProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/compat/router';\nimport React from 'react';\n\nimport { ClientClerkProvider } from '../app-router/client/ClerkProvider';\nimport { ClerkProvider as PageClerkProvider } from '../pages/ClerkProvider';\nimport { type NextClerkProviderProps } from '../types';\n\n/**\n * This is a compatibility layer to support a single ClerkProvider component in both the app and pages routers.\n */\nexport function ClerkProvider(props: NextClerkProviderProps) {\n  const router = useRouter();\n\n  const Provider = router ? PageClerkProvider : ClientClerkProvider;\n\n  return <Provider {...props} />;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA0B;AAC1B,mBAAkB;AAElB,2BAAoC;AACpC,IAAAA,wBAAmD;AAM5C,SAAS,cAAc,OAA+B;AAC3D,QAAM,aAAS,yBAAU;AAEzB,QAAM,WAAW,SAAS,sBAAAC,gBAAoB;AAE9C,SAAO,6BAAAC,QAAA,cAAC,YAAU,GAAG,OAAO;AAC9B;", "names": ["import_ClerkProvider", "PageClerkProvider", "React"]}