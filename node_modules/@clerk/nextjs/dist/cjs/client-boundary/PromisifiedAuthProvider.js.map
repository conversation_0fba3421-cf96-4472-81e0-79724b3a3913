{"version": 3, "sources": ["../../../src/client-boundary/PromisifiedAuthProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@clerk/clerk-react';\nimport { useDerivedAuth } from '@clerk/clerk-react/internal';\nimport type { InitialState } from '@clerk/types';\nimport { useRouter } from 'next/compat/router';\nimport React from 'react';\n\nconst PromisifiedAuthContext = React.createContext<Promise<InitialState> | InitialState | null>(null);\n\nexport function PromisifiedAuthProvider({\n  authPromise,\n  children,\n}: {\n  authPromise: Promise<InitialState> | InitialState;\n  children: React.ReactNode;\n}) {\n  return <PromisifiedAuthContext.Provider value={authPromise}>{children}</PromisifiedAuthContext.Provider>;\n}\n\n/**\n * Returns the current auth state, the user and session ids and the `getToken`\n * that can be used to retrieve the given template or the default Clerk token.\n *\n * Until Clerk loads, `isLoaded` will be set to `false`.\n * Once Clerk loads, `isLoaded` will be set to `true`, and you can\n * safely access the `userId` and `sessionId` variables.\n *\n * For projects using NextJs or Remix, you can have immediate access to this data during SSR\n * simply by using the `ClerkProvider`.\n *\n * @example\n * import { useAuth } from '@clerk/nextjs'\n *\n * function Hello() {\n *   const { isSignedIn, sessionId, userId } = useAuth();\n *   if(isSignedIn) {\n *     return null;\n *   }\n *   console.log(sessionId, userId)\n *   return <div>...</div>\n * }\n *\n * @example\n * This page will be fully rendered during SSR.\n *\n * ```tsx\n * import { useAuth } from '@clerk/nextjs'\n *\n * export HelloPage = () => {\n *   const { isSignedIn, sessionId, userId } = useAuth();\n *   console.log(isSignedIn, sessionId, userId)\n *   return <div>...</div>\n * }\n * ```\n */\nexport function usePromisifiedAuth(options: Parameters<typeof useAuth>[0] = {}) {\n  const isPagesRouter = useRouter();\n  const valueFromContext = React.useContext(PromisifiedAuthContext);\n\n  let resolvedData = valueFromContext;\n  if (valueFromContext && 'then' in valueFromContext) {\n    resolvedData = React.use(valueFromContext);\n  }\n\n  // At this point we should have a usable auth object\n  if (typeof window === 'undefined') {\n    // Pages router should always use useAuth as it is able to grab initial auth state from context during SSR.\n    if (isPagesRouter) {\n      return useAuth(options);\n    }\n\n    // We don't need to deal with Clerk being loaded here\n    return useDerivedAuth({ ...resolvedData, ...options });\n  } else {\n    return useAuth({ ...resolvedData, ...options });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,yBAAwB;AACxB,sBAA+B;AAE/B,oBAA0B;AAC1B,mBAAkB;AAElB,MAAM,yBAAyB,aAAAA,QAAM,cAA2D,IAAI;AAE7F,SAAS,wBAAwB;AAAA,EACtC;AAAA,EACA;AACF,GAGG;AACD,SAAO,6BAAAA,QAAA,cAAC,uBAAuB,UAAvB,EAAgC,OAAO,eAAc,QAAS;AACxE;AAsCO,SAAS,mBAAmB,UAAyC,CAAC,GAAG;AAC9E,QAAM,oBAAgB,yBAAU;AAChC,QAAM,mBAAmB,aAAAA,QAAM,WAAW,sBAAsB;AAEhE,MAAI,eAAe;AACnB,MAAI,oBAAoB,UAAU,kBAAkB;AAClD,mBAAe,aAAAA,QAAM,IAAI,gBAAgB;AAAA,EAC3C;AAGA,MAAI,OAAO,WAAW,aAAa;AAEjC,QAAI,eAAe;AACjB,iBAAO,4BAAQ,OAAO;AAAA,IACxB;AAGA,eAAO,gCAAe,EAAE,GAAG,cAAc,GAAG,QAAQ,CAAC;AAAA,EACvD,OAAO;AACL,eAAO,4BAAQ,EAAE,GAAG,cAAc,GAAG,QAAQ,CAAC;AAAA,EAChD;AACF;", "names": ["React"]}