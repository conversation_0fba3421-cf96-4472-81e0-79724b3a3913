{"version": 3, "sources": ["../../../src/client-boundary/NextOptionsContext.tsx"], "sourcesContent": ["import React from 'react';\n\nimport type { NextClerkProviderProps } from '../types';\n\ntype ClerkNextContextValue = Partial<Omit<NextClerkProviderProps, 'children'>>;\n\nconst ClerkNextOptionsCtx = React.createContext<{ value: ClerkNextContextValue } | undefined>(undefined);\nClerkNextOptionsCtx.displayName = 'ClerkNextOptionsCtx';\n\nconst useClerkNextOptions = () => {\n  const ctx = React.useContext(ClerkNextOptionsCtx) as { value: ClerkNextContextValue };\n  return ctx?.value;\n};\n\nconst ClerkNextOptionsProvider = (\n  props: React.PropsWithChildren<{ options: ClerkNextContextValue }>,\n): React.JSX.Element => {\n  const { children, options } = props;\n  return <ClerkNextOptionsCtx.Provider value={{ value: options }}>{children}</ClerkNextOptionsCtx.Provider>;\n};\n\nexport { ClerkNextOptionsProvider, useClerkNextOptions };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAMlB,MAAM,sBAAsB,aAAAA,QAAM,cAA4D,MAAS;AACvG,oBAAoB,cAAc;AAElC,MAAM,sBAAsB,MAAM;AAChC,QAAM,MAAM,aAAAA,QAAM,WAAW,mBAAmB;AAChD,SAAO,2BAAK;AACd;AAEA,MAAM,2BAA2B,CAC/B,UACsB;AACtB,QAAM,EAAE,UAAU,QAAQ,IAAI;AAC9B,SAAO,6BAAAA,QAAA,cAAC,oBAAoB,UAApB,EAA6B,OAAO,EAAE,OAAO,QAAQ,KAAI,QAAS;AAC5E;", "names": ["React"]}