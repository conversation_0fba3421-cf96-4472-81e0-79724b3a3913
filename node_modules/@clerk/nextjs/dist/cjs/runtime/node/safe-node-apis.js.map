{"version": 3, "sources": ["../../../../src/runtime/node/safe-node-apis.js"], "sourcesContent": ["/**\n * This file is used for conditional imports to mitigate bundling issues with Next.js server actions on version prior to 14.1.0.\n */\n// eslint-disable-next-line @typescript-eslint/no-require-imports\nconst { existsSync, writeFileSync, readFileSync, appendFileSync, mkdirSync, rmSync } = require('node:fs');\n// eslint-disable-next-line @typescript-eslint/no-require-imports\nconst path = require('node:path');\nconst fs = {\n  existsSync,\n  writeFileSync,\n  readFileSync,\n  appendFileSync,\n  mkdirSync,\n  rmSync,\n};\n\nconst cwd = () => process.cwd();\n\nmodule.exports = { fs, path, cwd };\n"], "mappings": ";AAIA,MAAM,EAAE,YAAY,eAAe,cAAc,gBAAgB,WAAW,OAAO,IAAI,QAAQ,SAAS;AAExG,MAAM,OAAO,QAAQ,WAAW;AAChC,MAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,MAAM,MAAM,MAAM,QAAQ,IAAI;AAE9B,OAAO,UAAU,EAAE,IAAI,MAAM,IAAI;", "names": []}