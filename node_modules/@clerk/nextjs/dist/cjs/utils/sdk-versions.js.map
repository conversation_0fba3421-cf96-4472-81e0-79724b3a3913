{"version": 3, "sources": ["../../../src/utils/sdk-versions.ts"], "sourcesContent": ["import nextPkg from 'next/package.json';\n\nconst isNext13 = nextPkg.version.startsWith('13.');\n\n/**\n * Those versions are affected by a bundling issue that will break the application if `node:fs` is used inside a server function.\n * The affected versions are >=next@13.5.4 and <=next@14.0.4\n */\nconst isNextWithUnstableServerActions = isNext13 || nextPkg.version.startsWith('14.0');\n\nexport { isNext13, isNextWithUnstableServerActions };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAAoB;AAEpB,MAAM,WAAW,eAAAA,QAAQ,QAAQ,WAAW,KAAK;AAMjD,MAAM,kCAAkC,YAAY,eAAAA,QAAQ,QAAQ,WAAW,MAAM;", "names": ["nextPkg"]}