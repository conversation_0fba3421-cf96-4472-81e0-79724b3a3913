{"version": 3, "sources": ["../../../src/utils/mergeNextClerkPropsWithEnv.ts"], "sourcesContent": ["import { isTruthy } from '@clerk/shared/underscore';\n\nimport { SDK_METADATA } from '../server/constants';\nimport type { NextClerkProviderProps } from '../types';\n\n// @ts-ignore - https://github.com/microsoft/TypeScript/issues/47663\nexport const mergeNextClerkPropsWithEnv = (props: Omit<NextClerkProviderProps, 'children'>): any => {\n  return {\n    ...props,\n    publishableKey: props.publishableKey || process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY || '',\n    clerkJSUrl: props.clerkJSUrl || process.env.NEXT_PUBLIC_CLERK_JS_URL,\n    clerkJSVersion: props.clerkJSVersion || process.env.NEXT_PUBLIC_CLERK_JS_VERSION,\n    proxyUrl: props.proxyUrl || process.env.NEXT_PUBLIC_CLERK_PROXY_URL || '',\n    domain: props.domain || process.env.NEXT_PUBLIC_CLERK_DOMAIN || '',\n    isSatellite: props.isSatellite || isTruthy(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE),\n    signInUrl: props.signInUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL || '',\n    signUpUrl: props.signUpUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL || '',\n    signInForceRedirectUrl:\n      props.signInForceRedirectUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_IN_FORCE_REDIRECT_URL || '',\n    signUpForceRedirectUrl:\n      props.signUpForceRedirectUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_UP_FORCE_REDIRECT_URL || '',\n    signInFallbackRedirectUrl:\n      props.signInFallbackRedirectUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL || '',\n    signUpFallbackRedirectUrl:\n      props.signUpFallbackRedirectUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL || '',\n    afterSignInUrl: props.afterSignInUrl || process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL || '',\n    afterSignUpUrl: props.afterSignUpUrl || process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL || '',\n    newSubscriptionRedirectUrl:\n      props.newSubscriptionRedirectUrl || process.env.NEXT_PUBLIC_CLERK_CHECKOUT_CONTINUE_URL || '',\n    telemetry: props.telemetry ?? {\n      disabled: isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED),\n      debug: isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG),\n    },\n    sdkMetadata: SDK_METADATA,\n  };\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAAyB;AAEzB,uBAA6B;AAItB,MAAM,6BAA6B,CAAC,UAAyD;AANpG;AAOE,SAAO;AAAA,IACL,GAAG;AAAA,IACH,gBAAgB,MAAM,kBAAkB,QAAQ,IAAI,qCAAqC;AAAA,IACzF,YAAY,MAAM,cAAc,QAAQ,IAAI;AAAA,IAC5C,gBAAgB,MAAM,kBAAkB,QAAQ,IAAI;AAAA,IACpD,UAAU,MAAM,YAAY,QAAQ,IAAI,+BAA+B;AAAA,IACvE,QAAQ,MAAM,UAAU,QAAQ,IAAI,4BAA4B;AAAA,IAChE,aAAa,MAAM,mBAAe,4BAAS,QAAQ,IAAI,8BAA8B;AAAA,IACrF,WAAW,MAAM,aAAa,QAAQ,IAAI,iCAAiC;AAAA,IAC3E,WAAW,MAAM,aAAa,QAAQ,IAAI,iCAAiC;AAAA,IAC3E,wBACE,MAAM,0BAA0B,QAAQ,IAAI,gDAAgD;AAAA,IAC9F,wBACE,MAAM,0BAA0B,QAAQ,IAAI,gDAAgD;AAAA,IAC9F,2BACE,MAAM,6BAA6B,QAAQ,IAAI,mDAAmD;AAAA,IACpG,2BACE,MAAM,6BAA6B,QAAQ,IAAI,mDAAmD;AAAA,IACpG,gBAAgB,MAAM,kBAAkB,QAAQ,IAAI,uCAAuC;AAAA,IAC3F,gBAAgB,MAAM,kBAAkB,QAAQ,IAAI,uCAAuC;AAAA,IAC3F,4BACE,MAAM,8BAA8B,QAAQ,IAAI,2CAA2C;AAAA,IAC7F,YAAW,WAAM,cAAN,YAAmB;AAAA,MAC5B,cAAU,4BAAS,QAAQ,IAAI,oCAAoC;AAAA,MACnE,WAAO,4BAAS,QAAQ,IAAI,iCAAiC;AAAA,IAC/D;AAAA,IACA,aAAa;AAAA,EACf;AACF;", "names": []}