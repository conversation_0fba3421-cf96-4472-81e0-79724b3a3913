{"version": 3, "sources": ["../../../src/utils/logFormatter.ts"], "sourcesContent": ["import type { LogEntry } from './debugLogger';\n\n// Move to shared once clerk/shared is used in clerk/nextjs\nconst maskSecretKey = (str: any) => {\n  if (!str || typeof str !== 'string') {\n    return str;\n  }\n\n  try {\n    return (str || '').replace(/^(sk_(live|test)_)(.+?)(.{3})$/, '$1*********$4');\n  } catch {\n    return '';\n  }\n};\n\nexport const logFormatter = (entry: LogEntry) => {\n  return (Array.isArray(entry) ? entry : [entry])\n    .map(entry => {\n      if (typeof entry === 'string') {\n        return maskSecretKey(entry);\n      }\n\n      const masked = Object.fromEntries(Object.entries(entry).map(([k, v]) => [k, maskSecretKey(v)]));\n      return JSON.stringify(masked, null, 2);\n    })\n    .join(', ');\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,MAAM,gBAAgB,CAAC,QAAa;AAClC,MAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;AACnC,WAAO;AAAA,EACT;AAEA,MAAI;AACF,YAAQ,OAAO,IAAI,QAAQ,kCAAkC,eAAe;AAAA,EAC9E,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAEO,MAAM,eAAe,CAAC,UAAoB;AAC/C,UAAQ,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK,GAC1C,IAAI,CAAAA,WAAS;AACZ,QAAI,OAAOA,WAAU,UAAU;AAC7B,aAAO,cAAcA,MAAK;AAAA,IAC5B;AAEA,UAAM,SAAS,OAAO,YAAY,OAAO,QAAQA,MAAK,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;AAC9F,WAAO,KAAK,UAAU,QAAQ,MAAM,CAAC;AAAA,EACvC,CAAC,EACA,KAAK,IAAI;AACd;", "names": ["entry"]}