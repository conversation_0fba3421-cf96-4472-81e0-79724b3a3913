{"version": 3, "sources": ["../../../src/utils/feature-flags.ts"], "sourcesContent": ["import { isDevelopmentEnvironment } from '@clerk/shared/utils';\n\nimport { KEYLESS_DISABLED } from '../server/constants';\nimport { isNextWithUnstableServerActions } from './sdk-versions';\n\nconst canUseKeyless =\n  !isNextWithUnstableServerActions &&\n  // Next.js will inline the value of 'development' or 'production' on the client bundle, so this is client-safe.\n  isDevelopmentEnvironment() &&\n  !KEYLESS_DISABLED;\n\nexport { canUseKeyless };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAyC;AAEzC,uBAAiC;AACjC,0BAAgD;AAEhD,MAAM,gBACJ,CAAC;AAAA,IAED,uCAAyB,KACzB,CAAC;", "names": []}