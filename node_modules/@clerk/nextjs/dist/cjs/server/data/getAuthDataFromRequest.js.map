{"version": 3, "sources": ["../../../../src/server/data/getAuthDataFromRequest.ts"], "sourcesContent": ["import type { AuthObject } from '@clerk/backend';\nimport {\n  authenticatedMachineObject,\n  type AuthenticateRequestOptions,\n  AuthStatus,\n  constants,\n  getAuthObjectFromJwt,\n  getMachineTokenType,\n  invalidTokenAuthObject,\n  isMachineTokenByPrefix,\n  isMachineTokenType,\n  isTokenTypeAccepted,\n  type MachineTokenType,\n  type SignedInAuthObject,\n  type SignedOutAuthObject,\n  signedOutAuthObject,\n  TokenType,\n  unauthenticatedMachineObject,\n  verifyMachineAuthToken,\n} from '@clerk/backend/internal';\nimport { decodeJwt } from '@clerk/backend/jwt';\nimport type { PendingSessionOptions } from '@clerk/types';\n\nimport type { LoggerNoCommit } from '../../utils/debugLogger';\nimport { API_URL, API_VERSION, PUBLISHABLE_KEY, SECRET_KEY } from '../constants';\nimport { getAuthKeyFromRequest, getHeader } from '../headers-utils';\nimport type { RequestLike } from '../types';\nimport { assertTokenSignature, decryptClerkRequestData } from '../utils';\n\nexport type GetAuthDataFromRequestOptions = {\n  secretKey?: string;\n  logger?: LoggerNoCommit;\n  acceptsToken?: AuthenticateRequestOptions['acceptsToken'];\n} & PendingSessionOptions;\n\nexport const getAuthDataFromRequestSync = (\n  req: RequestLike,\n  { treatPendingAsSignedOut = true, ...opts }: GetAuthDataFromRequestOptions = {},\n): SignedInAuthObject | SignedOutAuthObject => {\n  const { authStatus, authMessage, authReason, authToken, authSignature } = getAuthHeaders(req);\n\n  opts.logger?.debug('headers', { authStatus, authMessage, authReason });\n\n  const encryptedRequestData = getHeader(req, constants.Headers.ClerkRequestData);\n  const decryptedRequestData = decryptClerkRequestData(encryptedRequestData);\n\n  const options = {\n    secretKey: opts?.secretKey || decryptedRequestData.secretKey || SECRET_KEY,\n    publishableKey: decryptedRequestData.publishableKey || PUBLISHABLE_KEY,\n    apiUrl: API_URL,\n    apiVersion: API_VERSION,\n    authStatus,\n    authMessage,\n    authReason,\n    treatPendingAsSignedOut,\n  };\n\n  // Only accept session tokens in the synchronous version.\n  // Machine tokens are not supported in this function. Any machine token input will result in a signed-out state.\n  if (!isTokenTypeAccepted(TokenType.SessionToken, opts.acceptsToken || TokenType.SessionToken)) {\n    return signedOutAuthObject(options);\n  }\n\n  let authObject;\n  if (!authStatus || authStatus !== AuthStatus.SignedIn) {\n    authObject = signedOutAuthObject(options);\n  } else {\n    assertTokenSignature(authToken as string, options.secretKey, authSignature);\n\n    const jwt = decodeJwt(authToken as string);\n\n    opts.logger?.debug('jwt', jwt.raw);\n\n    return getAuthObjectFromJwt(jwt, options);\n  }\n\n  return authObject;\n};\n\n/**\n * Note: We intentionally avoid using interface/function overloads here since these functions\n * are used internally. The complex type overloads are more valuable at the public API level\n * (like in auth.protect(), auth()) where users interact directly with the types.\n *\n * Given a request object, builds an auth object from the request data. Used in server-side environments to get access\n * to auth data for a given request.\n */\nexport const getAuthDataFromRequestAsync = async (\n  req: RequestLike,\n  opts: GetAuthDataFromRequestOptions = {},\n): Promise<AuthObject> => {\n  const { authStatus, authMessage, authReason } = getAuthHeaders(req);\n  opts.logger?.debug('headers', { authStatus, authMessage, authReason });\n\n  const bearerToken = getHeader(req, constants.Headers.Authorization)?.replace('Bearer ', '');\n  const acceptsToken = opts.acceptsToken || TokenType.SessionToken;\n  const options = {\n    secretKey: opts?.secretKey || SECRET_KEY,\n    publishableKey: PUBLISHABLE_KEY,\n    apiUrl: API_URL,\n    authStatus,\n    authMessage,\n    authReason,\n  };\n\n  if (bearerToken) {\n    const isMachine = isMachineTokenByPrefix(bearerToken);\n    const tokenType = isMachine ? getMachineTokenType(bearerToken) : undefined;\n\n    if (Array.isArray(acceptsToken)) {\n      if (isMachine) {\n        return handleMachineToken({\n          bearerToken,\n          tokenType: tokenType as MachineTokenType,\n          acceptsToken,\n          options,\n        });\n      }\n    } else {\n      let intendedType: TokenType | undefined;\n      if (isMachineTokenType(acceptsToken)) {\n        intendedType = acceptsToken;\n      }\n      const result = await handleIntentBased({\n        isMachine,\n        tokenType,\n        intendedType,\n        bearerToken,\n        acceptsToken,\n        options,\n      });\n      if (result) {\n        return result;\n      }\n    }\n  }\n\n  if (Array.isArray(acceptsToken)) {\n    if (!isTokenTypeAccepted(TokenType.SessionToken, acceptsToken)) {\n      return invalidTokenAuthObject();\n    }\n  }\n\n  // Fall through to session logic\n  return getAuthDataFromRequestSync(req, opts);\n};\n\nconst getAuthHeaders = (req: RequestLike) => {\n  const authStatus = getAuthKeyFromRequest(req, 'AuthStatus');\n  const authToken = getAuthKeyFromRequest(req, 'AuthToken');\n  const authMessage = getAuthKeyFromRequest(req, 'AuthMessage');\n  const authReason = getAuthKeyFromRequest(req, 'AuthReason');\n  const authSignature = getAuthKeyFromRequest(req, 'AuthSignature');\n\n  return {\n    authStatus,\n    authToken,\n    authMessage,\n    authReason,\n    authSignature,\n  };\n};\n\n/**\n * Handles verification and response shaping for machine tokens.\n * Returns an authenticated or unauthenticated machine object based on verification and type acceptance.\n */\nasync function handleMachineToken({\n  bearerToken,\n  tokenType,\n  acceptsToken,\n  options,\n}: {\n  bearerToken: string;\n  tokenType: MachineTokenType;\n  acceptsToken: AuthenticateRequestOptions['acceptsToken'];\n  options: Record<string, any>;\n}) {\n  if (Array.isArray(acceptsToken)) {\n    // If the token is not in the accepted array, return invalid token auth object\n    if (!isTokenTypeAccepted(tokenType, acceptsToken)) {\n      return invalidTokenAuthObject();\n    }\n  }\n\n  if (!isTokenTypeAccepted(tokenType, acceptsToken ?? TokenType.SessionToken)) {\n    return unauthenticatedMachineObject(tokenType, options);\n  }\n  const { data, errors } = await verifyMachineAuthToken(bearerToken, options);\n  if (errors) {\n    return unauthenticatedMachineObject(tokenType, options);\n  }\n  return authenticatedMachineObject(tokenType, bearerToken, data);\n}\n\n/**\n * Handles intent-based fallback for single-value acceptsToken.\n * Returns an unauthenticated object for the intended type, or falls back to session logic if not applicable.\n */\nasync function handleIntentBased({\n  isMachine,\n  tokenType,\n  intendedType,\n  bearerToken,\n  acceptsToken,\n  options,\n}: {\n  isMachine: boolean;\n  tokenType: TokenType | undefined;\n  intendedType: TokenType | undefined;\n  bearerToken: string;\n  acceptsToken: AuthenticateRequestOptions['acceptsToken'];\n  options: Record<string, any>;\n}) {\n  if (isMachine) {\n    if (!tokenType) {\n      return signedOutAuthObject(options);\n    }\n    if (!isTokenTypeAccepted(tokenType, acceptsToken ?? TokenType.SessionToken)) {\n      if (intendedType && isMachineTokenType(intendedType)) {\n        return unauthenticatedMachineObject(intendedType, options);\n      }\n      return signedOutAuthObject(options);\n    }\n    const { data, errors } = await verifyMachineAuthToken(bearerToken, options);\n    if (errors) {\n      return unauthenticatedMachineObject(tokenType as MachineTokenType, options);\n    }\n    return authenticatedMachineObject(tokenType as MachineTokenType, bearerToken, data);\n  } else if (intendedType && isMachineTokenType(intendedType)) {\n    return unauthenticatedMachineObject(intendedType, options);\n  }\n  // else: fall through to session logic\n  return null;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,sBAkBO;AACP,iBAA0B;AAI1B,uBAAkE;AAClE,2BAAiD;AAEjD,mBAA8D;AAQvD,MAAM,6BAA6B,CACxC,KACA,EAAE,0BAA0B,MAAM,GAAG,KAAK,IAAmC,CAAC,MACjC;AAtC/C;AAuCE,QAAM,EAAE,YAAY,aAAa,YAAY,WAAW,cAAc,IAAI,eAAe,GAAG;AAE5F,aAAK,WAAL,mBAAa,MAAM,WAAW,EAAE,YAAY,aAAa,WAAW;AAEpE,QAAM,2BAAuB,gCAAU,KAAK,0BAAU,QAAQ,gBAAgB;AAC9E,QAAM,2BAAuB,sCAAwB,oBAAoB;AAEzE,QAAM,UAAU;AAAA,IACd,YAAW,6BAAM,cAAa,qBAAqB,aAAa;AAAA,IAChE,gBAAgB,qBAAqB,kBAAkB;AAAA,IACvD,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAIA,MAAI,KAAC,qCAAoB,0BAAU,cAAc,KAAK,gBAAgB,0BAAU,YAAY,GAAG;AAC7F,eAAO,qCAAoB,OAAO;AAAA,EACpC;AAEA,MAAI;AACJ,MAAI,CAAC,cAAc,eAAe,2BAAW,UAAU;AACrD,qBAAa,qCAAoB,OAAO;AAAA,EAC1C,OAAO;AACL,2CAAqB,WAAqB,QAAQ,WAAW,aAAa;AAE1E,UAAM,UAAM,sBAAU,SAAmB;AAEzC,eAAK,WAAL,mBAAa,MAAM,OAAO,IAAI;AAE9B,eAAO,sCAAqB,KAAK,OAAO;AAAA,EAC1C;AAEA,SAAO;AACT;AAUO,MAAM,8BAA8B,OACzC,KACA,OAAsC,CAAC,MACf;AA1F1B;AA2FE,QAAM,EAAE,YAAY,aAAa,WAAW,IAAI,eAAe,GAAG;AAClE,aAAK,WAAL,mBAAa,MAAM,WAAW,EAAE,YAAY,aAAa,WAAW;AAEpE,QAAM,eAAc,yCAAU,KAAK,0BAAU,QAAQ,aAAa,MAA9C,mBAAiD,QAAQ,WAAW;AACxF,QAAM,eAAe,KAAK,gBAAgB,0BAAU;AACpD,QAAM,UAAU;AAAA,IACd,YAAW,6BAAM,cAAa;AAAA,IAC9B,gBAAgB;AAAA,IAChB,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,MAAI,aAAa;AACf,UAAM,gBAAY,wCAAuB,WAAW;AACpD,UAAM,YAAY,gBAAY,qCAAoB,WAAW,IAAI;AAEjE,QAAI,MAAM,QAAQ,YAAY,GAAG;AAC/B,UAAI,WAAW;AACb,eAAO,mBAAmB;AAAA,UACxB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,UAAI;AACJ,cAAI,oCAAmB,YAAY,GAAG;AACpC,uBAAe;AAAA,MACjB;AACA,YAAM,SAAS,MAAM,kBAAkB;AAAA,QACrC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,UAAI,QAAQ;AACV,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAEA,MAAI,MAAM,QAAQ,YAAY,GAAG;AAC/B,QAAI,KAAC,qCAAoB,0BAAU,cAAc,YAAY,GAAG;AAC9D,iBAAO,wCAAuB;AAAA,IAChC;AAAA,EACF;AAGA,SAAO,2BAA2B,KAAK,IAAI;AAC7C;AAEA,MAAM,iBAAiB,CAAC,QAAqB;AAC3C,QAAM,iBAAa,4CAAsB,KAAK,YAAY;AAC1D,QAAM,gBAAY,4CAAsB,KAAK,WAAW;AACxD,QAAM,kBAAc,4CAAsB,KAAK,aAAa;AAC5D,QAAM,iBAAa,4CAAsB,KAAK,YAAY;AAC1D,QAAM,oBAAgB,4CAAsB,KAAK,eAAe;AAEhE,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAMA,eAAe,mBAAmB;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAKG;AACD,MAAI,MAAM,QAAQ,YAAY,GAAG;AAE/B,QAAI,KAAC,qCAAoB,WAAW,YAAY,GAAG;AACjD,iBAAO,wCAAuB;AAAA,IAChC;AAAA,EACF;AAEA,MAAI,KAAC,qCAAoB,WAAW,sCAAgB,0BAAU,YAAY,GAAG;AAC3E,eAAO,8CAA6B,WAAW,OAAO;AAAA,EACxD;AACA,QAAM,EAAE,MAAM,OAAO,IAAI,UAAM,wCAAuB,aAAa,OAAO;AAC1E,MAAI,QAAQ;AACV,eAAO,8CAA6B,WAAW,OAAO;AAAA,EACxD;AACA,aAAO,4CAA2B,WAAW,aAAa,IAAI;AAChE;AAMA,eAAe,kBAAkB;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAOG;AACD,MAAI,WAAW;AACb,QAAI,CAAC,WAAW;AACd,iBAAO,qCAAoB,OAAO;AAAA,IACpC;AACA,QAAI,KAAC,qCAAoB,WAAW,sCAAgB,0BAAU,YAAY,GAAG;AAC3E,UAAI,oBAAgB,oCAAmB,YAAY,GAAG;AACpD,mBAAO,8CAA6B,cAAc,OAAO;AAAA,MAC3D;AACA,iBAAO,qCAAoB,OAAO;AAAA,IACpC;AACA,UAAM,EAAE,MAAM,OAAO,IAAI,UAAM,wCAAuB,aAAa,OAAO;AAC1E,QAAI,QAAQ;AACV,iBAAO,8CAA6B,WAA+B,OAAO;AAAA,IAC5E;AACA,eAAO,4CAA2B,WAA+B,aAAa,IAAI;AAAA,EACpF,WAAW,oBAAgB,oCAAmB,YAAY,GAAG;AAC3D,eAAO,8CAA6B,cAAc,OAAO;AAAA,EAC3D;AAEA,SAAO;AACT;", "names": []}