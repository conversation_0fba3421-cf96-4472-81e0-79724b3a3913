"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var getAuthDataFromRequest_exports = {};
__export(getAuthDataFromRequest_exports, {
  getAuthDataFromRequestAsync: () => getAuthDataFromRequestAsync,
  getAuthDataFromRequestSync: () => getAuthDataFromRequestSync
});
module.exports = __toCommonJS(getAuthDataFromRequest_exports);
var import_internal = require("@clerk/backend/internal");
var import_jwt = require("@clerk/backend/jwt");
var import_constants = require("../constants");
var import_headers_utils = require("../headers-utils");
var import_utils = require("../utils");
const getAuthDataFromRequestSync = (req, { treatPendingAsSignedOut = true, ...opts } = {}) => {
  var _a, _b;
  const { authStatus, authMessage, authReason, authToken, authSignature } = getAuthHeaders(req);
  (_a = opts.logger) == null ? void 0 : _a.debug("headers", { authStatus, authMessage, authReason });
  const encryptedRequestData = (0, import_headers_utils.getHeader)(req, import_internal.constants.Headers.ClerkRequestData);
  const decryptedRequestData = (0, import_utils.decryptClerkRequestData)(encryptedRequestData);
  const options = {
    secretKey: (opts == null ? void 0 : opts.secretKey) || decryptedRequestData.secretKey || import_constants.SECRET_KEY,
    publishableKey: decryptedRequestData.publishableKey || import_constants.PUBLISHABLE_KEY,
    apiUrl: import_constants.API_URL,
    apiVersion: import_constants.API_VERSION,
    authStatus,
    authMessage,
    authReason,
    treatPendingAsSignedOut
  };
  if (!(0, import_internal.isTokenTypeAccepted)(import_internal.TokenType.SessionToken, opts.acceptsToken || import_internal.TokenType.SessionToken)) {
    return (0, import_internal.signedOutAuthObject)(options);
  }
  let authObject;
  if (!authStatus || authStatus !== import_internal.AuthStatus.SignedIn) {
    authObject = (0, import_internal.signedOutAuthObject)(options);
  } else {
    (0, import_utils.assertTokenSignature)(authToken, options.secretKey, authSignature);
    const jwt = (0, import_jwt.decodeJwt)(authToken);
    (_b = opts.logger) == null ? void 0 : _b.debug("jwt", jwt.raw);
    return (0, import_internal.getAuthObjectFromJwt)(jwt, options);
  }
  return authObject;
};
const getAuthDataFromRequestAsync = async (req, opts = {}) => {
  var _a, _b;
  const { authStatus, authMessage, authReason } = getAuthHeaders(req);
  (_a = opts.logger) == null ? void 0 : _a.debug("headers", { authStatus, authMessage, authReason });
  const bearerToken = (_b = (0, import_headers_utils.getHeader)(req, import_internal.constants.Headers.Authorization)) == null ? void 0 : _b.replace("Bearer ", "");
  const acceptsToken = opts.acceptsToken || import_internal.TokenType.SessionToken;
  const options = {
    secretKey: (opts == null ? void 0 : opts.secretKey) || import_constants.SECRET_KEY,
    publishableKey: import_constants.PUBLISHABLE_KEY,
    apiUrl: import_constants.API_URL,
    authStatus,
    authMessage,
    authReason
  };
  if (bearerToken) {
    const isMachine = (0, import_internal.isMachineTokenByPrefix)(bearerToken);
    const tokenType = isMachine ? (0, import_internal.getMachineTokenType)(bearerToken) : void 0;
    if (Array.isArray(acceptsToken)) {
      if (isMachine) {
        return handleMachineToken({
          bearerToken,
          tokenType,
          acceptsToken,
          options
        });
      }
    } else {
      let intendedType;
      if ((0, import_internal.isMachineTokenType)(acceptsToken)) {
        intendedType = acceptsToken;
      }
      const result = await handleIntentBased({
        isMachine,
        tokenType,
        intendedType,
        bearerToken,
        acceptsToken,
        options
      });
      if (result) {
        return result;
      }
    }
  }
  if (Array.isArray(acceptsToken)) {
    if (!(0, import_internal.isTokenTypeAccepted)(import_internal.TokenType.SessionToken, acceptsToken)) {
      return (0, import_internal.invalidTokenAuthObject)();
    }
  }
  return getAuthDataFromRequestSync(req, opts);
};
const getAuthHeaders = (req) => {
  const authStatus = (0, import_headers_utils.getAuthKeyFromRequest)(req, "AuthStatus");
  const authToken = (0, import_headers_utils.getAuthKeyFromRequest)(req, "AuthToken");
  const authMessage = (0, import_headers_utils.getAuthKeyFromRequest)(req, "AuthMessage");
  const authReason = (0, import_headers_utils.getAuthKeyFromRequest)(req, "AuthReason");
  const authSignature = (0, import_headers_utils.getAuthKeyFromRequest)(req, "AuthSignature");
  return {
    authStatus,
    authToken,
    authMessage,
    authReason,
    authSignature
  };
};
async function handleMachineToken({
  bearerToken,
  tokenType,
  acceptsToken,
  options
}) {
  if (Array.isArray(acceptsToken)) {
    if (!(0, import_internal.isTokenTypeAccepted)(tokenType, acceptsToken)) {
      return (0, import_internal.invalidTokenAuthObject)();
    }
  }
  if (!(0, import_internal.isTokenTypeAccepted)(tokenType, acceptsToken != null ? acceptsToken : import_internal.TokenType.SessionToken)) {
    return (0, import_internal.unauthenticatedMachineObject)(tokenType, options);
  }
  const { data, errors } = await (0, import_internal.verifyMachineAuthToken)(bearerToken, options);
  if (errors) {
    return (0, import_internal.unauthenticatedMachineObject)(tokenType, options);
  }
  return (0, import_internal.authenticatedMachineObject)(tokenType, bearerToken, data);
}
async function handleIntentBased({
  isMachine,
  tokenType,
  intendedType,
  bearerToken,
  acceptsToken,
  options
}) {
  if (isMachine) {
    if (!tokenType) {
      return (0, import_internal.signedOutAuthObject)(options);
    }
    if (!(0, import_internal.isTokenTypeAccepted)(tokenType, acceptsToken != null ? acceptsToken : import_internal.TokenType.SessionToken)) {
      if (intendedType && (0, import_internal.isMachineTokenType)(intendedType)) {
        return (0, import_internal.unauthenticatedMachineObject)(intendedType, options);
      }
      return (0, import_internal.signedOutAuthObject)(options);
    }
    const { data, errors } = await (0, import_internal.verifyMachineAuthToken)(bearerToken, options);
    if (errors) {
      return (0, import_internal.unauthenticatedMachineObject)(tokenType, options);
    }
    return (0, import_internal.authenticatedMachineObject)(tokenType, bearerToken, data);
  } else if (intendedType && (0, import_internal.isMachineTokenType)(intendedType)) {
    return (0, import_internal.unauthenticatedMachineObject)(intendedType, options);
  }
  return null;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  getAuthDataFromRequestAsync,
  getAuthDataFromRequestSync
});
//# sourceMappingURL=getAuthDataFromRequest.js.map