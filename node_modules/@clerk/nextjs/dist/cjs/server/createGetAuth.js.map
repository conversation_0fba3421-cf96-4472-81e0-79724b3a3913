{"version": 3, "sources": ["../../../src/server/createGetAuth.ts"], "sourcesContent": ["import type { AuthObject } from '@clerk/backend';\nimport { constants, type SignedInAuthObject, type SignedOutAuthObject } from '@clerk/backend/internal';\nimport { isTruthy } from '@clerk/shared/underscore';\nimport type { PendingSessionOptions } from '@clerk/types';\n\nimport { withLogger } from '../utils/debugLogger';\nimport { isNextWithUnstableServerActions } from '../utils/sdk-versions';\nimport type { GetAuthDataFromRequestOptions } from './data/getAuthDataFromRequest';\nimport {\n  getAuthDataFromRequestAsync as getAuthDataFromRequestAsyncOriginal,\n  getAuthDataFromRequestSync as getAuthDataFromRequestSyncOriginal,\n} from './data/getAuthDataFromRequest';\nimport { getAuthAuthHeaderMissing } from './errors';\nimport { detectClerkMiddleware, getHeader } from './headers-utils';\nimport type { RequestLike } from './types';\nimport { assertAuthStatus } from './utils';\n\nexport type GetAuthOptions = {\n  acceptsToken?: GetAuthDataFromRequestOptions['acceptsToken'];\n} & PendingSessionOptions;\n\n/**\n * The async variant of our old `createGetAuth` allows for asynchronous code inside its callback.\n * Should be used with function like `auth()` that are already asynchronous.\n */\nexport const createAsyncGetAuth = ({\n  debugLoggerName,\n  noAuthStatusMessage,\n}: {\n  debugLoggerName: string;\n  noAuthStatusMessage: string;\n}) =>\n  withLogger(debugLoggerName, logger => {\n    return async (req: RequestLike, opts?: { secretKey?: string } & GetAuthOptions): Promise<AuthObject> => {\n      if (isTruthy(getHeader(req, constants.Headers.EnableDebug))) {\n        logger.enable();\n      }\n\n      if (!detectClerkMiddleware(req)) {\n        // Keep the same behaviour for versions that may have issues with bundling `node:fs`\n        if (isNextWithUnstableServerActions) {\n          assertAuthStatus(req, noAuthStatusMessage);\n        }\n\n        const missConfiguredMiddlewareLocation = await import('./fs/middleware-location.js')\n          .then(m => m.suggestMiddlewareLocation())\n          .catch(() => undefined);\n\n        if (missConfiguredMiddlewareLocation) {\n          throw new Error(missConfiguredMiddlewareLocation);\n        }\n\n        // still throw there is no suggested move location\n        assertAuthStatus(req, noAuthStatusMessage);\n      }\n\n      const getAuthDataFromRequestAsync = (req: RequestLike, opts: GetAuthDataFromRequestOptions = {}) => {\n        return getAuthDataFromRequestAsyncOriginal(req, { ...opts, logger, acceptsToken: opts?.acceptsToken });\n      };\n\n      return getAuthDataFromRequestAsync(req, { ...opts, logger, acceptsToken: opts?.acceptsToken });\n    };\n  });\n\n/**\n * Previous known as `createGetAuth`. We needed to create a sync and async variant in order to allow for improvements\n * that required dynamic imports (using `require` would not work).\n * It powers the synchronous top-level api `getAuth()`.\n */\nexport const createSyncGetAuth = ({\n  debugLoggerName,\n  noAuthStatusMessage,\n}: {\n  debugLoggerName: string;\n  noAuthStatusMessage: string;\n}) =>\n  withLogger(debugLoggerName, logger => {\n    return (\n      req: RequestLike,\n      opts?: { secretKey?: string } & GetAuthOptions,\n    ): SignedInAuthObject | SignedOutAuthObject => {\n      if (isTruthy(getHeader(req, constants.Headers.EnableDebug))) {\n        logger.enable();\n      }\n\n      assertAuthStatus(req, noAuthStatusMessage);\n\n      const getAuthDataFromRequestSync = (req: RequestLike, opts: GetAuthDataFromRequestOptions = {}) => {\n        return getAuthDataFromRequestSyncOriginal(req, { ...opts, logger, acceptsToken: opts?.acceptsToken });\n      };\n\n      return getAuthDataFromRequestSync(req, { ...opts, logger, acceptsToken: opts?.acceptsToken });\n    };\n  });\n\n/**\n * The `getAuth()` helper retrieves authentication state from the request object.\n *\n * > [!NOTE]\n * > If you are using App Router, use the [`auth()` helper](https://clerk.com/docs/references/nextjs/auth) instead.\n *\n * @param req - The Next.js request object.\n * @param [options] - An optional object that can be used to configure the behavior of the `getAuth()` function.\n * @param [options.secretKey] - A string that represents the Secret Key used to sign the session token. If not provided, the Secret Key is retrieved from the environment variable `CLERK_SECRET_KEY`.\n * @returns The `Auth` object. See the [Auth reference](https://clerk.com/docs/references/backend/types/auth-object) for more information.\n *\n * @example\n * ### Protect API routes\n *\n * The following example demonstrates how to protect an API route by checking if the `userId` is present in the `getAuth()` response.\n *\n * ```tsx {{ filename: 'app/api/example/route.ts' }}\n * import { getAuth } from '@clerk/nextjs/server'\n * import type { NextApiRequest, NextApiResponse } from 'next'\n *\n * export default async function handler(req: NextApiRequest, res: NextApiResponse) {\n *   const { userId } = getAuth(req)\n *\n *   if (!userId) {\n *     return res.status(401).json({ error: 'Not authenticated' })\n *   }\n *\n *   // Add logic that retrieves the data for the API route\n *\n *   return res.status(200).json({ userId: userId })\n * }\n * ```\n *\n * @example\n * ### Usage with `getToken()`\n *\n * `getAuth()` returns [`getToken()`](https://clerk.com/docs/references/backend/types/auth-object#get-token), which is a method that returns the current user's session token or a custom JWT template.\n *\n * ```tsx {{ filename: 'app/api/example/route.ts' }}\n * import { getAuth } from '@clerk/nextjs/server'\n * import type { NextApiRequest, NextApiResponse } from 'next'\n *\n * export default async function handler(req: NextApiRequest, res: NextApiResponse) {\n *   const { getToken } = getAuth(req)\n *\n *   const token = await getToken({ template: 'supabase' })\n *\n *   // Add logic that retrieves the data\n *   // from your database using the token\n *\n *   return res.status(200).json({})\n * }\n * ```\n *\n * @example\n * ### Usage with `clerkClient`\n *\n * `clerkClient` is used to access the [Backend SDK](https://clerk.com/docs/references/backend/overview), which exposes Clerk's Backend API resources. You can use `getAuth()` to pass authentication information that many of the Backend SDK methods require, like the user's ID.\n *\n * ```tsx {{ filename: 'app/api/example/route.ts' }}\n * import { clerkClient, getAuth } from '@clerk/nextjs/server'\n * import type { NextApiRequest, NextApiResponse } from 'next'\n *\n * export default async function handler(req: NextApiRequest, res: NextApiResponse) {\n *   const { userId } = getAuth(req)\n *\n *   const client = await clerkClient()\n *\n *   const user = userId ? await client.users.getUser(userId) : null\n *\n *   return res.status(200).json({})\n * }\n * ```\n */\nexport const getAuth = createSyncGetAuth({\n  debugLoggerName: 'getAuth()',\n  noAuthStatusMessage: getAuthAuthHeaderMissing(),\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,sBAA6E;AAC7E,wBAAyB;AAGzB,yBAA2B;AAC3B,0BAAgD;AAEhD,oCAGO;AACP,oBAAyC;AACzC,2BAAiD;AAEjD,mBAAiC;AAU1B,MAAM,qBAAqB,CAAC;AAAA,EACjC;AAAA,EACA;AACF,UAIE,+BAAW,iBAAiB,YAAU;AACpC,SAAO,OAAO,KAAkB,SAAwE;AACtG,YAAI,gCAAS,gCAAU,KAAK,0BAAU,QAAQ,WAAW,CAAC,GAAG;AAC3D,aAAO,OAAO;AAAA,IAChB;AAEA,QAAI,KAAC,4CAAsB,GAAG,GAAG;AAE/B,UAAI,qDAAiC;AACnC,2CAAiB,KAAK,mBAAmB;AAAA,MAC3C;AAEA,YAAM,mCAAmC,MAAM,OAAO,6BAA6B,EAChF,KAAK,OAAK,EAAE,0BAA0B,CAAC,EACvC,MAAM,MAAM,MAAS;AAExB,UAAI,kCAAkC;AACpC,cAAM,IAAI,MAAM,gCAAgC;AAAA,MAClD;AAGA,yCAAiB,KAAK,mBAAmB;AAAA,IAC3C;AAEA,UAAM,8BAA8B,CAACA,MAAkBC,QAAsC,CAAC,MAAM;AAClG,iBAAO,8BAAAC,6BAAoCF,MAAK,EAAE,GAAGC,OAAM,QAAQ,cAAcA,SAAA,gBAAAA,MAAM,aAAa,CAAC;AAAA,IACvG;AAEA,WAAO,4BAA4B,KAAK,EAAE,GAAG,MAAM,QAAQ,cAAc,6BAAM,aAAa,CAAC;AAAA,EAC/F;AACF,CAAC;AAOI,MAAM,oBAAoB,CAAC;AAAA,EAChC;AAAA,EACA;AACF,UAIE,+BAAW,iBAAiB,YAAU;AACpC,SAAO,CACL,KACA,SAC6C;AAC7C,YAAI,gCAAS,gCAAU,KAAK,0BAAU,QAAQ,WAAW,CAAC,GAAG;AAC3D,aAAO,OAAO;AAAA,IAChB;AAEA,uCAAiB,KAAK,mBAAmB;AAEzC,UAAM,6BAA6B,CAACD,MAAkBC,QAAsC,CAAC,MAAM;AACjG,iBAAO,8BAAAE,4BAAmCH,MAAK,EAAE,GAAGC,OAAM,QAAQ,cAAcA,SAAA,gBAAAA,MAAM,aAAa,CAAC;AAAA,IACtG;AAEA,WAAO,2BAA2B,KAAK,EAAE,GAAG,MAAM,QAAQ,cAAc,6BAAM,aAAa,CAAC;AAAA,EAC9F;AACF,CAAC;AA4EI,MAAM,UAAU,kBAAkB;AAAA,EACvC,iBAAiB;AAAA,EACjB,yBAAqB,wCAAyB;AAChD,CAAC;", "names": ["req", "opts", "getAuthDataFromRequestAsyncOriginal", "getAuthDataFromRequestSyncOriginal"]}