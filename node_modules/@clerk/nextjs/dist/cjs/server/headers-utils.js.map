{"version": 3, "sources": ["../../../src/server/headers-utils.ts"], "sourcesContent": ["import { constants } from '@clerk/backend/internal';\nimport type { NextRequest } from 'next/server';\n\nimport type { RequestLike } from './types';\n\nexport function getCustomAttributeFromRequest(req: RequestLike, key: string): string | null | undefined {\n  // @ts-expect-error - TS doesn't like indexing into RequestLike\n  return key in req ? req[key] : undefined;\n}\n\nexport function getAuthKeyFromRequest(\n  req: RequestLike,\n  key: keyof typeof constants.Attributes,\n): string | null | undefined {\n  return getCustomAttributeFromRequest(req, constants.Attributes[key]) || getHeader(req, constants.Headers[key]);\n}\n\nexport function getHeader(req: RequestLike, name: string): string | null | undefined {\n  if (isNextRequest(req) || isRequestWebAPI(req)) {\n    return req.headers.get(name);\n  }\n\n  // If no header has been determined for IncomingMessage case, check if available within private `socket` headers\n  // When deployed to vercel, req.headers for API routes is a `IncomingHttpHeaders` key-val object which does not follow\n  // the Headers spec so the name is no longer case-insensitive.\n  return req.headers[name] || req.headers[name.toLowerCase()] || (req.socket as any)?._httpMessage?.getHeader(name);\n}\n\nexport function detectClerkMiddleware(req: RequestLike): boolean {\n  return Boolean(getAuthKeyFromRequest(req, 'AuthStatus'));\n}\n\nexport function isNextRequest(val: unknown): val is NextRequest {\n  try {\n    const { headers, nextUrl, cookies } = (val || {}) as NextRequest;\n    return (\n      typeof headers?.get === 'function' &&\n      typeof nextUrl?.searchParams.get === 'function' &&\n      typeof cookies?.get === 'function'\n    );\n  } catch {\n    return false;\n  }\n}\n\nexport function isRequestWebAPI(val: unknown): val is Request {\n  try {\n    const { headers } = (val || {}) as Request;\n    return typeof headers?.get === 'function';\n  } catch {\n    return false;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAA0B;AAKnB,SAAS,8BAA8B,KAAkB,KAAwC;AAEtG,SAAO,OAAO,MAAM,IAAI,GAAG,IAAI;AACjC;AAEO,SAAS,sBACd,KACA,KAC2B;AAC3B,SAAO,8BAA8B,KAAK,0BAAU,WAAW,GAAG,CAAC,KAAK,UAAU,KAAK,0BAAU,QAAQ,GAAG,CAAC;AAC/G;AAEO,SAAS,UAAU,KAAkB,MAAyC;AAjBrF;AAkBE,MAAI,cAAc,GAAG,KAAK,gBAAgB,GAAG,GAAG;AAC9C,WAAO,IAAI,QAAQ,IAAI,IAAI;AAAA,EAC7B;AAKA,SAAO,IAAI,QAAQ,IAAI,KAAK,IAAI,QAAQ,KAAK,YAAY,CAAC,OAAM,eAAI,WAAJ,mBAAoB,iBAApB,mBAAkC,UAAU;AAC9G;AAEO,SAAS,sBAAsB,KAA2B;AAC/D,SAAO,QAAQ,sBAAsB,KAAK,YAAY,CAAC;AACzD;AAEO,SAAS,cAAc,KAAkC;AAC9D,MAAI;AACF,UAAM,EAAE,SAAS,SAAS,QAAQ,IAAK,OAAO,CAAC;AAC/C,WACE,QAAO,mCAAS,SAAQ,cACxB,QAAO,mCAAS,aAAa,SAAQ,cACrC,QAAO,mCAAS,SAAQ;AAAA,EAE5B,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAEO,SAAS,gBAAgB,KAA8B;AAC5D,MAAI;AACF,UAAM,EAAE,QAAQ,IAAK,OAAO,CAAC;AAC7B,WAAO,QAAO,mCAAS,SAAQ;AAAA,EACjC,QAAQ;AACN,WAAO;AAAA,EACT;AACF;", "names": []}