{"version": 3, "sources": ["../../../../src/app-router/client/ClerkProvider.tsx"], "sourcesContent": ["'use client';\nimport { <PERSON><PERSON><PERSON><PERSON> as ReactClerkProvider } from '@clerk/clerk-react';\nimport { inBrowser } from '@clerk/shared/browser';\nimport { logger } from '@clerk/shared/logger';\nimport dynamic from 'next/dynamic';\nimport { useRouter } from 'next/navigation';\nimport nextPackage from 'next/package.json';\nimport React, { useEffect, useTransition } from 'react';\n\nimport { useSafeLayoutEffect } from '../../client-boundary/hooks/useSafeLayoutEffect';\nimport { ClerkNextOptionsProvider, useClerkNextOptions } from '../../client-boundary/NextOptionsContext';\nimport type { NextClerkProviderProps } from '../../types';\nimport { ClerkJSScript } from '../../utils/clerk-js-script';\nimport { canUseKeyless } from '../../utils/feature-flags';\nimport { mergeNextClerkPropsWithEnv } from '../../utils/mergeNextClerkPropsWithEnv';\nimport { RouterTelemetry } from '../../utils/router-telemetry';\nimport { isNextWithUnstableServerActions } from '../../utils/sdk-versions';\nimport { invalidateCacheAction } from '../server-actions';\nimport { useAwaitablePush } from './useAwaitablePush';\nimport { useAwaitableReplace } from './useAwaitableReplace';\n\n/**\n * LazyCreateKeylessApplication should only be loaded if the conditions below are met.\n * Note: Using lazy() with Suspense instead of dynamic is not possible as React will throw a hydration error when `ClerkProvider` wraps `<html><body>...`\n */\nconst LazyCreateKeylessApplication = dynamic(() =>\n  import('./keyless-creator-reader.js').then(m => m.KeylessCreatorOrReader),\n);\n\nconst NextClientClerkProvider = (props: NextClerkProviderProps) => {\n  if (isNextWithUnstableServerActions) {\n    const deprecationWarning = `Clerk:\\nYour current Next.js version (${nextPackage.version}) will be deprecated in the next major release of \"@clerk/nextjs\". Please upgrade to next@14.1.0 or later.`;\n    if (inBrowser()) {\n      logger.warnOnce(deprecationWarning);\n    } else {\n      logger.logOnce(`\\n\\x1b[43m----------\\n${deprecationWarning}\\n----------\\x1b[0m\\n`);\n    }\n  }\n\n  const { __unstable_invokeMiddlewareOnAuthStateChange = true, children } = props;\n  const router = useRouter();\n  const push = useAwaitablePush();\n  const replace = useAwaitableReplace();\n  const [isPending, startTransition] = useTransition();\n\n  // Avoid rendering nested ClerkProviders by checking for the existence of the ClerkNextOptions context provider\n  const isNested = Boolean(useClerkNextOptions());\n  if (isNested) {\n    return props.children;\n  }\n\n  useEffect(() => {\n    if (!isPending) {\n      window.__clerk_internal_invalidateCachePromise?.();\n    }\n  }, [isPending]);\n\n  useSafeLayoutEffect(() => {\n    window.__unstable__onBeforeSetActive = intent => {\n      /**\n       * We need to invalidate the cache in case the user is navigating to a page that\n       * was previously cached using the auth state that was active at the time.\n       *\n       *  We also need to await for the invalidation to happen before we navigate,\n       * otherwise the navigation will use the cached page.\n       *\n       * For example, if we did not invalidate the flow, the following scenario would be broken:\n       * - The middleware is configured in such a way that it redirects you back to the same page if a certain condition is true (eg, you need to pick an org)\n       * - The user has a <Link href=/> component in the page\n       * - The UB is mounted with afterSignOutUrl=/\n       * - The user clicks the Link. A nav to / happens, a 307 to the current page is returned so a navigation does not take place. The / navigation is now cached as a 307 to the current page\n       * - The user clicks sign out\n       * - We call router.refresh()\n       * - We navigate to / but its cached and instead, we 'redirect' to the current page\n       *\n       *  For more information on cache invalidation, see:\n       * https://nextjs.org/docs/app/building-your-application/caching#invalidation-1\n       */\n      return new Promise(resolve => {\n        window.__clerk_internal_invalidateCachePromise = resolve;\n\n        const nextVersion = window?.next?.version || '';\n\n        // ATTENTION: Avoid using wrapping code with `startTransition` on versions >= 14\n        // otherwise the fetcher of `useReverification()` will be pending indefinitely when called within `startTransition`.\n        if (nextVersion.startsWith('13')) {\n          startTransition(() => {\n            router.refresh();\n          });\n        }\n        // On Next.js v15 calling a server action that returns a 404 error when deployed on Vercel is prohibited, failing with 405 status code.\n        // When a user transitions from \"signed in\" to \"singed out\", we clear the `__session` cookie, then we call `__unstable__onBeforeSetActive`.\n        // If we were to call `invalidateCacheAction` while the user is already signed out (deleted cookie), any page protected by `auth.protect()`\n        // will result to the server action returning a 404 error (this happens because server actions inherit the protection rules of the page they are called from).\n        // SOLUTION:\n        // To mitigate this, since the router cache on version 15 is much less aggressive, we can treat this as a noop and simply resolve the promise.\n        // Once `setActive` performs the navigation, `__unstable__onAfterSetActive` will kick in and perform a router.refresh ensuring shared layouts will also update with the correct authentication context.\n        else if (nextVersion.startsWith('15') && intent === 'sign-out') {\n          resolve(); // noop\n        } else {\n          void invalidateCacheAction().then(() => resolve());\n        }\n      });\n    };\n\n    window.__unstable__onAfterSetActive = () => {\n      if (__unstable_invokeMiddlewareOnAuthStateChange) {\n        return router.refresh();\n      }\n    };\n  }, []);\n\n  const mergedProps = mergeNextClerkPropsWithEnv({\n    ...props,\n    // @ts-expect-error Error because of the stricter types of internal `push`\n    routerPush: push,\n    // @ts-expect-error Error because of the stricter types of internal `replace`\n    routerReplace: replace,\n  });\n\n  return (\n    <ClerkNextOptionsProvider options={mergedProps}>\n      <ReactClerkProvider {...mergedProps}>\n        <RouterTelemetry />\n        <ClerkJSScript router='app' />\n        {children}\n      </ReactClerkProvider>\n    </ClerkNextOptionsProvider>\n  );\n};\n\nexport const ClientClerkProvider = (props: NextClerkProviderProps & { disableKeyless?: boolean }) => {\n  const { children, disableKeyless = false, ...rest } = props;\n  const safePublishableKey = mergeNextClerkPropsWithEnv(rest).publishableKey;\n\n  if (safePublishableKey || !canUseKeyless || disableKeyless) {\n    return <NextClientClerkProvider {...rest}>{children}</NextClientClerkProvider>;\n  }\n\n  return (\n    <LazyCreateKeylessApplication>\n      <NextClientClerkProvider {...rest}>{children}</NextClientClerkProvider>\n    </LazyCreateKeylessApplication>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,yBAAoD;AACpD,qBAA0B;AAC1B,oBAAuB;AACvB,qBAAoB;AACpB,wBAA0B;AAC1B,qBAAwB;AACxB,mBAAgD;AAEhD,iCAAoC;AACpC,gCAA8D;AAE9D,6BAA8B;AAC9B,2BAA8B;AAC9B,wCAA2C;AAC3C,8BAAgC;AAChC,0BAAgD;AAChD,4BAAsC;AACtC,8BAAiC;AACjC,iCAAoC;AAMpC,MAAM,mCAA+B,eAAAA;AAAA,EAAQ,MAC3C,OAAO,6BAA6B,EAAE,KAAK,OAAK,EAAE,sBAAsB;AAC1E;AAEA,MAAM,0BAA0B,CAAC,UAAkC;AACjE,MAAI,qDAAiC;AACnC,UAAM,qBAAqB;AAAA,gCAAyC,eAAAC,QAAY,OAAO;AACvF,YAAI,0BAAU,GAAG;AACf,2BAAO,SAAS,kBAAkB;AAAA,IACpC,OAAO;AACL,2BAAO,QAAQ;AAAA;AAAA,EAAyB,kBAAkB;AAAA;AAAA,CAAuB;AAAA,IACnF;AAAA,EACF;AAEA,QAAM,EAAE,+CAA+C,MAAM,SAAS,IAAI;AAC1E,QAAM,aAAS,6BAAU;AACzB,QAAM,WAAO,0CAAiB;AAC9B,QAAM,cAAU,gDAAoB;AACpC,QAAM,CAAC,WAAW,eAAe,QAAI,4BAAc;AAGnD,QAAM,WAAW,YAAQ,+CAAoB,CAAC;AAC9C,MAAI,UAAU;AACZ,WAAO,MAAM;AAAA,EACf;AAEA,8BAAU,MAAM;AAnDlB;AAoDI,QAAI,CAAC,WAAW;AACd,mBAAO,4CAAP;AAAA,IACF;AAAA,EACF,GAAG,CAAC,SAAS,CAAC;AAEd,sDAAoB,MAAM;AACxB,WAAO,gCAAgC,YAAU;AAoB/C,aAAO,IAAI,QAAQ,aAAW;AA9EpC;AA+EQ,eAAO,0CAA0C;AAEjD,cAAM,gBAAc,sCAAQ,SAAR,mBAAc,YAAW;AAI7C,YAAI,YAAY,WAAW,IAAI,GAAG;AAChC,0BAAgB,MAAM;AACpB,mBAAO,QAAQ;AAAA,UACjB,CAAC;AAAA,QACH,WAQS,YAAY,WAAW,IAAI,KAAK,WAAW,YAAY;AAC9D,kBAAQ;AAAA,QACV,OAAO;AACL,mBAAK,6CAAsB,EAAE,KAAK,MAAM,QAAQ,CAAC;AAAA,QACnD;AAAA,MACF,CAAC;AAAA,IACH;AAEA,WAAO,+BAA+B,MAAM;AAC1C,UAAI,8CAA8C;AAChD,eAAO,OAAO,QAAQ;AAAA,MACxB;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,QAAM,kBAAc,8DAA2B;AAAA,IAC7C,GAAG;AAAA;AAAA,IAEH,YAAY;AAAA;AAAA,IAEZ,eAAe;AAAA,EACjB,CAAC;AAED,SACE,6BAAAC,QAAA,cAAC,sDAAyB,SAAS,eACjC,6BAAAA,QAAA,cAAC,mBAAAC,eAAA,EAAoB,GAAG,eACtB,6BAAAD,QAAA,cAAC,6CAAgB,GACjB,6BAAAA,QAAA,cAAC,wCAAc,QAAO,OAAM,GAC3B,QACH,CACF;AAEJ;AAEO,MAAM,sBAAsB,CAAC,UAAiE;AACnG,QAAM,EAAE,UAAU,iBAAiB,OAAO,GAAG,KAAK,IAAI;AACtD,QAAM,yBAAqB,8DAA2B,IAAI,EAAE;AAE5D,MAAI,sBAAsB,CAAC,sCAAiB,gBAAgB;AAC1D,WAAO,6BAAAA,QAAA,cAAC,2BAAyB,GAAG,QAAO,QAAS;AAAA,EACtD;AAEA,SACE,6BAAAA,QAAA,cAAC,oCACC,6BAAAA,QAAA,cAAC,2BAAyB,GAAG,QAAO,QAAS,CAC/C;AAEJ;", "names": ["dynamic", "nextPackage", "React", "ReactClerkProvider"]}