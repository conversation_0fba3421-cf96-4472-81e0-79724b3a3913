"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var auth_exports = {};
__export(auth_exports, {
  auth: () => auth
});
module.exports = __toCommonJS(auth_exports);
var import_internal = require("@clerk/backend/internal");
var import_navigation = require("next/navigation");
var import_constants = require("../../server/constants");
var import_createGetAuth = require("../../server/createGetAuth");
var import_errors = require("../../server/errors");
var import_headers_utils = require("../../server/headers-utils");
var import_nextErrors = require("../../server/nextErrors");
var import_protect = require("../../server/protect");
var import_utils = require("../../server/utils");
var import_sdk_versions = require("../../utils/sdk-versions");
var import_utils2 = require("./utils");
const auth = async (options) => {
  var _a;
  require("server-only");
  const request = await (0, import_utils2.buildRequestLike)();
  const stepsBasedOnSrcDirectory = async () => {
    if (import_sdk_versions.isNextWithUnstableServerActions) {
      return [];
    }
    try {
      const isSrcAppDir = await import("../../server/fs/middleware-location.js").then((m) => m.hasSrcAppDir());
      return [`Your Middleware exists at ./${isSrcAppDir ? "src/" : ""}middleware.(ts|js)`];
    } catch {
      return [];
    }
  };
  const authObject = await (0, import_createGetAuth.createAsyncGetAuth)({
    debugLoggerName: "auth()",
    noAuthStatusMessage: (0, import_errors.authAuthHeaderMissing)("auth", await stepsBasedOnSrcDirectory())
  })(request, {
    treatPendingAsSignedOut: options == null ? void 0 : options.treatPendingAsSignedOut,
    acceptsToken: (_a = options == null ? void 0 : options.acceptsToken) != null ? _a : import_internal.TokenType.SessionToken
  });
  const clerkUrl = (0, import_headers_utils.getAuthKeyFromRequest)(request, "ClerkUrl");
  const createRedirectForRequest = (...args) => {
    const { returnBackUrl } = args[0] || {};
    const clerkRequest = (0, import_internal.createClerkRequest)(request);
    const devBrowserToken = clerkRequest.clerkUrl.searchParams.get(import_internal.constants.QueryParameters.DevBrowser) || clerkRequest.cookies.get(import_internal.constants.Cookies.DevBrowser);
    const encryptedRequestData = (0, import_headers_utils.getHeader)(request, import_internal.constants.Headers.ClerkRequestData);
    const decryptedRequestData = (0, import_utils.decryptClerkRequestData)(encryptedRequestData);
    return [
      (0, import_internal.createRedirect)({
        redirectAdapter: import_navigation.redirect,
        devBrowserToken,
        baseUrl: clerkRequest.clerkUrl.toString(),
        publishableKey: decryptedRequestData.publishableKey || import_constants.PUBLISHABLE_KEY,
        signInUrl: decryptedRequestData.signInUrl || import_constants.SIGN_IN_URL,
        signUpUrl: decryptedRequestData.signUpUrl || import_constants.SIGN_UP_URL,
        sessionStatus: authObject.tokenType === import_internal.TokenType.SessionToken ? authObject.sessionStatus : null
      }),
      returnBackUrl === null ? "" : returnBackUrl || (clerkUrl == null ? void 0 : clerkUrl.toString())
    ];
  };
  const redirectToSignIn = (opts = {}) => {
    const [r, returnBackUrl] = createRedirectForRequest(opts);
    return r.redirectToSignIn({
      returnBackUrl
    });
  };
  const redirectToSignUp = (opts = {}) => {
    const [r, returnBackUrl] = createRedirectForRequest(opts);
    return r.redirectToSignUp({
      returnBackUrl
    });
  };
  return Object.assign(authObject, { redirectToSignIn, redirectToSignUp });
};
auth.protect = async (...args) => {
  require("server-only");
  const request = await (0, import_utils2.buildRequestLike)();
  const authObject = await auth();
  const protect = (0, import_protect.createProtect)({
    request,
    authObject,
    redirectToSignIn: authObject.redirectToSignIn,
    notFound: import_navigation.notFound,
    redirect: import_navigation.redirect,
    unauthorized: import_nextErrors.unauthorized
  });
  return protect(...args);
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  auth
});
//# sourceMappingURL=auth.js.map