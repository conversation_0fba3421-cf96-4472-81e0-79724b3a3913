{"name": "@clerk/clerk-react", "version": "5.32.0", "description": "Clerk React library", "keywords": ["clerk", "react", "auth", "authentication", "passwordless", "session", "jwt"], "homepage": "https://clerk.com/", "bugs": {"url": "https://github.com/clerk/javascript/issues"}, "repository": {"type": "git", "url": "git+https://github.com/clerk/javascript.git", "directory": "packages/react"}, "license": "MIT", "author": "Clerk", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "./internal": {"import": {"types": "./dist/internal.d.mts", "default": "./dist/internal.mjs"}, "require": {"types": "./dist/internal.d.ts", "default": "./dist/internal.js"}}, "./errors": {"import": {"types": "./dist/errors.d.mts", "default": "./dist/errors.mjs"}, "require": {"types": "./dist/errors.d.ts", "default": "./dist/errors.js"}}, "./package.json": "./package.json"}, "main": "./dist/index.js", "files": ["dist", "internal", "errors"], "dependencies": {"tslib": "2.8.1", "@clerk/shared": "^3.9.7", "@clerk/types": "^4.60.1"}, "devDependencies": {"@types/semver": "^7.7.0", "@clerk/localizations": "3.16.5", "@clerk/themes": "2.2.50"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0 || ^19.0.0-0", "react-dom": "^18.0.0 || ^19.0.0 || ^19.0.0-0"}, "engines": {"node": ">=18.17.0"}, "publishConfig": {"access": "public"}, "scripts": {"build": "tsup", "postbuild": "node ../../scripts/subpath-workaround.mjs react", "build:declarations": "tsc -p tsconfig.declarations.json", "clean": "rimraf ./dist", "dev": "tsup --watch", "dev:publish": "pnpm dev --env.publish", "lint": "eslint src", "lint:attw": "attw --pack . --profile node16", "lint:publint": "publint", "publish:local": "pnpm yalc push --replace  --sig", "test": "vitest run", "test:watch": "vitest watch"}}