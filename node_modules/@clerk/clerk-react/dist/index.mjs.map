{"version": 3, "sources": ["../src/polyfills.ts", "../src/index.ts", "../src/components/uiComponents.tsx", "../src/utils/childrenUtils.tsx", "../src/utils/isConstructor.ts", "../src/utils/useMaxAllowedInstancesGuard.tsx", "../src/utils/useCustomElementPortal.tsx", "../src/utils/useCustomPages.tsx", "../src/utils/componentValidation.ts", "../src/utils/useCustomMenuItems.tsx", "../src/utils/useWaitForComponentMount.ts", "../src/components/ClerkHostRenderer.tsx", "../src/components/SignInButton.tsx", "../src/components/SignUpButton.tsx", "../src/components/SignOutButton.tsx", "../src/components/SignInWithMetamaskButton.tsx", "../src/contexts/ClerkProvider.tsx", "../src/contexts/ClerkContextProvider.tsx", "../src/isomorphicClerk.ts"], "sourcesContent": ["/**\n * Vite does not define `global` by default\n * One workaround is to use the `define` config prop\n * https://vitejs.dev/config/#define\n * We are solving this in the SDK level to reduce setup steps.\n */\nif (typeof window !== 'undefined' && !window.global) {\n  window.global = typeof global === 'undefined' ? window : global;\n}\n\nexport {};\n", "import './polyfills';\n\nimport { setClerkJsLoadingErrorPackageName } from '@clerk/shared/loadClerkJsScript';\n\nimport { setErrorThrowerOptions } from './errors/errorThrower';\n\nexport * from './components';\nexport * from './contexts';\n\nexport * from './hooks';\nexport type { BrowserClerk, ClerkProp, HeadlessBrowserClerk, ClerkProviderProps } from './types';\n\nsetErrorThrowerOptions({ packageName: PACKAGE_NAME });\nsetClerkJsLoadingErrorPackageName(PACKAGE_NAME);\n", "import { logErrorInDevMode } from '@clerk/shared/utils';\nimport type {\n  APIKeysProps,\n  CreateOrganizationProps,\n  GoogleOneTapProps,\n  OrganizationListProps,\n  OrganizationProfileProps,\n  OrganizationSwitcherProps,\n  PricingTableProps,\n  SignInProps,\n  SignUpProps,\n  UserButtonProps,\n  UserProfileProps,\n  WaitlistProps,\n  Without,\n} from '@clerk/types';\nimport type { PropsWithChildren, ReactNode } from 'react';\nimport React, { createContext, createElement, useContext } from 'react';\n\nimport {\n  organizationProfileLinkRenderedError,\n  organizationProfilePageRenderedError,\n  userButtonMenuActionRenderedError,\n  userButtonMenuItemsRenderedError,\n  userButtonMenuLinkRenderedError,\n  userProfileLinkRenderedError,\n  userProfilePageRenderedError,\n} from '../errors/messages';\nimport type {\n  CustomPortalsRendererProps,\n  MountProps,\n  OrganizationProfileLinkProps,\n  OrganizationProfilePageProps,\n  UserButtonActionProps,\n  UserButtonLinkProps,\n  UserProfileLinkProps,\n  UserProfilePageProps,\n  WithClerkProp,\n} from '../types';\nimport {\n  useOrganizationProfileCustomPages,\n  useSanitizedChildren,\n  useUserButtonCustomMenuItems,\n  useUserProfileCustomPages,\n} from '../utils';\nimport { useWaitForComponentMount } from '../utils/useWaitForComponentMount';\nimport { ClerkHostRenderer } from './ClerkHostRenderer';\nimport { withClerk } from './withClerk';\n\ntype FallbackProp = {\n  /**\n   * An optional element to render while the component is mounting.\n   */\n  fallback?: ReactNode;\n};\n\ntype UserProfileExportType = typeof _UserProfile & {\n  Page: typeof UserProfilePage;\n  Link: typeof UserProfileLink;\n};\n\ntype UserButtonExportType = typeof _UserButton & {\n  UserProfilePage: typeof UserProfilePage;\n  UserProfileLink: typeof UserProfileLink;\n  MenuItems: typeof MenuItems;\n  Action: typeof MenuAction;\n  Link: typeof MenuLink;\n  /**\n   * The `<Outlet />` component can be used in conjunction with `asProvider` in order to control rendering\n   * of the `<UserButton />` without affecting its configuration or any custom pages that could be mounted\n   * @experimental This API is experimental and may change at any moment.\n   */\n  __experimental_Outlet: typeof UserButtonOutlet;\n};\n\ntype UserButtonPropsWithoutCustomPages = Without<\n  UserButtonProps,\n  'userProfileProps' | '__experimental_asStandalone'\n> & {\n  userProfileProps?: Pick<UserProfileProps, 'additionalOAuthScopes' | 'appearance'>;\n  /**\n   * Adding `asProvider` will defer rendering until the `<Outlet />` component is mounted.\n   * This API is experimental and may change at any moment.\n   * @experimental\n   * @default undefined\n   */\n  __experimental_asProvider?: boolean;\n};\n\ntype OrganizationProfileExportType = typeof _OrganizationProfile & {\n  Page: typeof OrganizationProfilePage;\n  Link: typeof OrganizationProfileLink;\n};\n\ntype OrganizationSwitcherExportType = typeof _OrganizationSwitcher & {\n  OrganizationProfilePage: typeof OrganizationProfilePage;\n  OrganizationProfileLink: typeof OrganizationProfileLink;\n  /**\n   * The `<Outlet />` component can be used in conjunction with `asProvider` in order to control rendering\n   * of the `<OrganizationSwitcher />` without affecting its configuration or any custom pages that could be mounted\n   * @experimental This API is experimental and may change at any moment.\n   */\n  __experimental_Outlet: typeof OrganizationSwitcherOutlet;\n};\n\ntype OrganizationSwitcherPropsWithoutCustomPages = Without<\n  OrganizationSwitcherProps,\n  'organizationProfileProps' | '__experimental_asStandalone'\n> & {\n  organizationProfileProps?: Pick<OrganizationProfileProps, 'appearance'>;\n  /**\n   * Adding `asProvider` will defer rendering until the `<Outlet />` component is mounted.\n   * This API is experimental and may change at any moment.\n   * @experimental\n   * @default undefined\n   */\n  __experimental_asProvider?: boolean;\n};\n\nconst CustomPortalsRenderer = (props: CustomPortalsRendererProps) => {\n  return (\n    <>\n      {props?.customPagesPortals?.map((portal, index) => createElement(portal, { key: index }))}\n      {props?.customMenuItemsPortals?.map((portal, index) => createElement(portal, { key: index }))}\n    </>\n  );\n};\n\nexport const SignIn = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<SignInProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountSignIn}\n            unmount={clerk.unmountSignIn}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'SignIn', renderWhileLoading: true },\n);\n\nexport const SignUp = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<SignUpProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountSignUp}\n            unmount={clerk.unmountSignUp}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'SignUp', renderWhileLoading: true },\n);\n\nexport function UserProfilePage({ children }: PropsWithChildren<UserProfilePageProps>) {\n  logErrorInDevMode(userProfilePageRenderedError);\n  return <>{children}</>;\n}\n\nexport function UserProfileLink({ children }: PropsWithChildren<UserProfileLinkProps>) {\n  logErrorInDevMode(userProfileLinkRenderedError);\n  return <>{children}</>;\n}\n\nconst _UserProfile = withClerk(\n  ({\n    clerk,\n    component,\n    fallback,\n    ...props\n  }: WithClerkProp<PropsWithChildren<Without<UserProfileProps, 'customPages'>> & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    const { customPages, customPagesPortals } = useUserProfileCustomPages(props.children);\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        <ClerkHostRenderer\n          component={component}\n          mount={clerk.mountUserProfile}\n          unmount={clerk.unmountUserProfile}\n          updateProps={(clerk as any).__unstable__updateProps}\n          props={{ ...props, customPages }}\n          rootProps={rendererRootProps}\n        >\n          <CustomPortalsRenderer customPagesPortals={customPagesPortals} />\n        </ClerkHostRenderer>\n      </>\n    );\n  },\n  { component: 'UserProfile', renderWhileLoading: true },\n);\n\nexport const UserProfile: UserProfileExportType = Object.assign(_UserProfile, {\n  Page: UserProfilePage,\n  Link: UserProfileLink,\n});\n\nconst UserButtonContext = createContext<MountProps>({\n  mount: () => {},\n  unmount: () => {},\n  updateProps: () => {},\n});\n\nconst _UserButton = withClerk(\n  ({\n    clerk,\n    component,\n    fallback,\n    ...props\n  }: WithClerkProp<PropsWithChildren<UserButtonPropsWithoutCustomPages> & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    const { customPages, customPagesPortals } = useUserProfileCustomPages(props.children, {\n      allowForAnyChildren: !!props.__experimental_asProvider,\n    });\n    const userProfileProps = Object.assign(props.userProfileProps || {}, { customPages });\n    const { customMenuItems, customMenuItemsPortals } = useUserButtonCustomMenuItems(props.children);\n    const sanitizedChildren = useSanitizedChildren(props.children);\n\n    const passableProps = {\n      mount: clerk.mountUserButton,\n      unmount: clerk.unmountUserButton,\n      updateProps: (clerk as any).__unstable__updateProps,\n      props: { ...props, userProfileProps, customMenuItems },\n    };\n    const portalProps = {\n      customPagesPortals: customPagesPortals,\n      customMenuItemsPortals: customMenuItemsPortals,\n    };\n\n    return (\n      <UserButtonContext.Provider value={passableProps}>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            {...passableProps}\n            hideRootHtmlElement={!!props.__experimental_asProvider}\n            rootProps={rendererRootProps}\n          >\n            {/*This mimics the previous behaviour before asProvider existed*/}\n            {props.__experimental_asProvider ? sanitizedChildren : null}\n            <CustomPortalsRenderer {...portalProps} />\n          </ClerkHostRenderer>\n        )}\n      </UserButtonContext.Provider>\n    );\n  },\n  { component: 'UserButton', renderWhileLoading: true },\n);\n\nexport function MenuItems({ children }: PropsWithChildren) {\n  logErrorInDevMode(userButtonMenuItemsRenderedError);\n  return <>{children}</>;\n}\n\nexport function MenuAction({ children }: PropsWithChildren<UserButtonActionProps>) {\n  logErrorInDevMode(userButtonMenuActionRenderedError);\n  return <>{children}</>;\n}\n\nexport function MenuLink({ children }: PropsWithChildren<UserButtonLinkProps>) {\n  logErrorInDevMode(userButtonMenuLinkRenderedError);\n  return <>{children}</>;\n}\n\nexport function UserButtonOutlet(outletProps: Without<UserButtonProps, 'userProfileProps'>) {\n  const providerProps = useContext(UserButtonContext);\n\n  const portalProps = {\n    ...providerProps,\n    props: {\n      ...providerProps.props,\n      ...outletProps,\n    },\n  } satisfies MountProps;\n\n  return <ClerkHostRenderer {...portalProps} />;\n}\n\nexport const UserButton: UserButtonExportType = Object.assign(_UserButton, {\n  UserProfilePage,\n  UserProfileLink,\n  MenuItems,\n  Action: MenuAction,\n  Link: MenuLink,\n  __experimental_Outlet: UserButtonOutlet,\n});\n\nexport function OrganizationProfilePage({ children }: PropsWithChildren<OrganizationProfilePageProps>) {\n  logErrorInDevMode(organizationProfilePageRenderedError);\n  return <>{children}</>;\n}\n\nexport function OrganizationProfileLink({ children }: PropsWithChildren<OrganizationProfileLinkProps>) {\n  logErrorInDevMode(organizationProfileLinkRenderedError);\n  return <>{children}</>;\n}\n\nconst _OrganizationProfile = withClerk(\n  ({\n    clerk,\n    component,\n    fallback,\n    ...props\n  }: WithClerkProp<PropsWithChildren<Without<OrganizationProfileProps, 'customPages'>> & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    const { customPages, customPagesPortals } = useOrganizationProfileCustomPages(props.children);\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountOrganizationProfile}\n            unmount={clerk.unmountOrganizationProfile}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={{ ...props, customPages }}\n            rootProps={rendererRootProps}\n          >\n            <CustomPortalsRenderer customPagesPortals={customPagesPortals} />\n          </ClerkHostRenderer>\n        )}\n      </>\n    );\n  },\n  { component: 'OrganizationProfile', renderWhileLoading: true },\n);\n\nexport const OrganizationProfile: OrganizationProfileExportType = Object.assign(_OrganizationProfile, {\n  Page: OrganizationProfilePage,\n  Link: OrganizationProfileLink,\n});\n\nexport const CreateOrganization = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<CreateOrganizationProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountCreateOrganization}\n            unmount={clerk.unmountCreateOrganization}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'CreateOrganization', renderWhileLoading: true },\n);\n\nconst OrganizationSwitcherContext = createContext<MountProps>({\n  mount: () => {},\n  unmount: () => {},\n  updateProps: () => {},\n});\n\nconst _OrganizationSwitcher = withClerk(\n  ({\n    clerk,\n    component,\n    fallback,\n    ...props\n  }: WithClerkProp<PropsWithChildren<OrganizationSwitcherPropsWithoutCustomPages> & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    const { customPages, customPagesPortals } = useOrganizationProfileCustomPages(props.children, {\n      allowForAnyChildren: !!props.__experimental_asProvider,\n    });\n    const organizationProfileProps = Object.assign(props.organizationProfileProps || {}, { customPages });\n    const sanitizedChildren = useSanitizedChildren(props.children);\n\n    const passableProps = {\n      mount: clerk.mountOrganizationSwitcher,\n      unmount: clerk.unmountOrganizationSwitcher,\n      updateProps: (clerk as any).__unstable__updateProps,\n      props: { ...props, organizationProfileProps },\n      rootProps: rendererRootProps,\n      component,\n    };\n\n    /**\n     * Prefetch organization list\n     */\n    clerk.__experimental_prefetchOrganizationSwitcher();\n\n    return (\n      <OrganizationSwitcherContext.Provider value={passableProps}>\n        <>\n          {shouldShowFallback && fallback}\n          {clerk.loaded && (\n            <ClerkHostRenderer\n              {...passableProps}\n              hideRootHtmlElement={!!props.__experimental_asProvider}\n            >\n              {/*This mimics the previous behaviour before asProvider existed*/}\n              {props.__experimental_asProvider ? sanitizedChildren : null}\n              <CustomPortalsRenderer customPagesPortals={customPagesPortals} />\n            </ClerkHostRenderer>\n          )}\n        </>\n      </OrganizationSwitcherContext.Provider>\n    );\n  },\n  { component: 'OrganizationSwitcher', renderWhileLoading: true },\n);\n\nexport function OrganizationSwitcherOutlet(\n  outletProps: Without<OrganizationSwitcherProps, 'organizationProfileProps'>,\n) {\n  const providerProps = useContext(OrganizationSwitcherContext);\n\n  const portalProps = {\n    ...providerProps,\n    props: {\n      ...providerProps.props,\n      ...outletProps,\n    },\n  } satisfies MountProps;\n\n  return <ClerkHostRenderer {...portalProps} />;\n}\n\nexport const OrganizationSwitcher: OrganizationSwitcherExportType = Object.assign(_OrganizationSwitcher, {\n  OrganizationProfilePage,\n  OrganizationProfileLink,\n  __experimental_Outlet: OrganizationSwitcherOutlet,\n});\n\nexport const OrganizationList = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<OrganizationListProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountOrganizationList}\n            unmount={clerk.unmountOrganizationList}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'OrganizationList', renderWhileLoading: true },\n);\n\nexport const GoogleOneTap = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<GoogleOneTapProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            open={clerk.openGoogleOneTap}\n            close={clerk.closeGoogleOneTap}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'GoogleOneTap', renderWhileLoading: true },\n);\n\nexport const Waitlist = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<WaitlistProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountWaitlist}\n            unmount={clerk.unmountWaitlist}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'Waitlist', renderWhileLoading: true },\n);\n\nexport const PricingTable = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<PricingTableProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountPricingTable}\n            unmount={clerk.unmountPricingTable}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'PricingTable', renderWhileLoading: true },\n);\n\n/**\n * @experimental\n * This component is in early access and may change in future releases.\n */\nexport const APIKeys = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<APIKeysProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountApiKeys}\n            unmount={clerk.unmountApiKeys}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'ApiKeys', renderWhileLoading: true },\n);\n", "import React from 'react';\n\nimport { errorThrower } from '../errors/errorThrower';\nimport { multipleChildrenInButtonComponent } from '../errors/messages';\n\nexport const assertSingleChild =\n  (children: React.ReactNode) =>\n  (name: 'SignInButton' | 'SignUpButton' | 'SignOutButton' | 'SignInWithMetamaskButton') => {\n    try {\n      return React.Children.only(children);\n    } catch {\n      return errorThrower.throw(multipleChildrenInButtonComponent(name));\n    }\n  };\n\nexport const normalizeWithDefaultValue = (children: React.ReactNode | undefined, defaultText: string) => {\n  if (!children) {\n    children = defaultText;\n  }\n  if (typeof children === 'string') {\n    children = <button>{children}</button>;\n  }\n  return children;\n};\n\nexport const safeExecute =\n  (cb: unknown) =>\n  (...args: any) => {\n    if (cb && typeof cb === 'function') {\n      return cb(...args);\n    }\n  };\n", "export function isConstructor<T>(f: any): f is T {\n  return typeof f === 'function';\n}\n", "import React from 'react';\n\nimport { errorThrower } from '../errors/errorThrower';\n\nconst counts = new Map<string, number>();\n\nexport function useMaxAllowedInstancesGuard(name: string, error: string, maxCount = 1): void {\n  React.useEffect(() => {\n    const count = counts.get(name) || 0;\n    if (count == maxCount) {\n      return errorThrower.throw(error);\n    }\n    counts.set(name, count + 1);\n\n    return () => {\n      counts.set(name, (counts.get(name) || 1) - 1);\n    };\n  }, []);\n}\n\nexport function withMaxAllowedInstancesGuard<P>(\n  WrappedComponent: React.ComponentType<P>,\n  name: string,\n  error: string,\n): React.ComponentType<P> {\n  const displayName = WrappedComponent.displayName || WrappedComponent.name || name || 'Component';\n  const Hoc = (props: P) => {\n    useMaxAllowedInstancesGuard(name, error);\n    return <WrappedComponent {...(props as any)} />;\n  };\n  Hoc.displayName = `withMaxAllowedInstancesGuard(${displayName})`;\n  return Hoc;\n}\n", "import React, { useState } from 'react';\nimport { createPortal } from 'react-dom';\n\nexport type UseCustomElementPortalParams = {\n  component: React.ReactNode;\n  id: number;\n};\n\nexport type UseCustomElementPortalReturn = {\n  portal: () => React.JSX.Element;\n  mount: (node: Element) => void;\n  unmount: () => void;\n  id: number;\n};\n\n// This function takes a component as prop, and returns functions that mount and unmount\n// the given component into a given node\nexport const useCustomElementPortal = (elements: UseCustomElementPortalParams[]) => {\n  const initialState = Array(elements.length).fill(null);\n  const [nodes, setNodes] = useState<(Element | null)[]>(initialState);\n\n  return elements.map((el, index) => ({\n    id: el.id,\n    mount: (node: Element) => setNodes(prevState => prevState.map((n, i) => (i === index ? node : n))),\n    unmount: () => setNodes(prevState => prevState.map((n, i) => (i === index ? null : n))),\n    portal: () => <>{nodes[index] ? createPortal(el.component, nodes[index]) : null}</>,\n  }));\n};\n", "import { logErrorInDevMode } from '@clerk/shared/utils';\nimport type { CustomPage } from '@clerk/types';\nimport type { ReactElement } from 'react';\nimport React from 'react';\n\nimport {\n  MenuItems,\n  OrganizationProfileLink,\n  OrganizationProfilePage,\n  UserProfileLink,\n  UserProfilePage,\n} from '../components/uiComponents';\nimport { customLinkWrongProps, customPagesIgnoredComponent, customPageWrongProps } from '../errors/messages';\nimport type { UserProfilePageProps } from '../types';\nimport { isThatComponent } from './componentValidation';\nimport type { UseCustomElementPortalParams, UseCustomElementPortalReturn } from './useCustomElementPortal';\nimport { useCustomElementPortal } from './useCustomElementPortal';\n\nexport const useUserProfileCustomPages = (\n  children: React.ReactNode | React.ReactNode[],\n  options?: UseCustomPagesOptions,\n) => {\n  const reorderItemsLabels = ['account', 'security'];\n  return useCustomPages(\n    {\n      children,\n      reorderItemsLabels,\n      LinkComponent: UserProfileLink,\n      PageComponent: UserProfilePage,\n      MenuItemsComponent: MenuItems,\n      componentName: 'UserProfile',\n    },\n    options,\n  );\n};\n\nexport const useOrganizationProfileCustomPages = (\n  children: React.ReactNode | React.ReactNode[],\n  options?: UseCustomPagesOptions,\n) => {\n  const reorderItemsLabels = ['general', 'members'];\n  return useCustomPages(\n    {\n      children,\n      reorderItemsLabels,\n      LinkComponent: OrganizationProfileLink,\n      PageComponent: OrganizationProfilePage,\n      componentName: 'OrganizationProfile',\n    },\n    options,\n  );\n};\n\ntype UseCustomPagesParams = {\n  children: React.ReactNode | React.ReactNode[];\n  LinkComponent: any;\n  PageComponent: any;\n  MenuItemsComponent?: any;\n  reorderItemsLabels: string[];\n  componentName: string;\n};\n\ntype UseCustomPagesOptions = {\n  allowForAnyChildren: boolean;\n};\n\ntype CustomPageWithIdType = UserProfilePageProps & { children?: React.ReactNode };\n\n/**\n * Exclude any children that is used for identifying Custom Pages or Custom Items.\n * Passing:\n * ```tsx\n *  <UserProfile.Page/>\n *  <OrganizationProfile.Link/>\n *  <MyComponent>\n *  <UserButton.MenuItems/>\n * ```\n * Gives back\n * ```tsx\n * <MyComponent>\n * ````\n */\nexport const useSanitizedChildren = (children: React.ReactNode) => {\n  const sanitizedChildren: React.ReactNode[] = [];\n\n  const excludedComponents: any[] = [\n    OrganizationProfileLink,\n    OrganizationProfilePage,\n    MenuItems,\n    UserProfilePage,\n    UserProfileLink,\n  ];\n\n  React.Children.forEach(children, child => {\n    if (!excludedComponents.some(component => isThatComponent(child, component))) {\n      sanitizedChildren.push(child);\n    }\n  });\n\n  return sanitizedChildren;\n};\n\nconst useCustomPages = (params: UseCustomPagesParams, options?: UseCustomPagesOptions) => {\n  const { children, LinkComponent, PageComponent, MenuItemsComponent, reorderItemsLabels, componentName } = params;\n  const { allowForAnyChildren = false } = options || {};\n  const validChildren: CustomPageWithIdType[] = [];\n\n  React.Children.forEach(children, child => {\n    if (\n      !isThatComponent(child, PageComponent) &&\n      !isThatComponent(child, LinkComponent) &&\n      !isThatComponent(child, MenuItemsComponent)\n    ) {\n      if (child && !allowForAnyChildren) {\n        logErrorInDevMode(customPagesIgnoredComponent(componentName));\n      }\n      return;\n    }\n\n    const { props } = child as ReactElement;\n\n    const { children, label, url, labelIcon } = props;\n\n    if (isThatComponent(child, PageComponent)) {\n      if (isReorderItem(props, reorderItemsLabels)) {\n        // This is a reordering item\n        validChildren.push({ label });\n      } else if (isCustomPage(props)) {\n        // this is a custom page\n        validChildren.push({ label, labelIcon, children, url });\n      } else {\n        logErrorInDevMode(customPageWrongProps(componentName));\n        return;\n      }\n    }\n\n    if (isThatComponent(child, LinkComponent)) {\n      if (isExternalLink(props)) {\n        // This is an external link\n        validChildren.push({ label, labelIcon, url });\n      } else {\n        logErrorInDevMode(customLinkWrongProps(componentName));\n        return;\n      }\n    }\n  });\n\n  const customPageContents: UseCustomElementPortalParams[] = [];\n  const customPageLabelIcons: UseCustomElementPortalParams[] = [];\n  const customLinkLabelIcons: UseCustomElementPortalParams[] = [];\n\n  validChildren.forEach((cp, index) => {\n    if (isCustomPage(cp)) {\n      customPageContents.push({ component: cp.children, id: index });\n      customPageLabelIcons.push({ component: cp.labelIcon, id: index });\n      return;\n    }\n    if (isExternalLink(cp)) {\n      customLinkLabelIcons.push({ component: cp.labelIcon, id: index });\n    }\n  });\n\n  const customPageContentsPortals = useCustomElementPortal(customPageContents);\n  const customPageLabelIconsPortals = useCustomElementPortal(customPageLabelIcons);\n  const customLinkLabelIconsPortals = useCustomElementPortal(customLinkLabelIcons);\n\n  const customPages: CustomPage[] = [];\n  const customPagesPortals: React.ComponentType[] = [];\n\n  validChildren.forEach((cp, index) => {\n    if (isReorderItem(cp, reorderItemsLabels)) {\n      customPages.push({ label: cp.label });\n      return;\n    }\n    if (isCustomPage(cp)) {\n      const {\n        portal: contentPortal,\n        mount,\n        unmount,\n      } = customPageContentsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      const {\n        portal: labelPortal,\n        mount: mountIcon,\n        unmount: unmountIcon,\n      } = customPageLabelIconsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      customPages.push({ label: cp.label, url: cp.url, mount, unmount, mountIcon, unmountIcon });\n      customPagesPortals.push(contentPortal);\n      customPagesPortals.push(labelPortal);\n      return;\n    }\n    if (isExternalLink(cp)) {\n      const {\n        portal: labelPortal,\n        mount: mountIcon,\n        unmount: unmountIcon,\n      } = customLinkLabelIconsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      customPages.push({ label: cp.label, url: cp.url, mountIcon, unmountIcon });\n      customPagesPortals.push(labelPortal);\n      return;\n    }\n  });\n\n  return { customPages, customPagesPortals };\n};\n\nconst isReorderItem = (childProps: any, validItems: string[]): boolean => {\n  const { children, label, url, labelIcon } = childProps;\n  return !children && !url && !labelIcon && validItems.some(v => v === label);\n};\n\nconst isCustomPage = (childProps: any): boolean => {\n  const { children, label, url, labelIcon } = childProps;\n  return !!children && !!url && !!labelIcon && !!label;\n};\n\nconst isExternalLink = (childProps: any): boolean => {\n  const { children, label, url, labelIcon } = childProps;\n  return !children && !!url && !!labelIcon && !!label;\n};\n", "import React from 'react';\n\nexport const isThatComponent = (v: any, component: React.ReactNode): v is React.ReactNode => {\n  return !!v && React.isValidElement(v) && (v as React.ReactElement)?.type === component;\n};\n", "import { logErrorInDevMode } from '@clerk/shared/utils';\nimport type { CustomMenuItem } from '@clerk/types';\nimport type { ReactElement } from 'react';\nimport React from 'react';\n\nimport { MenuAction, MenuItems, MenuLink, UserProfileLink, UserProfilePage } from '../components/uiComponents';\nimport {\n  customMenuItemsIgnoredComponent,\n  userButtonIgnoredComponent,\n  userButtonMenuItemLinkWrongProps,\n  userButtonMenuItemsActionWrongsProps,\n} from '../errors/messages';\nimport type { UserButtonActionProps, UserButtonLinkProps } from '../types';\nimport { isThatComponent } from './componentValidation';\nimport type { UseCustomElementPortalParams, UseCustomElementPortalReturn } from './useCustomElementPortal';\nimport { useCustomElementPortal } from './useCustomElementPortal';\n\nexport const useUserButtonCustomMenuItems = (children: React.ReactNode | React.ReactNode[]) => {\n  const reorderItemsLabels = ['manageAccount', 'signOut'];\n  return useCustomMenuItems({\n    children,\n    reorderItemsLabels,\n    MenuItemsComponent: MenuItems,\n    MenuActionComponent: MenuAction,\n    MenuLinkComponent: MenuLink,\n    UserProfileLinkComponent: UserProfileLink,\n    UserProfilePageComponent: UserProfilePage,\n  });\n};\n\ntype UseCustomMenuItemsParams = {\n  children: React.ReactNode | React.ReactNode[];\n  MenuItemsComponent?: any;\n  MenuActionComponent?: any;\n  MenuLinkComponent?: any;\n  UserProfileLinkComponent?: any;\n  UserProfilePageComponent?: any;\n  reorderItemsLabels: string[];\n};\n\ntype CustomMenuItemType = UserButtonActionProps | UserButtonLinkProps;\n\nconst useCustomMenuItems = ({\n  children,\n  MenuItemsComponent,\n  MenuActionComponent,\n  MenuLinkComponent,\n  UserProfileLinkComponent,\n  UserProfilePageComponent,\n  reorderItemsLabels,\n}: UseCustomMenuItemsParams) => {\n  const validChildren: CustomMenuItemType[] = [];\n  const customMenuItems: CustomMenuItem[] = [];\n  const customMenuItemsPortals: React.ComponentType[] = [];\n\n  React.Children.forEach(children, child => {\n    if (\n      !isThatComponent(child, MenuItemsComponent) &&\n      !isThatComponent(child, UserProfileLinkComponent) &&\n      !isThatComponent(child, UserProfilePageComponent)\n    ) {\n      if (child) {\n        logErrorInDevMode(userButtonIgnoredComponent);\n      }\n      return;\n    }\n\n    // Ignore UserProfileLinkComponent and UserProfilePageComponent\n    if (isThatComponent(child, UserProfileLinkComponent) || isThatComponent(child, UserProfilePageComponent)) {\n      return;\n    }\n\n    // Menu items children\n    const { props } = child as ReactElement;\n\n    React.Children.forEach(props.children, child => {\n      if (!isThatComponent(child, MenuActionComponent) && !isThatComponent(child, MenuLinkComponent)) {\n        if (child) {\n          logErrorInDevMode(customMenuItemsIgnoredComponent);\n        }\n\n        return;\n      }\n\n      const { props } = child as ReactElement;\n\n      const { label, labelIcon, href, onClick, open } = props;\n\n      if (isThatComponent(child, MenuActionComponent)) {\n        if (isReorderItem(props, reorderItemsLabels)) {\n          // This is a reordering item\n          validChildren.push({ label });\n        } else if (isCustomMenuItem(props)) {\n          const baseItem = {\n            label,\n            labelIcon,\n          };\n\n          if (onClick !== undefined) {\n            validChildren.push({\n              ...baseItem,\n              onClick,\n            });\n          } else if (open !== undefined) {\n            validChildren.push({\n              ...baseItem,\n              open: open.startsWith('/') ? open : `/${open}`,\n            });\n          } else {\n            // Handle the case where neither onClick nor open is defined\n            logErrorInDevMode('Custom menu item must have either onClick or open property');\n            return;\n          }\n        } else {\n          logErrorInDevMode(userButtonMenuItemsActionWrongsProps);\n          return;\n        }\n      }\n\n      if (isThatComponent(child, MenuLinkComponent)) {\n        if (isExternalLink(props)) {\n          validChildren.push({ label, labelIcon, href });\n        } else {\n          logErrorInDevMode(userButtonMenuItemLinkWrongProps);\n          return;\n        }\n      }\n    });\n  });\n\n  const customMenuItemLabelIcons: UseCustomElementPortalParams[] = [];\n  const customLinkLabelIcons: UseCustomElementPortalParams[] = [];\n  validChildren.forEach((mi, index) => {\n    if (isCustomMenuItem(mi)) {\n      customMenuItemLabelIcons.push({ component: mi.labelIcon, id: index });\n    }\n    if (isExternalLink(mi)) {\n      customLinkLabelIcons.push({ component: mi.labelIcon, id: index });\n    }\n  });\n\n  const customMenuItemLabelIconsPortals = useCustomElementPortal(customMenuItemLabelIcons);\n  const customLinkLabelIconsPortals = useCustomElementPortal(customLinkLabelIcons);\n\n  validChildren.forEach((mi, index) => {\n    if (isReorderItem(mi, reorderItemsLabels)) {\n      customMenuItems.push({\n        label: mi.label,\n      });\n    }\n    if (isCustomMenuItem(mi)) {\n      const {\n        portal: iconPortal,\n        mount: mountIcon,\n        unmount: unmountIcon,\n      } = customMenuItemLabelIconsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      const menuItem: CustomMenuItem = {\n        label: mi.label,\n        mountIcon,\n        unmountIcon,\n      };\n\n      if ('onClick' in mi) {\n        menuItem.onClick = mi.onClick;\n      } else if ('open' in mi) {\n        menuItem.open = mi.open;\n      }\n      customMenuItems.push(menuItem);\n      customMenuItemsPortals.push(iconPortal);\n    }\n    if (isExternalLink(mi)) {\n      const {\n        portal: iconPortal,\n        mount: mountIcon,\n        unmount: unmountIcon,\n      } = customLinkLabelIconsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      customMenuItems.push({\n        label: mi.label,\n        href: mi.href,\n        mountIcon,\n        unmountIcon,\n      });\n      customMenuItemsPortals.push(iconPortal);\n    }\n  });\n\n  return { customMenuItems, customMenuItemsPortals };\n};\n\nconst isReorderItem = (childProps: any, validItems: string[]): boolean => {\n  const { children, label, onClick, labelIcon } = childProps;\n  return !children && !onClick && !labelIcon && validItems.some(v => v === label);\n};\n\nconst isCustomMenuItem = (childProps: any): childProps is UserButtonActionProps => {\n  const { label, labelIcon, onClick, open } = childProps;\n  return !!labelIcon && !!label && (typeof onClick === 'function' || typeof open === 'string');\n};\n\nconst isExternalLink = (childProps: any): childProps is UserButtonLinkProps => {\n  const { label, href, labelIcon } = childProps;\n  return !!href && !!labelIcon && !!label;\n};\n", "import { useEffect, useRef, useState } from 'react';\n\n/**\n * Used to detect when a Clerk component has been added to the DOM.\n */\nfunction waitForElementChildren(options: { selector?: string; root?: HTMLElement | null; timeout?: number }) {\n  const { root = document?.body, selector, timeout = 0 } = options;\n\n  return new Promise<void>((resolve, reject) => {\n    if (!root) {\n      reject(new Error('No root element provided'));\n      return;\n    }\n\n    let elementToWatch: HTMLElement | null = root;\n    if (selector) {\n      elementToWatch = root?.querySelector(selector);\n    }\n\n    // Check if the element already has child nodes\n    const isElementAlreadyPresent = elementToWatch?.childElementCount && elementToWatch.childElementCount > 0;\n    if (isElementAlreadyPresent) {\n      resolve();\n      return;\n    }\n\n    // Set up a MutationObserver to detect when the element has children\n    const observer = new MutationObserver(mutationsList => {\n      for (const mutation of mutationsList) {\n        if (mutation.type === 'childList') {\n          if (!elementToWatch && selector) {\n            elementToWatch = root?.querySelector(selector);\n          }\n\n          if (elementToWatch?.childElementCount && elementToWatch.childElementCount > 0) {\n            observer.disconnect();\n            resolve();\n            return;\n          }\n        }\n      }\n    });\n\n    observer.observe(root, { childList: true, subtree: true });\n\n    // Set up an optional timeout to reject the promise if the element never gets child nodes\n    if (timeout > 0) {\n      setTimeout(() => {\n        observer.disconnect();\n        reject(new Error(`Timeout waiting for element children`));\n      }, timeout);\n    }\n  });\n}\n\n/**\n * Detect when a Clerk component has mounted by watching DOM updates to an element with a `data-clerk-component=\"${component}\"` property.\n */\nexport function useWaitForComponentMount(component?: string) {\n  const watcherRef = useRef<Promise<void>>();\n  const [status, setStatus] = useState<'rendering' | 'rendered' | 'error'>('rendering');\n\n  useEffect(() => {\n    if (!component) {\n      throw new Error('Clerk: no component name provided, unable to detect mount.');\n    }\n\n    if (typeof window !== 'undefined' && !watcherRef.current) {\n      watcherRef.current = waitForElementChildren({ selector: `[data-clerk-component=\"${component}\"]` })\n        .then(() => {\n          setStatus('rendered');\n        })\n        .catch(() => {\n          setStatus('error');\n        });\n    }\n  }, [component]);\n\n  return status;\n}\n", "import { without } from '@clerk/shared/object';\nimport { isDeeplyEqual } from '@clerk/shared/react';\nimport type { PropsWithChildren } from 'react';\nimport React from 'react';\n\nimport type { MountProps, OpenProps } from '../types';\n\nconst isMountProps = (props: any): props is MountProps => {\n  return 'mount' in props;\n};\n\nconst isOpenProps = (props: any): props is OpenProps => {\n  return 'open' in props;\n};\n\nconst stripMenuItemIconHandlers = (\n  menuItems?: Array<{\n    mountIcon?: (el: HTMLDivElement) => void;\n    unmountIcon?: (el: HTMLDivElement) => void;\n    [key: string]: any;\n  }>,\n) => {\n  return menuItems?.map(({ mountIcon, unmountIcon, ...rest }) => rest);\n};\n\n// README: <ClerkHostRenderer/> should be a class pure component in order for mount and unmount\n// lifecycle props to be invoked correctly. Replacing the class component with a\n// functional component wrapped with a React.memo is not identical to the original\n// class implementation due to React intricacies such as the useEffect’s cleanup\n// seems to run AFTER unmount, while componentWillUnmount runs BEFORE.\n\n// More information can be found at https://clerk.slack.com/archives/C015S0BGH8R/p1624891993016300\n\n// The function Portal implementation is commented out for future reference.\n\n// const Portal = React.memo(({ props, mount, unmount }: MountProps) => {\n//   const portalRef = React.createRef<HTMLDivElement>();\n\n//   useEffect(() => {\n//     if (portalRef.current) {\n//       mount(portalRef.current, props);\n//     }\n//     return () => {\n//       if (portalRef.current) {\n//         unmount(portalRef.current);\n//       }\n//     };\n//   }, []);\n\n//   return <div ref={portalRef} />;\n// });\n\n// Portal.displayName = 'ClerkPortal';\n\n/**\n * Used to orchestrate mounting of Clerk components in a host React application.\n * Components are rendered into a specific DOM node using mount/unmount methods provided by the Clerk class.\n */\nexport class ClerkHostRenderer extends React.PureComponent<\n  PropsWithChildren<\n    (MountProps | OpenProps) & {\n      component?: string;\n      hideRootHtmlElement?: boolean;\n      rootProps?: JSX.IntrinsicElements['div'];\n    }\n  >\n> {\n  private rootRef = React.createRef<HTMLDivElement>();\n\n  componentDidUpdate(_prevProps: Readonly<MountProps | OpenProps>) {\n    if (!isMountProps(_prevProps) || !isMountProps(this.props)) {\n      return;\n    }\n\n    // Remove children and customPages from props before comparing\n    // children might hold circular references which deepEqual can't handle\n    // and the implementation of customPages relies on props getting new references\n    const prevProps = without(_prevProps.props, 'customPages', 'customMenuItems', 'children');\n    const newProps = without(this.props.props, 'customPages', 'customMenuItems', 'children');\n\n    // instead, we simply use the length of customPages to determine if it changed or not\n    const customPagesChanged = prevProps.customPages?.length !== newProps.customPages?.length;\n    const customMenuItemsChanged = prevProps.customMenuItems?.length !== newProps.customMenuItems?.length;\n\n    // Strip out mountIcon and unmountIcon handlers since they're always generated as new function references,\n    // which would cause unnecessary re-renders in deep equality checks\n    const prevMenuItemsWithoutHandlers = stripMenuItemIconHandlers(_prevProps.props.customMenuItems);\n    const newMenuItemsWithoutHandlers = stripMenuItemIconHandlers(this.props.props.customMenuItems);\n\n    if (\n      !isDeeplyEqual(prevProps, newProps) ||\n      !isDeeplyEqual(prevMenuItemsWithoutHandlers, newMenuItemsWithoutHandlers) ||\n      customPagesChanged ||\n      customMenuItemsChanged\n    ) {\n      if (this.rootRef.current) {\n        this.props.updateProps({ node: this.rootRef.current, props: this.props.props });\n      }\n    }\n  }\n\n  componentDidMount() {\n    if (this.rootRef.current) {\n      if (isMountProps(this.props)) {\n        this.props.mount(this.rootRef.current, this.props.props);\n      }\n\n      if (isOpenProps(this.props)) {\n        this.props.open(this.props.props);\n      }\n    }\n  }\n\n  componentWillUnmount() {\n    if (this.rootRef.current) {\n      if (isMountProps(this.props)) {\n        this.props.unmount(this.rootRef.current);\n      }\n      if (isOpenProps(this.props)) {\n        this.props.close();\n      }\n    }\n  }\n\n  render() {\n    const { hideRootHtmlElement = false } = this.props;\n    const rootAttributes = {\n      ref: this.rootRef,\n      ...this.props.rootProps,\n      ...(this.props.component && { 'data-clerk-component': this.props.component }),\n    };\n\n    return (\n      <>\n        {!hideRootHtmlElement && <div {...rootAttributes} />}\n        {this.props.children}\n      </>\n    );\n  }\n}\n", "import type { SignInButtonProps, SignInProps } from '@clerk/types';\nimport React from 'react';\n\nimport type { WithClerkProp } from '../types';\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from '../utils';\nimport { withClerk } from './withClerk';\n\nexport const SignInButton = withClerk(\n  ({ clerk, children, ...props }: WithClerkProp<React.PropsWithChildren<SignInButtonProps>>) => {\n    const {\n      signUpFallbackRedirectUrl,\n      forceRedirectUrl,\n      fallbackRedirectUrl,\n      signUpForceRedirectUrl,\n      mode,\n      initialValues,\n      withSignUp,\n      oauthFlow,\n      ...rest\n    } = props;\n    children = normalizeWithDefaultValue(children, 'Sign in');\n    const child = assertSingleChild(children)('SignInButton');\n\n    const clickHandler = () => {\n      const opts: SignInProps = {\n        forceRedirectUrl,\n        fallbackRedirectUrl,\n        signUpFallbackRedirectUrl,\n        signUpForceRedirectUrl,\n        initialValues,\n        withSignUp,\n        oauthFlow,\n      };\n\n      if (mode === 'modal') {\n        return clerk.openSignIn({ ...opts, appearance: props.appearance });\n      }\n      return clerk.redirectToSignIn({\n        ...opts,\n        signInFallbackRedirectUrl: fallbackRedirectUrl,\n        signInForceRedirectUrl: forceRedirectUrl,\n      });\n    };\n\n    const wrappedChildClickHandler: React.MouseEventHandler = async e => {\n      if (child && typeof child === 'object' && 'props' in child) {\n        await safeExecute(child.props.onClick)(e);\n      }\n      return clickHandler();\n    };\n\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return React.cloneElement(child as React.ReactElement<unknown>, childProps);\n  },\n  { component: 'SignInButton', renderWhileLoading: true },\n);\n", "import type { SignUpButtonProps, SignUpProps } from '@clerk/types';\nimport React from 'react';\n\nimport type { WithClerkProp } from '../types';\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from '../utils';\nimport { withClerk } from './withClerk';\n\nexport const SignUpButton = withClerk(\n  ({ clerk, children, ...props }: WithClerkProp<React.PropsWithChildren<SignUpButtonProps>>) => {\n    const {\n      fallbackRedirectUrl,\n      forceRedirectUrl,\n      signInFallbackRedirectUrl,\n      signInForceRedirectUrl,\n      mode,\n      unsafeMetadata,\n      initialValues,\n      oauthFlow,\n      ...rest\n    } = props;\n\n    children = normalizeWithDefaultValue(children, 'Sign up');\n    const child = assertSingleChild(children)('SignUpButton');\n\n    const clickHandler = () => {\n      const opts: SignUpProps = {\n        fallbackRedirectUrl,\n        forceRedirectUrl,\n        signInFallbackRedirectUrl,\n        signInForceRedirectUrl,\n        unsafeMetadata,\n        initialValues,\n        oauthFlow,\n      };\n\n      if (mode === 'modal') {\n        return clerk.openSignUp({ ...opts, appearance: props.appearance });\n      }\n\n      return clerk.redirectToSignUp({\n        ...opts,\n        signUpFallbackRedirectUrl: fallbackRedirectUrl,\n        signUpForceRedirectUrl: forceRedirectUrl,\n      });\n    };\n\n    const wrappedChildClickHandler: React.MouseEventHandler = async e => {\n      if (child && typeof child === 'object' && 'props' in child) {\n        await safeExecute(child.props.onClick)(e);\n      }\n      return clickHandler();\n    };\n\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return React.cloneElement(child as React.ReactElement<unknown>, childProps);\n  },\n  { component: 'SignUpButton', renderWhileLoading: true },\n);\n", "import type { SignOutOptions } from '@clerk/types';\nimport React from 'react';\n\nimport type { WithClerkProp } from '../types';\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from '../utils';\nimport { withClerk } from './withClerk';\n\nexport type SignOutButtonProps = {\n  redirectUrl?: string;\n  signOutOptions?: SignOutOptions;\n  children?: React.ReactNode;\n};\n\nexport const SignOutButton = withClerk(\n  ({ clerk, children, ...props }: React.PropsWithChildren<WithClerkProp<SignOutButtonProps>>) => {\n    const { redirectUrl = '/', signOutOptions, ...rest } = props;\n\n    children = normalizeWithDefaultValue(children, 'Sign out');\n    const child = assertSingleChild(children)('SignOutButton');\n\n    const clickHandler = () => clerk.signOut({ redirectUrl, ...signOutOptions });\n    const wrappedChildClickHandler: React.MouseEventHandler = async e => {\n      await safeExecute((child as any).props.onClick)(e);\n      return clickHandler();\n    };\n\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return React.cloneElement(child as React.ReactElement<unknown>, childProps);\n  },\n  { component: 'SignOutButton', renderWhileLoading: true },\n);\n", "import React from 'react';\n\nimport type { SignInWithMetamaskButtonProps, WithClerkProp } from '../types';\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from '../utils';\nimport { withClerk } from './withClerk';\n\nexport const SignInWithMetamaskButton = withClerk(\n  ({ clerk, children, ...props }: WithClerkProp<SignInWithMetamaskButtonProps>) => {\n    const { redirectUrl, ...rest } = props;\n\n    children = normalizeWithDefaultValue(children, 'Sign in with Metamask');\n    const child = assertSingleChild(children)('SignInWithMetamaskButton');\n\n    // TODO: Properly fix this code\n    // eslint-disable-next-line @typescript-eslint/require-await\n    const clickHandler = async () => {\n      async function authenticate() {\n        await clerk.authenticateWithMetamask({ redirectUrl: redirectUrl || undefined });\n      }\n      void authenticate();\n    };\n\n    const wrappedChildClickHandler: React.MouseEventHandler = async e => {\n      await safeExecute((child as any).props.onClick)(e);\n      return clickHandler();\n    };\n\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return React.cloneElement(child as React.ReactElement<unknown>, childProps);\n  },\n  { component: 'SignInWithMetamask', renderWhileLoading: true },\n);\n", "import { isPublishable<PERSON><PERSON> } from '@clerk/shared/keys';\nimport React from 'react';\n\nimport { errorThrower } from '../errors/errorThrower';\nimport { multipleClerkProvidersError } from '../errors/messages';\nimport type { ClerkProviderProps } from '../types';\nimport { withMaxAllowedInstancesGuard } from '../utils';\nimport { ClerkContextProvider } from './ClerkContextProvider';\n\nfunction ClerkProviderBase(props: ClerkProviderProps) {\n  const { initialState, children, __internal_bypassMissingPublishableKey, ...restIsomorphicClerkOptions } = props;\n  const { publishableKey = '', Clerk: userInitialisedClerk } = restIsomorphicClerkOptions;\n\n  if (!userInitialisedClerk && !__internal_bypassMissingPublishableKey) {\n    if (!publishableKey) {\n      errorThrower.throwMissingPublishableKeyError();\n    } else if (publishableKey && !isPublishableKey(publishableKey)) {\n      errorThrower.throwInvalidPublishableKeyError({ key: publishableKey });\n    }\n  }\n\n  return (\n    <ClerkContextProvider\n      initialState={initialState}\n      isomorphicClerkOptions={restIsomorphicClerkOptions}\n    >\n      {children}\n    </ClerkContextProvider>\n  );\n}\n\nconst ClerkProvider = withMaxAllowedInstancesGuard(ClerkProviderBase, 'ClerkProvider', multipleClerkProvidersError);\n\nClerkProvider.displayName = 'ClerkProvider';\n\nexport { ClerkProvider };\n", "import { deriveState } from '@clerk/shared/deriveState';\nimport { ClientContext, OrganizationProvider, SessionContext, UserContext } from '@clerk/shared/react';\nimport type { ClientResource, InitialState, Resources } from '@clerk/types';\nimport React from 'react';\n\nimport { IsomorphicClerk } from '../isomorphicClerk';\nimport type { IsomorphicClerkOptions } from '../types';\nimport { AuthContext } from './AuthContext';\nimport { IsomorphicClerkContext } from './IsomorphicClerkContext';\n\ntype ClerkContextProvider = {\n  isomorphicClerkOptions: IsomorphicClerkOptions;\n  initialState: InitialState | undefined;\n  children: React.ReactNode;\n};\n\nexport type ClerkContextProviderState = Resources;\n\nexport function ClerkContextProvider(props: ClerkContextProvider) {\n  const { isomorphicClerkOptions, initialState, children } = props;\n  const { isomorphicClerk: clerk, clerkStatus } = useLoadedIsomorphicClerk(isomorphicClerkOptions);\n\n  const [state, setState] = React.useState<ClerkContextProviderState>({\n    client: clerk.client as ClientResource,\n    session: clerk.session,\n    user: clerk.user,\n    organization: clerk.organization,\n  });\n\n  React.useEffect(() => {\n    return clerk.addListener(e => setState({ ...e }));\n  }, []);\n\n  const derivedState = deriveState(clerk.loaded, state, initialState);\n  const clerkCtx = React.useMemo(\n    () => ({ value: clerk }),\n    [\n      // Only update the clerk reference on status change\n      clerkStatus,\n    ],\n  );\n  const clientCtx = React.useMemo(() => ({ value: state.client }), [state.client]);\n\n  const {\n    sessionId,\n    sessionStatus,\n    sessionClaims,\n    session,\n    userId,\n    user,\n    orgId,\n    actor,\n    organization,\n    orgRole,\n    orgSlug,\n    orgPermissions,\n    factorVerificationAge,\n  } = derivedState;\n\n  const authCtx = React.useMemo(() => {\n    const value = {\n      sessionId,\n      sessionStatus,\n      sessionClaims,\n      userId,\n      actor,\n      orgId,\n      orgRole,\n      orgSlug,\n      orgPermissions,\n      factorVerificationAge,\n    };\n    return { value };\n  }, [sessionId, sessionStatus, userId, actor, orgId, orgRole, orgSlug, factorVerificationAge, sessionClaims?.__raw]);\n\n  const sessionCtx = React.useMemo(() => ({ value: session }), [sessionId, session]);\n  const userCtx = React.useMemo(() => ({ value: user }), [userId, user]);\n  const organizationCtx = React.useMemo(() => {\n    const value = {\n      organization: organization,\n    };\n    return { value };\n  }, [orgId, organization]);\n\n  return (\n    // @ts-expect-error value passed is of type IsomorphicClerk where the context expects LoadedClerk\n    <IsomorphicClerkContext.Provider value={clerkCtx}>\n      <ClientContext.Provider value={clientCtx}>\n        <SessionContext.Provider value={sessionCtx}>\n          <OrganizationProvider {...organizationCtx.value}>\n            <AuthContext.Provider value={authCtx}>\n              <UserContext.Provider value={userCtx}>{children}</UserContext.Provider>\n            </AuthContext.Provider>\n          </OrganizationProvider>\n        </SessionContext.Provider>\n      </ClientContext.Provider>\n    </IsomorphicClerkContext.Provider>\n  );\n}\n\nconst useLoadedIsomorphicClerk = (options: IsomorphicClerkOptions) => {\n  const isomorphicClerkRef = React.useRef(IsomorphicClerk.getOrCreateInstance(options));\n  const [clerkStatus, setClerkStatus] = React.useState(isomorphicClerkRef.current.status);\n\n  React.useEffect(() => {\n    void isomorphicClerkRef.current.__unstable__updateProps({ appearance: options.appearance });\n  }, [options.appearance]);\n\n  React.useEffect(() => {\n    void isomorphicClerkRef.current.__unstable__updateProps({ options });\n  }, [options.localization]);\n\n  React.useEffect(() => {\n    isomorphicClerkRef.current.on('status', setClerkStatus);\n    return () => {\n      if (isomorphicClerkRef.current) {\n        isomorphicClerkRef.current.off('status', setClerkStatus);\n      }\n      IsomorphicClerk.clearInstance();\n    };\n  }, []);\n\n  return { isomorphicClerk: isomorphicClerkRef.current, clerkStatus };\n};\n", "import { inBrowser } from '@clerk/shared/browser';\nimport { clerkEvents, createClerkEventBus } from '@clerk/shared/clerkEventBus';\nimport { loadClerkJsScript } from '@clerk/shared/loadClerkJsScript';\nimport { handleValueOrFn } from '@clerk/shared/utils';\nimport type {\n  __internal_CheckoutProps,\n  __internal_OAuthConsentProps,\n  __internal_PlanDetailsProps,\n  __internal_UserVerificationModalProps,\n  __internal_UserVerificationProps,\n  APIKeysNamespace,\n  APIKeysProps,\n  AuthenticateWithCoinbaseWalletParams,\n  AuthenticateWithGoogleOneTapParams,\n  AuthenticateWithMetamaskParams,\n  AuthenticateWithOKXWalletParams,\n  Clerk,\n  ClerkAuthenticateWithWeb3Params,\n  ClerkOptions,\n  ClerkStatus,\n  ClientResource,\n  CommerceBillingNamespace,\n  CreateOrganizationParams,\n  CreateOrganizationProps,\n  DomainOrProxyUrl,\n  GoogleOneTapProps,\n  HandleEmailLinkVerificationParams,\n  HandleOAuthCallbackParams,\n  JoinWaitlistParams,\n  ListenerCallback,\n  LoadedClerk,\n  NextTaskParams,\n  OrganizationListProps,\n  OrganizationProfileProps,\n  OrganizationResource,\n  OrganizationSwitcherProps,\n  PricingTableProps,\n  RedirectOptions,\n  SetActiveParams,\n  SignInProps,\n  SignInRedirectOptions,\n  SignInResource,\n  SignUpProps,\n  SignUpRedirectOptions,\n  SignUpResource,\n  UnsubscribeCallback,\n  UserButtonProps,\n  UserProfileProps,\n  WaitlistProps,\n  WaitlistResource,\n  Without,\n} from '@clerk/types';\n\nimport { errorThrower } from './errors/errorThrower';\nimport { unsupportedNonBrowserDomainOrProxyUrlFunction } from './errors/messages';\nimport type {\n  BrowserClerk,\n  BrowserClerkConstructor,\n  ClerkProp,\n  HeadlessBrowserClerk,\n  HeadlessBrowserClerkConstructor,\n  IsomorphicClerkOptions,\n} from './types';\nimport { isConstructor } from './utils';\n\nif (typeof globalThis.__BUILD_DISABLE_RHC__ === 'undefined') {\n  globalThis.__BUILD_DISABLE_RHC__ = false;\n}\n\nconst SDK_METADATA = {\n  name: PACKAGE_NAME,\n  version: PACKAGE_VERSION,\n  environment: process.env.NODE_ENV,\n};\n\nexport interface Global {\n  Clerk?: HeadlessBrowserClerk | BrowserClerk;\n}\n\ndeclare const global: Global;\n\ntype GenericFunction<TArgs = never> = (...args: TArgs[]) => unknown;\n\ntype MethodName<T> = {\n  [P in keyof T]: T[P] extends GenericFunction ? P : never;\n}[keyof T];\n\ntype MethodCallback = () => Promise<unknown> | unknown;\n\ntype WithVoidReturn<F extends (...args: any) => any> = (\n  ...args: Parameters<F>\n) => ReturnType<F> extends Promise<infer T> ? Promise<T | void> : ReturnType<F> | void;\ntype WithVoidReturnFunctions<T> = {\n  [K in keyof T]: T[K] extends (...args: any) => any ? WithVoidReturn<T[K]> : T[K];\n};\n\ntype IsomorphicLoadedClerk = Without<\n  WithVoidReturnFunctions<LoadedClerk>,\n  | 'client'\n  | '__internal_addNavigationListener'\n  | '__internal_getCachedResources'\n  | '__internal_reloadInitialResources'\n  | 'billing'\n  | 'apiKeys'\n  | '__internal_setComponentNavigationContext'\n  | '__internal_setActiveInProgress'\n> & {\n  client: ClientResource | undefined;\n  billing: CommerceBillingNamespace | undefined;\n  apiKeys: APIKeysNamespace | undefined;\n};\n\nexport class IsomorphicClerk implements IsomorphicLoadedClerk {\n  private readonly mode: 'browser' | 'server';\n  private readonly options: IsomorphicClerkOptions;\n  private readonly Clerk: ClerkProp;\n  private clerkjs: BrowserClerk | HeadlessBrowserClerk | null = null;\n  private preopenOneTap?: null | GoogleOneTapProps = null;\n  private preopenUserVerification?: null | __internal_UserVerificationProps = null;\n  private preopenSignIn?: null | SignInProps = null;\n  private preopenCheckout?: null | __internal_CheckoutProps = null;\n  private preopenPlanDetails?: null | __internal_PlanDetailsProps = null;\n  private preopenSignUp?: null | SignUpProps = null;\n  private preopenUserProfile?: null | UserProfileProps = null;\n  private preopenOrganizationProfile?: null | OrganizationProfileProps = null;\n  private preopenCreateOrganization?: null | CreateOrganizationProps = null;\n  private preOpenWaitlist?: null | WaitlistProps = null;\n  private premountSignInNodes = new Map<HTMLDivElement, SignInProps | undefined>();\n  private premountSignUpNodes = new Map<HTMLDivElement, SignUpProps | undefined>();\n  private premountUserProfileNodes = new Map<HTMLDivElement, UserProfileProps | undefined>();\n  private premountUserButtonNodes = new Map<HTMLDivElement, UserButtonProps | undefined>();\n  private premountOrganizationProfileNodes = new Map<HTMLDivElement, OrganizationProfileProps | undefined>();\n  private premountCreateOrganizationNodes = new Map<HTMLDivElement, CreateOrganizationProps | undefined>();\n  private premountOrganizationSwitcherNodes = new Map<HTMLDivElement, OrganizationSwitcherProps | undefined>();\n  private premountOrganizationListNodes = new Map<HTMLDivElement, OrganizationListProps | undefined>();\n  private premountMethodCalls = new Map<MethodName<BrowserClerk>, MethodCallback>();\n  private premountWaitlistNodes = new Map<HTMLDivElement, WaitlistProps | undefined>();\n  private premountPricingTableNodes = new Map<HTMLDivElement, PricingTableProps | undefined>();\n  private premountApiKeysNodes = new Map<HTMLDivElement, APIKeysProps | undefined>();\n  private premountOAuthConsentNodes = new Map<HTMLDivElement, __internal_OAuthConsentProps | undefined>();\n  // A separate Map of `addListener` method calls to handle multiple listeners.\n  private premountAddListenerCalls = new Map<\n    ListenerCallback,\n    {\n      unsubscribe: UnsubscribeCallback;\n      nativeUnsubscribe?: UnsubscribeCallback;\n    }\n  >();\n  private loadedListeners: Array<() => void> = [];\n\n  #status: ClerkStatus = 'loading';\n  #domain: DomainOrProxyUrl['domain'];\n  #proxyUrl: DomainOrProxyUrl['proxyUrl'];\n  #publishableKey: string;\n  #eventBus = createClerkEventBus();\n\n  get publishableKey(): string {\n    return this.#publishableKey;\n  }\n\n  get loaded(): boolean {\n    return this.clerkjs?.loaded || false;\n  }\n\n  get status(): ClerkStatus {\n    /**\n     * If clerk-js is not available the returned value can either be \"loading\" or \"error\".\n     */\n    if (!this.clerkjs) {\n      return this.#status;\n    }\n    return (\n      this.clerkjs?.status ||\n      /**\n       * Support older clerk-js versions.\n       * If clerk-js is available but `.status` is missing it we need to fallback to `.loaded`.\n       * Since \"degraded\" an \"error\" did not exist before,\n       * map \"loaded\" to \"ready\" and \"not loaded\" to \"loading\".\n       */\n      (this.clerkjs.loaded ? 'ready' : 'loading')\n    );\n  }\n\n  static #instance: IsomorphicClerk | null | undefined;\n\n  static getOrCreateInstance(options: IsomorphicClerkOptions) {\n    // During SSR: a new instance should be created for every request\n    // During CSR: use the cached instance for the whole lifetime of the app\n    // Also will recreate the instance if the provided Clerk instance changes\n    // This method should be idempotent in both scenarios\n    if (\n      !inBrowser() ||\n      !this.#instance ||\n      (options.Clerk && this.#instance.Clerk !== options.Clerk) ||\n      // Allow hot swapping PKs on the client\n      this.#instance.publishableKey !== options.publishableKey\n    ) {\n      this.#instance = new IsomorphicClerk(options);\n    }\n    return this.#instance;\n  }\n\n  static clearInstance() {\n    this.#instance = null;\n  }\n\n  get domain(): string {\n    // This getter can run in environments where window is not available.\n    // In those cases we should expect and use domain as a string\n    if (typeof window !== 'undefined' && window.location) {\n      return handleValueOrFn(this.#domain, new URL(window.location.href), '');\n    }\n    if (typeof this.#domain === 'function') {\n      return errorThrower.throw(unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return this.#domain || '';\n  }\n\n  get proxyUrl(): string {\n    // This getter can run in environments where window is not available.\n    // In those cases we should expect and use proxy as a string\n    if (typeof window !== 'undefined' && window.location) {\n      return handleValueOrFn(this.#proxyUrl, new URL(window.location.href), '');\n    }\n    if (typeof this.#proxyUrl === 'function') {\n      return errorThrower.throw(unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return this.#proxyUrl || '';\n  }\n\n  /**\n   * Accesses private options from the `Clerk` instance and defaults to\n   * `IsomorphicClerk` options when in SSR context.\n   *  @internal\n   */\n  public __internal_getOption<K extends keyof ClerkOptions>(key: K): ClerkOptions[K] | undefined {\n    return this.clerkjs?.__internal_getOption ? this.clerkjs?.__internal_getOption(key) : this.options[key];\n  }\n\n  constructor(options: IsomorphicClerkOptions) {\n    const { Clerk = null, publishableKey } = options || {};\n    this.#publishableKey = publishableKey;\n    this.#proxyUrl = options?.proxyUrl;\n    this.#domain = options?.domain;\n    this.options = options;\n    this.Clerk = Clerk;\n    this.mode = inBrowser() ? 'browser' : 'server';\n\n    if (!this.options.sdkMetadata) {\n      this.options.sdkMetadata = SDK_METADATA;\n    }\n    this.#eventBus.emit(clerkEvents.Status, 'loading');\n    this.#eventBus.prioritizedOn(clerkEvents.Status, status => (this.#status = status));\n\n    if (this.#publishableKey) {\n      void this.loadClerkJS();\n    }\n  }\n\n  get sdkMetadata() {\n    return this.clerkjs?.sdkMetadata || this.options.sdkMetadata || undefined;\n  }\n\n  get instanceType() {\n    return this.clerkjs?.instanceType;\n  }\n\n  get frontendApi() {\n    return this.clerkjs?.frontendApi || '';\n  }\n\n  get isStandardBrowser() {\n    return this.clerkjs?.isStandardBrowser || this.options.standardBrowser || false;\n  }\n\n  get isSatellite() {\n    // This getter can run in environments where window is not available.\n    // In those cases we should expect and use domain as a string\n    if (typeof window !== 'undefined' && window.location) {\n      return handleValueOrFn(this.options.isSatellite, new URL(window.location.href), false);\n    }\n    if (typeof this.options.isSatellite === 'function') {\n      return errorThrower.throw(unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return false;\n  }\n\n  buildSignInUrl = (opts?: RedirectOptions): string | void => {\n    const callback = () => this.clerkjs?.buildSignInUrl(opts) || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildSignInUrl', callback);\n    }\n  };\n\n  buildSignUpUrl = (opts?: RedirectOptions): string | void => {\n    const callback = () => this.clerkjs?.buildSignUpUrl(opts) || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildSignUpUrl', callback);\n    }\n  };\n\n  buildAfterSignInUrl = (...args: Parameters<Clerk['buildAfterSignInUrl']>): string | void => {\n    const callback = () => this.clerkjs?.buildAfterSignInUrl(...args) || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildAfterSignInUrl', callback);\n    }\n  };\n\n  buildAfterSignUpUrl = (...args: Parameters<Clerk['buildAfterSignUpUrl']>): string | void => {\n    const callback = () => this.clerkjs?.buildAfterSignUpUrl(...args) || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildAfterSignUpUrl', callback);\n    }\n  };\n\n  buildAfterSignOutUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildAfterSignOutUrl() || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildAfterSignOutUrl', callback);\n    }\n  };\n\n  buildNewSubscriptionRedirectUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildNewSubscriptionRedirectUrl() || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildNewSubscriptionRedirectUrl', callback);\n    }\n  };\n\n  buildAfterMultiSessionSingleSignOutUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildAfterMultiSessionSingleSignOutUrl() || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildAfterMultiSessionSingleSignOutUrl', callback);\n    }\n  };\n\n  buildUserProfileUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildUserProfileUrl() || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildUserProfileUrl', callback);\n    }\n  };\n\n  buildCreateOrganizationUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildCreateOrganizationUrl() || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildCreateOrganizationUrl', callback);\n    }\n  };\n\n  buildOrganizationProfileUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildOrganizationProfileUrl() || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildOrganizationProfileUrl', callback);\n    }\n  };\n\n  buildWaitlistUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildWaitlistUrl() || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildWaitlistUrl', callback);\n    }\n  };\n\n  buildUrlWithAuth = (to: string): string | void => {\n    const callback = () => this.clerkjs?.buildUrlWithAuth(to) || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildUrlWithAuth', callback);\n    }\n  };\n\n  handleUnauthenticated = async () => {\n    const callback = () => this.clerkjs?.handleUnauthenticated();\n    if (this.clerkjs && this.loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('handleUnauthenticated', callback);\n    }\n  };\n\n  #waitForClerkJS(): Promise<HeadlessBrowserClerk | BrowserClerk> {\n    return new Promise<HeadlessBrowserClerk | BrowserClerk>(resolve => {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      this.addOnLoaded(() => resolve(this.clerkjs!));\n    });\n  }\n\n  async loadClerkJS(): Promise<HeadlessBrowserClerk | BrowserClerk | undefined> {\n    if (this.mode !== 'browser' || this.loaded) {\n      return;\n    }\n\n    // Store frontendAPI value on window as a fallback. This value can be used as a\n    // fallback during ClerkJS hot loading in case ClerkJS fails to find the\n    // \"data-clerk-frontend-api\" attribute on its script tag.\n\n    // This can happen when the DOM is altered completely during client rehydration.\n    // For example, in Remix with React 18 the document changes completely via `hydrateRoot(document)`.\n\n    // For more information refer to:\n    // - https://github.com/remix-run/remix/issues/2947\n    // - https://github.com/facebook/react/issues/24430\n    if (typeof window !== 'undefined') {\n      window.__clerk_publishable_key = this.#publishableKey;\n      window.__clerk_proxy_url = this.proxyUrl;\n      window.__clerk_domain = this.domain;\n    }\n\n    try {\n      if (this.Clerk) {\n        // Set a fixed Clerk version\n        let c: ClerkProp;\n\n        if (isConstructor<BrowserClerkConstructor | HeadlessBrowserClerkConstructor>(this.Clerk)) {\n          // Construct a new Clerk object if a constructor is passed\n          c = new this.Clerk(this.#publishableKey, {\n            proxyUrl: this.proxyUrl,\n            domain: this.domain,\n          } as any);\n\n          this.beforeLoad(c);\n          await c.load(this.options);\n        } else {\n          // Otherwise use the instantiated Clerk object\n          c = this.Clerk;\n          if (!c.loaded) {\n            this.beforeLoad(c);\n            await c.load(this.options);\n          }\n        }\n\n        global.Clerk = c;\n      } else if (!__BUILD_DISABLE_RHC__) {\n        // Hot-load latest ClerkJS from Clerk CDN\n        if (!global.Clerk) {\n          await loadClerkJsScript({\n            ...this.options,\n            publishableKey: this.#publishableKey,\n            proxyUrl: this.proxyUrl,\n            domain: this.domain,\n            nonce: this.options.nonce,\n          });\n        }\n\n        if (!global.Clerk) {\n          throw new Error('Failed to download latest ClerkJS. Contact <EMAIL>.');\n        }\n\n        this.beforeLoad(global.Clerk);\n        await global.Clerk.load(this.options);\n      }\n\n      if (global.Clerk?.loaded) {\n        return this.hydrateClerkJS(global.Clerk);\n      }\n      return;\n    } catch (err) {\n      const error = err as Error;\n      this.#eventBus.emit(clerkEvents.Status, 'error');\n      console.error(error.stack || error.message || error);\n      return;\n    }\n  }\n\n  public on: Clerk['on'] = (...args) => {\n    // Support older clerk-js versions.\n    if (this.clerkjs?.on) {\n      return this.clerkjs.on(...args);\n    } else {\n      this.#eventBus.on(...args);\n    }\n  };\n\n  public off: Clerk['off'] = (...args) => {\n    // Support older clerk-js versions.\n    if (this.clerkjs?.off) {\n      return this.clerkjs.off(...args);\n    } else {\n      this.#eventBus.off(...args);\n    }\n  };\n\n  /**\n   * @deprecated Please use `addStatusListener`. This api will be removed in the next major.\n   */\n  public addOnLoaded = (cb: () => void) => {\n    this.loadedListeners.push(cb);\n    /**\n     * When IsomorphicClerk is loaded execute the callback directly\n     */\n    if (this.loaded) {\n      this.emitLoaded();\n    }\n  };\n\n  /**\n   * @deprecated Please use `__internal_setStatus`. This api will be removed in the next major.\n   */\n  public emitLoaded = () => {\n    this.loadedListeners.forEach(cb => cb());\n    this.loadedListeners = [];\n  };\n\n  private beforeLoad = (clerkjs: BrowserClerk | HeadlessBrowserClerk | undefined) => {\n    if (!clerkjs) {\n      throw new Error('Failed to hydrate latest Clerk JS');\n    }\n  };\n\n  private hydrateClerkJS = (clerkjs: BrowserClerk | HeadlessBrowserClerk | undefined) => {\n    if (!clerkjs) {\n      throw new Error('Failed to hydrate latest Clerk JS');\n    }\n\n    this.clerkjs = clerkjs;\n\n    this.premountMethodCalls.forEach(cb => cb());\n    this.premountAddListenerCalls.forEach((listenerHandlers, listener) => {\n      listenerHandlers.nativeUnsubscribe = clerkjs.addListener(listener);\n    });\n\n    this.#eventBus.internal.retrieveListeners('status')?.forEach(listener => {\n      // Since clerkjs exists it will call `this.clerkjs.on('status', listener)`\n      this.on('status', listener, { notify: true });\n    });\n\n    if (this.preopenSignIn !== null) {\n      clerkjs.openSignIn(this.preopenSignIn);\n    }\n\n    if (this.preopenCheckout !== null) {\n      clerkjs.__internal_openCheckout(this.preopenCheckout);\n    }\n\n    if (this.preopenPlanDetails !== null) {\n      clerkjs.__internal_openPlanDetails(this.preopenPlanDetails);\n    }\n\n    if (this.preopenSignUp !== null) {\n      clerkjs.openSignUp(this.preopenSignUp);\n    }\n\n    if (this.preopenUserProfile !== null) {\n      clerkjs.openUserProfile(this.preopenUserProfile);\n    }\n\n    if (this.preopenUserVerification !== null) {\n      clerkjs.__internal_openReverification(this.preopenUserVerification);\n    }\n\n    if (this.preopenOneTap !== null) {\n      clerkjs.openGoogleOneTap(this.preopenOneTap);\n    }\n\n    if (this.preopenOrganizationProfile !== null) {\n      clerkjs.openOrganizationProfile(this.preopenOrganizationProfile);\n    }\n\n    if (this.preopenCreateOrganization !== null) {\n      clerkjs.openCreateOrganization(this.preopenCreateOrganization);\n    }\n\n    if (this.preOpenWaitlist !== null) {\n      clerkjs.openWaitlist(this.preOpenWaitlist);\n    }\n\n    this.premountSignInNodes.forEach((props, node) => {\n      clerkjs.mountSignIn(node, props);\n    });\n\n    this.premountSignUpNodes.forEach((props, node) => {\n      clerkjs.mountSignUp(node, props);\n    });\n\n    this.premountUserProfileNodes.forEach((props, node) => {\n      clerkjs.mountUserProfile(node, props);\n    });\n\n    this.premountUserButtonNodes.forEach((props, node) => {\n      clerkjs.mountUserButton(node, props);\n    });\n\n    this.premountOrganizationListNodes.forEach((props, node) => {\n      clerkjs.mountOrganizationList(node, props);\n    });\n\n    this.premountWaitlistNodes.forEach((props, node) => {\n      clerkjs.mountWaitlist(node, props);\n    });\n\n    this.premountPricingTableNodes.forEach((props, node) => {\n      clerkjs.mountPricingTable(node, props);\n    });\n\n    this.premountApiKeysNodes.forEach((props, node) => {\n      clerkjs.mountApiKeys(node, props);\n    });\n\n    this.premountOAuthConsentNodes.forEach((props, node) => {\n      clerkjs.__internal_mountOAuthConsent(node, props);\n    });\n\n    /**\n     * Only update status in case `clerk.status` is missing. In any other case, `clerk-js` should be the orchestrator.\n     */\n    if (typeof this.clerkjs.status === 'undefined') {\n      this.#eventBus.emit(clerkEvents.Status, 'ready');\n    }\n\n    this.emitLoaded();\n    return this.clerkjs;\n  };\n\n  get version() {\n    return this.clerkjs?.version;\n  }\n\n  get client(): ClientResource | undefined {\n    if (this.clerkjs) {\n      return this.clerkjs.client;\n      // TODO: add ssr condition\n    } else {\n      return undefined;\n    }\n  }\n\n  get session() {\n    if (this.clerkjs) {\n      return this.clerkjs.session;\n    } else {\n      return undefined;\n    }\n  }\n\n  get user() {\n    if (this.clerkjs) {\n      return this.clerkjs.user;\n    } else {\n      return undefined;\n    }\n  }\n\n  get organization() {\n    if (this.clerkjs) {\n      return this.clerkjs.organization;\n    } else {\n      return undefined;\n    }\n  }\n\n  get telemetry() {\n    if (this.clerkjs) {\n      return this.clerkjs.telemetry;\n    } else {\n      return undefined;\n    }\n  }\n\n  get __unstable__environment(): any {\n    if (this.clerkjs) {\n      return (this.clerkjs as any).__unstable__environment;\n      // TODO: add ssr condition\n    } else {\n      return undefined;\n    }\n  }\n\n  get isSignedIn(): boolean {\n    if (this.clerkjs) {\n      return this.clerkjs.isSignedIn;\n    } else {\n      return false;\n    }\n  }\n\n  get billing(): CommerceBillingNamespace | undefined {\n    return this.clerkjs?.billing;\n  }\n\n  get apiKeys(): APIKeysNamespace | undefined {\n    return this.clerkjs?.apiKeys;\n  }\n\n  __unstable__setEnvironment(...args: any): void {\n    if (this.clerkjs && '__unstable__setEnvironment' in this.clerkjs) {\n      (this.clerkjs as any).__unstable__setEnvironment(args);\n    } else {\n      return undefined;\n    }\n  }\n\n  __unstable__updateProps = async (props: any): Promise<void> => {\n    const clerkjs = await this.#waitForClerkJS();\n    // Handle case where accounts has clerk-react@4 installed, but clerk-js@3 is manually loaded\n    if (clerkjs && '__unstable__updateProps' in clerkjs) {\n      return (clerkjs as any).__unstable__updateProps(props);\n    }\n  };\n\n  __experimental_navigateToTask = async (params?: NextTaskParams): Promise<void> => {\n    if (this.clerkjs) {\n      return this.clerkjs.__experimental_navigateToTask(params);\n    } else {\n      return Promise.reject();\n    }\n  };\n\n  /**\n   * `setActive` can be used to set the active session and/or organization.\n   */\n  setActive = (params: SetActiveParams): Promise<void> => {\n    if (this.clerkjs) {\n      return this.clerkjs.setActive(params);\n    } else {\n      return Promise.reject();\n    }\n  };\n\n  openSignIn = (props?: SignInProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.openSignIn(props);\n    } else {\n      this.preopenSignIn = props;\n    }\n  };\n\n  closeSignIn = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.closeSignIn();\n    } else {\n      this.preopenSignIn = null;\n    }\n  };\n\n  __internal_openCheckout = (props?: __internal_CheckoutProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_openCheckout(props);\n    } else {\n      this.preopenCheckout = props;\n    }\n  };\n\n  __internal_closeCheckout = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_closeCheckout();\n    } else {\n      this.preopenCheckout = null;\n    }\n  };\n\n  __internal_openPlanDetails = (props?: __internal_PlanDetailsProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_openPlanDetails(props);\n    } else {\n      this.preopenPlanDetails = props;\n    }\n  };\n\n  __internal_closePlanDetails = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_closePlanDetails();\n    } else {\n      this.preopenPlanDetails = null;\n    }\n  };\n\n  __internal_openReverification = (props?: __internal_UserVerificationModalProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_openReverification(props);\n    } else {\n      this.preopenUserVerification = props;\n    }\n  };\n\n  __internal_closeReverification = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_closeReverification();\n    } else {\n      this.preopenUserVerification = null;\n    }\n  };\n\n  openGoogleOneTap = (props?: GoogleOneTapProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.openGoogleOneTap(props);\n    } else {\n      this.preopenOneTap = props;\n    }\n  };\n\n  closeGoogleOneTap = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.closeGoogleOneTap();\n    } else {\n      this.preopenOneTap = null;\n    }\n  };\n\n  openUserProfile = (props?: UserProfileProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.openUserProfile(props);\n    } else {\n      this.preopenUserProfile = props;\n    }\n  };\n\n  closeUserProfile = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.closeUserProfile();\n    } else {\n      this.preopenUserProfile = null;\n    }\n  };\n\n  openOrganizationProfile = (props?: OrganizationProfileProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.openOrganizationProfile(props);\n    } else {\n      this.preopenOrganizationProfile = props;\n    }\n  };\n\n  closeOrganizationProfile = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.closeOrganizationProfile();\n    } else {\n      this.preopenOrganizationProfile = null;\n    }\n  };\n\n  openCreateOrganization = (props?: CreateOrganizationProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.openCreateOrganization(props);\n    } else {\n      this.preopenCreateOrganization = props;\n    }\n  };\n\n  closeCreateOrganization = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.closeCreateOrganization();\n    } else {\n      this.preopenCreateOrganization = null;\n    }\n  };\n\n  openWaitlist = (props?: WaitlistProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.openWaitlist(props);\n    } else {\n      this.preOpenWaitlist = props;\n    }\n  };\n\n  closeWaitlist = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.closeWaitlist();\n    } else {\n      this.preOpenWaitlist = null;\n    }\n  };\n\n  openSignUp = (props?: SignUpProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.openSignUp(props);\n    } else {\n      this.preopenSignUp = props;\n    }\n  };\n\n  closeSignUp = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.closeSignUp();\n    } else {\n      this.preopenSignUp = null;\n    }\n  };\n\n  mountSignIn = (node: HTMLDivElement, props?: SignInProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountSignIn(node, props);\n    } else {\n      this.premountSignInNodes.set(node, props);\n    }\n  };\n\n  unmountSignIn = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountSignIn(node);\n    } else {\n      this.premountSignInNodes.delete(node);\n    }\n  };\n\n  mountSignUp = (node: HTMLDivElement, props?: SignUpProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountSignUp(node, props);\n    } else {\n      this.premountSignUpNodes.set(node, props);\n    }\n  };\n\n  unmountSignUp = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountSignUp(node);\n    } else {\n      this.premountSignUpNodes.delete(node);\n    }\n  };\n\n  mountUserProfile = (node: HTMLDivElement, props?: UserProfileProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountUserProfile(node, props);\n    } else {\n      this.premountUserProfileNodes.set(node, props);\n    }\n  };\n\n  unmountUserProfile = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountUserProfile(node);\n    } else {\n      this.premountUserProfileNodes.delete(node);\n    }\n  };\n\n  mountOrganizationProfile = (node: HTMLDivElement, props?: OrganizationProfileProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountOrganizationProfile(node, props);\n    } else {\n      this.premountOrganizationProfileNodes.set(node, props);\n    }\n  };\n\n  unmountOrganizationProfile = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountOrganizationProfile(node);\n    } else {\n      this.premountOrganizationProfileNodes.delete(node);\n    }\n  };\n\n  mountCreateOrganization = (node: HTMLDivElement, props?: CreateOrganizationProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountCreateOrganization(node, props);\n    } else {\n      this.premountCreateOrganizationNodes.set(node, props);\n    }\n  };\n\n  unmountCreateOrganization = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountCreateOrganization(node);\n    } else {\n      this.premountCreateOrganizationNodes.delete(node);\n    }\n  };\n\n  mountOrganizationSwitcher = (node: HTMLDivElement, props?: OrganizationSwitcherProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountOrganizationSwitcher(node, props);\n    } else {\n      this.premountOrganizationSwitcherNodes.set(node, props);\n    }\n  };\n\n  unmountOrganizationSwitcher = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountOrganizationSwitcher(node);\n    } else {\n      this.premountOrganizationSwitcherNodes.delete(node);\n    }\n  };\n\n  __experimental_prefetchOrganizationSwitcher = () => {\n    const callback = () => this.clerkjs?.__experimental_prefetchOrganizationSwitcher();\n    if (this.clerkjs && this.loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('__experimental_prefetchOrganizationSwitcher', callback);\n    }\n  };\n\n  mountOrganizationList = (node: HTMLDivElement, props?: OrganizationListProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountOrganizationList(node, props);\n    } else {\n      this.premountOrganizationListNodes.set(node, props);\n    }\n  };\n\n  unmountOrganizationList = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountOrganizationList(node);\n    } else {\n      this.premountOrganizationListNodes.delete(node);\n    }\n  };\n\n  mountUserButton = (node: HTMLDivElement, userButtonProps?: UserButtonProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountUserButton(node, userButtonProps);\n    } else {\n      this.premountUserButtonNodes.set(node, userButtonProps);\n    }\n  };\n\n  unmountUserButton = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountUserButton(node);\n    } else {\n      this.premountUserButtonNodes.delete(node);\n    }\n  };\n\n  mountWaitlist = (node: HTMLDivElement, props?: WaitlistProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountWaitlist(node, props);\n    } else {\n      this.premountWaitlistNodes.set(node, props);\n    }\n  };\n\n  unmountWaitlist = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountWaitlist(node);\n    } else {\n      this.premountWaitlistNodes.delete(node);\n    }\n  };\n\n  mountPricingTable = (node: HTMLDivElement, props?: PricingTableProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountPricingTable(node, props);\n    } else {\n      this.premountPricingTableNodes.set(node, props);\n    }\n  };\n\n  unmountPricingTable = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountPricingTable(node);\n    } else {\n      this.premountPricingTableNodes.delete(node);\n    }\n  };\n\n  mountApiKeys = (node: HTMLDivElement, props?: APIKeysProps): void => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountApiKeys(node, props);\n    } else {\n      this.premountApiKeysNodes.set(node, props);\n    }\n  };\n\n  unmountApiKeys = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountApiKeys(node);\n    } else {\n      this.premountApiKeysNodes.delete(node);\n    }\n  };\n\n  __internal_mountOAuthConsent = (node: HTMLDivElement, props?: __internal_OAuthConsentProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_mountOAuthConsent(node, props);\n    } else {\n      this.premountOAuthConsentNodes.set(node, props);\n    }\n  };\n\n  __internal_unmountOAuthConsent = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_unmountOAuthConsent(node);\n    } else {\n      this.premountOAuthConsentNodes.delete(node);\n    }\n  };\n\n  addListener = (listener: ListenerCallback): UnsubscribeCallback => {\n    if (this.clerkjs) {\n      return this.clerkjs.addListener(listener);\n    } else {\n      const unsubscribe = () => {\n        const listenerHandlers = this.premountAddListenerCalls.get(listener);\n        if (listenerHandlers) {\n          listenerHandlers.nativeUnsubscribe?.();\n          this.premountAddListenerCalls.delete(listener);\n        }\n      };\n      this.premountAddListenerCalls.set(listener, { unsubscribe, nativeUnsubscribe: undefined });\n      return unsubscribe;\n    }\n  };\n\n  navigate = (to: string) => {\n    const callback = () => this.clerkjs?.navigate(to);\n    if (this.clerkjs && this.loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('navigate', callback);\n    }\n  };\n\n  redirectWithAuth = async (...args: Parameters<Clerk['redirectWithAuth']>) => {\n    const callback = () => this.clerkjs?.redirectWithAuth(...args);\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectWithAuth', callback);\n      return;\n    }\n  };\n\n  redirectToSignIn = async (opts?: SignInRedirectOptions) => {\n    const callback = () => this.clerkjs?.redirectToSignIn(opts as any);\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectToSignIn', callback);\n      return;\n    }\n  };\n\n  redirectToSignUp = async (opts?: SignUpRedirectOptions) => {\n    const callback = () => this.clerkjs?.redirectToSignUp(opts as any);\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectToSignUp', callback);\n      return;\n    }\n  };\n\n  redirectToUserProfile = async () => {\n    const callback = () => this.clerkjs?.redirectToUserProfile();\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectToUserProfile', callback);\n      return;\n    }\n  };\n\n  redirectToAfterSignUp = (): void => {\n    const callback = () => this.clerkjs?.redirectToAfterSignUp();\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectToAfterSignUp', callback);\n    }\n  };\n\n  redirectToAfterSignIn = () => {\n    const callback = () => this.clerkjs?.redirectToAfterSignIn();\n    if (this.clerkjs && this.loaded) {\n      callback();\n    } else {\n      this.premountMethodCalls.set('redirectToAfterSignIn', callback);\n    }\n  };\n\n  redirectToAfterSignOut = () => {\n    const callback = () => this.clerkjs?.redirectToAfterSignOut();\n    if (this.clerkjs && this.loaded) {\n      callback();\n    } else {\n      this.premountMethodCalls.set('redirectToAfterSignOut', callback);\n    }\n  };\n\n  redirectToOrganizationProfile = async () => {\n    const callback = () => this.clerkjs?.redirectToOrganizationProfile();\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectToOrganizationProfile', callback);\n      return;\n    }\n  };\n\n  redirectToCreateOrganization = async () => {\n    const callback = () => this.clerkjs?.redirectToCreateOrganization();\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectToCreateOrganization', callback);\n      return;\n    }\n  };\n\n  redirectToWaitlist = async () => {\n    const callback = () => this.clerkjs?.redirectToWaitlist();\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectToWaitlist', callback);\n      return;\n    }\n  };\n\n  handleRedirectCallback = async (params: HandleOAuthCallbackParams): Promise<void> => {\n    const callback = () => this.clerkjs?.handleRedirectCallback(params);\n    if (this.clerkjs && this.loaded) {\n      void callback()?.catch(() => {\n        // This error is caused when the host app is using React18\n        // and strictMode is enabled. This useEffects runs twice because\n        // the clerk-react ui components mounts, unmounts and mounts again\n        // so the clerk-js component loses its state because of the custom\n        // unmount callback we're using.\n        // This needs to be solved by tweaking the logic in uiComponents.tsx\n        // or by making handleRedirectCallback idempotent\n      });\n    } else {\n      this.premountMethodCalls.set('handleRedirectCallback', callback);\n    }\n  };\n\n  handleGoogleOneTapCallback = async (\n    signInOrUp: SignInResource | SignUpResource,\n    params: HandleOAuthCallbackParams,\n  ): Promise<void> => {\n    const callback = () => this.clerkjs?.handleGoogleOneTapCallback(signInOrUp, params);\n    if (this.clerkjs && this.loaded) {\n      void callback()?.catch(() => {\n        // This error is caused when the host app is using React18\n        // and strictMode is enabled. This useEffects runs twice because\n        // the clerk-react ui components mounts, unmounts and mounts again\n        // so the clerk-js component loses its state because of the custom\n        // unmount callback we're using.\n        // This needs to be solved by tweaking the logic in uiComponents.tsx\n        // or by making handleRedirectCallback idempotent\n      });\n    } else {\n      this.premountMethodCalls.set('handleGoogleOneTapCallback', callback);\n    }\n  };\n\n  handleEmailLinkVerification = async (params: HandleEmailLinkVerificationParams) => {\n    const callback = () => this.clerkjs?.handleEmailLinkVerification(params);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('handleEmailLinkVerification', callback);\n    }\n  };\n\n  authenticateWithMetamask = async (params?: AuthenticateWithMetamaskParams) => {\n    const callback = () => this.clerkjs?.authenticateWithMetamask(params);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('authenticateWithMetamask', callback);\n    }\n  };\n\n  authenticateWithCoinbaseWallet = async (params?: AuthenticateWithCoinbaseWalletParams) => {\n    const callback = () => this.clerkjs?.authenticateWithCoinbaseWallet(params);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('authenticateWithCoinbaseWallet', callback);\n    }\n  };\n\n  authenticateWithOKXWallet = async (params?: AuthenticateWithOKXWalletParams) => {\n    const callback = () => this.clerkjs?.authenticateWithOKXWallet(params);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('authenticateWithOKXWallet', callback);\n    }\n  };\n\n  authenticateWithWeb3 = async (params: ClerkAuthenticateWithWeb3Params) => {\n    const callback = () => this.clerkjs?.authenticateWithWeb3(params);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('authenticateWithWeb3', callback);\n    }\n  };\n\n  authenticateWithGoogleOneTap = async (params: AuthenticateWithGoogleOneTapParams) => {\n    const clerkjs = await this.#waitForClerkJS();\n    return clerkjs.authenticateWithGoogleOneTap(params);\n  };\n\n  createOrganization = async (params: CreateOrganizationParams): Promise<OrganizationResource | void> => {\n    const callback = () => this.clerkjs?.createOrganization(params);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<OrganizationResource>;\n    } else {\n      this.premountMethodCalls.set('createOrganization', callback);\n    }\n  };\n\n  getOrganization = async (organizationId: string): Promise<OrganizationResource | void> => {\n    const callback = () => this.clerkjs?.getOrganization(organizationId);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<OrganizationResource>;\n    } else {\n      this.premountMethodCalls.set('getOrganization', callback);\n    }\n  };\n\n  joinWaitlist = async (params: JoinWaitlistParams): Promise<WaitlistResource | void> => {\n    const callback = () => this.clerkjs?.joinWaitlist(params);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<WaitlistResource>;\n    } else {\n      this.premountMethodCalls.set('joinWaitlist', callback);\n    }\n  };\n\n  signOut = async (...args: Parameters<Clerk['signOut']>) => {\n    const callback = () => this.clerkjs?.signOut(...args);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('signOut', callback);\n    }\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAI,OAAO,WAAW,eAAe,CAAC,OAAO,QAAQ;AACnD,SAAO,SAAS,OAAO,WAAW,cAAc,SAAS;AAC3D;;;ACNA,SAAS,yCAAyC;;;ACFlD,SAAS,qBAAAA,0BAAyB;AAiBlC,OAAOC,UAAS,eAAe,eAAe,kBAAkB;;;ACjBhE,OAAO,WAAW;AAKX,IAAM,oBACX,CAAC,aACD,CAAC,SAAyF;AACxF,MAAI;AACF,WAAO,MAAM,SAAS,KAAK,QAAQ;AAAA,EACrC,QAAQ;AACN,WAAO,aAAa,MAAM,kCAAkC,IAAI,CAAC;AAAA,EACnE;AACF;AAEK,IAAM,4BAA4B,CAAC,UAAuC,gBAAwB;AACvG,MAAI,CAAC,UAAU;AACb,eAAW;AAAA,EACb;AACA,MAAI,OAAO,aAAa,UAAU;AAChC,eAAW,oCAAC,gBAAQ,QAAS;AAAA,EAC/B;AACA,SAAO;AACT;AAEO,IAAM,cACX,CAAC,OACD,IAAI,SAAc;AAChB,MAAI,MAAM,OAAO,OAAO,YAAY;AAClC,WAAO,GAAG,GAAG,IAAI;AAAA,EACnB;AACF;;;AC/BK,SAAS,cAAiB,GAAgB;AAC/C,SAAO,OAAO,MAAM;AACtB;;;ACFA,OAAOC,YAAW;AAIlB,IAAM,SAAS,oBAAI,IAAoB;AAEhC,SAAS,4BAA4B,MAAc,OAAe,WAAW,GAAS;AAC3F,EAAAC,OAAM,UAAU,MAAM;AACpB,UAAM,QAAQ,OAAO,IAAI,IAAI,KAAK;AAClC,QAAI,SAAS,UAAU;AACrB,aAAO,aAAa,MAAM,KAAK;AAAA,IACjC;AACA,WAAO,IAAI,MAAM,QAAQ,CAAC;AAE1B,WAAO,MAAM;AACX,aAAO,IAAI,OAAO,OAAO,IAAI,IAAI,KAAK,KAAK,CAAC;AAAA,IAC9C;AAAA,EACF,GAAG,CAAC,CAAC;AACP;AAEO,SAAS,6BACd,kBACA,MACA,OACwB;AACxB,QAAM,cAAc,iBAAiB,eAAe,iBAAiB,QAAQ,QAAQ;AACrF,QAAM,MAAM,CAAC,UAAa;AACxB,gCAA4B,MAAM,KAAK;AACvC,WAAO,gBAAAA,OAAA,cAAC,oBAAkB,GAAI,OAAe;AAAA,EAC/C;AACA,MAAI,cAAc,gCAAgC,WAAW;AAC7D,SAAO;AACT;;;AChCA,OAAOC,UAAS,gBAAgB;AAChC,SAAS,oBAAoB;AAgBtB,IAAM,yBAAyB,CAAC,aAA6C;AAClF,QAAM,eAAe,MAAM,SAAS,MAAM,EAAE,KAAK,IAAI;AACrD,QAAM,CAAC,OAAO,QAAQ,IAAI,SAA6B,YAAY;AAEnE,SAAO,SAAS,IAAI,CAAC,IAAI,WAAW;AAAA,IAClC,IAAI,GAAG;AAAA,IACP,OAAO,CAAC,SAAkB,SAAS,eAAa,UAAU,IAAI,CAAC,GAAG,MAAO,MAAM,QAAQ,OAAO,CAAE,CAAC;AAAA,IACjG,SAAS,MAAM,SAAS,eAAa,UAAU,IAAI,CAAC,GAAG,MAAO,MAAM,QAAQ,OAAO,CAAE,CAAC;AAAA,IACtF,QAAQ,MAAM,gBAAAA,OAAA,cAAAA,OAAA,gBAAG,MAAM,KAAK,IAAI,aAAa,GAAG,WAAW,MAAM,KAAK,CAAC,IAAI,IAAK;AAAA,EAClF,EAAE;AACJ;;;AC3BA,SAAS,yBAAyB;AAGlC,OAAOC,YAAW;;;ACHlB,OAAOC,YAAW;AAEX,IAAM,kBAAkB,CAAC,GAAQ,cAAqD;AAC3F,SAAO,CAAC,CAAC,KAAKA,OAAM,eAAe,CAAC,MAAM,uBAA0B,UAAS;AAC/E;;;ADcO,IAAM,4BAA4B,CACvC,UACA,YACG;AACH,QAAM,qBAAqB,CAAC,WAAW,UAAU;AACjD,SAAO;AAAA,IACL;AAAA,MACE;AAAA,MACA;AAAA,MACA,eAAe;AAAA,MACf,eAAe;AAAA,MACf,oBAAoB;AAAA,MACpB,eAAe;AAAA,IACjB;AAAA,IACA;AAAA,EACF;AACF;AAEO,IAAM,oCAAoC,CAC/C,UACA,YACG;AACH,QAAM,qBAAqB,CAAC,WAAW,SAAS;AAChD,SAAO;AAAA,IACL;AAAA,MACE;AAAA,MACA;AAAA,MACA,eAAe;AAAA,MACf,eAAe;AAAA,MACf,eAAe;AAAA,IACjB;AAAA,IACA;AAAA,EACF;AACF;AA+BO,IAAM,uBAAuB,CAAC,aAA8B;AACjE,QAAM,oBAAuC,CAAC;AAE9C,QAAM,qBAA4B;AAAA,IAChC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,EAAAC,OAAM,SAAS,QAAQ,UAAU,WAAS;AACxC,QAAI,CAAC,mBAAmB,KAAK,eAAa,gBAAgB,OAAO,SAAS,CAAC,GAAG;AAC5E,wBAAkB,KAAK,KAAK;AAAA,IAC9B;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAEA,IAAM,iBAAiB,CAAC,QAA8B,YAAoC;AACxF,QAAM,EAAE,UAAU,eAAe,eAAe,oBAAoB,oBAAoB,cAAc,IAAI;AAC1G,QAAM,EAAE,sBAAsB,MAAM,IAAI,WAAW,CAAC;AACpD,QAAM,gBAAwC,CAAC;AAE/C,EAAAA,OAAM,SAAS,QAAQ,UAAU,WAAS;AACxC,QACE,CAAC,gBAAgB,OAAO,aAAa,KACrC,CAAC,gBAAgB,OAAO,aAAa,KACrC,CAAC,gBAAgB,OAAO,kBAAkB,GAC1C;AACA,UAAI,SAAS,CAAC,qBAAqB;AACjC,0BAAkB,4BAA4B,aAAa,CAAC;AAAA,MAC9D;AACA;AAAA,IACF;AAEA,UAAM,EAAE,MAAM,IAAI;AAElB,UAAM,EAAE,UAAAC,WAAU,OAAO,KAAK,UAAU,IAAI;AAE5C,QAAI,gBAAgB,OAAO,aAAa,GAAG;AACzC,UAAI,cAAc,OAAO,kBAAkB,GAAG;AAE5C,sBAAc,KAAK,EAAE,MAAM,CAAC;AAAA,MAC9B,WAAW,aAAa,KAAK,GAAG;AAE9B,sBAAc,KAAK,EAAE,OAAO,WAAW,UAAAA,WAAU,IAAI,CAAC;AAAA,MACxD,OAAO;AACL,0BAAkB,qBAAqB,aAAa,CAAC;AACrD;AAAA,MACF;AAAA,IACF;AAEA,QAAI,gBAAgB,OAAO,aAAa,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG;AAEzB,sBAAc,KAAK,EAAE,OAAO,WAAW,IAAI,CAAC;AAAA,MAC9C,OAAO;AACL,0BAAkB,qBAAqB,aAAa,CAAC;AACrD;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AAED,QAAM,qBAAqD,CAAC;AAC5D,QAAM,uBAAuD,CAAC;AAC9D,QAAM,uBAAuD,CAAC;AAE9D,gBAAc,QAAQ,CAAC,IAAI,UAAU;AACnC,QAAI,aAAa,EAAE,GAAG;AACpB,yBAAmB,KAAK,EAAE,WAAW,GAAG,UAAU,IAAI,MAAM,CAAC;AAC7D,2BAAqB,KAAK,EAAE,WAAW,GAAG,WAAW,IAAI,MAAM,CAAC;AAChE;AAAA,IACF;AACA,QAAI,eAAe,EAAE,GAAG;AACtB,2BAAqB,KAAK,EAAE,WAAW,GAAG,WAAW,IAAI,MAAM,CAAC;AAAA,IAClE;AAAA,EACF,CAAC;AAED,QAAM,4BAA4B,uBAAuB,kBAAkB;AAC3E,QAAM,8BAA8B,uBAAuB,oBAAoB;AAC/E,QAAM,8BAA8B,uBAAuB,oBAAoB;AAE/E,QAAM,cAA4B,CAAC;AACnC,QAAM,qBAA4C,CAAC;AAEnD,gBAAc,QAAQ,CAAC,IAAI,UAAU;AACnC,QAAI,cAAc,IAAI,kBAAkB,GAAG;AACzC,kBAAY,KAAK,EAAE,OAAO,GAAG,MAAM,CAAC;AACpC;AAAA,IACF;AACA,QAAI,aAAa,EAAE,GAAG;AACpB,YAAM;AAAA,QACJ,QAAQ;AAAA,QACR;AAAA,QACA;AAAA,MACF,IAAI,0BAA0B,KAAK,OAAK,EAAE,OAAO,KAAK;AACtD,YAAM;AAAA,QACJ,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,MACX,IAAI,4BAA4B,KAAK,OAAK,EAAE,OAAO,KAAK;AACxD,kBAAY,KAAK,EAAE,OAAO,GAAG,OAAO,KAAK,GAAG,KAAK,OAAO,SAAS,WAAW,YAAY,CAAC;AACzF,yBAAmB,KAAK,aAAa;AACrC,yBAAmB,KAAK,WAAW;AACnC;AAAA,IACF;AACA,QAAI,eAAe,EAAE,GAAG;AACtB,YAAM;AAAA,QACJ,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,MACX,IAAI,4BAA4B,KAAK,OAAK,EAAE,OAAO,KAAK;AACxD,kBAAY,KAAK,EAAE,OAAO,GAAG,OAAO,KAAK,GAAG,KAAK,WAAW,YAAY,CAAC;AACzE,yBAAmB,KAAK,WAAW;AACnC;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO,EAAE,aAAa,mBAAmB;AAC3C;AAEA,IAAM,gBAAgB,CAAC,YAAiB,eAAkC;AACxE,QAAM,EAAE,UAAU,OAAO,KAAK,UAAU,IAAI;AAC5C,SAAO,CAAC,YAAY,CAAC,OAAO,CAAC,aAAa,WAAW,KAAK,OAAK,MAAM,KAAK;AAC5E;AAEA,IAAM,eAAe,CAAC,eAA6B;AACjD,QAAM,EAAE,UAAU,OAAO,KAAK,UAAU,IAAI;AAC5C,SAAO,CAAC,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC;AACjD;AAEA,IAAM,iBAAiB,CAAC,eAA6B;AACnD,QAAM,EAAE,UAAU,OAAO,KAAK,UAAU,IAAI;AAC5C,SAAO,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC;AAChD;;;AE1NA,SAAS,qBAAAC,0BAAyB;AAGlC,OAAOC,YAAW;AAcX,IAAM,+BAA+B,CAAC,aAAkD;AAC7F,QAAM,qBAAqB,CAAC,iBAAiB,SAAS;AACtD,SAAO,mBAAmB;AAAA,IACxB;AAAA,IACA;AAAA,IACA,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,IAC1B,0BAA0B;AAAA,EAC5B,CAAC;AACH;AAcA,IAAM,qBAAqB,CAAC;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAgC;AAC9B,QAAM,gBAAsC,CAAC;AAC7C,QAAM,kBAAoC,CAAC;AAC3C,QAAM,yBAAgD,CAAC;AAEvD,EAAAC,OAAM,SAAS,QAAQ,UAAU,WAAS;AACxC,QACE,CAAC,gBAAgB,OAAO,kBAAkB,KAC1C,CAAC,gBAAgB,OAAO,wBAAwB,KAChD,CAAC,gBAAgB,OAAO,wBAAwB,GAChD;AACA,UAAI,OAAO;AACT,QAAAC,mBAAkB,0BAA0B;AAAA,MAC9C;AACA;AAAA,IACF;AAGA,QAAI,gBAAgB,OAAO,wBAAwB,KAAK,gBAAgB,OAAO,wBAAwB,GAAG;AACxG;AAAA,IACF;AAGA,UAAM,EAAE,MAAM,IAAI;AAElB,IAAAD,OAAM,SAAS,QAAQ,MAAM,UAAU,CAAAE,WAAS;AAC9C,UAAI,CAAC,gBAAgBA,QAAO,mBAAmB,KAAK,CAAC,gBAAgBA,QAAO,iBAAiB,GAAG;AAC9F,YAAIA,QAAO;AACT,UAAAD,mBAAkB,+BAA+B;AAAA,QACnD;AAEA;AAAA,MACF;AAEA,YAAM,EAAE,OAAAE,OAAM,IAAID;AAElB,YAAM,EAAE,OAAO,WAAW,MAAM,SAAS,KAAK,IAAIC;AAElD,UAAI,gBAAgBD,QAAO,mBAAmB,GAAG;AAC/C,YAAIE,eAAcD,QAAO,kBAAkB,GAAG;AAE5C,wBAAc,KAAK,EAAE,MAAM,CAAC;AAAA,QAC9B,WAAW,iBAAiBA,MAAK,GAAG;AAClC,gBAAM,WAAW;AAAA,YACf;AAAA,YACA;AAAA,UACF;AAEA,cAAI,YAAY,QAAW;AACzB,0BAAc,KAAK;AAAA,cACjB,GAAG;AAAA,cACH;AAAA,YACF,CAAC;AAAA,UACH,WAAW,SAAS,QAAW;AAC7B,0BAAc,KAAK;AAAA,cACjB,GAAG;AAAA,cACH,MAAM,KAAK,WAAW,GAAG,IAAI,OAAO,IAAI,IAAI;AAAA,YAC9C,CAAC;AAAA,UACH,OAAO;AAEL,YAAAF,mBAAkB,4DAA4D;AAC9E;AAAA,UACF;AAAA,QACF,OAAO;AACL,UAAAA,mBAAkB,oCAAoC;AACtD;AAAA,QACF;AAAA,MACF;AAEA,UAAI,gBAAgBC,QAAO,iBAAiB,GAAG;AAC7C,YAAIG,gBAAeF,MAAK,GAAG;AACzB,wBAAc,KAAK,EAAE,OAAO,WAAW,KAAK,CAAC;AAAA,QAC/C,OAAO;AACL,UAAAF,mBAAkB,gCAAgC;AAClD;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AAED,QAAM,2BAA2D,CAAC;AAClE,QAAM,uBAAuD,CAAC;AAC9D,gBAAc,QAAQ,CAAC,IAAI,UAAU;AACnC,QAAI,iBAAiB,EAAE,GAAG;AACxB,+BAAyB,KAAK,EAAE,WAAW,GAAG,WAAW,IAAI,MAAM,CAAC;AAAA,IACtE;AACA,QAAII,gBAAe,EAAE,GAAG;AACtB,2BAAqB,KAAK,EAAE,WAAW,GAAG,WAAW,IAAI,MAAM,CAAC;AAAA,IAClE;AAAA,EACF,CAAC;AAED,QAAM,kCAAkC,uBAAuB,wBAAwB;AACvF,QAAM,8BAA8B,uBAAuB,oBAAoB;AAE/E,gBAAc,QAAQ,CAAC,IAAI,UAAU;AACnC,QAAID,eAAc,IAAI,kBAAkB,GAAG;AACzC,sBAAgB,KAAK;AAAA,QACnB,OAAO,GAAG;AAAA,MACZ,CAAC;AAAA,IACH;AACA,QAAI,iBAAiB,EAAE,GAAG;AACxB,YAAM;AAAA,QACJ,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,MACX,IAAI,gCAAgC,KAAK,OAAK,EAAE,OAAO,KAAK;AAC5D,YAAM,WAA2B;AAAA,QAC/B,OAAO,GAAG;AAAA,QACV;AAAA,QACA;AAAA,MACF;AAEA,UAAI,aAAa,IAAI;AACnB,iBAAS,UAAU,GAAG;AAAA,MACxB,WAAW,UAAU,IAAI;AACvB,iBAAS,OAAO,GAAG;AAAA,MACrB;AACA,sBAAgB,KAAK,QAAQ;AAC7B,6BAAuB,KAAK,UAAU;AAAA,IACxC;AACA,QAAIC,gBAAe,EAAE,GAAG;AACtB,YAAM;AAAA,QACJ,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,MACX,IAAI,4BAA4B,KAAK,OAAK,EAAE,OAAO,KAAK;AACxD,sBAAgB,KAAK;AAAA,QACnB,OAAO,GAAG;AAAA,QACV,MAAM,GAAG;AAAA,QACT;AAAA,QACA;AAAA,MACF,CAAC;AACD,6BAAuB,KAAK,UAAU;AAAA,IACxC;AAAA,EACF,CAAC;AAED,SAAO,EAAE,iBAAiB,uBAAuB;AACnD;AAEA,IAAMD,iBAAgB,CAAC,YAAiB,eAAkC;AACxE,QAAM,EAAE,UAAU,OAAO,SAAS,UAAU,IAAI;AAChD,SAAO,CAAC,YAAY,CAAC,WAAW,CAAC,aAAa,WAAW,KAAK,OAAK,MAAM,KAAK;AAChF;AAEA,IAAM,mBAAmB,CAAC,eAAyD;AACjF,QAAM,EAAE,OAAO,WAAW,SAAS,KAAK,IAAI;AAC5C,SAAO,CAAC,CAAC,aAAa,CAAC,CAAC,UAAU,OAAO,YAAY,cAAc,OAAO,SAAS;AACrF;AAEA,IAAMC,kBAAiB,CAAC,eAAuD;AAC7E,QAAM,EAAE,OAAO,MAAM,UAAU,IAAI;AACnC,SAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;AACpC;;;AC1MA,SAAS,WAAW,QAAQ,YAAAC,iBAAgB;AAK5C,SAAS,uBAAuB,SAA6E;AAC3G,QAAM,EAAE,OAAO,qCAAU,MAAM,UAAU,UAAU,EAAE,IAAI;AAEzD,SAAO,IAAI,QAAc,CAAC,SAAS,WAAW;AAC5C,QAAI,CAAC,MAAM;AACT,aAAO,IAAI,MAAM,0BAA0B,CAAC;AAC5C;AAAA,IACF;AAEA,QAAI,iBAAqC;AACzC,QAAI,UAAU;AACZ,uBAAiB,6BAAM,cAAc;AAAA,IACvC;AAGA,UAAM,2BAA0B,iDAAgB,sBAAqB,eAAe,oBAAoB;AACxG,QAAI,yBAAyB;AAC3B,cAAQ;AACR;AAAA,IACF;AAGA,UAAM,WAAW,IAAI,iBAAiB,mBAAiB;AACrD,iBAAW,YAAY,eAAe;AACpC,YAAI,SAAS,SAAS,aAAa;AACjC,cAAI,CAAC,kBAAkB,UAAU;AAC/B,6BAAiB,6BAAM,cAAc;AAAA,UACvC;AAEA,eAAI,iDAAgB,sBAAqB,eAAe,oBAAoB,GAAG;AAC7E,qBAAS,WAAW;AACpB,oBAAQ;AACR;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAED,aAAS,QAAQ,MAAM,EAAE,WAAW,MAAM,SAAS,KAAK,CAAC;AAGzD,QAAI,UAAU,GAAG;AACf,iBAAW,MAAM;AACf,iBAAS,WAAW;AACpB,eAAO,IAAI,MAAM,sCAAsC,CAAC;AAAA,MAC1D,GAAG,OAAO;AAAA,IACZ;AAAA,EACF,CAAC;AACH;AAKO,SAAS,yBAAyB,WAAoB;AAC3D,QAAM,aAAa,OAAsB;AACzC,QAAM,CAAC,QAAQ,SAAS,IAAIA,UAA6C,WAAW;AAEpF,YAAU,MAAM;AACd,QAAI,CAAC,WAAW;AACd,YAAM,IAAI,MAAM,4DAA4D;AAAA,IAC9E;AAEA,QAAI,OAAO,WAAW,eAAe,CAAC,WAAW,SAAS;AACxD,iBAAW,UAAU,uBAAuB,EAAE,UAAU,0BAA0B,SAAS,KAAK,CAAC,EAC9F,KAAK,MAAM;AACV,kBAAU,UAAU;AAAA,MACtB,CAAC,EACA,MAAM,MAAM;AACX,kBAAU,OAAO;AAAA,MACnB,CAAC;AAAA,IACL;AAAA,EACF,GAAG,CAAC,SAAS,CAAC;AAEd,SAAO;AACT;;;AC/EA,SAAS,eAAe;AACxB,SAAS,qBAAqB;AAE9B,OAAOC,YAAW;AAIlB,IAAM,eAAe,CAAC,UAAoC;AACxD,SAAO,WAAW;AACpB;AAEA,IAAM,cAAc,CAAC,UAAmC;AACtD,SAAO,UAAU;AACnB;AAEA,IAAM,4BAA4B,CAChC,cAKG;AACH,SAAO,uCAAW,IAAI,CAAC,EAAE,WAAW,aAAa,GAAG,KAAK,MAAM;AACjE;AAmCO,IAAM,oBAAN,cAAgCA,OAAM,cAQ3C;AAAA,EARK;AAAA;AASL,SAAQ,UAAUA,OAAM,UAA0B;AAAA;AAAA,EAElD,mBAAmB,YAA8C;AArEnE;AAsEI,QAAI,CAAC,aAAa,UAAU,KAAK,CAAC,aAAa,KAAK,KAAK,GAAG;AAC1D;AAAA,IACF;AAKA,UAAM,YAAY,QAAQ,WAAW,OAAO,eAAe,mBAAmB,UAAU;AACxF,UAAM,WAAW,QAAQ,KAAK,MAAM,OAAO,eAAe,mBAAmB,UAAU;AAGvF,UAAM,uBAAqB,eAAU,gBAAV,mBAAuB,cAAW,cAAS,gBAAT,mBAAsB;AACnF,UAAM,2BAAyB,eAAU,oBAAV,mBAA2B,cAAW,cAAS,oBAAT,mBAA0B;AAI/F,UAAM,+BAA+B,0BAA0B,WAAW,MAAM,eAAe;AAC/F,UAAM,8BAA8B,0BAA0B,KAAK,MAAM,MAAM,eAAe;AAE9F,QACE,CAAC,cAAc,WAAW,QAAQ,KAClC,CAAC,cAAc,8BAA8B,2BAA2B,KACxE,sBACA,wBACA;AACA,UAAI,KAAK,QAAQ,SAAS;AACxB,aAAK,MAAM,YAAY,EAAE,MAAM,KAAK,QAAQ,SAAS,OAAO,KAAK,MAAM,MAAM,CAAC;AAAA,MAChF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,oBAAoB;AAClB,QAAI,KAAK,QAAQ,SAAS;AACxB,UAAI,aAAa,KAAK,KAAK,GAAG;AAC5B,aAAK,MAAM,MAAM,KAAK,QAAQ,SAAS,KAAK,MAAM,KAAK;AAAA,MACzD;AAEA,UAAI,YAAY,KAAK,KAAK,GAAG;AAC3B,aAAK,MAAM,KAAK,KAAK,MAAM,KAAK;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,uBAAuB;AACrB,QAAI,KAAK,QAAQ,SAAS;AACxB,UAAI,aAAa,KAAK,KAAK,GAAG;AAC5B,aAAK,MAAM,QAAQ,KAAK,QAAQ,OAAO;AAAA,MACzC;AACA,UAAI,YAAY,KAAK,KAAK,GAAG;AAC3B,aAAK,MAAM,MAAM;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,SAAS;AACP,UAAM,EAAE,sBAAsB,MAAM,IAAI,KAAK;AAC7C,UAAM,iBAAiB;AAAA,MACrB,KAAK,KAAK;AAAA,MACV,GAAG,KAAK,MAAM;AAAA,MACd,GAAI,KAAK,MAAM,aAAa,EAAE,wBAAwB,KAAK,MAAM,UAAU;AAAA,IAC7E;AAEA,WACE,gBAAAA,OAAA,cAAAA,OAAA,gBACG,CAAC,uBAAuB,gBAAAA,OAAA,cAAC,SAAK,GAAG,gBAAgB,GACjD,KAAK,MAAM,QACd;AAAA,EAEJ;AACF;;;ATpBA,IAAM,wBAAwB,CAAC,UAAsC;AAvHrE;AAwHE,SACE,gBAAAC,OAAA,cAAAA,OAAA,iBACG,oCAAO,uBAAP,mBAA2B,IAAI,CAAC,QAAQ,UAAU,cAAc,QAAQ,EAAE,KAAK,MAAM,CAAC,KACtF,oCAAO,2BAAP,mBAA+B,IAAI,CAAC,QAAQ,UAAU,cAAc,QAAQ,EAAE,KAAK,MAAM,CAAC,EAC7F;AAEJ;AAEO,IAAM,SAAS;AAAA,EACpB,CAAC,EAAE,OAAO,WAAW,UAAU,GAAG,MAAM,MAAiD;AACvF,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;AAAA,MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;AAAA,IACrE;AAEA,WACE,gBAAAA,OAAA,cAAAA,OAAA,gBACG,sBAAsB,UACtB,MAAM,UACL,gBAAAA,OAAA;AAAA,MAAC;AAAA;AAAA,QACC;AAAA,QACA,OAAO,MAAM;AAAA,QACb,SAAS,MAAM;AAAA,QACf,aAAc,MAAc;AAAA,QAC5B;AAAA,QACA,WAAW;AAAA;AAAA,IACb,CAEJ;AAAA,EAEJ;AAAA,EACA,EAAE,WAAW,UAAU,oBAAoB,KAAK;AAClD;AAEO,IAAM,SAAS;AAAA,EACpB,CAAC,EAAE,OAAO,WAAW,UAAU,GAAG,MAAM,MAAiD;AACvF,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;AAAA,MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;AAAA,IACrE;AAEA,WACE,gBAAAA,OAAA,cAAAA,OAAA,gBACG,sBAAsB,UACtB,MAAM,UACL,gBAAAA,OAAA;AAAA,MAAC;AAAA;AAAA,QACC;AAAA,QACA,OAAO,MAAM;AAAA,QACb,SAAS,MAAM;AAAA,QACf,aAAc,MAAc;AAAA,QAC5B;AAAA,QACA,WAAW;AAAA;AAAA,IACb,CAEJ;AAAA,EAEJ;AAAA,EACA,EAAE,WAAW,UAAU,oBAAoB,KAAK;AAClD;AAEO,SAAS,gBAAgB,EAAE,SAAS,GAA4C;AACrF,EAAAC,mBAAkB,4BAA4B;AAC9C,SAAO,gBAAAD,OAAA,cAAAA,OAAA,gBAAG,QAAS;AACrB;AAEO,SAAS,gBAAgB,EAAE,SAAS,GAA4C;AACrF,EAAAC,mBAAkB,4BAA4B;AAC9C,SAAO,gBAAAD,OAAA,cAAAA,OAAA,gBAAG,QAAS;AACrB;AAEA,IAAM,eAAe;AAAA,EACnB,CAAC;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,MAAiG;AAC/F,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;AAAA,MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;AAAA,IACrE;AAEA,UAAM,EAAE,aAAa,mBAAmB,IAAI,0BAA0B,MAAM,QAAQ;AACpF,WACE,gBAAAA,OAAA,cAAAA,OAAA,gBACG,sBAAsB,UACvB,gBAAAA,OAAA;AAAA,MAAC;AAAA;AAAA,QACC;AAAA,QACA,OAAO,MAAM;AAAA,QACb,SAAS,MAAM;AAAA,QACf,aAAc,MAAc;AAAA,QAC5B,OAAO,EAAE,GAAG,OAAO,YAAY;AAAA,QAC/B,WAAW;AAAA;AAAA,MAEX,gBAAAA,OAAA,cAAC,yBAAsB,oBAAwC;AAAA,IACjE,CACF;AAAA,EAEJ;AAAA,EACA,EAAE,WAAW,eAAe,oBAAoB,KAAK;AACvD;AAEO,IAAM,cAAqC,OAAO,OAAO,cAAc;AAAA,EAC5E,MAAM;AAAA,EACN,MAAM;AACR,CAAC;AAED,IAAM,oBAAoB,cAA0B;AAAA,EAClD,OAAO,MAAM;AAAA,EAAC;AAAA,EACd,SAAS,MAAM;AAAA,EAAC;AAAA,EAChB,aAAa,MAAM;AAAA,EAAC;AACtB,CAAC;AAED,IAAM,cAAc;AAAA,EAClB,CAAC;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,MAA0F;AACxF,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;AAAA,MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;AAAA,IACrE;AAEA,UAAM,EAAE,aAAa,mBAAmB,IAAI,0BAA0B,MAAM,UAAU;AAAA,MACpF,qBAAqB,CAAC,CAAC,MAAM;AAAA,IAC/B,CAAC;AACD,UAAM,mBAAmB,OAAO,OAAO,MAAM,oBAAoB,CAAC,GAAG,EAAE,YAAY,CAAC;AACpF,UAAM,EAAE,iBAAiB,uBAAuB,IAAI,6BAA6B,MAAM,QAAQ;AAC/F,UAAM,oBAAoB,qBAAqB,MAAM,QAAQ;AAE7D,UAAM,gBAAgB;AAAA,MACpB,OAAO,MAAM;AAAA,MACb,SAAS,MAAM;AAAA,MACf,aAAc,MAAc;AAAA,MAC5B,OAAO,EAAE,GAAG,OAAO,kBAAkB,gBAAgB;AAAA,IACvD;AACA,UAAM,cAAc;AAAA,MAClB;AAAA,MACA;AAAA,IACF;AAEA,WACE,gBAAAA,OAAA,cAAC,kBAAkB,UAAlB,EAA2B,OAAO,iBAChC,sBAAsB,UACtB,MAAM,UACL,gBAAAA,OAAA;AAAA,MAAC;AAAA;AAAA,QACC;AAAA,QACC,GAAG;AAAA,QACJ,qBAAqB,CAAC,CAAC,MAAM;AAAA,QAC7B,WAAW;AAAA;AAAA,MAGV,MAAM,4BAA4B,oBAAoB;AAAA,MACvD,gBAAAA,OAAA,cAAC,yBAAuB,GAAG,aAAa;AAAA,IAC1C,CAEJ;AAAA,EAEJ;AAAA,EACA,EAAE,WAAW,cAAc,oBAAoB,KAAK;AACtD;AAEO,SAAS,UAAU,EAAE,SAAS,GAAsB;AACzD,EAAAC,mBAAkB,gCAAgC;AAClD,SAAO,gBAAAD,OAAA,cAAAA,OAAA,gBAAG,QAAS;AACrB;AAEO,SAAS,WAAW,EAAE,SAAS,GAA6C;AACjF,EAAAC,mBAAkB,iCAAiC;AACnD,SAAO,gBAAAD,OAAA,cAAAA,OAAA,gBAAG,QAAS;AACrB;AAEO,SAAS,SAAS,EAAE,SAAS,GAA2C;AAC7E,EAAAC,mBAAkB,+BAA+B;AACjD,SAAO,gBAAAD,OAAA,cAAAA,OAAA,gBAAG,QAAS;AACrB;AAEO,SAAS,iBAAiB,aAA2D;AAC1F,QAAM,gBAAgB,WAAW,iBAAiB;AAElD,QAAM,cAAc;AAAA,IAClB,GAAG;AAAA,IACH,OAAO;AAAA,MACL,GAAG,cAAc;AAAA,MACjB,GAAG;AAAA,IACL;AAAA,EACF;AAEA,SAAO,gBAAAA,OAAA,cAAC,qBAAmB,GAAG,aAAa;AAC7C;AAEO,IAAM,aAAmC,OAAO,OAAO,aAAa;AAAA,EACzE;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,uBAAuB;AACzB,CAAC;AAEM,SAAS,wBAAwB,EAAE,SAAS,GAAoD;AACrG,EAAAC,mBAAkB,oCAAoC;AACtD,SAAO,gBAAAD,OAAA,cAAAA,OAAA,gBAAG,QAAS;AACrB;AAEO,SAAS,wBAAwB,EAAE,SAAS,GAAoD;AACrG,EAAAC,mBAAkB,oCAAoC;AACtD,SAAO,gBAAAD,OAAA,cAAAA,OAAA,gBAAG,QAAS;AACrB;AAEA,IAAM,uBAAuB;AAAA,EAC3B,CAAC;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,MAAyG;AACvG,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;AAAA,MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;AAAA,IACrE;AAEA,UAAM,EAAE,aAAa,mBAAmB,IAAI,kCAAkC,MAAM,QAAQ;AAC5F,WACE,gBAAAA,OAAA,cAAAA,OAAA,gBACG,sBAAsB,UACtB,MAAM,UACL,gBAAAA,OAAA;AAAA,MAAC;AAAA;AAAA,QACC;AAAA,QACA,OAAO,MAAM;AAAA,QACb,SAAS,MAAM;AAAA,QACf,aAAc,MAAc;AAAA,QAC5B,OAAO,EAAE,GAAG,OAAO,YAAY;AAAA,QAC/B,WAAW;AAAA;AAAA,MAEX,gBAAAA,OAAA,cAAC,yBAAsB,oBAAwC;AAAA,IACjE,CAEJ;AAAA,EAEJ;AAAA,EACA,EAAE,WAAW,uBAAuB,oBAAoB,KAAK;AAC/D;AAEO,IAAM,sBAAqD,OAAO,OAAO,sBAAsB;AAAA,EACpG,MAAM;AAAA,EACN,MAAM;AACR,CAAC;AAEM,IAAM,qBAAqB;AAAA,EAChC,CAAC,EAAE,OAAO,WAAW,UAAU,GAAG,MAAM,MAA6D;AACnG,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;AAAA,MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;AAAA,IACrE;AAEA,WACE,gBAAAA,OAAA,cAAAA,OAAA,gBACG,sBAAsB,UACtB,MAAM,UACL,gBAAAA,OAAA;AAAA,MAAC;AAAA;AAAA,QACC;AAAA,QACA,OAAO,MAAM;AAAA,QACb,SAAS,MAAM;AAAA,QACf,aAAc,MAAc;AAAA,QAC5B;AAAA,QACA,WAAW;AAAA;AAAA,IACb,CAEJ;AAAA,EAEJ;AAAA,EACA,EAAE,WAAW,sBAAsB,oBAAoB,KAAK;AAC9D;AAEA,IAAM,8BAA8B,cAA0B;AAAA,EAC5D,OAAO,MAAM;AAAA,EAAC;AAAA,EACd,SAAS,MAAM;AAAA,EAAC;AAAA,EAChB,aAAa,MAAM;AAAA,EAAC;AACtB,CAAC;AAED,IAAM,wBAAwB;AAAA,EAC5B,CAAC;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,MAAoG;AAClG,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;AAAA,MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;AAAA,IACrE;AAEA,UAAM,EAAE,aAAa,mBAAmB,IAAI,kCAAkC,MAAM,UAAU;AAAA,MAC5F,qBAAqB,CAAC,CAAC,MAAM;AAAA,IAC/B,CAAC;AACD,UAAM,2BAA2B,OAAO,OAAO,MAAM,4BAA4B,CAAC,GAAG,EAAE,YAAY,CAAC;AACpG,UAAM,oBAAoB,qBAAqB,MAAM,QAAQ;AAE7D,UAAM,gBAAgB;AAAA,MACpB,OAAO,MAAM;AAAA,MACb,SAAS,MAAM;AAAA,MACf,aAAc,MAAc;AAAA,MAC5B,OAAO,EAAE,GAAG,OAAO,yBAAyB;AAAA,MAC5C,WAAW;AAAA,MACX;AAAA,IACF;AAKA,UAAM,4CAA4C;AAElD,WACE,gBAAAA,OAAA,cAAC,4BAA4B,UAA5B,EAAqC,OAAO,iBAC3C,gBAAAA,OAAA,cAAAA,OAAA,gBACG,sBAAsB,UACtB,MAAM,UACL,gBAAAA,OAAA;AAAA,MAAC;AAAA;AAAA,QACE,GAAG;AAAA,QACJ,qBAAqB,CAAC,CAAC,MAAM;AAAA;AAAA,MAG5B,MAAM,4BAA4B,oBAAoB;AAAA,MACvD,gBAAAA,OAAA,cAAC,yBAAsB,oBAAwC;AAAA,IACjE,CAEJ,CACF;AAAA,EAEJ;AAAA,EACA,EAAE,WAAW,wBAAwB,oBAAoB,KAAK;AAChE;AAEO,SAAS,2BACd,aACA;AACA,QAAM,gBAAgB,WAAW,2BAA2B;AAE5D,QAAM,cAAc;AAAA,IAClB,GAAG;AAAA,IACH,OAAO;AAAA,MACL,GAAG,cAAc;AAAA,MACjB,GAAG;AAAA,IACL;AAAA,EACF;AAEA,SAAO,gBAAAA,OAAA,cAAC,qBAAmB,GAAG,aAAa;AAC7C;AAEO,IAAM,uBAAuD,OAAO,OAAO,uBAAuB;AAAA,EACvG;AAAA,EACA;AAAA,EACA,uBAAuB;AACzB,CAAC;AAEM,IAAM,mBAAmB;AAAA,EAC9B,CAAC,EAAE,OAAO,WAAW,UAAU,GAAG,MAAM,MAA2D;AACjG,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;AAAA,MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;AAAA,IACrE;AAEA,WACE,gBAAAA,OAAA,cAAAA,OAAA,gBACG,sBAAsB,UACtB,MAAM,UACL,gBAAAA,OAAA;AAAA,MAAC;AAAA;AAAA,QACC;AAAA,QACA,OAAO,MAAM;AAAA,QACb,SAAS,MAAM;AAAA,QACf,aAAc,MAAc;AAAA,QAC5B;AAAA,QACA,WAAW;AAAA;AAAA,IACb,CAEJ;AAAA,EAEJ;AAAA,EACA,EAAE,WAAW,oBAAoB,oBAAoB,KAAK;AAC5D;AAEO,IAAM,eAAe;AAAA,EAC1B,CAAC,EAAE,OAAO,WAAW,UAAU,GAAG,MAAM,MAAuD;AAC7F,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;AAAA,MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;AAAA,IACrE;AAEA,WACE,gBAAAA,OAAA,cAAAA,OAAA,gBACG,sBAAsB,UACtB,MAAM,UACL,gBAAAA,OAAA;AAAA,MAAC;AAAA;AAAA,QACC;AAAA,QACA,MAAM,MAAM;AAAA,QACZ,OAAO,MAAM;AAAA,QACb,aAAc,MAAc;AAAA,QAC5B;AAAA,QACA,WAAW;AAAA;AAAA,IACb,CAEJ;AAAA,EAEJ;AAAA,EACA,EAAE,WAAW,gBAAgB,oBAAoB,KAAK;AACxD;AAEO,IAAM,WAAW;AAAA,EACtB,CAAC,EAAE,OAAO,WAAW,UAAU,GAAG,MAAM,MAAmD;AACzF,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;AAAA,MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;AAAA,IACrE;AAEA,WACE,gBAAAA,OAAA,cAAAA,OAAA,gBACG,sBAAsB,UACtB,MAAM,UACL,gBAAAA,OAAA;AAAA,MAAC;AAAA;AAAA,QACC;AAAA,QACA,OAAO,MAAM;AAAA,QACb,SAAS,MAAM;AAAA,QACf,aAAc,MAAc;AAAA,QAC5B;AAAA,QACA,WAAW;AAAA;AAAA,IACb,CAEJ;AAAA,EAEJ;AAAA,EACA,EAAE,WAAW,YAAY,oBAAoB,KAAK;AACpD;AAEO,IAAM,eAAe;AAAA,EAC1B,CAAC,EAAE,OAAO,WAAW,UAAU,GAAG,MAAM,MAAuD;AAC7F,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;AAAA,MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;AAAA,IACrE;AAEA,WACE,gBAAAA,OAAA,cAAAA,OAAA,gBACG,sBAAsB,UACtB,MAAM,UACL,gBAAAA,OAAA;AAAA,MAAC;AAAA;AAAA,QACC;AAAA,QACA,OAAO,MAAM;AAAA,QACb,SAAS,MAAM;AAAA,QACf,aAAc,MAAc;AAAA,QAC5B;AAAA,QACA,WAAW;AAAA;AAAA,IACb,CAEJ;AAAA,EAEJ;AAAA,EACA,EAAE,WAAW,gBAAgB,oBAAoB,KAAK;AACxD;AAMO,IAAM,UAAU;AAAA,EACrB,CAAC,EAAE,OAAO,WAAW,UAAU,GAAG,MAAM,MAAkD;AACxF,UAAM,iBAAiB,yBAAyB,SAAS;AACzD,UAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM;AAEpE,UAAM,oBAAoB;AAAA,MACxB,GAAI,sBAAsB,YAAY,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE;AAAA,IACrE;AAEA,WACE,gBAAAA,OAAA,cAAAA,OAAA,gBACG,sBAAsB,UACtB,MAAM,UACL,gBAAAA,OAAA;AAAA,MAAC;AAAA;AAAA,QACC;AAAA,QACA,OAAO,MAAM;AAAA,QACb,SAAS,MAAM;AAAA,QACf,aAAc,MAAc;AAAA,QAC5B;AAAA,QACA,WAAW;AAAA;AAAA,IACb,CAEJ;AAAA,EAEJ;AAAA,EACA,EAAE,WAAW,WAAW,oBAAoB,KAAK;AACnD;;;AUznBA,OAAOE,YAAW;AAMX,IAAM,eAAe;AAAA,EAC1B,CAAC,EAAE,OAAO,UAAU,GAAG,MAAM,MAAiE;AAC5F,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL,IAAI;AACJ,eAAW,0BAA0B,UAAU,SAAS;AACxD,UAAM,QAAQ,kBAAkB,QAAQ,EAAE,cAAc;AAExD,UAAM,eAAe,MAAM;AACzB,YAAM,OAAoB;AAAA,QACxB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,UAAI,SAAS,SAAS;AACpB,eAAO,MAAM,WAAW,EAAE,GAAG,MAAM,YAAY,MAAM,WAAW,CAAC;AAAA,MACnE;AACA,aAAO,MAAM,iBAAiB;AAAA,QAC5B,GAAG;AAAA,QACH,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,MAC1B,CAAC;AAAA,IACH;AAEA,UAAM,2BAAoD,OAAM,MAAK;AACnE,UAAI,SAAS,OAAO,UAAU,YAAY,WAAW,OAAO;AAC1D,cAAM,YAAY,MAAM,MAAM,OAAO,EAAE,CAAC;AAAA,MAC1C;AACA,aAAO,aAAa;AAAA,IACtB;AAEA,UAAM,aAAa,EAAE,GAAG,MAAM,SAAS,yBAAyB;AAChE,WAAOC,OAAM,aAAa,OAAsC,UAAU;AAAA,EAC5E;AAAA,EACA,EAAE,WAAW,gBAAgB,oBAAoB,KAAK;AACxD;;;ACtDA,OAAOC,aAAW;AAMX,IAAM,eAAe;AAAA,EAC1B,CAAC,EAAE,OAAO,UAAU,GAAG,MAAM,MAAiE;AAC5F,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL,IAAI;AAEJ,eAAW,0BAA0B,UAAU,SAAS;AACxD,UAAM,QAAQ,kBAAkB,QAAQ,EAAE,cAAc;AAExD,UAAM,eAAe,MAAM;AACzB,YAAM,OAAoB;AAAA,QACxB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,UAAI,SAAS,SAAS;AACpB,eAAO,MAAM,WAAW,EAAE,GAAG,MAAM,YAAY,MAAM,WAAW,CAAC;AAAA,MACnE;AAEA,aAAO,MAAM,iBAAiB;AAAA,QAC5B,GAAG;AAAA,QACH,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,MAC1B,CAAC;AAAA,IACH;AAEA,UAAM,2BAAoD,OAAM,MAAK;AACnE,UAAI,SAAS,OAAO,UAAU,YAAY,WAAW,OAAO;AAC1D,cAAM,YAAY,MAAM,MAAM,OAAO,EAAE,CAAC;AAAA,MAC1C;AACA,aAAO,aAAa;AAAA,IACtB;AAEA,UAAM,aAAa,EAAE,GAAG,MAAM,SAAS,yBAAyB;AAChE,WAAOC,QAAM,aAAa,OAAsC,UAAU;AAAA,EAC5E;AAAA,EACA,EAAE,WAAW,gBAAgB,oBAAoB,KAAK;AACxD;;;ACxDA,OAAOC,aAAW;AAYX,IAAM,gBAAgB;AAAA,EAC3B,CAAC,EAAE,OAAO,UAAU,GAAG,MAAM,MAAkE;AAC7F,UAAM,EAAE,cAAc,KAAK,gBAAgB,GAAG,KAAK,IAAI;AAEvD,eAAW,0BAA0B,UAAU,UAAU;AACzD,UAAM,QAAQ,kBAAkB,QAAQ,EAAE,eAAe;AAEzD,UAAM,eAAe,MAAM,MAAM,QAAQ,EAAE,aAAa,GAAG,eAAe,CAAC;AAC3E,UAAM,2BAAoD,OAAM,MAAK;AACnE,YAAM,YAAa,MAAc,MAAM,OAAO,EAAE,CAAC;AACjD,aAAO,aAAa;AAAA,IACtB;AAEA,UAAM,aAAa,EAAE,GAAG,MAAM,SAAS,yBAAyB;AAChE,WAAOC,QAAM,aAAa,OAAsC,UAAU;AAAA,EAC5E;AAAA,EACA,EAAE,WAAW,iBAAiB,oBAAoB,KAAK;AACzD;;;AC9BA,OAAOC,aAAW;AAMX,IAAM,2BAA2B;AAAA,EACtC,CAAC,EAAE,OAAO,UAAU,GAAG,MAAM,MAAoD;AAC/E,UAAM,EAAE,aAAa,GAAG,KAAK,IAAI;AAEjC,eAAW,0BAA0B,UAAU,uBAAuB;AACtE,UAAM,QAAQ,kBAAkB,QAAQ,EAAE,0BAA0B;AAIpE,UAAM,eAAe,YAAY;AAC/B,qBAAe,eAAe;AAC5B,cAAM,MAAM,yBAAyB,EAAE,aAAa,eAAe,OAAU,CAAC;AAAA,MAChF;AACA,WAAK,aAAa;AAAA,IACpB;AAEA,UAAM,2BAAoD,OAAM,MAAK;AACnE,YAAM,YAAa,MAAc,MAAM,OAAO,EAAE,CAAC;AACjD,aAAO,aAAa;AAAA,IACtB;AAEA,UAAM,aAAa,EAAE,GAAG,MAAM,SAAS,yBAAyB;AAChE,WAAOC,QAAM,aAAa,OAAsC,UAAU;AAAA,EAC5E;AAAA,EACA,EAAE,WAAW,sBAAsB,oBAAoB,KAAK;AAC9D;;;AC/BA,SAAS,wBAAwB;AACjC,OAAOC,aAAW;;;ACDlB,SAAS,mBAAmB;AAC5B,SAAS,eAAe,sBAAsB,gBAAgB,mBAAmB;AAEjF,OAAOC,aAAW;;;ACHlB,SAAS,iBAAiB;AAC1B,SAAS,aAAa,2BAA2B;AACjD,SAAS,yBAAyB;AAClC,SAAS,uBAAuB;AA8DhC,IAAI,OAAO,WAAW,0BAA0B,aAAa;AAC3D,aAAW,wBAAwB;AACrC;AAEA,IAAM,eAAe;AAAA,EACnB,MAAM;AAAA,EACN,SAAS;AAAA,EACT,aAAa,QAAQ,IAAI;AAC3B;AAzEA;AAgHO,IAAM,mBAAN,MAAM,iBAAiD;AAAA,EA+H5D,YAAY,SAAiC;AA/HxC;AAIL,SAAQ,UAAsD;AAC9D,SAAQ,gBAA2C;AACnD,SAAQ,0BAAoE;AAC5E,SAAQ,gBAAqC;AAC7C,SAAQ,kBAAoD;AAC5D,SAAQ,qBAA0D;AAClE,SAAQ,gBAAqC;AAC7C,SAAQ,qBAA+C;AACvD,SAAQ,6BAA+D;AACvE,SAAQ,4BAA6D;AACrE,SAAQ,kBAAyC;AACjD,SAAQ,sBAAsB,oBAAI,IAA6C;AAC/E,SAAQ,sBAAsB,oBAAI,IAA6C;AAC/E,SAAQ,2BAA2B,oBAAI,IAAkD;AACzF,SAAQ,0BAA0B,oBAAI,IAAiD;AACvF,SAAQ,mCAAmC,oBAAI,IAA0D;AACzG,SAAQ,kCAAkC,oBAAI,IAAyD;AACvG,SAAQ,oCAAoC,oBAAI,IAA2D;AAC3G,SAAQ,gCAAgC,oBAAI,IAAuD;AACnG,SAAQ,sBAAsB,oBAAI,IAA8C;AAChF,SAAQ,wBAAwB,oBAAI,IAA+C;AACnF,SAAQ,4BAA4B,oBAAI,IAAmD;AAC3F,SAAQ,uBAAuB,oBAAI,IAA8C;AACjF,SAAQ,4BAA4B,oBAAI,IAA8D;AAEtG;AAAA,SAAQ,2BAA2B,oBAAI,IAMrC;AACF,SAAQ,kBAAqC,CAAC;AAE9C,gCAAuB;AACvB;AACA;AACA;AACA,kCAAY,oBAAoB;AAqIhC,0BAAiB,CAAC,SAA0C;AAC1D,YAAM,WAAW,MAAG;AAhSxB;AAgS2B,2BAAK,YAAL,mBAAc,eAAe,UAAS;AAAA;AAC7D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,kBAAkB,QAAQ;AAAA,MACzD;AAAA,IACF;AAEA,0BAAiB,CAAC,SAA0C;AAC1D,YAAM,WAAW,MAAG;AAzSxB;AAyS2B,2BAAK,YAAL,mBAAc,eAAe,UAAS;AAAA;AAC7D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,kBAAkB,QAAQ;AAAA,MACzD;AAAA,IACF;AAEA,+BAAsB,IAAI,SAAkE;AAC1F,YAAM,WAAW,MAAG;AAlTxB;AAkT2B,2BAAK,YAAL,mBAAc,oBAAoB,GAAG,UAAS;AAAA;AACrE,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,uBAAuB,QAAQ;AAAA,MAC9D;AAAA,IACF;AAEA,+BAAsB,IAAI,SAAkE;AAC1F,YAAM,WAAW,MAAG;AA3TxB;AA2T2B,2BAAK,YAAL,mBAAc,oBAAoB,GAAG,UAAS;AAAA;AACrE,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,uBAAuB,QAAQ;AAAA,MAC9D;AAAA,IACF;AAEA,gCAAuB,MAAqB;AAC1C,YAAM,WAAW,MAAG;AApUxB;AAoU2B,2BAAK,YAAL,mBAAc,2BAA0B;AAAA;AAC/D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,wBAAwB,QAAQ;AAAA,MAC/D;AAAA,IACF;AAEA,2CAAkC,MAAqB;AACrD,YAAM,WAAW,MAAG;AA7UxB;AA6U2B,2BAAK,YAAL,mBAAc,sCAAqC;AAAA;AAC1E,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,mCAAmC,QAAQ;AAAA,MAC1E;AAAA,IACF;AAEA,kDAAyC,MAAqB;AAC5D,YAAM,WAAW,MAAG;AAtVxB;AAsV2B,2BAAK,YAAL,mBAAc,6CAA4C;AAAA;AACjF,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,0CAA0C,QAAQ;AAAA,MACjF;AAAA,IACF;AAEA,+BAAsB,MAAqB;AACzC,YAAM,WAAW,MAAG;AA/VxB;AA+V2B,2BAAK,YAAL,mBAAc,0BAAyB;AAAA;AAC9D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,uBAAuB,QAAQ;AAAA,MAC9D;AAAA,IACF;AAEA,sCAA6B,MAAqB;AAChD,YAAM,WAAW,MAAG;AAxWxB;AAwW2B,2BAAK,YAAL,mBAAc,iCAAgC;AAAA;AACrE,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,8BAA8B,QAAQ;AAAA,MACrE;AAAA,IACF;AAEA,uCAA8B,MAAqB;AACjD,YAAM,WAAW,MAAG;AAjXxB;AAiX2B,2BAAK,YAAL,mBAAc,kCAAiC;AAAA;AACtE,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,+BAA+B,QAAQ;AAAA,MACtE;AAAA,IACF;AAEA,4BAAmB,MAAqB;AACtC,YAAM,WAAW,MAAG;AA1XxB;AA0X2B,2BAAK,YAAL,mBAAc,uBAAsB;AAAA;AAC3D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,oBAAoB,QAAQ;AAAA,MAC3D;AAAA,IACF;AAEA,4BAAmB,CAAC,OAA8B;AAChD,YAAM,WAAW,MAAG;AAnYxB;AAmY2B,2BAAK,YAAL,mBAAc,iBAAiB,QAAO;AAAA;AAC7D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,oBAAoB,QAAQ;AAAA,MAC3D;AAAA,IACF;AAEA,iCAAwB,YAAY;AAClC,YAAM,WAAW,MAAG;AA5YxB;AA4Y2B,0BAAK,YAAL,mBAAc;AAAA;AACrC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,SAAS;AAAA,MAChB,OAAO;AACL,aAAK,oBAAoB,IAAI,yBAAyB,QAAQ;AAAA,MAChE;AAAA,IACF;AAsFA,SAAO,KAAkB,IAAI,SAAS;AAxexC;AA0eI,WAAI,UAAK,YAAL,mBAAc,IAAI;AACpB,eAAO,KAAK,QAAQ,GAAG,GAAG,IAAI;AAAA,MAChC,OAAO;AACL,2BAAK,WAAU,GAAG,GAAG,IAAI;AAAA,MAC3B;AAAA,IACF;AAEA,SAAO,MAAoB,IAAI,SAAS;AAjf1C;AAmfI,WAAI,UAAK,YAAL,mBAAc,KAAK;AACrB,eAAO,KAAK,QAAQ,IAAI,GAAG,IAAI;AAAA,MACjC,OAAO;AACL,2BAAK,WAAU,IAAI,GAAG,IAAI;AAAA,MAC5B;AAAA,IACF;AAKA;AAAA;AAAA;AAAA,SAAO,cAAc,CAAC,OAAmB;AACvC,WAAK,gBAAgB,KAAK,EAAE;AAI5B,UAAI,KAAK,QAAQ;AACf,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AAKA;AAAA;AAAA;AAAA,SAAO,aAAa,MAAM;AACxB,WAAK,gBAAgB,QAAQ,QAAM,GAAG,CAAC;AACvC,WAAK,kBAAkB,CAAC;AAAA,IAC1B;AAEA,SAAQ,aAAa,CAAC,YAA6D;AACjF,UAAI,CAAC,SAAS;AACZ,cAAM,IAAI,MAAM,mCAAmC;AAAA,MACrD;AAAA,IACF;AAEA,SAAQ,iBAAiB,CAAC,YAA6D;AArhBzF;AAshBI,UAAI,CAAC,SAAS;AACZ,cAAM,IAAI,MAAM,mCAAmC;AAAA,MACrD;AAEA,WAAK,UAAU;AAEf,WAAK,oBAAoB,QAAQ,QAAM,GAAG,CAAC;AAC3C,WAAK,yBAAyB,QAAQ,CAAC,kBAAkB,aAAa;AACpE,yBAAiB,oBAAoB,QAAQ,YAAY,QAAQ;AAAA,MACnE,CAAC;AAED,+BAAK,WAAU,SAAS,kBAAkB,QAAQ,MAAlD,mBAAqD,QAAQ,cAAY;AAEvE,aAAK,GAAG,UAAU,UAAU,EAAE,QAAQ,KAAK,CAAC;AAAA,MAC9C;AAEA,UAAI,KAAK,kBAAkB,MAAM;AAC/B,gBAAQ,WAAW,KAAK,aAAa;AAAA,MACvC;AAEA,UAAI,KAAK,oBAAoB,MAAM;AACjC,gBAAQ,wBAAwB,KAAK,eAAe;AAAA,MACtD;AAEA,UAAI,KAAK,uBAAuB,MAAM;AACpC,gBAAQ,2BAA2B,KAAK,kBAAkB;AAAA,MAC5D;AAEA,UAAI,KAAK,kBAAkB,MAAM;AAC/B,gBAAQ,WAAW,KAAK,aAAa;AAAA,MACvC;AAEA,UAAI,KAAK,uBAAuB,MAAM;AACpC,gBAAQ,gBAAgB,KAAK,kBAAkB;AAAA,MACjD;AAEA,UAAI,KAAK,4BAA4B,MAAM;AACzC,gBAAQ,8BAA8B,KAAK,uBAAuB;AAAA,MACpE;AAEA,UAAI,KAAK,kBAAkB,MAAM;AAC/B,gBAAQ,iBAAiB,KAAK,aAAa;AAAA,MAC7C;AAEA,UAAI,KAAK,+BAA+B,MAAM;AAC5C,gBAAQ,wBAAwB,KAAK,0BAA0B;AAAA,MACjE;AAEA,UAAI,KAAK,8BAA8B,MAAM;AAC3C,gBAAQ,uBAAuB,KAAK,yBAAyB;AAAA,MAC/D;AAEA,UAAI,KAAK,oBAAoB,MAAM;AACjC,gBAAQ,aAAa,KAAK,eAAe;AAAA,MAC3C;AAEA,WAAK,oBAAoB,QAAQ,CAAC,OAAO,SAAS;AAChD,gBAAQ,YAAY,MAAM,KAAK;AAAA,MACjC,CAAC;AAED,WAAK,oBAAoB,QAAQ,CAAC,OAAO,SAAS;AAChD,gBAAQ,YAAY,MAAM,KAAK;AAAA,MACjC,CAAC;AAED,WAAK,yBAAyB,QAAQ,CAAC,OAAO,SAAS;AACrD,gBAAQ,iBAAiB,MAAM,KAAK;AAAA,MACtC,CAAC;AAED,WAAK,wBAAwB,QAAQ,CAAC,OAAO,SAAS;AACpD,gBAAQ,gBAAgB,MAAM,KAAK;AAAA,MACrC,CAAC;AAED,WAAK,8BAA8B,QAAQ,CAAC,OAAO,SAAS;AAC1D,gBAAQ,sBAAsB,MAAM,KAAK;AAAA,MAC3C,CAAC;AAED,WAAK,sBAAsB,QAAQ,CAAC,OAAO,SAAS;AAClD,gBAAQ,cAAc,MAAM,KAAK;AAAA,MACnC,CAAC;AAED,WAAK,0BAA0B,QAAQ,CAAC,OAAO,SAAS;AACtD,gBAAQ,kBAAkB,MAAM,KAAK;AAAA,MACvC,CAAC;AAED,WAAK,qBAAqB,QAAQ,CAAC,OAAO,SAAS;AACjD,gBAAQ,aAAa,MAAM,KAAK;AAAA,MAClC,CAAC;AAED,WAAK,0BAA0B,QAAQ,CAAC,OAAO,SAAS;AACtD,gBAAQ,6BAA6B,MAAM,KAAK;AAAA,MAClD,CAAC;AAKD,UAAI,OAAO,KAAK,QAAQ,WAAW,aAAa;AAC9C,2BAAK,WAAU,KAAK,YAAY,QAAQ,OAAO;AAAA,MACjD;AAEA,WAAK,WAAW;AAChB,aAAO,KAAK;AAAA,IACd;AAgFA,mCAA0B,OAAO,UAA8B;AAC7D,YAAM,UAAU,MAAM,sBAAK,+CAAL;AAEtB,UAAI,WAAW,6BAA6B,SAAS;AACnD,eAAQ,QAAgB,wBAAwB,KAAK;AAAA,MACvD;AAAA,IACF;AAEA,yCAAgC,OAAO,WAA2C;AAChF,UAAI,KAAK,SAAS;AAChB,eAAO,KAAK,QAAQ,8BAA8B,MAAM;AAAA,MAC1D,OAAO;AACL,eAAO,QAAQ,OAAO;AAAA,MACxB;AAAA,IACF;AAKA;AAAA;AAAA;AAAA,qBAAY,CAAC,WAA2C;AACtD,UAAI,KAAK,SAAS;AAChB,eAAO,KAAK,QAAQ,UAAU,MAAM;AAAA,MACtC,OAAO;AACL,eAAO,QAAQ,OAAO;AAAA,MACxB;AAAA,IACF;AAEA,sBAAa,CAAC,UAAwB;AACpC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,WAAW,KAAK;AAAA,MAC/B,OAAO;AACL,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAEA,uBAAc,MAAM;AAClB,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,YAAY;AAAA,MAC3B,OAAO;AACL,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAEA,mCAA0B,CAAC,UAAqC;AAC9D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,wBAAwB,KAAK;AAAA,MAC5C,OAAO;AACL,aAAK,kBAAkB;AAAA,MACzB;AAAA,IACF;AAEA,oCAA2B,MAAM;AAC/B,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,yBAAyB;AAAA,MACxC,OAAO;AACL,aAAK,kBAAkB;AAAA,MACzB;AAAA,IACF;AAEA,sCAA6B,CAAC,UAAwC;AACpE,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,2BAA2B,KAAK;AAAA,MAC/C,OAAO;AACL,aAAK,qBAAqB;AAAA,MAC5B;AAAA,IACF;AAEA,uCAA8B,MAAM;AAClC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,4BAA4B;AAAA,MAC3C,OAAO;AACL,aAAK,qBAAqB;AAAA,MAC5B;AAAA,IACF;AAEA,yCAAgC,CAAC,UAAkD;AACjF,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,8BAA8B,KAAK;AAAA,MAClD,OAAO;AACL,aAAK,0BAA0B;AAAA,MACjC;AAAA,IACF;AAEA,0CAAiC,MAAM;AACrC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,+BAA+B;AAAA,MAC9C,OAAO;AACL,aAAK,0BAA0B;AAAA,MACjC;AAAA,IACF;AAEA,4BAAmB,CAAC,UAA8B;AAChD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,iBAAiB,KAAK;AAAA,MACrC,OAAO;AACL,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAEA,6BAAoB,MAAM;AACxB,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,kBAAkB;AAAA,MACjC,OAAO;AACL,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAEA,2BAAkB,CAAC,UAA6B;AAC9C,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,gBAAgB,KAAK;AAAA,MACpC,OAAO;AACL,aAAK,qBAAqB;AAAA,MAC5B;AAAA,IACF;AAEA,4BAAmB,MAAM;AACvB,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,iBAAiB;AAAA,MAChC,OAAO;AACL,aAAK,qBAAqB;AAAA,MAC5B;AAAA,IACF;AAEA,mCAA0B,CAAC,UAAqC;AAC9D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,wBAAwB,KAAK;AAAA,MAC5C,OAAO;AACL,aAAK,6BAA6B;AAAA,MACpC;AAAA,IACF;AAEA,oCAA2B,MAAM;AAC/B,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,yBAAyB;AAAA,MACxC,OAAO;AACL,aAAK,6BAA6B;AAAA,MACpC;AAAA,IACF;AAEA,kCAAyB,CAAC,UAAoC;AAC5D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,uBAAuB,KAAK;AAAA,MAC3C,OAAO;AACL,aAAK,4BAA4B;AAAA,MACnC;AAAA,IACF;AAEA,mCAA0B,MAAM;AAC9B,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,wBAAwB;AAAA,MACvC,OAAO;AACL,aAAK,4BAA4B;AAAA,MACnC;AAAA,IACF;AAEA,wBAAe,CAAC,UAA0B;AACxC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,aAAa,KAAK;AAAA,MACjC,OAAO;AACL,aAAK,kBAAkB;AAAA,MACzB;AAAA,IACF;AAEA,yBAAgB,MAAM;AACpB,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,cAAc;AAAA,MAC7B,OAAO;AACL,aAAK,kBAAkB;AAAA,MACzB;AAAA,IACF;AAEA,sBAAa,CAAC,UAAwB;AACpC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,WAAW,KAAK;AAAA,MAC/B,OAAO;AACL,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAEA,uBAAc,MAAM;AAClB,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,YAAY;AAAA,MAC3B,OAAO;AACL,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAEA,uBAAc,CAAC,MAAsB,UAAwB;AAC3D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,YAAY,MAAM,KAAK;AAAA,MACtC,OAAO;AACL,aAAK,oBAAoB,IAAI,MAAM,KAAK;AAAA,MAC1C;AAAA,IACF;AAEA,yBAAgB,CAAC,SAAyB;AACxC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,cAAc,IAAI;AAAA,MACjC,OAAO;AACL,aAAK,oBAAoB,OAAO,IAAI;AAAA,MACtC;AAAA,IACF;AAEA,uBAAc,CAAC,MAAsB,UAAwB;AAC3D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,YAAY,MAAM,KAAK;AAAA,MACtC,OAAO;AACL,aAAK,oBAAoB,IAAI,MAAM,KAAK;AAAA,MAC1C;AAAA,IACF;AAEA,yBAAgB,CAAC,SAAyB;AACxC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,cAAc,IAAI;AAAA,MACjC,OAAO;AACL,aAAK,oBAAoB,OAAO,IAAI;AAAA,MACtC;AAAA,IACF;AAEA,4BAAmB,CAAC,MAAsB,UAA6B;AACrE,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,iBAAiB,MAAM,KAAK;AAAA,MAC3C,OAAO;AACL,aAAK,yBAAyB,IAAI,MAAM,KAAK;AAAA,MAC/C;AAAA,IACF;AAEA,8BAAqB,CAAC,SAAyB;AAC7C,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,mBAAmB,IAAI;AAAA,MACtC,OAAO;AACL,aAAK,yBAAyB,OAAO,IAAI;AAAA,MAC3C;AAAA,IACF;AAEA,oCAA2B,CAAC,MAAsB,UAAqC;AACrF,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,yBAAyB,MAAM,KAAK;AAAA,MACnD,OAAO;AACL,aAAK,iCAAiC,IAAI,MAAM,KAAK;AAAA,MACvD;AAAA,IACF;AAEA,sCAA6B,CAAC,SAAyB;AACrD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,2BAA2B,IAAI;AAAA,MAC9C,OAAO;AACL,aAAK,iCAAiC,OAAO,IAAI;AAAA,MACnD;AAAA,IACF;AAEA,mCAA0B,CAAC,MAAsB,UAAoC;AACnF,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,wBAAwB,MAAM,KAAK;AAAA,MAClD,OAAO;AACL,aAAK,gCAAgC,IAAI,MAAM,KAAK;AAAA,MACtD;AAAA,IACF;AAEA,qCAA4B,CAAC,SAAyB;AACpD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,0BAA0B,IAAI;AAAA,MAC7C,OAAO;AACL,aAAK,gCAAgC,OAAO,IAAI;AAAA,MAClD;AAAA,IACF;AAEA,qCAA4B,CAAC,MAAsB,UAAsC;AACvF,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,0BAA0B,MAAM,KAAK;AAAA,MACpD,OAAO;AACL,aAAK,kCAAkC,IAAI,MAAM,KAAK;AAAA,MACxD;AAAA,IACF;AAEA,uCAA8B,CAAC,SAAyB;AACtD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,4BAA4B,IAAI;AAAA,MAC/C,OAAO;AACL,aAAK,kCAAkC,OAAO,IAAI;AAAA,MACpD;AAAA,IACF;AAEA,uDAA8C,MAAM;AAClD,YAAM,WAAW,MAAG;AAv+BxB;AAu+B2B,0BAAK,YAAL,mBAAc;AAAA;AACrC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,SAAS;AAAA,MAChB,OAAO;AACL,aAAK,oBAAoB,IAAI,+CAA+C,QAAQ;AAAA,MACtF;AAAA,IACF;AAEA,iCAAwB,CAAC,MAAsB,UAAkC;AAC/E,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,sBAAsB,MAAM,KAAK;AAAA,MAChD,OAAO;AACL,aAAK,8BAA8B,IAAI,MAAM,KAAK;AAAA,MACpD;AAAA,IACF;AAEA,mCAA0B,CAAC,SAAyB;AAClD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,wBAAwB,IAAI;AAAA,MAC3C,OAAO;AACL,aAAK,8BAA8B,OAAO,IAAI;AAAA,MAChD;AAAA,IACF;AAEA,2BAAkB,CAAC,MAAsB,oBAAsC;AAC7E,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,gBAAgB,MAAM,eAAe;AAAA,MACpD,OAAO;AACL,aAAK,wBAAwB,IAAI,MAAM,eAAe;AAAA,MACxD;AAAA,IACF;AAEA,6BAAoB,CAAC,SAAyB;AAC5C,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,kBAAkB,IAAI;AAAA,MACrC,OAAO;AACL,aAAK,wBAAwB,OAAO,IAAI;AAAA,MAC1C;AAAA,IACF;AAEA,yBAAgB,CAAC,MAAsB,UAA0B;AAC/D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,cAAc,MAAM,KAAK;AAAA,MACxC,OAAO;AACL,aAAK,sBAAsB,IAAI,MAAM,KAAK;AAAA,MAC5C;AAAA,IACF;AAEA,2BAAkB,CAAC,SAAyB;AAC1C,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,gBAAgB,IAAI;AAAA,MACnC,OAAO;AACL,aAAK,sBAAsB,OAAO,IAAI;AAAA,MACxC;AAAA,IACF;AAEA,6BAAoB,CAAC,MAAsB,UAA8B;AACvE,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,kBAAkB,MAAM,KAAK;AAAA,MAC5C,OAAO;AACL,aAAK,0BAA0B,IAAI,MAAM,KAAK;AAAA,MAChD;AAAA,IACF;AAEA,+BAAsB,CAAC,SAAyB;AAC9C,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,oBAAoB,IAAI;AAAA,MACvC,OAAO;AACL,aAAK,0BAA0B,OAAO,IAAI;AAAA,MAC5C;AAAA,IACF;AAEA,wBAAe,CAAC,MAAsB,UAA+B;AACnE,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,aAAa,MAAM,KAAK;AAAA,MACvC,OAAO;AACL,aAAK,qBAAqB,IAAI,MAAM,KAAK;AAAA,MAC3C;AAAA,IACF;AAEA,0BAAiB,CAAC,SAA+B;AAC/C,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,eAAe,IAAI;AAAA,MAClC,OAAO;AACL,aAAK,qBAAqB,OAAO,IAAI;AAAA,MACvC;AAAA,IACF;AAEA,wCAA+B,CAAC,MAAsB,UAAyC;AAC7F,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,6BAA6B,MAAM,KAAK;AAAA,MACvD,OAAO;AACL,aAAK,0BAA0B,IAAI,MAAM,KAAK;AAAA,MAChD;AAAA,IACF;AAEA,0CAAiC,CAAC,SAAyB;AACzD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,QAAQ,+BAA+B,IAAI;AAAA,MAClD,OAAO;AACL,aAAK,0BAA0B,OAAO,IAAI;AAAA,MAC5C;AAAA,IACF;AAEA,uBAAc,CAAC,aAAoD;AACjE,UAAI,KAAK,SAAS;AAChB,eAAO,KAAK,QAAQ,YAAY,QAAQ;AAAA,MAC1C,OAAO;AACL,cAAM,cAAc,MAAM;AAnlChC;AAolCQ,gBAAM,mBAAmB,KAAK,yBAAyB,IAAI,QAAQ;AACnE,cAAI,kBAAkB;AACpB,mCAAiB,sBAAjB;AACA,iBAAK,yBAAyB,OAAO,QAAQ;AAAA,UAC/C;AAAA,QACF;AACA,aAAK,yBAAyB,IAAI,UAAU,EAAE,aAAa,mBAAmB,OAAU,CAAC;AACzF,eAAO;AAAA,MACT;AAAA,IACF;AAEA,oBAAW,CAAC,OAAe;AACzB,YAAM,WAAW,MAAG;AAhmCxB;AAgmC2B,0BAAK,YAAL,mBAAc,SAAS;AAAA;AAC9C,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAK,SAAS;AAAA,MAChB,OAAO;AACL,aAAK,oBAAoB,IAAI,YAAY,QAAQ;AAAA,MACnD;AAAA,IACF;AAEA,4BAAmB,UAAU,SAAgD;AAC3E,YAAM,WAAW,MAAG;AAzmCxB;AAymC2B,0BAAK,YAAL,mBAAc,iBAAiB,GAAG;AAAA;AACzD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,oBAAoB,QAAQ;AACzD;AAAA,MACF;AAAA,IACF;AAEA,4BAAmB,OAAO,SAAiC;AACzD,YAAM,WAAW,MAAG;AAnnCxB;AAmnC2B,0BAAK,YAAL,mBAAc,iBAAiB;AAAA;AACtD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,oBAAoB,QAAQ;AACzD;AAAA,MACF;AAAA,IACF;AAEA,4BAAmB,OAAO,SAAiC;AACzD,YAAM,WAAW,MAAG;AA7nCxB;AA6nC2B,0BAAK,YAAL,mBAAc,iBAAiB;AAAA;AACtD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,oBAAoB,QAAQ;AACzD;AAAA,MACF;AAAA,IACF;AAEA,iCAAwB,YAAY;AAClC,YAAM,WAAW,MAAG;AAvoCxB;AAuoC2B,0BAAK,YAAL,mBAAc;AAAA;AACrC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,yBAAyB,QAAQ;AAC9D;AAAA,MACF;AAAA,IACF;AAEA,iCAAwB,MAAY;AAClC,YAAM,WAAW,MAAG;AAjpCxB;AAipC2B,0BAAK,YAAL,mBAAc;AAAA;AACrC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,yBAAyB,QAAQ;AAAA,MAChE;AAAA,IACF;AAEA,iCAAwB,MAAM;AAC5B,YAAM,WAAW,MAAG;AA1pCxB;AA0pC2B,0BAAK,YAAL,mBAAc;AAAA;AACrC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,iBAAS;AAAA,MACX,OAAO;AACL,aAAK,oBAAoB,IAAI,yBAAyB,QAAQ;AAAA,MAChE;AAAA,IACF;AAEA,kCAAyB,MAAM;AAC7B,YAAM,WAAW,MAAG;AAnqCxB;AAmqC2B,0BAAK,YAAL,mBAAc;AAAA;AACrC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,iBAAS;AAAA,MACX,OAAO;AACL,aAAK,oBAAoB,IAAI,0BAA0B,QAAQ;AAAA,MACjE;AAAA,IACF;AAEA,yCAAgC,YAAY;AAC1C,YAAM,WAAW,MAAG;AA5qCxB;AA4qC2B,0BAAK,YAAL,mBAAc;AAAA;AACrC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,iCAAiC,QAAQ;AACtE;AAAA,MACF;AAAA,IACF;AAEA,wCAA+B,YAAY;AACzC,YAAM,WAAW,MAAG;AAtrCxB;AAsrC2B,0BAAK,YAAL,mBAAc;AAAA;AACrC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,gCAAgC,QAAQ;AACrE;AAAA,MACF;AAAA,IACF;AAEA,8BAAqB,YAAY;AAC/B,YAAM,WAAW,MAAG;AAhsCxB;AAgsC2B,0BAAK,YAAL,mBAAc;AAAA;AACrC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,sBAAsB,QAAQ;AAC3D;AAAA,MACF;AAAA,IACF;AAEA,kCAAyB,OAAO,WAAqD;AAzsCvF;AA0sCI,YAAM,WAAW,MAAG;AA1sCxB,YAAAC;AA0sC2B,gBAAAA,MAAA,KAAK,YAAL,gBAAAA,IAAc,uBAAuB;AAAA;AAC5D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAK,cAAS,MAAT,mBAAY,MAAM,MAAM;AAAA,QAQ7B;AAAA,MACF,OAAO;AACL,aAAK,oBAAoB,IAAI,0BAA0B,QAAQ;AAAA,MACjE;AAAA,IACF;AAEA,sCAA6B,OAC3B,YACA,WACkB;AA7tCtB;AA8tCI,YAAM,WAAW,MAAG;AA9tCxB,YAAAA;AA8tC2B,gBAAAA,MAAA,KAAK,YAAL,gBAAAA,IAAc,2BAA2B,YAAY;AAAA;AAC5E,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAK,cAAS,MAAT,mBAAY,MAAM,MAAM;AAAA,QAQ7B;AAAA,MACF,OAAO;AACL,aAAK,oBAAoB,IAAI,8BAA8B,QAAQ;AAAA,MACrE;AAAA,IACF;AAEA,uCAA8B,OAAO,WAA8C;AACjF,YAAM,WAAW,MAAG;AA/uCxB;AA+uC2B,0BAAK,YAAL,mBAAc,4BAA4B;AAAA;AACjE,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,+BAA+B,QAAQ;AAAA,MACtE;AAAA,IACF;AAEA,oCAA2B,OAAO,WAA4C;AAC5E,YAAM,WAAW,MAAG;AAxvCxB;AAwvC2B,0BAAK,YAAL,mBAAc,yBAAyB;AAAA;AAC9D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,4BAA4B,QAAQ;AAAA,MACnE;AAAA,IACF;AAEA,0CAAiC,OAAO,WAAkD;AACxF,YAAM,WAAW,MAAG;AAjwCxB;AAiwC2B,0BAAK,YAAL,mBAAc,+BAA+B;AAAA;AACpE,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,kCAAkC,QAAQ;AAAA,MACzE;AAAA,IACF;AAEA,qCAA4B,OAAO,WAA6C;AAC9E,YAAM,WAAW,MAAG;AA1wCxB;AA0wC2B,0BAAK,YAAL,mBAAc,0BAA0B;AAAA;AAC/D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,6BAA6B,QAAQ;AAAA,MACpE;AAAA,IACF;AAEA,gCAAuB,OAAO,WAA4C;AACxE,YAAM,WAAW,MAAG;AAnxCxB;AAmxC2B,0BAAK,YAAL,mBAAc,qBAAqB;AAAA;AAC1D,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,wBAAwB,QAAQ;AAAA,MAC/D;AAAA,IACF;AAEA,wCAA+B,OAAO,WAA+C;AACnF,YAAM,UAAU,MAAM,sBAAK,+CAAL;AACtB,aAAO,QAAQ,6BAA6B,MAAM;AAAA,IACpD;AAEA,8BAAqB,OAAO,WAA2E;AACrG,YAAM,WAAW,MAAG;AAjyCxB;AAiyC2B,0BAAK,YAAL,mBAAc,mBAAmB;AAAA;AACxD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,sBAAsB,QAAQ;AAAA,MAC7D;AAAA,IACF;AAEA,2BAAkB,OAAO,mBAAiE;AACxF,YAAM,WAAW,MAAG;AA1yCxB;AA0yC2B,0BAAK,YAAL,mBAAc,gBAAgB;AAAA;AACrD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,mBAAmB,QAAQ;AAAA,MAC1D;AAAA,IACF;AAEA,wBAAe,OAAO,WAAiE;AACrF,YAAM,WAAW,MAAG;AAnzCxB;AAmzC2B,0BAAK,YAAL,mBAAc,aAAa;AAAA;AAClD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,gBAAgB,QAAQ;AAAA,MACvD;AAAA,IACF;AAEA,mBAAU,UAAU,SAAuC;AACzD,YAAM,WAAW,MAAG;AA5zCxB;AA4zC2B,0BAAK,YAAL,mBAAc,QAAQ,GAAG;AAAA;AAChD,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,WAAW,QAAQ;AAAA,MAClD;AAAA,IACF;AAllCE,UAAM,EAAE,QAAQ,MAAM,eAAe,IAAI,WAAW,CAAC;AACrD,uBAAK,iBAAkB;AACvB,uBAAK,WAAY,mCAAS;AAC1B,uBAAK,SAAU,mCAAS;AACxB,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,OAAO,UAAU,IAAI,YAAY;AAEtC,QAAI,CAAC,KAAK,QAAQ,aAAa;AAC7B,WAAK,QAAQ,cAAc;AAAA,IAC7B;AACA,uBAAK,WAAU,KAAK,YAAY,QAAQ,SAAS;AACjD,uBAAK,WAAU,cAAc,YAAY,QAAQ,YAAW,mBAAK,SAAU,OAAO;AAElF,QAAI,mBAAK,kBAAiB;AACxB,WAAK,KAAK,YAAY;AAAA,IACxB;AAAA,EACF;AAAA,EArGA,IAAI,iBAAyB;AAC3B,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAkB;AAhKxB;AAiKI,aAAO,UAAK,YAAL,mBAAc,WAAU;AAAA,EACjC;AAAA,EAEA,IAAI,SAAsB;AApK5B;AAwKI,QAAI,CAAC,KAAK,SAAS;AACjB,aAAO,mBAAK;AAAA,IACd;AACA,aACE,UAAK,YAAL,mBAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAOb,KAAK,QAAQ,SAAS,UAAU;AAAA,EAErC;AAAA,EAIA,OAAO,oBAAoB,SAAiC;AAK1D,QACE,CAAC,UAAU,KACX,CAAC,mBAAK,cACL,QAAQ,SAAS,mBAAK,WAAU,UAAU,QAAQ;AAAA,IAEnD,mBAAK,WAAU,mBAAmB,QAAQ,gBAC1C;AACA,yBAAK,WAAY,IAAI,iBAAgB,OAAO;AAAA,IAC9C;AACA,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,OAAO,gBAAgB;AACrB,uBAAK,WAAY;AAAA,EACnB;AAAA,EAEA,IAAI,SAAiB;AAGnB,QAAI,OAAO,WAAW,eAAe,OAAO,UAAU;AACpD,aAAO,gBAAgB,mBAAK,UAAS,IAAI,IAAI,OAAO,SAAS,IAAI,GAAG,EAAE;AAAA,IACxE;AACA,QAAI,OAAO,mBAAK,aAAY,YAAY;AACtC,aAAO,aAAa,MAAM,6CAA6C;AAAA,IACzE;AACA,WAAO,mBAAK,YAAW;AAAA,EACzB;AAAA,EAEA,IAAI,WAAmB;AAGrB,QAAI,OAAO,WAAW,eAAe,OAAO,UAAU;AACpD,aAAO,gBAAgB,mBAAK,YAAW,IAAI,IAAI,OAAO,SAAS,IAAI,GAAG,EAAE;AAAA,IAC1E;AACA,QAAI,OAAO,mBAAK,eAAc,YAAY;AACxC,aAAO,aAAa,MAAM,6CAA6C;AAAA,IACzE;AACA,WAAO,mBAAK,cAAa;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,qBAAmD,KAAqC;AA3OjG;AA4OI,aAAO,UAAK,YAAL,mBAAc,yBAAuB,UAAK,YAAL,mBAAc,qBAAqB,OAAO,KAAK,QAAQ,GAAG;AAAA,EACxG;AAAA,EAsBA,IAAI,cAAc;AAnQpB;AAoQI,aAAO,UAAK,YAAL,mBAAc,gBAAe,KAAK,QAAQ,eAAe;AAAA,EAClE;AAAA,EAEA,IAAI,eAAe;AAvQrB;AAwQI,YAAO,UAAK,YAAL,mBAAc;AAAA,EACvB;AAAA,EAEA,IAAI,cAAc;AA3QpB;AA4QI,aAAO,UAAK,YAAL,mBAAc,gBAAe;AAAA,EACtC;AAAA,EAEA,IAAI,oBAAoB;AA/Q1B;AAgRI,aAAO,UAAK,YAAL,mBAAc,sBAAqB,KAAK,QAAQ,mBAAmB;AAAA,EAC5E;AAAA,EAEA,IAAI,cAAc;AAGhB,QAAI,OAAO,WAAW,eAAe,OAAO,UAAU;AACpD,aAAO,gBAAgB,KAAK,QAAQ,aAAa,IAAI,IAAI,OAAO,SAAS,IAAI,GAAG,KAAK;AAAA,IACvF;AACA,QAAI,OAAO,KAAK,QAAQ,gBAAgB,YAAY;AAClD,aAAO,aAAa,MAAM,6CAA6C;AAAA,IACzE;AACA,WAAO;AAAA,EACT;AAAA,EA8HA,MAAM,cAAwE;AA3ZhF;AA4ZI,QAAI,KAAK,SAAS,aAAa,KAAK,QAAQ;AAC1C;AAAA,IACF;AAYA,QAAI,OAAO,WAAW,aAAa;AACjC,aAAO,0BAA0B,mBAAK;AACtC,aAAO,oBAAoB,KAAK;AAChC,aAAO,iBAAiB,KAAK;AAAA,IAC/B;AAEA,QAAI;AACF,UAAI,KAAK,OAAO;AAEd,YAAI;AAEJ,YAAI,cAAyE,KAAK,KAAK,GAAG;AAExF,cAAI,IAAI,KAAK,MAAM,mBAAK,kBAAiB;AAAA,YACvC,UAAU,KAAK;AAAA,YACf,QAAQ,KAAK;AAAA,UACf,CAAQ;AAER,eAAK,WAAW,CAAC;AACjB,gBAAM,EAAE,KAAK,KAAK,OAAO;AAAA,QAC3B,OAAO;AAEL,cAAI,KAAK;AACT,cAAI,CAAC,EAAE,QAAQ;AACb,iBAAK,WAAW,CAAC;AACjB,kBAAM,EAAE,KAAK,KAAK,OAAO;AAAA,UAC3B;AAAA,QACF;AAEA,eAAO,QAAQ;AAAA,MACjB,WAAW,CAAC,uBAAuB;AAEjC,YAAI,CAAC,OAAO,OAAO;AACjB,gBAAM,kBAAkB;AAAA,YACtB,GAAG,KAAK;AAAA,YACR,gBAAgB,mBAAK;AAAA,YACrB,UAAU,KAAK;AAAA,YACf,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK,QAAQ;AAAA,UACtB,CAAC;AAAA,QACH;AAEA,YAAI,CAAC,OAAO,OAAO;AACjB,gBAAM,IAAI,MAAM,+DAA+D;AAAA,QACjF;AAEA,aAAK,WAAW,OAAO,KAAK;AAC5B,cAAM,OAAO,MAAM,KAAK,KAAK,OAAO;AAAA,MACtC;AAEA,WAAI,YAAO,UAAP,mBAAc,QAAQ;AACxB,eAAO,KAAK,eAAe,OAAO,KAAK;AAAA,MACzC;AACA;AAAA,IACF,SAAS,KAAK;AACZ,YAAM,QAAQ;AACd,yBAAK,WAAU,KAAK,YAAY,QAAQ,OAAO;AAC/C,cAAQ,MAAM,MAAM,SAAS,MAAM,WAAW,KAAK;AACnD;AAAA,IACF;AAAA,EACF;AAAA,EAuJA,IAAI,UAAU;AA7nBhB;AA8nBI,YAAO,UAAK,YAAL,mBAAc;AAAA,EACvB;AAAA,EAEA,IAAI,SAAqC;AACvC,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,QAAQ;AAAA,IAEtB,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,IAAI,UAAU;AACZ,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,QAAQ;AAAA,IACtB,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,IAAI,OAAO;AACT,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,QAAQ;AAAA,IACtB,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,IAAI,eAAe;AACjB,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,QAAQ;AAAA,IACtB,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,IAAI,YAAY;AACd,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,QAAQ;AAAA,IACtB,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,IAAI,0BAA+B;AACjC,QAAI,KAAK,SAAS;AAChB,aAAQ,KAAK,QAAgB;AAAA,IAE/B,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,IAAI,aAAsB;AACxB,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,QAAQ;AAAA,IACtB,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,IAAI,UAAgD;AA3rBtD;AA4rBI,YAAO,UAAK,YAAL,mBAAc;AAAA,EACvB;AAAA,EAEA,IAAI,UAAwC;AA/rB9C;AAgsBI,YAAO,UAAK,YAAL,mBAAc;AAAA,EACvB;AAAA,EAEA,8BAA8B,MAAiB;AAC7C,QAAI,KAAK,WAAW,gCAAgC,KAAK,SAAS;AAChE,MAAC,KAAK,QAAgB,2BAA2B,IAAI;AAAA,IACvD,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AA0nBF;AA7qCE;AACA;AACA;AACA;AACA;AA6BO;AAvEF;AAoSL,oBAAe,WAAiD;AAC9D,SAAO,IAAI,QAA6C,aAAW;AAEjE,SAAK,YAAY,MAAM,QAAQ,KAAK,OAAQ,CAAC;AAAA,EAC/C,CAAC;AACH;AAlOA,aAvEW,kBAuEJ;AAvEF,IAAM,kBAAN;;;AD9FA,SAAS,qBAAqB,OAA6B;AAChE,QAAM,EAAE,wBAAwB,cAAc,SAAS,IAAI;AAC3D,QAAM,EAAE,iBAAiB,OAAO,YAAY,IAAI,yBAAyB,sBAAsB;AAE/F,QAAM,CAAC,OAAO,QAAQ,IAAIC,QAAM,SAAoC;AAAA,IAClE,QAAQ,MAAM;AAAA,IACd,SAAS,MAAM;AAAA,IACf,MAAM,MAAM;AAAA,IACZ,cAAc,MAAM;AAAA,EACtB,CAAC;AAED,EAAAA,QAAM,UAAU,MAAM;AACpB,WAAO,MAAM,YAAY,OAAK,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC;AAAA,EAClD,GAAG,CAAC,CAAC;AAEL,QAAM,eAAe,YAAY,MAAM,QAAQ,OAAO,YAAY;AAClE,QAAM,WAAWA,QAAM;AAAA,IACrB,OAAO,EAAE,OAAO,MAAM;AAAA,IACtB;AAAA;AAAA,MAEE;AAAA,IACF;AAAA,EACF;AACA,QAAM,YAAYA,QAAM,QAAQ,OAAO,EAAE,OAAO,MAAM,OAAO,IAAI,CAAC,MAAM,MAAM,CAAC;AAE/E,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,QAAM,UAAUA,QAAM,QAAQ,MAAM;AAClC,UAAM,QAAQ;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,WAAO,EAAE,MAAM;AAAA,EACjB,GAAG,CAAC,WAAW,eAAe,QAAQ,OAAO,OAAO,SAAS,SAAS,uBAAuB,+CAAe,KAAK,CAAC;AAElH,QAAM,aAAaA,QAAM,QAAQ,OAAO,EAAE,OAAO,QAAQ,IAAI,CAAC,WAAW,OAAO,CAAC;AACjF,QAAM,UAAUA,QAAM,QAAQ,OAAO,EAAE,OAAO,KAAK,IAAI,CAAC,QAAQ,IAAI,CAAC;AACrE,QAAM,kBAAkBA,QAAM,QAAQ,MAAM;AAC1C,UAAM,QAAQ;AAAA,MACZ;AAAA,IACF;AACA,WAAO,EAAE,MAAM;AAAA,EACjB,GAAG,CAAC,OAAO,YAAY,CAAC;AAExB;AAAA;AAAA,IAEE,gBAAAA,QAAA,cAAC,uBAAuB,UAAvB,EAAgC,OAAO,YACtC,gBAAAA,QAAA,cAAC,cAAc,UAAd,EAAuB,OAAO,aAC7B,gBAAAA,QAAA,cAAC,eAAe,UAAf,EAAwB,OAAO,cAC9B,gBAAAA,QAAA,cAAC,wBAAsB,GAAG,gBAAgB,SACxC,gBAAAA,QAAA,cAAC,YAAY,UAAZ,EAAqB,OAAO,WAC3B,gBAAAA,QAAA,cAAC,YAAY,UAAZ,EAAqB,OAAO,WAAU,QAAS,CAClD,CACF,CACF,CACF,CACF;AAAA;AAEJ;AAEA,IAAM,2BAA2B,CAAC,YAAoC;AACpE,QAAM,qBAAqBA,QAAM,OAAO,gBAAgB,oBAAoB,OAAO,CAAC;AACpF,QAAM,CAAC,aAAa,cAAc,IAAIA,QAAM,SAAS,mBAAmB,QAAQ,MAAM;AAEtF,EAAAA,QAAM,UAAU,MAAM;AACpB,SAAK,mBAAmB,QAAQ,wBAAwB,EAAE,YAAY,QAAQ,WAAW,CAAC;AAAA,EAC5F,GAAG,CAAC,QAAQ,UAAU,CAAC;AAEvB,EAAAA,QAAM,UAAU,MAAM;AACpB,SAAK,mBAAmB,QAAQ,wBAAwB,EAAE,QAAQ,CAAC;AAAA,EACrE,GAAG,CAAC,QAAQ,YAAY,CAAC;AAEzB,EAAAA,QAAM,UAAU,MAAM;AACpB,uBAAmB,QAAQ,GAAG,UAAU,cAAc;AACtD,WAAO,MAAM;AACX,UAAI,mBAAmB,SAAS;AAC9B,2BAAmB,QAAQ,IAAI,UAAU,cAAc;AAAA,MACzD;AACA,sBAAgB,cAAc;AAAA,IAChC;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,SAAO,EAAE,iBAAiB,mBAAmB,SAAS,YAAY;AACpE;;;ADlHA,SAAS,kBAAkB,OAA2B;AACpD,QAAM,EAAE,cAAc,UAAU,wCAAwC,GAAG,2BAA2B,IAAI;AAC1G,QAAM,EAAE,iBAAiB,IAAI,OAAO,qBAAqB,IAAI;AAE7D,MAAI,CAAC,wBAAwB,CAAC,wCAAwC;AACpE,QAAI,CAAC,gBAAgB;AACnB,mBAAa,gCAAgC;AAAA,IAC/C,WAAW,kBAAkB,CAAC,iBAAiB,cAAc,GAAG;AAC9D,mBAAa,gCAAgC,EAAE,KAAK,eAAe,CAAC;AAAA,IACtE;AAAA,EACF;AAEA,SACE,gBAAAC,QAAA;AAAA,IAAC;AAAA;AAAA,MACC;AAAA,MACA,wBAAwB;AAAA;AAAA,IAEvB;AAAA,EACH;AAEJ;AAEA,IAAM,gBAAgB,6BAA6B,mBAAmB,iBAAiB,2BAA2B;AAElH,cAAc,cAAc;;;AfrB5B,uBAAuB,EAAE,aAAa,qBAAa,CAAC;AACpD,kCAAkC,oBAAY;", "names": ["logErrorInDevMode", "React", "React", "React", "React", "React", "React", "React", "children", "logErrorInDevMode", "React", "React", "logErrorInDevMode", "child", "props", "isReorderItem", "isExternalLink", "useState", "React", "React", "logErrorInDevMode", "React", "React", "React", "React", "React", "React", "React", "React", "React", "React", "_a", "React", "React"]}