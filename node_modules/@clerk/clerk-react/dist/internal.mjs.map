{"version": 3, "sources": ["../src/hooks/useRoutingProps.ts", "../src/internal.ts"], "sourcesContent": ["import type { RoutingOptions } from '@clerk/types';\n\nimport { errorThrower } from '../errors/errorThrower';\nimport { incompatibleRoutingWithPathProvidedError, noPathProvidedError } from '../errors/messages';\n\nexport function useRoutingProps<T extends RoutingOptions>(\n  componentName: string,\n  props: T,\n  routingOptions?: RoutingOptions,\n): T {\n  const path = props.path || routingOptions?.path;\n  const routing = props.routing || routingOptions?.routing || 'path';\n\n  if (routing === 'path') {\n    if (!path) {\n      return errorThrower.throw(noPathProvidedError(componentName));\n    }\n\n    return {\n      ...routingOptions,\n      ...props,\n      routing: 'path',\n    };\n  }\n\n  if (props.path) {\n    return errorThrower.throw(incompatibleRoutingWithPathProvidedError(componentName));\n  }\n\n  return {\n    ...routingOptions,\n    ...props,\n    path: undefined,\n  };\n}\n", "export { setErrorThrowerOptions } from './errors/errorThrower';\nexport { MultisessionAppSupport } from './components/controlComponents';\nexport { useRoutingProps } from './hooks/useRoutingProps';\nexport { useDerivedAuth } from './hooks/useAuth';\n\nexport {\n  clerkJsScriptUrl,\n  buildClerkJsScriptAttributes,\n  setClerkJsLoadingErrorPackageName,\n} from '@clerk/shared/loadClerkJsScript';\n"], "mappings": ";;;;;;;;;;;AAKO,SAAS,gBACd,eACA,OACA,gBACG;AACH,QAAM,OAAO,MAAM,SAAQ,iDAAgB;AAC3C,QAAM,UAAU,MAAM,YAAW,iDAAgB,YAAW;AAE5D,MAAI,YAAY,QAAQ;AACtB,QAAI,CAAC,MAAM;AACT,aAAO,aAAa,MAAM,oBAAoB,aAAa,CAAC;AAAA,IAC9D;AAEA,WAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,MACH,SAAS;AAAA,IACX;AAAA,EACF;AAEA,MAAI,MAAM,MAAM;AACd,WAAO,aAAa,MAAM,yCAAyC,aAAa,CAAC;AAAA,EACnF;AAEA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,MAAM;AAAA,EACR;AACF;;;AC7BA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,OACK;", "names": []}