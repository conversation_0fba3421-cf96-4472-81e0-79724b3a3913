import "./chunk-OANWQR3B.mjs";

// src/errors.ts
import {
  isClerkAPIResponseError,
  isClerkRuntimeError,
  isEmailLinkError,
  isKnownError,
  isMetamaskError,
  isReverificationCancelledError,
  EmailLinkErrorCode,
  EmailLinkErrorCodeStatus
} from "@clerk/shared/error";
export {
  EmailLinkErrorCode,
  EmailLinkErrorCodeStatus,
  isClerkAPIResponseError,
  isClerkRuntimeError,
  isEmailLinkError,
  isKnownError,
  isMetamaskError,
  isReverificationCancelledError
};
//# sourceMappingURL=errors.mjs.map