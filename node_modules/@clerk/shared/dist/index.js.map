{"version": 3, "sources": ["../src/index.ts", "../src/utils/noop.ts", "../src/utils/createDeferredPromise.ts", "../src/utils/allSettled.ts", "../src/utils/instance.ts", "../src/utils/runtimeEnvironment.ts", "../src/utils/logErrorInDevMode.ts", "../src/utils/handleValueOrFn.ts", "../src/utils/fastDeepMerge.ts", "../src/constants.ts", "../src/isomorphicAtob.ts", "../src/isomorphicBtoa.ts", "../src/keys.ts", "../src/apiUrlFromPublishableKey.ts", "../src/browser.ts", "../src/color.ts", "../src/date.ts", "../src/deprecated.ts", "../src/deriveState.ts", "../src/error.ts", "../src/file.ts", "../src/retry.ts", "../src/loadScript.ts", "../src/proxy.ts", "../src/url.ts", "../src/versionSelector.ts", "../src/loadClerkJsScript.ts", "../src/localStorageBroadcastChannel.ts", "../src/workerTimers/workerTimers.worker.ts", "../src/workerTimers/createWorkerTimers.ts", "../src/poller.ts", "../src/underscore.ts", "../src/object.ts", "../src/logger.ts", "../src/devBrowser.ts", "../src/getEnvVariable.ts", "../src/compiled/path-to-regexp/index.js", "../src/pathToRegexp.ts", "../src/pathMatcher.ts", "../src/netlifyCacheHandler.ts"], "sourcesContent": ["/** The following files are not exported on purpose:\n * - cookie.ts\n * - globs.ts\n *\n * The following folders are also not exported on purpose:\n * - react\n *\n * People should always use @clerk/shared/<name> instead\n */\n\nexport * from './utils';\n\nexport { apiUrlFromPublishableKey } from './apiUrlFromPublishableKey';\nexport * from './browser';\nexport * from './color';\nexport * from './constants';\nexport * from './date';\nexport * from './deprecated';\nexport { deriveState } from './deriveState';\nexport * from './error';\nexport * from './file';\nexport { isomorphicAtob } from './isomorphicAtob';\nexport { isomorphicBtoa } from './isomorphicBtoa';\nexport * from './keys';\nexport * from './loadClerkJsScript';\nexport { loadScript } from './loadScript';\nexport { LocalStorageBroadcastChannel } from './localStorageBroadcastChannel';\nexport * from './poller';\nexport * from './proxy';\nexport * from './underscore';\nexport * from './url';\nexport { versionSelector } from './versionSelector';\nexport * from './object';\nexport * from './logger';\nexport { createWorkerTimers } from './workerTimers';\nexport { DEV_BROWSER_JWT_KEY, extractDevBrowserJWTFromURL, setDevBrowserJWTInURL } from './devBrowser';\nexport { getEnvVariable } from './getEnvVariable';\nexport * from './pathMatcher';\nexport * from './netlifyCacheHandler';\n", "export const noop = (..._args: any[]): void => {\n  // do nothing.\n};\n", "import { noop } from './noop';\n\ntype Callback = (val?: any) => void;\n\n/**\n * Create a promise that can be resolved or rejected from\n * outside the Promise constructor callback\n * A ES6 compatible utility that implements `Promise.withResolvers`\n * @internal\n */\nexport const createDeferredPromise = () => {\n  let resolve: Callback = noop;\n  let reject: Callback = noop;\n  const promise = new Promise((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return { promise, resolve, reject };\n};\n", "/**\n * A ES6 compatible utility that implements `Promise.allSettled`\n * @internal\n */\nexport function allSettled<T>(\n  iterable: Iterable<Promise<T>>,\n): Promise<({ status: 'fulfilled'; value: T } | { status: 'rejected'; reason: any })[]> {\n  const promises = Array.from(iterable).map(p =>\n    p.then(\n      value => ({ status: 'fulfilled', value }) as const,\n      reason => ({ status: 'rejected', reason }) as const,\n    ),\n  );\n  return Promise.all(promises);\n}\n", "/**\n * Check if the frontendApi ends with a staging domain\n */\nexport function isStaging(frontendApi: string): boolean {\n  return (\n    frontendApi.endsWith('.lclstage.dev') ||\n    frontendApi.endsWith('.stgstage.dev') ||\n    frontendApi.endsWith('.clerkstage.dev') ||\n    frontendApi.endsWith('.accountsstage.dev')\n  );\n}\n", "export const isDevelopmentEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'development';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n\n  return false;\n};\n\nexport const isTestEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'test';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n\nexport const isProductionEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'production';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n", "import { isDevelopmentEnvironment } from './runtimeEnvironment';\n\nexport const logErrorInDevMode = (message: string) => {\n  if (isDevelopmentEnvironment()) {\n    console.error(`Clerk: ${message}`);\n  }\n};\n", "type VOrFnReturnsV<T> = T | undefined | ((v: URL) => T);\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL): T | undefined;\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL, defaultValue: T): T;\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL, defaultValue?: unknown): unknown {\n  if (typeof value === 'function') {\n    return (value as (v: URL) => T)(url);\n  }\n\n  if (typeof value !== 'undefined') {\n    return value;\n  }\n\n  if (typeof defaultValue !== 'undefined') {\n    return defaultValue;\n  }\n\n  return undefined;\n}\n", "/**\n * Merges 2 objects without creating new object references\n * The merged props will appear on the `target` object\n * If `target` already has a value for a given key it will not be overwritten\n */\nexport const fastDeepMergeAndReplace = (\n  source: Record<any, any> | undefined | null,\n  target: Record<any, any> | undefined | null,\n) => {\n  if (!source || !target) {\n    return;\n  }\n\n  for (const key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key) && source[key] !== null && typeof source[key] === `object`) {\n      if (target[key] === undefined) {\n        target[key] = new (Object.getPrototypeOf(source[key]).constructor)();\n      }\n      fastDeepMergeAndReplace(source[key], target[key]);\n    } else if (Object.prototype.hasOwnProperty.call(source, key)) {\n      target[key] = source[key];\n    }\n  }\n};\n\nexport const fastDeepMergeAndKeep = (\n  source: Record<any, any> | undefined | null,\n  target: Record<any, any> | undefined | null,\n) => {\n  if (!source || !target) {\n    return;\n  }\n\n  for (const key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key) && source[key] !== null && typeof source[key] === `object`) {\n      if (target[key] === undefined) {\n        target[key] = new (Object.getPrototypeOf(source[key]).constructor)();\n      }\n      fastDeepMergeAndKeep(source[key], target[key]);\n    } else if (Object.prototype.hasOwnProperty.call(source, key) && target[key] === undefined) {\n      target[key] = source[key];\n    }\n  }\n};\n", "export const LEGACY_DEV_INSTANCE_SUFFIXES = ['.lcl.dev', '.lclstage.dev', '.lclclerk.com'];\nexport const CURRENT_DEV_INSTANCE_SUFFIXES = ['.accounts.dev', '.accountsstage.dev', '.accounts.lclclerk.com'];\nexport const DEV_OR_STAGING_SUFFIXES = [\n  '.lcl.dev',\n  '.stg.dev',\n  '.lclstage.dev',\n  '.stgstage.dev',\n  '.dev.lclclerk.com',\n  '.stg.lclclerk.com',\n  '.accounts.lclclerk.com',\n  'accountsstage.dev',\n  'accounts.dev',\n];\nexport const LOCAL_ENV_SUFFIXES = ['.lcl.dev', 'lclstage.dev', '.lclclerk.com', '.accounts.lclclerk.com'];\nexport const STAGING_ENV_SUFFIXES = ['.accountsstage.dev'];\nexport const LOCAL_API_URL = 'https://api.lclclerk.com';\nexport const STAGING_API_URL = 'https://api.clerkstage.dev';\nexport const PROD_API_URL = 'https://api.clerk.com';\n\n/**\n * Returns the URL for a static image\n * using the new img.clerk.com service\n */\nexport function iconImageUrl(id: string, format: 'svg' | 'jpeg' = 'svg'): string {\n  return `https://img.clerk.com/static/${id}.${format}`;\n}\n", "/**\n * A function that decodes a string of data which has been encoded using base-64 encoding.\n * Uses `atob` if available, otherwise uses `<PERSON><PERSON><PERSON>` from `global`. If neither are available, returns the data as-is.\n */\nexport const isomorphicAtob = (data: string) => {\n  if (typeof atob !== 'undefined' && typeof atob === 'function') {\n    return atob(data);\n  } else if (typeof global !== 'undefined' && global.Buffer) {\n    return new global.Buffer(data, 'base64').toString();\n  }\n  return data;\n};\n", "export const isomorphicBtoa = (data: string) => {\n  if (typeof btoa !== 'undefined' && typeof btoa === 'function') {\n    return btoa(data);\n  } else if (typeof global !== 'undefined' && global.Buffer) {\n    return new global.Buffer(data).toString('base64');\n  }\n  return data;\n};\n", "import type { Publishable<PERSON>ey } from '@clerk/types';\n\nimport { DEV_OR_STAGING_SUFFIXES, LEGACY_DEV_INSTANCE_SUFFIXES } from './constants';\nimport { isomorphicAtob } from './isomorphicAtob';\nimport { isomorphicBtoa } from './isomorphicBtoa';\n\ntype ParsePublishableKeyOptions = {\n  fatal?: boolean;\n  domain?: string;\n  proxyUrl?: string;\n  isSatellite?: boolean;\n};\n\nconst PUBLISHABLE_KEY_LIVE_PREFIX = 'pk_live_';\nconst PUBLISHABLE_KEY_TEST_PREFIX = 'pk_test_';\n\n// This regex matches the publishable like frontend API keys (e.g. foo-bar-13.clerk.accounts.dev)\nconst PUBLISHABLE_FRONTEND_API_DEV_REGEX = /^(([a-z]+)-){2}([0-9]{1,2})\\.clerk\\.accounts([a-z.]*)(dev|com)$/i;\n\nexport function buildPublishableKey(frontendApi: string): string {\n  const isDevKey =\n    PUBLISHABLE_FRONTEND_API_DEV_REGEX.test(frontendApi) ||\n    (frontendApi.startsWith('clerk.') && LEGACY_DEV_INSTANCE_SUFFIXES.some(s => frontendApi.endsWith(s)));\n  const keyPrefix = isDevKey ? PUBLISHABLE_KEY_TEST_PREFIX : PUBLISHABLE_KEY_LIVE_PREFIX;\n  return `${keyPrefix}${isomorphicBtoa(`${frontendApi}$`)}`;\n}\n\nexport function parsePublishableKey(\n  key: string | undefined,\n  options: ParsePublishableKeyOptions & { fatal: true },\n): PublishableKey;\nexport function parsePublishableKey(\n  key: string | undefined,\n  options?: ParsePublishableKeyOptions,\n): PublishableKey | null;\nexport function parsePublishableKey(\n  key: string | undefined,\n  options: { fatal?: boolean; domain?: string; proxyUrl?: string; isSatellite?: boolean } = {},\n): PublishableKey | null {\n  key = key || '';\n\n  if (!key || !isPublishableKey(key)) {\n    if (options.fatal && !key) {\n      throw new Error(\n        'Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys',\n      );\n    }\n    if (options.fatal && !isPublishableKey(key)) {\n      throw new Error('Publishable key not valid.');\n    }\n    return null;\n  }\n\n  const instanceType = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) ? 'production' : 'development';\n\n  let frontendApi = isomorphicAtob(key.split('_')[2]);\n\n  // TODO(@dimkl): validate packages/clerk-js/src/utils/instance.ts\n  frontendApi = frontendApi.slice(0, -1);\n\n  if (options.proxyUrl) {\n    frontendApi = options.proxyUrl;\n  } else if (instanceType !== 'development' && options.domain && options.isSatellite) {\n    frontendApi = `clerk.${options.domain}`;\n  }\n\n  return {\n    instanceType,\n    frontendApi,\n  };\n}\n\n/**\n * Checks if the provided key is a valid publishable key.\n *\n * @param key - The key to be checked. Defaults to an empty string if not provided.\n * @returns `true` if 'key' is a valid publishable key, `false` otherwise.\n */\nexport function isPublishableKey(key: string = '') {\n  try {\n    const hasValidPrefix = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) || key.startsWith(PUBLISHABLE_KEY_TEST_PREFIX);\n\n    const hasValidFrontendApiPostfix = isomorphicAtob(key.split('_')[2] || '').endsWith('$');\n\n    return hasValidPrefix && hasValidFrontendApiPostfix;\n  } catch {\n    return false;\n  }\n}\n\nexport function createDevOrStagingUrlCache() {\n  const devOrStagingUrlCache = new Map<string, boolean>();\n\n  return {\n    isDevOrStagingUrl: (url: string | URL): boolean => {\n      if (!url) {\n        return false;\n      }\n\n      const hostname = typeof url === 'string' ? url : url.hostname;\n      let res = devOrStagingUrlCache.get(hostname);\n      if (res === undefined) {\n        res = DEV_OR_STAGING_SUFFIXES.some(s => hostname.endsWith(s));\n        devOrStagingUrlCache.set(hostname, res);\n      }\n      return res;\n    },\n  };\n}\n\nexport function isDevelopmentFromPublishableKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('pk_test_');\n}\n\nexport function isProductionFromPublishableKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('pk_live_');\n}\n\nexport function isDevelopmentFromSecretKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('sk_test_');\n}\n\nexport function isProductionFromSecretKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('sk_live_');\n}\n\nexport async function getCookieSuffix(\n  publishableKey: string,\n  subtle: SubtleCrypto = globalThis.crypto.subtle,\n): Promise<string> {\n  const data = new TextEncoder().encode(publishableKey);\n  const digest = await subtle.digest('sha-1', data);\n  const stringDigest = String.fromCharCode(...new Uint8Array(digest));\n  // Base 64 Encoding with URL and Filename Safe Alphabet: https://datatracker.ietf.org/doc/html/rfc4648#section-5\n  return isomorphicBtoa(stringDigest).replace(/\\+/gi, '-').replace(/\\//gi, '_').substring(0, 8);\n}\n\nexport const getSuffixedCookieName = (cookieName: string, cookieSuffix: string): string => {\n  return `${cookieName}_${cookieSuffix}`;\n};\n", "import {\n  LEGACY_DEV_INSTANCE_SUFFIXES,\n  LOCAL_API_URL,\n  LOCAL_ENV_SUFFIXES,\n  PROD_API_URL,\n  STAGING_API_URL,\n  STAGING_ENV_SUFFIXES,\n} from './constants';\nimport { parsePublishableKey } from './keys';\n\n/**\n * Get the correct API url based on the publishable key.\n *\n * @param publishableKey - The publishable key to parse.\n * @returns One of Clerk's API URLs.\n */\nexport const apiUrlFromPublishableKey = (publishableKey: string) => {\n  const frontendApi = parsePublishableKey(publishableKey)?.frontendApi;\n\n  if (frontendApi?.startsWith('clerk.') && LEGACY_DEV_INSTANCE_SUFFIXES.some(suffix => frontendApi?.endsWith(suffix))) {\n    return PROD_API_URL;\n  }\n\n  if (LOCAL_ENV_SUFFIXES.some(suffix => frontendApi?.endsWith(suffix))) {\n    return LOCAL_API_URL;\n  }\n  if (STAGING_ENV_SUFFIXES.some(suffix => frontendApi?.endsWith(suffix))) {\n    return STAGING_API_URL;\n  }\n  return PROD_API_URL;\n};\n", "/**\n * Checks if the window object is defined. You can also use this to check if something is happening on the client side.\n * @returns {boolean}\n */\nexport function inBrowser(): boolean {\n  return typeof window !== 'undefined';\n}\n\nconst botAgents = [\n  'bot',\n  'spider',\n  'crawl',\n  'APIs-Google',\n  'AdsBot',\n  'Googlebot',\n  'mediapartners',\n  'Google Favicon',\n  'FeedFetcher',\n  'Google-Read-Aloud',\n  'DuplexWeb-Google',\n  'googleweblight',\n  'bing',\n  'yandex',\n  'baidu',\n  'duckduck',\n  'yahoo',\n  'ecosia',\n  'ia_archiver',\n  'facebook',\n  'instagram',\n  'pinterest',\n  'reddit',\n  'slack',\n  'twitter',\n  'whatsapp',\n  'youtube',\n  'semrush',\n];\nconst botAgentRegex = new RegExp(botAgents.join('|'), 'i');\n\n/**\n * Checks if the user agent is a bot.\n * @param userAgent - Any user agent string\n * @returns {boolean}\n */\nexport function userAgentIsRobot(userAgent: string): boolean {\n  return !userAgent ? false : botAgentRegex.test(userAgent);\n}\n\n/**\n * Checks if the current environment is a browser and the user agent is not a bot.\n * @returns {boolean}\n */\nexport function isValidBrowser(): boolean {\n  const navigator = inBrowser() ? window?.navigator : null;\n  if (!navigator) {\n    return false;\n  }\n  return !userAgentIsRobot(navigator?.userAgent) && !navigator?.webdriver;\n}\n\n/**\n * Checks if the current environment is a browser and if the navigator is online.\n * @returns {boolean}\n */\nexport function isBrowserOnline(): boolean {\n  const navigator = inBrowser() ? window?.navigator : null;\n  if (!navigator) {\n    return false;\n  }\n\n  const isNavigatorOnline = navigator?.onLine;\n\n  // Being extra safe with the experimental `connection` property, as it is not defined in all browsers\n  // https://developer.mozilla.org/en-US/docs/Web/API/Navigator/connection#browser_compatibility\n  // @ts-ignore\n  const isExperimentalConnectionOnline = navigator?.connection?.rtt !== 0 && navigator?.connection?.downlink !== 0;\n  return isExperimentalConnectionOnline && isNavigatorOnline;\n}\n\n/**\n * Runs `isBrowserOnline` and `isValidBrowser` to check if the current environment is a valid browser and if the navigator is online.\n * @returns {boolean}\n */\nexport function isValidBrowserOnline(): boolean {\n  return isBrowserOnline() && isValidBrowser();\n}\n", "import type { Color, HslaColor, RgbaColor, TransparentColor } from '@clerk/types';\n\nconst IS_HEX_COLOR_REGEX = /^#?([A-F0-9]{6}|[A-F0-9]{3})$/i;\n\nconst IS_RGB_COLOR_REGEX = /^rgb\\((\\d+),\\s*(\\d+),\\s*(\\d+)\\)$/i;\nconst IS_RGBA_COLOR_REGEX = /^rgba\\((\\d+),\\s*(\\d+),\\s*(\\d+)(,\\s*\\d+(\\.\\d+)?)\\)$/i;\n\nconst IS_HSL_COLOR_REGEX = /^hsl\\((\\d+),\\s*([\\d.]+)%,\\s*([\\d.]+)%\\)$/i;\nconst IS_HSLA_COLOR_REGEX = /^hsla\\((\\d+),\\s*([\\d.]+)%,\\s*([\\d.]+)%(,\\s*\\d+(\\.\\d+)?)*\\)$/i;\n\nexport const isValidHexString = (s: string) => {\n  return !!s.match(IS_HEX_COLOR_REGEX);\n};\n\nexport const isValidRgbaString = (s: string) => {\n  return !!(s.match(IS_RGB_COLOR_REGEX) || s.match(IS_RGBA_COLOR_REGEX));\n};\n\nexport const isValidHslaString = (s: string) => {\n  return !!s.match(IS_HSL_COLOR_REGEX) || !!s.match(IS_HSLA_COLOR_REGEX);\n};\n\nexport const isRGBColor = (c: Color): c is RgbaColor => {\n  return typeof c !== 'string' && 'r' in c;\n};\n\nexport const isHSLColor = (c: Color): c is HslaColor => {\n  return typeof c !== 'string' && 'h' in c;\n};\n\nexport const isTransparent = (c: Color): c is TransparentColor => {\n  return c === 'transparent';\n};\n\nexport const hasAlpha = (color: Color): boolean => {\n  return typeof color !== 'string' && color.a != undefined && color.a < 1;\n};\n\nconst CLEAN_HSLA_REGEX = /[hsla()]/g;\nconst CLEAN_RGBA_REGEX = /[rgba()]/g;\n\nexport const stringToHslaColor = (value: string): HslaColor | null => {\n  if (value === 'transparent') {\n    return { h: 0, s: 0, l: 0, a: 0 };\n  }\n\n  if (isValidHexString(value)) {\n    return hexStringToHslaColor(value);\n  }\n\n  if (isValidHslaString(value)) {\n    return parseHslaString(value);\n  }\n\n  if (isValidRgbaString(value)) {\n    return rgbaStringToHslaColor(value);\n  }\n\n  return null;\n};\n\nexport const stringToSameTypeColor = (value: string): Color => {\n  value = value.trim();\n  if (isValidHexString(value)) {\n    return value.startsWith('#') ? value : `#${value}`;\n  }\n\n  if (isValidRgbaString(value)) {\n    return parseRgbaString(value);\n  }\n\n  if (isValidHslaString(value)) {\n    return parseHslaString(value);\n  }\n\n  if (isTransparent(value)) {\n    return value;\n  }\n  return '';\n};\n\nexport const colorToSameTypeString = (color: Color): string | TransparentColor => {\n  if (typeof color === 'string' && (isValidHexString(color) || isTransparent(color))) {\n    return color;\n  }\n\n  if (isRGBColor(color)) {\n    return rgbaColorToRgbaString(color);\n  }\n\n  if (isHSLColor(color)) {\n    return hslaColorToHslaString(color);\n  }\n\n  return '';\n};\n\nexport const hexStringToRgbaColor = (hex: string): RgbaColor => {\n  hex = hex.replace('#', '');\n  const r = parseInt(hex.substring(0, 2), 16);\n  const g = parseInt(hex.substring(2, 4), 16);\n  const b = parseInt(hex.substring(4, 6), 16);\n  return { r, g, b };\n};\n\nconst rgbaColorToRgbaString = (color: RgbaColor): string => {\n  const { a, b, g, r } = color;\n  return color.a === 0 ? 'transparent' : color.a != undefined ? `rgba(${r},${g},${b},${a})` : `rgb(${r},${g},${b})`;\n};\n\nconst hslaColorToHslaString = (color: HslaColor): string => {\n  const { h, s, l, a } = color;\n  const sPerc = Math.round(s * 100);\n  const lPerc = Math.round(l * 100);\n  return color.a === 0\n    ? 'transparent'\n    : color.a != undefined\n      ? `hsla(${h},${sPerc}%,${lPerc}%,${a})`\n      : `hsl(${h},${sPerc}%,${lPerc}%)`;\n};\n\nconst hexStringToHslaColor = (hex: string): HslaColor => {\n  const rgbaString = colorToSameTypeString(hexStringToRgbaColor(hex));\n  return rgbaStringToHslaColor(rgbaString);\n};\n\nconst rgbaStringToHslaColor = (rgba: string): HslaColor => {\n  const rgbaColor = parseRgbaString(rgba);\n  const r = rgbaColor.r / 255;\n  const g = rgbaColor.g / 255;\n  const b = rgbaColor.b / 255;\n\n  const max = Math.max(r, g, b),\n    min = Math.min(r, g, b);\n  let h, s;\n  const l = (max + min) / 2;\n\n  if (max == min) {\n    h = s = 0;\n  } else {\n    const d = max - min;\n    s = l >= 0.5 ? d / (2 - (max + min)) : d / (max + min);\n    switch (max) {\n      case r:\n        h = ((g - b) / d) * 60;\n        break;\n      case g:\n        h = ((b - r) / d + 2) * 60;\n        break;\n      default:\n        h = ((r - g) / d + 4) * 60;\n        break;\n    }\n  }\n\n  const res: HslaColor = { h: Math.round(h), s, l };\n  const a = rgbaColor.a;\n  if (a != undefined) {\n    res.a = a;\n  }\n  return res;\n};\n\nconst parseRgbaString = (str: string): RgbaColor => {\n  const [r, g, b, a] = str\n    .replace(CLEAN_RGBA_REGEX, '')\n    .split(',')\n    .map(c => Number.parseFloat(c));\n  return { r, g, b, a };\n};\n\nconst parseHslaString = (str: string): HslaColor => {\n  const [h, s, l, a] = str\n    .replace(CLEAN_HSLA_REGEX, '')\n    .split(',')\n    .map(c => Number.parseFloat(c));\n  return { h, s: s / 100, l: l / 100, a };\n};\n", "const MILLISECONDS_IN_DAY = 86400000;\n\nexport function dateTo12HourTime(date: Date): string {\n  if (!date) {\n    return '';\n  }\n  return date.toLocaleString('en-US', {\n    hour: '2-digit',\n    minute: 'numeric',\n    hour12: true,\n  });\n}\n\nexport function differenceInCalendarDays(a: Date, b: Date, { absolute = true } = {}): number {\n  if (!a || !b) {\n    return 0;\n  }\n  const utcA = Date.UTC(a.getFullYear(), a.getMonth(), a.getDate());\n  const utcB = Date.UTC(b.getFullYear(), b.getMonth(), b.getDate());\n  const diff = Math.floor((utcB - utcA) / MILLISECONDS_IN_DAY);\n  return absolute ? Math.abs(diff) : diff;\n}\n\nexport function normalizeDate(d: Date | string | number): Date {\n  try {\n    return new Date(d || new Date());\n  } catch {\n    return new Date();\n  }\n}\n\ntype DateFormatRelativeParams = {\n  date: Date | string | number;\n  relativeTo: Date | string | number;\n};\n\nexport type RelativeDateCase = 'previous6Days' | 'lastDay' | 'sameDay' | 'nextDay' | 'next6Days' | 'other';\ntype RelativeDateReturn = { relativeDateCase: RelativeDateCase; date: Date } | null;\n\nexport function formatRelative(props: DateFormatRelativeParams): RelativeDateReturn {\n  const { date, relativeTo } = props;\n  if (!date || !relativeTo) {\n    return null;\n  }\n  const a = normalizeDate(date);\n  const b = normalizeDate(relativeTo);\n  const differenceInDays = differenceInCalendarDays(b, a, { absolute: false });\n\n  if (differenceInDays < -6) {\n    return { relativeDateCase: 'other', date: a };\n  }\n  if (differenceInDays < -1) {\n    return { relativeDateCase: 'previous6Days', date: a };\n  }\n  if (differenceInDays === -1) {\n    return { relativeDateCase: 'lastDay', date: a };\n  }\n  if (differenceInDays === 0) {\n    return { relativeDateCase: 'sameDay', date: a };\n  }\n  if (differenceInDays === 1) {\n    return { relativeDateCase: 'nextDay', date: a };\n  }\n  if (differenceInDays < 7) {\n    return { relativeDateCase: 'next6Days', date: a };\n  }\n  return { relativeDateCase: 'other', date: a };\n}\n\nexport function addYears(initialDate: Date | number | string, yearsToAdd: number): Date {\n  const date = normalizeDate(initialDate);\n  date.setFullYear(date.getFullYear() + yearsToAdd);\n  return date;\n}\n", "import { isProductionEnvironment, isTestEnvironment } from './utils/runtimeEnvironment';\n/**\n * Mark class method / function as deprecated.\n *\n * A console WARNING will be displayed when class method / function is invoked.\n *\n * Examples\n * 1. Deprecate class method\n * class Example {\n *   getSomething = (arg1, arg2) => {\n *       deprecated('Example.getSomething', 'Use `getSomethingElse` instead.');\n *       return `getSomethingValue:${arg1 || '-'}:${arg2 || '-'}`;\n *   };\n * }\n *\n * 2. Deprecate function\n * const getSomething = () => {\n *   deprecated('getSomething', 'Use `getSomethingElse` instead.');\n *   return 'getSomethingValue';\n * };\n */\nconst displayedWarnings = new Set<string>();\nexport const deprecated = (fnName: string, warning: string, key?: string): void => {\n  const hideWarning = isTestEnvironment() || isProductionEnvironment();\n  const messageId = key ?? fnName;\n  if (displayedWarnings.has(messageId) || hideWarning) {\n    return;\n  }\n  displayedWarnings.add(messageId);\n\n  console.warn(\n    `Clerk - DEPRECATION WARNING: \"${fnName}\" is deprecated and will be removed in the next major release.\\n${warning}`,\n  );\n};\n/**\n * Mark class property as deprecated.\n *\n * A console WARNING will be displayed when class property is being accessed.\n *\n * 1. Deprecate class property\n * class Example {\n *   something: string;\n *   constructor(something: string) {\n *     this.something = something;\n *   }\n * }\n *\n * deprecatedProperty(Example, 'something', 'Use `somethingElse` instead.');\n *\n * 2. Deprecate class static property\n * class Example {\n *   static something: string;\n * }\n *\n * deprecatedProperty(Example, 'something', 'Use `somethingElse` instead.', true);\n */\ntype AnyClass = new (...args: any[]) => any;\n\nexport const deprecatedProperty = (cls: AnyClass, propName: string, warning: string, isStatic = false): void => {\n  const target = isStatic ? cls : cls.prototype;\n\n  let value = target[propName];\n  Object.defineProperty(target, propName, {\n    get() {\n      deprecated(propName, warning, `${cls.name}:${propName}`);\n      return value;\n    },\n    set(v: unknown) {\n      value = v;\n    },\n  });\n};\n\n/**\n * Mark object property as deprecated.\n *\n * A console WARNING will be displayed when object property is being accessed.\n *\n * 1. Deprecate object property\n * const obj = { something: 'aloha' };\n *\n * deprecatedObjectProperty(obj, 'something', 'Use `somethingElse` instead.');\n */\nexport const deprecatedObjectProperty = (\n  obj: Record<string, any>,\n  propName: string,\n  warning: string,\n  key?: string,\n): void => {\n  let value = obj[propName];\n  Object.defineProperty(obj, propName, {\n    get() {\n      deprecated(propName, warning, key);\n      return value;\n    },\n    set(v: unknown) {\n      value = v;\n    },\n  });\n};\n", "import type {\n  InitialState,\n  JwtPayload,\n  OrganizationCustomPermissionKey,\n  OrganizationCustomRoleKey,\n  OrganizationResource,\n  Resources,\n  SignedInSessionResource,\n  UserResource,\n} from '@clerk/types';\n\n/**\n * Derives authentication state based on the current rendering context (SSR or client-side).\n */\nexport const deriveState = (clerkOperational: boolean, state: Resources, initialState: InitialState | undefined) => {\n  if (!clerkOperational && initialState) {\n    return deriveFromSsrInitialState(initialState);\n  }\n  return deriveFromClientSideState(state);\n};\n\nconst deriveFromSsrInitialState = (initialState: InitialState) => {\n  const userId = initialState.userId;\n  const user = initialState.user as UserResource;\n  const sessionId = initialState.sessionId;\n  const sessionStatus = initialState.sessionStatus;\n  const sessionClaims = initialState.sessionClaims;\n  const session = initialState.session as SignedInSessionResource;\n  const organization = initialState.organization as OrganizationResource;\n  const orgId = initialState.orgId;\n  const orgRole = initialState.orgRole as OrganizationCustomRoleKey;\n  const orgPermissions = initialState.orgPermissions as OrganizationCustomPermissionKey[];\n  const orgSlug = initialState.orgSlug;\n  const actor = initialState.actor;\n  const factorVerificationAge = initialState.factorVerificationAge;\n\n  return {\n    userId,\n    user,\n    sessionId,\n    session,\n    sessionStatus,\n    sessionClaims,\n    organization,\n    orgId,\n    orgRole,\n    orgPermissions,\n    orgSlug,\n    actor,\n    factorVerificationAge,\n  };\n};\n\nconst deriveFromClientSideState = (state: Resources) => {\n  const userId: string | null | undefined = state.user ? state.user.id : state.user;\n  const user = state.user;\n  const sessionId: string | null | undefined = state.session ? state.session.id : state.session;\n  const session = state.session;\n  const sessionStatus = state.session?.status;\n  const sessionClaims: JwtPayload | null | undefined = state.session\n    ? state.session.lastActiveToken?.jwt?.claims\n    : null;\n  const factorVerificationAge: [number, number] | null = state.session ? state.session.factorVerificationAge : null;\n  const actor = session?.actor;\n  const organization = state.organization;\n  const orgId: string | null | undefined = state.organization ? state.organization.id : state.organization;\n  const orgSlug = organization?.slug;\n  const membership = organization\n    ? user?.organizationMemberships?.find(om => om.organization.id === orgId)\n    : organization;\n  const orgPermissions = membership ? membership.permissions : membership;\n  const orgRole = membership ? membership.role : membership;\n\n  return {\n    userId,\n    user,\n    sessionId,\n    session,\n    sessionStatus,\n    sessionClaims,\n    organization,\n    orgId,\n    orgRole,\n    orgSlug,\n    orgPermissions,\n    actor,\n    factorVerificationAge,\n  };\n};\n", "import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@clerk/types';\n\nexport function isUnauthorizedError(e: any): boolean {\n  const status = e?.status;\n  const code = e?.errors?.[0]?.code;\n  return code === 'authentication_invalid' && status === 401;\n}\n\nexport function isCaptchaError(e: ClerkAPIResponseError): boolean {\n  return ['captcha_invalid', 'captcha_not_enabled', 'captcha_missing_token'].includes(e.errors[0].code);\n}\n\nexport function is4xxError(e: any): boolean {\n  const status = e?.status;\n  return !!status && status >= 400 && status < 500;\n}\n\nexport function isNetworkError(e: any): boolean {\n  // TODO: revise during error handling epic\n  const message = (`${e.message}${e.name}` || '').toLowerCase().replace(/\\s+/g, '');\n  return message.includes('networkerror');\n}\n\ninterface ClerkAPIResponseOptions {\n  data: ClerkAPIErrorJSON[];\n  status: number;\n  clerkTraceId?: string;\n  retryAfter?: number;\n}\n\n// For a comprehensive Metamask error list, please see\n// https://docs.metamask.io/guide/ethereum-provider.html#errors\nexport interface MetamaskError extends Error {\n  code: 4001 | 32602 | 32603;\n  message: string;\n  data?: unknown;\n}\n\nexport function isKnownError(error: any): error is ClerkAPIResponseError | ClerkRuntimeError | MetamaskError {\n  return isClerkAPIResponseError(error) || isMetamaskError(error) || isClerkRuntimeError(error);\n}\n\nexport function isClerkAPIResponseError(err: any): err is ClerkAPIResponseError {\n  return 'clerkError' in err;\n}\n\n/**\n * Checks if the provided error object is an instance of ClerkRuntimeError.\n *\n * @param {any} err - The error object to check.\n * @returns {boolean} True if the error is a ClerkRuntimeError, false otherwise.\n *\n * @example\n * const error = new ClerkRuntimeError('An error occurred');\n * if (isClerkRuntimeError(error)) {\n *   // Handle ClerkRuntimeError\n *   console.error('ClerkRuntimeError:', error.message);\n * } else {\n *   // Handle other errors\n *   console.error('Other error:', error.message);\n * }\n */\nexport function isClerkRuntimeError(err: any): err is ClerkRuntimeError {\n  return 'clerkRuntimeError' in err;\n}\n\nexport function isReverificationCancelledError(err: any) {\n  return isClerkRuntimeError(err) && err.code === 'reverification_cancelled';\n}\n\nexport function isMetamaskError(err: any): err is MetamaskError {\n  return 'code' in err && [4001, 32602, 32603].includes(err.code) && 'message' in err;\n}\n\nexport function isUserLockedError(err: any) {\n  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === 'user_locked';\n}\n\nexport function isPasswordPwnedError(err: any) {\n  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === 'form_password_pwned';\n}\n\nexport function parseErrors(data: ClerkAPIErrorJSON[] = []): ClerkAPIError[] {\n  return data.length > 0 ? data.map(parseError) : [];\n}\n\nexport function parseError(error: ClerkAPIErrorJSON): ClerkAPIError {\n  return {\n    code: error.code,\n    message: error.message,\n    longMessage: error.long_message,\n    meta: {\n      paramName: error?.meta?.param_name,\n      sessionId: error?.meta?.session_id,\n      emailAddresses: error?.meta?.email_addresses,\n      identifiers: error?.meta?.identifiers,\n      zxcvbn: error?.meta?.zxcvbn,\n      plan: error?.meta?.plan,\n    },\n  };\n}\n\nexport function errorToJSON(error: ClerkAPIError | null): ClerkAPIErrorJSON {\n  return {\n    code: error?.code || '',\n    message: error?.message || '',\n    long_message: error?.longMessage,\n    meta: {\n      param_name: error?.meta?.paramName,\n      session_id: error?.meta?.sessionId,\n      email_addresses: error?.meta?.emailAddresses,\n      identifiers: error?.meta?.identifiers,\n      zxcvbn: error?.meta?.zxcvbn,\n      plan: error?.meta?.plan,\n    },\n  };\n}\n\nexport class ClerkAPIResponseError extends Error {\n  clerkError: true;\n\n  status: number;\n  message: string;\n  clerkTraceId?: string;\n  retryAfter?: number;\n\n  errors: ClerkAPIError[];\n\n  constructor(message: string, { data, status, clerkTraceId, retryAfter }: ClerkAPIResponseOptions) {\n    super(message);\n\n    Object.setPrototypeOf(this, ClerkAPIResponseError.prototype);\n\n    this.status = status;\n    this.message = message;\n    this.clerkTraceId = clerkTraceId;\n    this.retryAfter = retryAfter;\n    this.clerkError = true;\n    this.errors = parseErrors(data);\n  }\n\n  public toString = () => {\n    let message = `[${this.name}]\\nMessage:${this.message}\\nStatus:${this.status}\\nSerialized errors: ${this.errors.map(\n      e => JSON.stringify(e),\n    )}`;\n\n    if (this.clerkTraceId) {\n      message += `\\nClerk Trace ID: ${this.clerkTraceId}`;\n    }\n\n    return message;\n  };\n}\n\n/**\n * Custom error class for representing Clerk runtime errors.\n *\n * @class ClerkRuntimeError\n * @example\n *   throw new ClerkRuntimeError('An error occurred', { code: 'password_invalid' });\n */\nexport class ClerkRuntimeError extends Error {\n  clerkRuntimeError: true;\n\n  /**\n   * The error message.\n   *\n   * @type {string}\n   */\n  message: string;\n\n  /**\n   * A unique code identifying the error, can be used for localization.\n   *\n   * @type {string}\n   */\n  code: string;\n\n  constructor(message: string, { code }: { code: string }) {\n    const prefix = '🔒 Clerk:';\n    const regex = new RegExp(prefix.replace(' ', '\\\\s*'), 'i');\n    const sanitized = message.replace(regex, '');\n    const _message = `${prefix} ${sanitized.trim()}\\n\\n(code=\"${code}\")\\n`;\n    super(_message);\n\n    Object.setPrototypeOf(this, ClerkRuntimeError.prototype);\n\n    this.code = code;\n    this.message = _message;\n    this.clerkRuntimeError = true;\n    this.name = 'ClerkRuntimeError';\n  }\n\n  /**\n   * Returns a string representation of the error.\n   *\n   * @returns {string} A formatted string with the error name and message.\n   */\n  public toString = () => {\n    return `[${this.name}]\\nMessage:${this.message}`;\n  };\n}\n\nexport class EmailLinkError extends Error {\n  code: string;\n\n  constructor(code: string) {\n    super(code);\n    this.code = code;\n    this.name = 'EmailLinkError' as const;\n    Object.setPrototypeOf(this, EmailLinkError.prototype);\n  }\n}\n\nexport function isEmailLinkError(err: Error): err is EmailLinkError {\n  return err.name === 'EmailLinkError';\n}\n\n/**\n * @deprecated Use `EmailLinkErrorCodeStatus` instead.\n *\n * @hidden\n */\nexport const EmailLinkErrorCode = {\n  Expired: 'expired',\n  Failed: 'failed',\n  ClientMismatch: 'client_mismatch',\n};\n\nexport const EmailLinkErrorCodeStatus = {\n  Expired: 'expired',\n  Failed: 'failed',\n  ClientMismatch: 'client_mismatch',\n} as const;\n\nconst DefaultMessages = Object.freeze({\n  InvalidProxyUrlErrorMessage: `The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})`,\n  InvalidPublishableKeyErrorMessage: `The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})`,\n  MissingPublishableKeyErrorMessage: `Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,\n  MissingSecretKeyErrorMessage: `Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,\n  MissingClerkProvider: `{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider`,\n});\n\ntype MessageKeys = keyof typeof DefaultMessages;\n\ntype Messages = Record<MessageKeys, string>;\n\ntype CustomMessages = Partial<Messages>;\n\nexport type ErrorThrowerOptions = {\n  packageName: string;\n  customMessages?: CustomMessages;\n};\n\nexport interface ErrorThrower {\n  setPackageName(options: ErrorThrowerOptions): ErrorThrower;\n\n  setMessages(options: ErrorThrowerOptions): ErrorThrower;\n\n  throwInvalidPublishableKeyError(params: { key?: string }): never;\n\n  throwInvalidProxyUrl(params: { url?: string }): never;\n\n  throwMissingPublishableKeyError(): never;\n\n  throwMissingSecretKeyError(): never;\n\n  throwMissingClerkProviderError(params: { source?: string }): never;\n\n  throw(message: string): never;\n}\n\nexport function buildErrorThrower({ packageName, customMessages }: ErrorThrowerOptions): ErrorThrower {\n  let pkg = packageName;\n\n  const messages = {\n    ...DefaultMessages,\n    ...customMessages,\n  };\n\n  function buildMessage(rawMessage: string, replacements?: Record<string, string | number>) {\n    if (!replacements) {\n      return `${pkg}: ${rawMessage}`;\n    }\n\n    let msg = rawMessage;\n    const matches = rawMessage.matchAll(/{{([a-zA-Z0-9-_]+)}}/g);\n\n    for (const match of matches) {\n      const replacement = (replacements[match[1]] || '').toString();\n      msg = msg.replace(`{{${match[1]}}}`, replacement);\n    }\n\n    return `${pkg}: ${msg}`;\n  }\n\n  return {\n    setPackageName({ packageName }: ErrorThrowerOptions): ErrorThrower {\n      if (typeof packageName === 'string') {\n        pkg = packageName;\n      }\n      return this;\n    },\n\n    setMessages({ customMessages }: ErrorThrowerOptions): ErrorThrower {\n      Object.assign(messages, customMessages || {});\n      return this;\n    },\n\n    throwInvalidPublishableKeyError(params: { key?: string }): never {\n      throw new Error(buildMessage(messages.InvalidPublishableKeyErrorMessage, params));\n    },\n\n    throwInvalidProxyUrl(params: { url?: string }): never {\n      throw new Error(buildMessage(messages.InvalidProxyUrlErrorMessage, params));\n    },\n\n    throwMissingPublishableKeyError(): never {\n      throw new Error(buildMessage(messages.MissingPublishableKeyErrorMessage));\n    },\n\n    throwMissingSecretKeyError(): never {\n      throw new Error(buildMessage(messages.MissingSecretKeyErrorMessage));\n    },\n\n    throwMissingClerkProviderError(params: { source?: string }): never {\n      throw new Error(buildMessage(messages.MissingClerkProvider, params));\n    },\n\n    throw(message: string): never {\n      throw new Error(buildMessage(message));\n    },\n  };\n}\n\ntype ClerkWebAuthnErrorCode =\n  // Generic\n  | 'passkey_not_supported'\n  | 'passkey_pa_not_supported'\n  | 'passkey_invalid_rpID_or_domain'\n  | 'passkey_already_exists'\n  | 'passkey_operation_aborted'\n  // Retrieval\n  | 'passkey_retrieval_cancelled'\n  | 'passkey_retrieval_failed'\n  // Registration\n  | 'passkey_registration_cancelled'\n  | 'passkey_registration_failed';\n\nexport class ClerkWebAuthnError extends ClerkRuntimeError {\n  /**\n   * A unique code identifying the error, can be used for localization.\n   */\n  code: ClerkWebAuthnErrorCode;\n\n  constructor(message: string, { code }: { code: ClerkWebAuthnErrorCode }) {\n    super(message, { code });\n    this.code = code;\n  }\n}\n", "/**\n * Read an expected JSON type File.\n *\n * Probably paired with:\n *  <input type='file' accept='application/JSON' ... />\n */\nexport function readJSONFile(file: File): Promise<unknown> {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.addEventListener('load', function () {\n      const result = JSON.parse(reader.result as string);\n      resolve(result);\n    });\n\n    reader.addEventListener('error', reject);\n    reader.readAsText(file);\n  });\n}\n\nconst MimeTypeToExtensionMap = Object.freeze({\n  'image/png': 'png',\n  'image/jpeg': 'jpg',\n  'image/gif': 'gif',\n  'image/webp': 'webp',\n  'image/x-icon': 'ico',\n  'image/vnd.microsoft.icon': 'ico',\n} as const);\n\nexport type SupportedMimeType = keyof typeof MimeTypeToExtensionMap;\n\nexport const extension = (mimeType: SupportedMimeType): string => {\n  return MimeTypeToExtensionMap[mimeType];\n};\n", "type Milliseconds = number;\n\ntype RetryOptions = Partial<{\n  /**\n   * The initial delay before the first retry.\n   * @default 125\n   */\n  initialDelay: Milliseconds;\n  /**\n   * The maximum delay between retries.\n   * The delay between retries will never exceed this value.\n   * If set to 0, the delay will increase indefinitely.\n   * @default 0\n   */\n  maxDelayBetweenRetries: Milliseconds;\n  /**\n   * The multiplier for the exponential backoff.\n   * @default 2\n   */\n  factor: number;\n  /**\n   * A function to determine if the operation should be retried.\n   * The callback accepts the error that was thrown and the number of iterations.\n   * The iterations variable references the number of retries AFTER attempt\n   * that caused the error and starts at 1 (as in, this is the 1st, 2nd, nth retry).\n   * @default (error, iterations) => iterations < 5\n   */\n  shouldRetry: (error: unknown, iterations: number) => boolean;\n  /**\n   * Controls whether the helper should retry the operation immediately once before applying exponential backoff.\n   * The delay for the immediate retry is 100ms.\n   * @default false\n   */\n  retryImmediately: boolean;\n  /**\n   * If true, the intervals will be multiplied by a factor in the range of [1,2].\n   * @default true\n   */\n  jitter: boolean;\n}>;\n\nconst defaultOptions: Required<RetryOptions> = {\n  initialDelay: 125,\n  maxDelayBetweenRetries: 0,\n  factor: 2,\n  shouldRetry: (_: unknown, iteration: number) => iteration < 5,\n  retryImmediately: false,\n  jitter: true,\n};\n\nconst RETRY_IMMEDIATELY_DELAY = 100;\n\nconst sleep = async (ms: Milliseconds) => new Promise(s => setTimeout(s, ms));\n\nconst applyJitter = (delay: Milliseconds, jitter: boolean) => {\n  return jitter ? delay * (1 + Math.random()) : delay;\n};\n\nconst createExponentialDelayAsyncFn = (\n  opts: Required<Pick<RetryOptions, 'initialDelay' | 'maxDelayBetweenRetries' | 'factor' | 'jitter'>>,\n) => {\n  let timesCalled = 0;\n\n  const calculateDelayInMs = () => {\n    const constant = opts.initialDelay;\n    const base = opts.factor;\n    let delay = constant * Math.pow(base, timesCalled);\n    delay = applyJitter(delay, opts.jitter);\n    return Math.min(opts.maxDelayBetweenRetries || delay, delay);\n  };\n\n  return async (): Promise<void> => {\n    await sleep(calculateDelayInMs());\n    timesCalled++;\n  };\n};\n\n/**\n * Retries a callback until it succeeds or the shouldRetry function returns false.\n * See {@link RetryOptions} for the available options.\n */\nexport const retry = async <T>(callback: () => T | Promise<T>, options: RetryOptions = {}): Promise<T> => {\n  let iterations = 0;\n  const { shouldRetry, initialDelay, maxDelayBetweenRetries, factor, retryImmediately, jitter } = {\n    ...defaultOptions,\n    ...options,\n  };\n\n  const delay = createExponentialDelayAsyncFn({\n    initialDelay,\n    maxDelayBetweenRetries,\n    factor,\n    jitter,\n  });\n\n  while (true) {\n    try {\n      return await callback();\n    } catch (e) {\n      iterations++;\n      if (!shouldRetry(e, iterations)) {\n        throw e;\n      }\n      if (retryImmediately && iterations === 1) {\n        await sleep(applyJitter(RETRY_IMMEDIATELY_DELAY, jitter));\n      } else {\n        await delay();\n      }\n    }\n  }\n};\n", "import { retry } from './retry';\n\nconst NO_DOCUMENT_ERROR = 'loadScript cannot be called when document does not exist';\nconst NO_SRC_ERROR = 'loadScript cannot be called without a src';\n\ntype LoadScriptOptions = {\n  async?: boolean;\n  defer?: boolean;\n  crossOrigin?: 'anonymous' | 'use-credentials';\n  nonce?: string;\n  beforeLoad?: (script: HTMLScriptElement) => void;\n};\n\nexport async function loadScript(src = '', opts: LoadScriptOptions): Promise<HTMLScriptElement> {\n  const { async, defer, beforeLoad, crossOrigin, nonce } = opts || {};\n\n  const load = () => {\n    return new Promise<HTMLScriptElement>((resolve, reject) => {\n      if (!src) {\n        reject(new Error(NO_SRC_ERROR));\n      }\n\n      if (!document || !document.body) {\n        reject(NO_DOCUMENT_ERROR);\n      }\n\n      const script = document.createElement('script');\n\n      if (crossOrigin) script.setAttribute('crossorigin', crossOrigin);\n      script.async = async || false;\n      script.defer = defer || false;\n\n      script.addEventListener('load', () => {\n        script.remove();\n        resolve(script);\n      });\n\n      script.addEventListener('error', () => {\n        script.remove();\n        reject();\n      });\n\n      script.src = src;\n      script.nonce = nonce;\n      beforeLoad?.(script);\n      document.body.appendChild(script);\n    });\n  };\n\n  return retry(load, { shouldRetry: (_, iterations) => iterations <= 5 });\n}\n", "export function isValidProxyUrl(key: string | undefined) {\n  if (!key) {\n    return true;\n  }\n\n  return isHttpOrHttps(key) || isProxyUrlRelative(key);\n}\n\nexport function isHttpOrHttps(key: string | undefined) {\n  return /^http(s)?:\\/\\//.test(key || '');\n}\n\nexport function isProxyUrlRelative(key: string) {\n  return key.startsWith('/');\n}\n\nexport function proxyUrlToAbsoluteURL(url: string | undefined): string {\n  if (!url) {\n    return '';\n  }\n  return isProxyUrlRelative(url) ? new URL(url, window.location.origin).toString() : url;\n}\n", "import { CURRENT_DEV_INSTANCE_SUFFIXES, LEGACY_DEV_INSTANCE_SUFFIXES } from './constants';\nimport { isStaging } from './utils/instance';\n\nexport function parseSearchParams(queryString = ''): URLSearchParams {\n  if (queryString.startsWith('?')) {\n    queryString = queryString.slice(1);\n  }\n  return new URLSearchParams(queryString);\n}\n\nexport function stripScheme(url = ''): string {\n  return (url || '').replace(/^.+:\\/\\//, '');\n}\n\nexport function addClerkPrefix(str: string | undefined) {\n  if (!str) {\n    return '';\n  }\n  let regex;\n  if (str.match(/^(clerk\\.)+\\w*$/)) {\n    regex = /(clerk\\.)*(?=clerk\\.)/;\n  } else if (str.match(/\\.clerk.accounts/)) {\n    return str;\n  } else {\n    regex = /^(clerk\\.)*/gi;\n  }\n\n  const stripped = str.replace(regex, '');\n  return `clerk.${stripped}`;\n}\n\n/**\n *\n * Retrieve the clerk-js major tag using the major version from the pkgVersion\n * param or use the frontendApi to determine if the canary tag should be used.\n * The default tag is `latest`.\n */\nexport const getClerkJsMajorVersionOrTag = (frontendApi: string, version?: string) => {\n  if (!version && isStaging(frontendApi)) {\n    return 'canary';\n  }\n\n  if (!version) {\n    return 'latest';\n  }\n\n  return version.split('.')[0] || 'latest';\n};\n\n/**\n *\n * Retrieve the clerk-js script url from the frontendApi and the major tag\n * using the {@link getClerkJsMajorVersionOrTag} or a provided clerkJSVersion tag.\n */\nexport const getScriptUrl = (frontendApi: string, { clerkJSVersion }: { clerkJSVersion?: string }) => {\n  const noSchemeFrontendApi = frontendApi.replace(/http(s)?:\\/\\//, '');\n  const major = getClerkJsMajorVersionOrTag(frontendApi, clerkJSVersion);\n  return `https://${noSchemeFrontendApi}/npm/@clerk/clerk-js@${clerkJSVersion || major}/dist/clerk.browser.js`;\n};\n\n// Returns true for hosts such as:\n// * accounts.foo.bar-13.lcl.dev\n// * accounts.foo.bar-13.lclstage.dev\n// * accounts.foo.bar-13.dev.lclclerk.com\nexport function isLegacyDevAccountPortalOrigin(host: string): boolean {\n  return LEGACY_DEV_INSTANCE_SUFFIXES.some(legacyDevSuffix => {\n    return host.startsWith('accounts.') && host.endsWith(legacyDevSuffix);\n  });\n}\n\n// Returns true for hosts such as:\n// * foo-bar-13.accounts.dev\n// * foo-bar-13.accountsstage.dev\n// * foo-bar-13.accounts.lclclerk.com\n// But false for:\n// * foo-bar-13.clerk.accounts.lclclerk.com\nexport function isCurrentDevAccountPortalOrigin(host: string): boolean {\n  return CURRENT_DEV_INSTANCE_SUFFIXES.some(currentDevSuffix => {\n    return host.endsWith(currentDevSuffix) && !host.endsWith('.clerk' + currentDevSuffix);\n  });\n}\n\n/* Functions below are taken from https://github.com/unjs/ufo/blob/main/src/utils.ts. LICENSE: MIT */\n\nconst TRAILING_SLASH_RE = /\\/$|\\/\\?|\\/#/;\n\nexport function hasTrailingSlash(input = '', respectQueryAndFragment?: boolean): boolean {\n  if (!respectQueryAndFragment) {\n    return input.endsWith('/');\n  }\n  return TRAILING_SLASH_RE.test(input);\n}\n\nexport function withTrailingSlash(input = '', respectQueryAndFragment?: boolean): string {\n  if (!respectQueryAndFragment) {\n    return input.endsWith('/') ? input : input + '/';\n  }\n  if (hasTrailingSlash(input, true)) {\n    return input || '/';\n  }\n  let path = input;\n  let fragment = '';\n  const fragmentIndex = input.indexOf('#');\n  if (fragmentIndex >= 0) {\n    path = input.slice(0, fragmentIndex);\n    fragment = input.slice(fragmentIndex);\n    if (!path) {\n      return fragment;\n    }\n  }\n  const [s0, ...s] = path.split('?');\n  return s0 + '/' + (s.length > 0 ? `?${s.join('?')}` : '') + fragment;\n}\n\nexport function withoutTrailingSlash(input = '', respectQueryAndFragment?: boolean): string {\n  if (!respectQueryAndFragment) {\n    return (hasTrailingSlash(input) ? input.slice(0, -1) : input) || '/';\n  }\n  if (!hasTrailingSlash(input, true)) {\n    return input || '/';\n  }\n  let path = input;\n  let fragment = '';\n  const fragmentIndex = input.indexOf('#');\n  if (fragmentIndex >= 0) {\n    path = input.slice(0, fragmentIndex);\n    fragment = input.slice(fragmentIndex);\n  }\n  const [s0, ...s] = path.split('?');\n  return (s0.slice(0, -1) || '/') + (s.length > 0 ? `?${s.join('?')}` : '') + fragment;\n}\n\nexport function hasLeadingSlash(input = ''): boolean {\n  return input.startsWith('/');\n}\n\nexport function withoutLeadingSlash(input = ''): string {\n  return (hasLeadingSlash(input) ? input.slice(1) : input) || '/';\n}\n\nexport function withLeadingSlash(input = ''): string {\n  return hasLeadingSlash(input) ? input : '/' + input;\n}\n\nexport function cleanDoubleSlashes(input = ''): string {\n  return input\n    .split('://')\n    .map(string_ => string_.replace(/\\/{2,}/g, '/'))\n    .join('://');\n}\n\nexport function isNonEmptyURL(url: string) {\n  return url && url !== '/';\n}\n\nconst JOIN_LEADING_SLASH_RE = /^\\.?\\//;\n\nexport function joinURL(base: string, ...input: string[]): string {\n  let url = base || '';\n\n  for (const segment of input.filter(url => isNonEmptyURL(url))) {\n    if (url) {\n      // TODO: Handle .. when joining\n      const _segment = segment.replace(JOIN_LEADING_SLASH_RE, '');\n      url = withTrailingSlash(url) + _segment;\n    } else {\n      url = segment;\n    }\n  }\n\n  return url;\n}\n\n/* Code below is taken from https://github.com/vercel/next.js/blob/fe7ff3f468d7651a92865350bfd0f16ceba27db5/packages/next/src/shared/lib/utils.ts. LICENSE: MIT */\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url);\n", "/**\n * This version selector is a bit complicated, so here is the flow:\n * 1. Use the clerkJSVersion prop on the provider\n * 2. Use the exact `@clerk/clerk-js` version if it is a `@snapshot` prerelease\n * 3. Use the prerelease tag of `@clerk/clerk-js` or the packageVersion provided\n * 4. Fallback to the major version of `@clerk/clerk-js` or the packageVersion provided\n * @param clerkJSVersion - The optional clerkJSVersion prop on the provider\n * @param packageVersion - The version of `@clerk/clerk-js` that will be used if an explicit version is not provided\n * @returns The npm tag, version or major version to use\n */\nexport const versionSelector = (clerkJSVersion: string | undefined, packageVersion = JS_PACKAGE_VERSION) => {\n  if (clerkJSVersion) {\n    return clerkJSVersion;\n  }\n\n  const prereleaseTag = getPrereleaseTag(packageVersion);\n  if (prereleaseTag) {\n    if (prereleaseTag === 'snapshot') {\n      return JS_PACKAGE_VERSION;\n    }\n\n    return prereleaseTag;\n  }\n\n  return getMajorVersion(packageVersion);\n};\n\nconst getPrereleaseTag = (packageVersion: string) =>\n  packageVersion\n    .trim()\n    .replace(/^v/, '')\n    .match(/-(.+?)(\\.|$)/)?.[1];\n\nexport const getMajorVersion = (packageVersion: string) => packageVersion.trim().replace(/^v/, '').split('.')[0];\n", "import type { ClerkOptions, SDKMetadata, Without } from '@clerk/types';\n\nimport { buildErrorThrower } from './error';\nimport { createDevOrStagingUrlCache, parsePublishableKey } from './keys';\nimport { loadScript } from './loadScript';\nimport { isValidProxyUrl, proxyUrlToAbsoluteURL } from './proxy';\nimport { addClerkPrefix } from './url';\nimport { versionSelector } from './versionSelector';\n\nconst FAILED_TO_LOAD_ERROR = 'Clerk: Failed to load Clerk';\n\nconst { isDevOrStagingUrl } = createDevOrStagingUrlCache();\n\nconst errorThrower = buildErrorThrower({ packageName: '@clerk/shared' });\n\n/**\n * Sets the package name for error messages during ClerkJS script loading.\n *\n * @example\n * setClerkJsLoadingErrorPackageName('@clerk/clerk-react');\n */\nexport function setClerkJsLoadingErrorPackageName(packageName: string) {\n  errorThrower.setPackageName({ packageName });\n}\n\ntype LoadClerkJsScriptOptions = Without<ClerkOptions, 'isSatellite'> & {\n  publishableKey: string;\n  clerkJSUrl?: string;\n  clerkJSVariant?: 'headless' | '';\n  clerkJSVersion?: string;\n  sdkMetadata?: SDKMetadata;\n  proxyUrl?: string;\n  domain?: string;\n  nonce?: string;\n};\n\n/**\n * Hotloads the Clerk JS script.\n *\n * Checks for an existing Clerk JS script. If found, it returns a promise\n * that resolves when the script loads. If not found, it uses the provided options to\n * build the Clerk JS script URL and load the script.\n *\n * @param opts - The options used to build the Clerk JS script URL and load the script.\n *               Must include a `publishableKey` if no existing script is found.\n *\n * @example\n * loadClerkJsScript({ publishableKey: 'pk_' });\n */\nconst loadClerkJsScript = async (opts?: LoadClerkJsScriptOptions) => {\n  const existingScript = document.querySelector<HTMLScriptElement>('script[data-clerk-js-script]');\n\n  if (existingScript) {\n    return new Promise((resolve, reject) => {\n      existingScript.addEventListener('load', () => {\n        resolve(existingScript);\n      });\n\n      existingScript.addEventListener('error', () => {\n        reject(FAILED_TO_LOAD_ERROR);\n      });\n    });\n  }\n\n  if (!opts?.publishableKey) {\n    errorThrower.throwMissingPublishableKeyError();\n    return;\n  }\n\n  return loadScript(clerkJsScriptUrl(opts), {\n    async: true,\n    crossOrigin: 'anonymous',\n    nonce: opts.nonce,\n    beforeLoad: applyClerkJsScriptAttributes(opts),\n  }).catch(() => {\n    throw new Error(FAILED_TO_LOAD_ERROR);\n  });\n};\n\n/**\n * Generates a Clerk JS script URL.\n *\n * @param opts - The options to use when building the Clerk JS script URL.\n *\n * @example\n * clerkJsScriptUrl({ publishableKey: 'pk_' });\n */\nconst clerkJsScriptUrl = (opts: LoadClerkJsScriptOptions) => {\n  const { clerkJSUrl, clerkJSVariant, clerkJSVersion, proxyUrl, domain, publishableKey } = opts;\n\n  if (clerkJSUrl) {\n    return clerkJSUrl;\n  }\n\n  let scriptHost = '';\n  if (!!proxyUrl && isValidProxyUrl(proxyUrl)) {\n    scriptHost = proxyUrlToAbsoluteURL(proxyUrl).replace(/http(s)?:\\/\\//, '');\n  } else if (domain && !isDevOrStagingUrl(parsePublishableKey(publishableKey)?.frontendApi || '')) {\n    scriptHost = addClerkPrefix(domain);\n  } else {\n    scriptHost = parsePublishableKey(publishableKey)?.frontendApi || '';\n  }\n\n  const variant = clerkJSVariant ? `${clerkJSVariant.replace(/\\.+$/, '')}.` : '';\n  const version = versionSelector(clerkJSVersion);\n  return `https://${scriptHost}/npm/@clerk/clerk-js@${version}/dist/clerk.${variant}browser.js`;\n};\n\n/**\n * Builds an object of Clerk JS script attributes.\n */\nconst buildClerkJsScriptAttributes = (options: LoadClerkJsScriptOptions) => {\n  const obj: Record<string, string> = {};\n\n  if (options.publishableKey) {\n    obj['data-clerk-publishable-key'] = options.publishableKey;\n  }\n\n  if (options.proxyUrl) {\n    obj['data-clerk-proxy-url'] = options.proxyUrl;\n  }\n\n  if (options.domain) {\n    obj['data-clerk-domain'] = options.domain;\n  }\n\n  if (options.nonce) {\n    obj.nonce = options.nonce;\n  }\n\n  return obj;\n};\n\nconst applyClerkJsScriptAttributes = (options: LoadClerkJsScriptOptions) => (script: HTMLScriptElement) => {\n  const attributes = buildClerkJsScriptAttributes(options);\n  for (const attribute in attributes) {\n    script.setAttribute(attribute, attributes[attribute]);\n  }\n};\n\nexport { loadClerkJsScript, buildClerkJsScriptAttributes, clerkJsScriptUrl };\nexport type { LoadClerkJsScriptOptions };\n", "type Listener<T> = (e: MessageEvent<T>) => void;\n\nconst KEY_PREFIX = '__lsbc__';\n\nexport class LocalStorageBroadcastChannel<E> {\n  private readonly eventTarget = window;\n  private readonly channelKey: string;\n\n  constructor(name: string) {\n    this.channelKey = KEY_PREFIX + name;\n    this.setupLocalStorageListener();\n  }\n\n  public postMessage = (data: E): void => {\n    if (typeof window === 'undefined') {\n      // Silently do nothing\n      return;\n    }\n\n    try {\n      window.localStorage.setItem(this.channelKey, JSON.stringify(data));\n      window.localStorage.removeItem(this.channelKey);\n    } catch {\n      // Silently do nothing\n    }\n  };\n\n  public addEventListener = (eventName: 'message', listener: Listener<E>): void => {\n    this.eventTarget.addEventListener(this.prefixEventName(eventName), e => {\n      listener(e as MessageEvent);\n    });\n  };\n\n  private setupLocalStorageListener = () => {\n    const notifyListeners = (e: StorageEvent) => {\n      if (e.key !== this.channelKey || !e.newValue) {\n        return;\n      }\n\n      try {\n        const data = JSON.parse(e.newValue || '');\n        const event = new MessageEvent(this.prefixEventName('message'), {\n          data,\n        });\n        this.eventTarget.dispatchEvent(event);\n      } catch {\n        //\n      }\n    };\n\n    window.addEventListener('storage', notifyListeners);\n  };\n\n  private prefixEventName(eventName: string): string {\n    return this.channelKey + eventName;\n  }\n}\n", "const respond=r=>{self.postMessage(r)},workerToTabIds={};self.addEventListener(\"message\",r=>{const e=r.data;switch(e.type){case\"setTimeout\":workerToTabIds[e.id]=setTimeout(()=>{respond({id:e.id}),delete workerToTabIds[e.id]},e.ms);break;case\"clearTimeout\":workerToTabIds[e.id]&&(clearTimeout(workerToTabIds[e.id]),delete workerToTabIds[e.id]);break;case\"setInterval\":workerToTabIds[e.id]=setInterval(()=>{respond({id:e.id})},e.ms);break;case\"clearInterval\":workerToTabIds[e.id]&&(clearInterval(workerToTabIds[e.id]),delete workerToTabIds[e.id]);break}});\n", "import { noop } from '../utils/noop';\nimport type {\n  WorkerClearTimeout,\n  WorkerSetTimeout,\n  WorkerTimeoutCallback,\n  WorkerTimerEvent,\n  WorkerTimerId,\n  WorkerTimerResponseEvent,\n} from './workerTimers.types';\n// @ts-ignore\n// eslint-disable-next-line import/default\nimport pollerWorkerSource from './workerTimers.worker';\n\nconst createWebWorker = (source: string, opts: ConstructorParameters<typeof Worker>[1] = {}): Worker | null => {\n  if (typeof Worker === 'undefined') {\n    return null;\n  }\n\n  try {\n    const blob = new Blob([source], { type: 'application/javascript; charset=utf-8' });\n    const workerScript = globalThis.URL.createObjectURL(blob);\n    return new Worker(workerScript, opts);\n  } catch {\n    console.warn('Clerk: Cannot create worker from blob. Consider adding worker-src blob:; to your CSP');\n    return null;\n  }\n};\n\nconst fallbackTimers = () => {\n  const setTimeout = globalThis.setTimeout.bind(globalThis) as WorkerSetTimeout;\n  const setInterval = globalThis.setInterval.bind(globalThis) as WorkerSetTimeout;\n  const clearTimeout = globalThis.clearTimeout.bind(globalThis) as WorkerClearTimeout;\n  const clearInterval = globalThis.clearInterval.bind(globalThis) as WorkerClearTimeout;\n  return { setTimeout, setInterval, clearTimeout, clearInterval, cleanup: noop };\n};\n\nexport const createWorkerTimers = () => {\n  let id = 0;\n  const generateId = () => id++;\n  const callbacks = new Map<WorkerTimerId, WorkerTimeoutCallback>();\n  const post = (w: Worker | null, p: WorkerTimerEvent) => w?.postMessage(p);\n  const handleMessage = (e: MessageEvent<WorkerTimerResponseEvent>) => {\n    callbacks.get(e.data.id)?.();\n  };\n\n  let worker = createWebWorker(pollerWorkerSource, { name: 'clerk-timers' });\n  worker?.addEventListener('message', handleMessage);\n\n  if (!worker) {\n    return fallbackTimers();\n  }\n\n  const init = () => {\n    if (!worker) {\n      worker = createWebWorker(pollerWorkerSource, { name: 'clerk-timers' });\n      worker?.addEventListener('message', handleMessage);\n    }\n  };\n\n  const cleanup = () => {\n    if (worker) {\n      worker.terminate();\n      worker = null;\n      callbacks.clear();\n    }\n  };\n\n  const setTimeout: WorkerSetTimeout = (cb, ms) => {\n    init();\n    const id = generateId();\n    callbacks.set(id, () => {\n      cb();\n      callbacks.delete(id);\n    });\n    post(worker, { type: 'setTimeout', id, ms });\n    return id;\n  };\n\n  const setInterval: WorkerSetTimeout = (cb, ms) => {\n    init();\n    const id = generateId();\n    callbacks.set(id, cb);\n    post(worker, { type: 'setInterval', id, ms });\n    return id;\n  };\n\n  const clearTimeout: WorkerClearTimeout = id => {\n    init();\n    callbacks.delete(id);\n    post(worker, { type: 'clearTimeout', id });\n  };\n\n  const clearInterval: WorkerClearTimeout = id => {\n    init();\n    callbacks.delete(id);\n    post(worker, { type: 'clearInterval', id });\n  };\n\n  return { setTimeout, setInterval, clearTimeout, clearInterval, cleanup };\n};\n", "import { createWorkerTimers } from './workerTimers';\n\nexport type PollerStop = () => void;\nexport type PollerCallback = (stop: PollerStop) => Promise<unknown>;\nexport type PollerRun = (cb: PollerCallback) => Promise<void>;\n\ntype PollerOptions = {\n  delayInMs: number;\n};\n\nexport type Poller = {\n  run: PollerRun;\n  stop: PollerStop;\n};\n\nexport function Poller({ delayInMs }: PollerOptions = { delayInMs: 1000 }): Poller {\n  const workerTimers = createWorkerTimers();\n\n  let timerId: number | undefined;\n  let stopped = false;\n\n  const stop: PollerStop = () => {\n    if (timerId) {\n      workerTimers.clearTimeout(timerId);\n      workerTimers.cleanup();\n    }\n    stopped = true;\n  };\n\n  const run: PollerRun = async cb => {\n    stopped = false;\n    await cb(stop);\n    if (stopped) {\n      return;\n    }\n\n    timerId = workerTimers.setTimeout(() => {\n      void run(cb);\n    }, delayInMs) as any as number;\n  };\n\n  return { run, stop };\n}\n", "/**\n * Convert words to a sentence.\n *\n * @param items - An array of words to be joined.\n * @returns A string with the items joined by a comma and the last item joined by \", or\".\n */\nexport const toSentence = (items: string[]): string => {\n  // TODO: Once Safari supports it, use Intl.ListFormat\n  if (items.length == 0) {\n    return '';\n  }\n  if (items.length == 1) {\n    return items[0];\n  }\n  let sentence = items.slice(0, -1).join(', ');\n  sentence += `, or ${items.slice(-1)}`;\n  return sentence;\n};\n\nconst IP_V4_ADDRESS_REGEX =\n  /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n\n/**\n * Checks if a string is a valid IPv4 address.\n *\n * @returns True if the string is a valid IPv4 address, false otherwise.\n */\nexport function isIPV4Address(str: string | undefined | null): boolean {\n  return IP_V4_ADDRESS_REGEX.test(str || '');\n}\n\n/**\n * Converts the first character of a string to uppercase.\n *\n * @param str - The string to be converted.\n * @returns The modified string with the rest of the string unchanged.\n *\n * @example\n * ```ts\n * titleize('hello world') // 'Hello world'\n * ```\n */\nexport function titleize(str: string | undefined | null): string {\n  const s = str || '';\n  return s.charAt(0).toUpperCase() + s.slice(1);\n}\n\n/**\n * Converts a string from snake_case to camelCase.\n */\nexport function snakeToCamel(str: string | undefined): string {\n  return str ? str.replace(/([-_][a-z])/g, match => match.toUpperCase().replace(/-|_/, '')) : '';\n}\n\n/**\n * Converts a string from camelCase to snake_case.\n */\nexport function camelToSnake(str: string | undefined): string {\n  return str ? str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`) : '';\n}\n\nconst createDeepObjectTransformer = (transform: any) => {\n  const deepTransform = (obj: any): any => {\n    if (!obj) {\n      return obj;\n    }\n\n    if (Array.isArray(obj)) {\n      return obj.map(el => {\n        if (typeof el === 'object' || Array.isArray(el)) {\n          return deepTransform(el);\n        }\n        return el;\n      });\n    }\n\n    const copy = { ...obj };\n    const keys = Object.keys(copy);\n    for (const oldName of keys) {\n      const newName = transform(oldName.toString());\n      if (newName !== oldName) {\n        copy[newName] = copy[oldName];\n        delete copy[oldName];\n      }\n      if (typeof copy[newName] === 'object') {\n        copy[newName] = deepTransform(copy[newName]);\n      }\n    }\n    return copy;\n  };\n\n  return deepTransform;\n};\n\n/**\n * Transforms camelCased objects/ arrays to snake_cased.\n * This function recursively traverses all objects and arrays of the passed value\n * camelCased keys are removed.\n *\n * @function\n */\nexport const deepCamelToSnake = createDeepObjectTransformer(camelToSnake);\n\n/**\n * Transforms snake_cased objects/ arrays to camelCased.\n * This function recursively traverses all objects and arrays of the passed value\n * camelCased keys are removed.\n *\n * @function\n */\nexport const deepSnakeToCamel = createDeepObjectTransformer(snakeToCamel);\n\n/**\n * A function to determine if a value is truthy.\n *\n * @returns True for `true`, true, positive numbers. False for `false`, false, 0, negative integers and anything else.\n */\nexport function isTruthy(value: unknown): boolean {\n  // Return if Boolean\n  if (typeof value === `boolean`) {\n    return value;\n  }\n\n  // Return false if null or undefined\n  if (value === undefined || value === null) {\n    return false;\n  }\n\n  // If the String is true or false\n  if (typeof value === `string`) {\n    if (value.toLowerCase() === `true`) {\n      return true;\n    }\n\n    if (value.toLowerCase() === `false`) {\n      return false;\n    }\n  }\n\n  // Now check if it's a number\n  const number = parseInt(value as string, 10);\n  if (isNaN(number)) {\n    return false;\n  }\n\n  if (number > 0) {\n    return true;\n  }\n\n  // Default to false\n  return false;\n}\n\n/**\n * Get all non-undefined values from an object.\n */\nexport function getNonUndefinedValues<T extends object>(obj: T): Partial<T> {\n  return Object.entries(obj).reduce((acc, [key, value]) => {\n    if (value !== undefined) {\n      acc[key as keyof T] = value;\n    }\n    return acc;\n  }, {} as Partial<T>);\n}\n", "export const without = <T extends object, P extends keyof T>(obj: T, ...props: P[]): Omit<T, P> => {\n  const copy = { ...obj };\n  for (const prop of props) {\n    delete copy[prop];\n  }\n  return copy;\n};\n\nexport const removeUndefined = <T extends object>(obj: T): Partial<T> => {\n  return Object.entries(obj).reduce((acc, [key, value]) => {\n    if (value !== undefined && value !== null) {\n      acc[key as keyof T] = value;\n    }\n    return acc;\n  }, {} as Partial<T>);\n};\n\nexport const applyFunctionToObj = <T extends Record<string, any>, R>(\n  obj: T,\n  fn: (val: any, key: string) => R,\n): Record<string, R> => {\n  const result = {} as Record<string, R>;\n  for (const key in obj) {\n    result[key] = fn(obj[key], key);\n  }\n  return result;\n};\n\nexport const filterProps = <T extends Record<string, any>>(obj: T, filter: (a: any) => boolean): T => {\n  const result = {} as T;\n  for (const key in obj) {\n    if (obj[key] && filter(obj[key])) {\n      result[key] = obj[key];\n    }\n  }\n  return result;\n};\n", "const loggedMessages: Set<string> = new Set();\n\nexport const logger = {\n  /**\n   * A custom logger that ensures messages are logged only once.\n   * Reduces noise and duplicated messages when logs are in a hot codepath.\n   */\n  warnOnce: (msg: string) => {\n    if (loggedMessages.has(msg)) {\n      return;\n    }\n\n    loggedMessages.add(msg);\n    console.warn(msg);\n  },\n  logOnce: (msg: string) => {\n    if (loggedMessages.has(msg)) {\n      return;\n    }\n\n    console.log(msg);\n    loggedMessages.add(msg);\n  },\n};\n", "export const DEV_BROWSER_JWT_KEY = '__clerk_db_jwt';\nexport const DEV_BROWSER_JWT_HEADER = 'Clerk-Db-Jwt';\n\n// Sets the dev_browser JWT in the hash or the search\nexport function setDevBrowserJWTInURL(url: URL, jwt: string): URL {\n  const resultURL = new URL(url);\n\n  // extract & strip existing jwt from search\n  const jwtFromSearch = resultURL.searchParams.get(DEV_BROWSER_JWT_KEY);\n  resultURL.searchParams.delete(DEV_BROWSER_JWT_KEY);\n\n  // Existing jwt takes precedence\n  const jwtToSet = jwtFromSearch || jwt;\n\n  if (jwtToSet) {\n    resultURL.searchParams.set(DEV_BROWSER_JWT_KEY, jwtToSet);\n  }\n\n  return resultURL;\n}\n\n/**\n * Gets the __clerk_db_jwt JWT from either the hash or the search\n * Side effect:\n * Removes __clerk_db_jwt JWT from the URL (hash and searchParams) and updates the browser history\n */\nexport function extractDevBrowserJWTFromURL(url: URL): string {\n  const jwt = readDevBrowserJwtFromSearchParams(url);\n  const cleanUrl = removeDevBrowserJwt(url);\n  if (cleanUrl.href !== url.href && typeof globalThis.history !== 'undefined') {\n    globalThis.history.replaceState(null, '', removeDevBrowserJwt(url));\n  }\n  return jwt;\n}\n\nconst readDevBrowserJwtFromSearchParams = (url: URL) => {\n  return url.searchParams.get(DEV_BROWSER_JWT_KEY) || '';\n};\n\nconst removeDevBrowserJwt = (url: URL) => {\n  return removeDevBrowserJwtFromURLSearchParams(removeLegacyDevBrowserJwt(url));\n};\n\nconst removeDevBrowserJwtFromURLSearchParams = (_url: URL) => {\n  const url = new URL(_url);\n  url.searchParams.delete(DEV_BROWSER_JWT_KEY);\n  return url;\n};\n\n/**\n * Removes the __clerk_db_jwt JWT from the URL hash, as well as\n * the legacy __dev_session JWT from the URL searchParams\n * We no longer need to use this value, however, we should remove it from the URL\n * Existing v4 apps will write the JWT to the hash and the search params in order to ensure\n * backwards compatibility with older v4 apps.\n * The only use case where this is needed now is when a user upgrades to clerk@5 locally\n * without changing the component's version on their dashboard.\n * In this scenario, the AP@4 -> localhost@5 redirect will still have the JWT in the hash,\n * in which case we need to remove it.\n */\nconst removeLegacyDevBrowserJwt = (_url: URL) => {\n  const DEV_BROWSER_JWT_MARKER_REGEXP = /__clerk_db_jwt\\[(.*)\\]/;\n  const DEV_BROWSER_JWT_LEGACY_KEY = '__dev_session';\n  const url = new URL(_url);\n  url.searchParams.delete(DEV_BROWSER_JWT_LEGACY_KEY);\n  url.hash = decodeURI(url.hash).replace(DEV_BROWSER_JWT_MARKER_REGEXP, '');\n  if (url.href.endsWith('#')) {\n    url.hash = '';\n  }\n  return url;\n};\n", "type CloudflareEnv = { env: Record<string, string> };\n\nconst hasCloudflareProxyContext = (context: any): context is { cloudflare: CloudflareEnv } => {\n  return !!context?.cloudflare?.env;\n};\n\nconst hasCloudflareContext = (context: any): context is CloudflareEnv => {\n  return !!context?.env;\n};\n\n/**\n * Retrieves an environment variable across runtime environments.\n * @param name - The environment variable name to retrieve\n * @param context - Optional context object that may contain environment values\n * @returns The environment variable value or empty string if not found\n */\nexport const getEnvVariable = (name: string, context?: Record<string, any>): string => {\n  // Node envs\n  if (typeof process !== 'undefined' && process.env && typeof process.env[name] === 'string') {\n    return process.env[name];\n  }\n\n  // Vite specific\n  if (typeof import.meta !== 'undefined' && import.meta.env && typeof import.meta.env[name] === 'string') {\n    return import.meta.env[name];\n  }\n\n  if (hasCloudflareProxyContext(context)) {\n    return context.cloudflare.env[name] || '';\n  }\n\n  // Cloudflare\n  if (hasCloudflareContext(context)) {\n    return context.env[name] || '';\n  }\n\n  // Check whether the value exists in the context object directly\n  if (context && typeof context[name] === 'string') {\n    return context[name];\n  }\n\n  // Cloudflare workers\n  try {\n    return globalThis[name as keyof typeof globalThis];\n  } catch {\n    // This will raise an error in Cloudflare Pages\n  }\n\n  return '';\n};\n", "/* eslint-disable no-redeclare, curly */\n\nfunction _(r) {\n  for (var n = [], e = 0; e < r.length; ) {\n    var a = r[e];\n    if (a === '*' || a === '+' || a === '?') {\n      n.push({\n        type: 'MODIFIER',\n        index: e,\n        value: r[e++],\n      });\n      continue;\n    }\n    if (a === '\\\\') {\n      n.push({\n        type: 'ESCAPED_CHAR',\n        index: e++,\n        value: r[e++],\n      });\n      continue;\n    }\n    if (a === '{') {\n      n.push({\n        type: 'OPEN',\n        index: e,\n        value: r[e++],\n      });\n      continue;\n    }\n    if (a === '}') {\n      n.push({\n        type: 'CLOSE',\n        index: e,\n        value: r[e++],\n      });\n      continue;\n    }\n    if (a === ':') {\n      for (var u = '', t = e + 1; t < r.length; ) {\n        var c = r.charCodeAt(t);\n        if ((c >= 48 && c <= 57) || (c >= 65 && c <= 90) || (c >= 97 && c <= 122) || c === 95) {\n          u += r[t++];\n          continue;\n        }\n        break;\n      }\n      if (!u) throw new TypeError('Missing parameter name at '.concat(e));\n      n.push({\n        type: 'NAME',\n        index: e,\n        value: u,\n      }),\n        (e = t);\n      continue;\n    }\n    if (a === '(') {\n      var o = 1,\n        m = '',\n        t = e + 1;\n      if (r[t] === '?') throw new TypeError('Pattern cannot start with \"?\" at '.concat(t));\n      for (; t < r.length; ) {\n        if (r[t] === '\\\\') {\n          m += r[t++] + r[t++];\n          continue;\n        }\n        if (r[t] === ')') {\n          if ((o--, o === 0)) {\n            t++;\n            break;\n          }\n        } else if (r[t] === '(' && (o++, r[t + 1] !== '?'))\n          throw new TypeError('Capturing groups are not allowed at '.concat(t));\n        m += r[t++];\n      }\n      if (o) throw new TypeError('Unbalanced pattern at '.concat(e));\n      if (!m) throw new TypeError('Missing pattern at '.concat(e));\n      n.push({\n        type: 'PATTERN',\n        index: e,\n        value: m,\n      }),\n        (e = t);\n      continue;\n    }\n    n.push({\n      type: 'CHAR',\n      index: e,\n      value: r[e++],\n    });\n  }\n  return (\n    n.push({\n      type: 'END',\n      index: e,\n      value: '',\n    }),\n    n\n  );\n}\n\nfunction F(r, n) {\n  n === void 0 && (n = {});\n  for (\n    var e = _(r),\n      a = n.prefixes,\n      u = a === void 0 ? './' : a,\n      t = n.delimiter,\n      c = t === void 0 ? '/#?' : t,\n      o = [],\n      m = 0,\n      h = 0,\n      p = '',\n      f = function (l) {\n        if (h < e.length && e[h].type === l) return e[h++].value;\n      },\n      w = function (l) {\n        var v = f(l);\n        if (v !== void 0) return v;\n        var E = e[h],\n          N = E.type,\n          S = E.index;\n        throw new TypeError('Unexpected '.concat(N, ' at ').concat(S, ', expected ').concat(l));\n      },\n      d = function () {\n        for (var l = '', v; (v = f('CHAR') || f('ESCAPED_CHAR')); ) l += v;\n        return l;\n      },\n      M = function (l) {\n        for (var v = 0, E = c; v < E.length; v++) {\n          var N = E[v];\n          if (l.indexOf(N) > -1) return !0;\n        }\n        return !1;\n      },\n      A = function (l) {\n        var v = o[o.length - 1],\n          E = l || (v && typeof v == 'string' ? v : '');\n        if (v && !E)\n          throw new TypeError('Must have text between two parameters, missing text after \"'.concat(v.name, '\"'));\n        return !E || M(E) ? '[^'.concat(s(c), ']+?') : '(?:(?!'.concat(s(E), ')[^').concat(s(c), '])+?');\n      };\n    h < e.length;\n\n  ) {\n    var T = f('CHAR'),\n      x = f('NAME'),\n      C = f('PATTERN');\n    if (x || C) {\n      var g = T || '';\n      u.indexOf(g) === -1 && ((p += g), (g = '')),\n        p && (o.push(p), (p = '')),\n        o.push({\n          name: x || m++,\n          prefix: g,\n          suffix: '',\n          pattern: C || A(g),\n          modifier: f('MODIFIER') || '',\n        });\n      continue;\n    }\n    var i = T || f('ESCAPED_CHAR');\n    if (i) {\n      p += i;\n      continue;\n    }\n    p && (o.push(p), (p = ''));\n    var R = f('OPEN');\n    if (R) {\n      var g = d(),\n        y = f('NAME') || '',\n        O = f('PATTERN') || '',\n        b = d();\n      w('CLOSE'),\n        o.push({\n          name: y || (O ? m++ : ''),\n          pattern: y && !O ? A(g) : O,\n          prefix: g,\n          suffix: b,\n          modifier: f('MODIFIER') || '',\n        });\n      continue;\n    }\n    w('END');\n  }\n  return o;\n}\n\nfunction H(r, n) {\n  var e = [],\n    a = P(r, e, n);\n  return I(a, e, n);\n}\n\nfunction I(r, n, e) {\n  e === void 0 && (e = {});\n  var a = e.decode,\n    u =\n      a === void 0\n        ? function (t) {\n            return t;\n          }\n        : a;\n  return function (t) {\n    var c = r.exec(t);\n    if (!c) return !1;\n    for (\n      var o = c[0],\n        m = c.index,\n        h = Object.create(null),\n        p = function (w) {\n          if (c[w] === void 0) return 'continue';\n          var d = n[w - 1];\n          d.modifier === '*' || d.modifier === '+'\n            ? (h[d.name] = c[w].split(d.prefix + d.suffix).map(function (M) {\n                return u(M, d);\n              }))\n            : (h[d.name] = u(c[w], d));\n        },\n        f = 1;\n      f < c.length;\n      f++\n    )\n      p(f);\n    return {\n      path: o,\n      index: m,\n      params: h,\n    };\n  };\n}\n\nfunction s(r) {\n  return r.replace(/([.+*?=^!:${}()[\\]|/\\\\])/g, '\\\\$1');\n}\n\nfunction D(r) {\n  return r && r.sensitive ? '' : 'i';\n}\n\nfunction $(r, n) {\n  if (!n) return r;\n  for (var e = /\\((?:\\?<(.*?)>)?(?!\\?)/g, a = 0, u = e.exec(r.source); u; )\n    n.push({\n      name: u[1] || a++,\n      prefix: '',\n      suffix: '',\n      modifier: '',\n      pattern: '',\n    }),\n      (u = e.exec(r.source));\n  return r;\n}\n\nfunction W(r, n, e) {\n  var a = r.map(function (u) {\n    return P(u, n, e).source;\n  });\n  return new RegExp('(?:'.concat(a.join('|'), ')'), D(e));\n}\n\nfunction L(r, n, e) {\n  return U(F(r, e), n, e);\n}\n\nfunction U(r, n, e) {\n  e === void 0 && (e = {});\n  for (\n    var a = e.strict,\n      u = a === void 0 ? !1 : a,\n      t = e.start,\n      c = t === void 0 ? !0 : t,\n      o = e.end,\n      m = o === void 0 ? !0 : o,\n      h = e.encode,\n      p =\n        h === void 0\n          ? function (v) {\n              return v;\n            }\n          : h,\n      f = e.delimiter,\n      w = f === void 0 ? '/#?' : f,\n      d = e.endsWith,\n      M = d === void 0 ? '' : d,\n      A = '['.concat(s(M), ']|$'),\n      T = '['.concat(s(w), ']'),\n      x = c ? '^' : '',\n      C = 0,\n      g = r;\n    C < g.length;\n    C++\n  ) {\n    var i = g[C];\n    if (typeof i == 'string') x += s(p(i));\n    else {\n      var R = s(p(i.prefix)),\n        y = s(p(i.suffix));\n      if (i.pattern)\n        if ((n && n.push(i), R || y))\n          if (i.modifier === '+' || i.modifier === '*') {\n            var O = i.modifier === '*' ? '?' : '';\n            x += '(?:'\n              .concat(R, '((?:')\n              .concat(i.pattern, ')(?:')\n              .concat(y)\n              .concat(R, '(?:')\n              .concat(i.pattern, '))*)')\n              .concat(y, ')')\n              .concat(O);\n          } else x += '(?:'.concat(R, '(').concat(i.pattern, ')').concat(y, ')').concat(i.modifier);\n        else {\n          if (i.modifier === '+' || i.modifier === '*')\n            throw new TypeError('Can not repeat \"'.concat(i.name, '\" without a prefix and suffix'));\n          x += '('.concat(i.pattern, ')').concat(i.modifier);\n        }\n      else x += '(?:'.concat(R).concat(y, ')').concat(i.modifier);\n    }\n  }\n  if (m) u || (x += ''.concat(T, '?')), (x += e.endsWith ? '(?='.concat(A, ')') : '$');\n  else {\n    var b = r[r.length - 1],\n      l = typeof b == 'string' ? T.indexOf(b[b.length - 1]) > -1 : b === void 0;\n    u || (x += '(?:'.concat(T, '(?=').concat(A, '))?')), l || (x += '(?='.concat(T, '|').concat(A, ')'));\n  }\n  return new RegExp(x, D(e));\n}\n\nfunction P(r, n, e) {\n  return r instanceof RegExp ? $(r, n) : Array.isArray(r) ? W(r, n, e) : L(r, n, e);\n}\nexport { H as match, P as pathToRegexp };\n", "import type {\n  Match,\n  MatchFunction,\n  ParseOptions,\n  Path,\n  RegexpToFunctionOptions,\n  TokensToRegexpOptions,\n} from './compiled/path-to-regexp';\nimport { match as matchBase, pathToRegexp as pathToRegexpBase } from './compiled/path-to-regexp';\n\nexport const pathToRegexp = (path: string) => {\n  try {\n    // @ts-ignore no types exists for the pre-compiled package\n    return pathToRegexpBase(path);\n  } catch (e: any) {\n    throw new Error(\n      `Invalid path: ${path}.\\nConsult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x\\n${e.message}`,\n    );\n  }\n};\n\nexport function match<P extends object = object>(\n  str: Path,\n  options?: ParseOptions & TokensToRegexpOptions & RegexpToFunctionOptions,\n): MatchFunction<P> {\n  try {\n    // @ts-ignore no types exists for the pre-compiled package\n    return matchBase(str, options);\n  } catch (e: any) {\n    throw new Error(\n      `Invalid path and options: Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x\\n${e.message}`,\n    );\n  }\n}\n\nexport { type Match, type MatchFunction };\n", "import type { Autocomplete } from '@clerk/types';\n\nimport { pathToRegexp } from './pathToRegexp';\n\nexport type WithPathPatternWildcard<T = string> = `${T & string}(.*)`;\nexport type PathPattern = Autocomplete<WithPathPatternWildcard>;\nexport type PathMatcherParam = Array<RegExp | PathPattern> | RegExp | PathPattern;\n\nconst precomputePathRegex = (patterns: Array<string | RegExp>) => {\n  return patterns.map(pattern => (pattern instanceof RegExp ? pattern : pathToRegexp(pattern)));\n};\n\n/**\n * Creates a function that matches paths against a set of patterns.\n *\n * @param patterns - A string, RegExp, or array of patterns to match against\n * @returns A function that takes a pathname and returns true if it matches any of the patterns\n */\nexport const createPathMatcher = (patterns: PathMatcherParam) => {\n  const routePatterns = [patterns || ''].flat().filter(Boolean);\n  const matchers = precomputePathRegex(routePatterns);\n  return (pathname: string) => matchers.some(matcher => matcher.test(pathname));\n};\n", "/* eslint-disable turbo/no-undeclared-env-vars */\nimport { isDevelopmentFromPublishableKey } from './keys';\n\n/**\n * Cache busting parameter for Netlify to prevent cached responses\n * during handshake flows with Clerk development instances.\n *\n * Note: This query parameter will be removed in the \"@clerk/clerk-js\" package.\n *\n * @internal\n */\nexport const CLERK_NETLIFY_CACHE_BUST_PARAM = '__clerk_netlify_cache_bust';\n\n/**\n * Prevents infinite redirects in Netlify's functions by adding a cache bust parameter\n * to the original redirect URL. This ensures that <PERSON>lify doesn't serve a cached response\n * during the handshake flow.\n *\n * The issue happens only on Clerk development instances running on Netlify. This is\n * a workaround until we find a better solution.\n *\n * See https://answers.netlify.com/t/cache-handling-recommendation-for-authentication-handshake-redirects/143969/1\n *\n * @internal\n */\nexport function handleNetlifyCacheInDevInstance({\n  locationHeader,\n  requestStateHeaders,\n  publishableKey,\n}: {\n  locationHeader: string;\n  requestStateHeaders: Headers;\n  publishableKey: string;\n}) {\n  const isOnNetlify =\n    process.env.NETLIFY || process.env.URL?.endsWith('netlify.app') || Boolean(process.env.NETLIFY_FUNCTIONS_TOKEN);\n  const isDevelopmentInstance = isDevelopmentFromPublishableKey(publishableKey);\n  if (isOnNetlify && isDevelopmentInstance) {\n    const hasHandshakeQueryParam = locationHeader.includes('__clerk_handshake');\n    // If location header is the original URL before the handshake flow, add cache bust param\n    // The param should be removed in clerk-js\n    if (!hasHandshakeQueryParam) {\n      const url = new URL(locationHeader);\n      url.searchParams.append(CLERK_NETLIFY_CACHE_BUST_PARAM, Date.now().toString());\n      requestStateHeaders.set('Location', url.toString());\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAO,IAAM,OAAO,IAAI,UAAuB;AAE/C;;;ACQO,IAAM,wBAAwB,MAAM;AACzC,MAAI,UAAoB;AACxB,MAAI,SAAmB;AACvB,QAAM,UAAU,IAAI,QAAQ,CAAC,KAAK,QAAQ;AACxC,cAAU;AACV,aAAS;AAAA,EACX,CAAC;AACD,SAAO,EAAE,SAAS,SAAS,OAAO;AACpC;;;ACdO,SAAS,WACd,UACsF;AACtF,QAAM,WAAW,MAAM,KAAK,QAAQ,EAAE;AAAA,IAAI,OACxC,EAAE;AAAA,MACA,YAAU,EAAE,QAAQ,aAAa,MAAM;AAAA,MACvC,aAAW,EAAE,QAAQ,YAAY,OAAO;AAAA,IAC1C;AAAA,EACF;AACA,SAAO,QAAQ,IAAI,QAAQ;AAC7B;;;ACXO,SAAS,UAAU,aAA8B;AACtD,SACE,YAAY,SAAS,eAAe,KACpC,YAAY,SAAS,eAAe,KACpC,YAAY,SAAS,iBAAiB,KACtC,YAAY,SAAS,oBAAoB;AAE7C;;;ACVO,IAAM,2BAA2B,MAAe;AACrD,MAAI;AACF,WAAO,QAAQ,IAAI,aAAa;AAAA,EAElC,QAAQ;AAAA,EAAC;AAIT,SAAO;AACT;AAEO,IAAM,oBAAoB,MAAe;AAC9C,MAAI;AACF,WAAO,QAAQ,IAAI,aAAa;AAAA,EAElC,QAAQ;AAAA,EAAC;AAGT,SAAO;AACT;AAEO,IAAM,0BAA0B,MAAe;AACpD,MAAI;AACF,WAAO,QAAQ,IAAI,aAAa;AAAA,EAElC,QAAQ;AAAA,EAAC;AAGT,SAAO;AACT;;;AC3BO,IAAM,oBAAoB,CAAC,YAAoB;AACpD,MAAI,yBAAyB,GAAG;AAC9B,YAAQ,MAAM,UAAU,OAAO,EAAE;AAAA,EACnC;AACF;;;ACHO,SAAS,gBAAmB,OAAyB,KAAU,cAAiC;AACrG,MAAI,OAAO,UAAU,YAAY;AAC/B,WAAQ,MAAwB,GAAG;AAAA,EACrC;AAEA,MAAI,OAAO,UAAU,aAAa;AAChC,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,iBAAiB,aAAa;AACvC,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ACZO,IAAM,0BAA0B,CACrC,QACA,WACG;AACH,MAAI,CAAC,UAAU,CAAC,QAAQ;AACtB;AAAA,EACF;AAEA,aAAW,OAAO,QAAQ;AACxB,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,KAAK,OAAO,GAAG,MAAM,QAAQ,OAAO,OAAO,GAAG,MAAM,UAAU;AAChH,UAAI,OAAO,GAAG,MAAM,QAAW;AAC7B,eAAO,GAAG,IAAI,KAAK,OAAO,eAAe,OAAO,GAAG,CAAC,GAAE,YAAa;AAAA,MACrE;AACA,8BAAwB,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;AAAA,IAClD,WAAW,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAC5D,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACF;AAEO,IAAM,uBAAuB,CAClC,QACA,WACG;AACH,MAAI,CAAC,UAAU,CAAC,QAAQ;AACtB;AAAA,EACF;AAEA,aAAW,OAAO,QAAQ;AACxB,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,KAAK,OAAO,GAAG,MAAM,QAAQ,OAAO,OAAO,GAAG,MAAM,UAAU;AAChH,UAAI,OAAO,GAAG,MAAM,QAAW;AAC7B,eAAO,GAAG,IAAI,KAAK,OAAO,eAAe,OAAO,GAAG,CAAC,GAAE,YAAa;AAAA,MACrE;AACA,2BAAqB,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;AAAA,IAC/C,WAAW,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,KAAK,OAAO,GAAG,MAAM,QAAW;AACzF,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACF;;;AC3CO,IAAM,+BAA+B,CAAC,YAAY,iBAAiB,eAAe;AAClF,IAAM,gCAAgC,CAAC,iBAAiB,sBAAsB,wBAAwB;AACtG,IAAM,0BAA0B;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACO,IAAM,qBAAqB,CAAC,YAAY,gBAAgB,iBAAiB,wBAAwB;AACjG,IAAM,uBAAuB,CAAC,oBAAoB;AAClD,IAAM,gBAAgB;AACtB,IAAM,kBAAkB;AACxB,IAAM,eAAe;AAMrB,SAAS,aAAa,IAAY,SAAyB,OAAe;AAC/E,SAAO,gCAAgC,EAAE,IAAI,MAAM;AACrD;;;ACrBO,IAAM,iBAAiB,CAAC,SAAiB;AAC9C,MAAI,OAAO,SAAS,eAAe,OAAO,SAAS,YAAY;AAC7D,WAAO,KAAK,IAAI;AAAA,EAClB,WAAW,OAAO,WAAW,eAAe,OAAO,QAAQ;AACzD,WAAO,IAAI,OAAO,OAAO,MAAM,QAAQ,EAAE,SAAS;AAAA,EACpD;AACA,SAAO;AACT;;;ACXO,IAAM,iBAAiB,CAAC,SAAiB;AAC9C,MAAI,OAAO,SAAS,eAAe,OAAO,SAAS,YAAY;AAC7D,WAAO,KAAK,IAAI;AAAA,EAClB,WAAW,OAAO,WAAW,eAAe,OAAO,QAAQ;AACzD,WAAO,IAAI,OAAO,OAAO,IAAI,EAAE,SAAS,QAAQ;AAAA,EAClD;AACA,SAAO;AACT;;;ACMA,IAAM,8BAA8B;AACpC,IAAM,8BAA8B;AAGpC,IAAM,qCAAqC;AAEpC,SAAS,oBAAoB,aAA6B;AAC/D,QAAM,WACJ,mCAAmC,KAAK,WAAW,KAClD,YAAY,WAAW,QAAQ,KAAK,6BAA6B,KAAK,CAAAA,OAAK,YAAY,SAASA,EAAC,CAAC;AACrG,QAAM,YAAY,WAAW,8BAA8B;AAC3D,SAAO,GAAG,SAAS,GAAG,eAAe,GAAG,WAAW,GAAG,CAAC;AACzD;AAUO,SAAS,oBACd,KACA,UAA0F,CAAC,GACpE;AACvB,QAAM,OAAO;AAEb,MAAI,CAAC,OAAO,CAAC,iBAAiB,GAAG,GAAG;AAClC,QAAI,QAAQ,SAAS,CAAC,KAAK;AACzB,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,QAAI,QAAQ,SAAS,CAAC,iBAAiB,GAAG,GAAG;AAC3C,YAAM,IAAI,MAAM,4BAA4B;AAAA,IAC9C;AACA,WAAO;AAAA,EACT;AAEA,QAAM,eAAe,IAAI,WAAW,2BAA2B,IAAI,eAAe;AAElF,MAAI,cAAc,eAAe,IAAI,MAAM,GAAG,EAAE,CAAC,CAAC;AAGlD,gBAAc,YAAY,MAAM,GAAG,EAAE;AAErC,MAAI,QAAQ,UAAU;AACpB,kBAAc,QAAQ;AAAA,EACxB,WAAW,iBAAiB,iBAAiB,QAAQ,UAAU,QAAQ,aAAa;AAClF,kBAAc,SAAS,QAAQ,MAAM;AAAA,EACvC;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAQO,SAAS,iBAAiB,MAAc,IAAI;AACjD,MAAI;AACF,UAAM,iBAAiB,IAAI,WAAW,2BAA2B,KAAK,IAAI,WAAW,2BAA2B;AAEhH,UAAM,6BAA6B,eAAe,IAAI,MAAM,GAAG,EAAE,CAAC,KAAK,EAAE,EAAE,SAAS,GAAG;AAEvF,WAAO,kBAAkB;AAAA,EAC3B,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAEO,SAAS,6BAA6B;AAC3C,QAAM,uBAAuB,oBAAI,IAAqB;AAEtD,SAAO;AAAA,IACL,mBAAmB,CAAC,QAA+B;AACjD,UAAI,CAAC,KAAK;AACR,eAAO;AAAA,MACT;AAEA,YAAM,WAAW,OAAO,QAAQ,WAAW,MAAM,IAAI;AACrD,UAAI,MAAM,qBAAqB,IAAI,QAAQ;AAC3C,UAAI,QAAQ,QAAW;AACrB,cAAM,wBAAwB,KAAK,CAAAA,OAAK,SAAS,SAASA,EAAC,CAAC;AAC5D,6BAAqB,IAAI,UAAU,GAAG;AAAA,MACxC;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEO,SAAS,gCAAgC,QAAyB;AACvE,SAAO,OAAO,WAAW,OAAO,KAAK,OAAO,WAAW,UAAU;AACnE;AAEO,SAAS,+BAA+B,QAAyB;AACtE,SAAO,OAAO,WAAW,OAAO,KAAK,OAAO,WAAW,UAAU;AACnE;AAEO,SAAS,2BAA2B,QAAyB;AAClE,SAAO,OAAO,WAAW,OAAO,KAAK,OAAO,WAAW,UAAU;AACnE;AAEO,SAAS,0BAA0B,QAAyB;AACjE,SAAO,OAAO,WAAW,OAAO,KAAK,OAAO,WAAW,UAAU;AACnE;AAEA,eAAsB,gBACpB,gBACA,SAAuB,WAAW,OAAO,QACxB;AACjB,QAAM,OAAO,IAAI,YAAY,EAAE,OAAO,cAAc;AACpD,QAAM,SAAS,MAAM,OAAO,OAAO,SAAS,IAAI;AAChD,QAAM,eAAe,OAAO,aAAa,GAAG,IAAI,WAAW,MAAM,CAAC;AAElE,SAAO,eAAe,YAAY,EAAE,QAAQ,QAAQ,GAAG,EAAE,QAAQ,QAAQ,GAAG,EAAE,UAAU,GAAG,CAAC;AAC9F;AAEO,IAAM,wBAAwB,CAAC,YAAoB,iBAAiC;AACzF,SAAO,GAAG,UAAU,IAAI,YAAY;AACtC;;;AC3HO,IAAM,2BAA2B,CAAC,mBAA2B;AAClE,QAAM,cAAc,oBAAoB,cAAc,GAAG;AAEzD,MAAI,aAAa,WAAW,QAAQ,KAAK,6BAA6B,KAAK,YAAU,aAAa,SAAS,MAAM,CAAC,GAAG;AACnH,WAAO;AAAA,EACT;AAEA,MAAI,mBAAmB,KAAK,YAAU,aAAa,SAAS,MAAM,CAAC,GAAG;AACpE,WAAO;AAAA,EACT;AACA,MAAI,qBAAqB,KAAK,YAAU,aAAa,SAAS,MAAM,CAAC,GAAG;AACtE,WAAO;AAAA,EACT;AACA,SAAO;AACT;;;AC1BO,SAAS,YAAqB;AACnC,SAAO,OAAO,WAAW;AAC3B;AAEA,IAAM,YAAY;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,gBAAgB,IAAI,OAAO,UAAU,KAAK,GAAG,GAAG,GAAG;AAOlD,SAAS,iBAAiB,WAA4B;AAC3D,SAAO,CAAC,YAAY,QAAQ,cAAc,KAAK,SAAS;AAC1D;AAMO,SAAS,iBAA0B;AACxC,QAAM,YAAY,UAAU,IAAI,QAAQ,YAAY;AACpD,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,EACT;AACA,SAAO,CAAC,iBAAiB,WAAW,SAAS,KAAK,CAAC,WAAW;AAChE;AAMO,SAAS,kBAA2B;AACzC,QAAM,YAAY,UAAU,IAAI,QAAQ,YAAY;AACpD,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,EACT;AAEA,QAAM,oBAAoB,WAAW;AAKrC,QAAM,iCAAiC,WAAW,YAAY,QAAQ,KAAK,WAAW,YAAY,aAAa;AAC/G,SAAO,kCAAkC;AAC3C;AAMO,SAAS,uBAAgC;AAC9C,SAAO,gBAAgB,KAAK,eAAe;AAC7C;;;ACpFA,IAAM,qBAAqB;AAE3B,IAAM,qBAAqB;AAC3B,IAAM,sBAAsB;AAE5B,IAAM,qBAAqB;AAC3B,IAAM,sBAAsB;AAErB,IAAM,mBAAmB,CAACC,OAAc;AAC7C,SAAO,CAAC,CAACA,GAAE,MAAM,kBAAkB;AACrC;AAEO,IAAM,oBAAoB,CAACA,OAAc;AAC9C,SAAO,CAAC,EAAEA,GAAE,MAAM,kBAAkB,KAAKA,GAAE,MAAM,mBAAmB;AACtE;AAEO,IAAM,oBAAoB,CAACA,OAAc;AAC9C,SAAO,CAAC,CAACA,GAAE,MAAM,kBAAkB,KAAK,CAAC,CAACA,GAAE,MAAM,mBAAmB;AACvE;AAEO,IAAM,aAAa,CAAC,MAA6B;AACtD,SAAO,OAAO,MAAM,YAAY,OAAO;AACzC;AAEO,IAAM,aAAa,CAAC,MAA6B;AACtD,SAAO,OAAO,MAAM,YAAY,OAAO;AACzC;AAEO,IAAM,gBAAgB,CAAC,MAAoC;AAChE,SAAO,MAAM;AACf;AAEO,IAAM,WAAW,CAAC,UAA0B;AACjD,SAAO,OAAO,UAAU,YAAY,MAAM,KAAK,UAAa,MAAM,IAAI;AACxE;AAEA,IAAM,mBAAmB;AACzB,IAAM,mBAAmB;AAElB,IAAM,oBAAoB,CAAC,UAAoC;AACpE,MAAI,UAAU,eAAe;AAC3B,WAAO,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;AAAA,EAClC;AAEA,MAAI,iBAAiB,KAAK,GAAG;AAC3B,WAAO,qBAAqB,KAAK;AAAA,EACnC;AAEA,MAAI,kBAAkB,KAAK,GAAG;AAC5B,WAAO,gBAAgB,KAAK;AAAA,EAC9B;AAEA,MAAI,kBAAkB,KAAK,GAAG;AAC5B,WAAO,sBAAsB,KAAK;AAAA,EACpC;AAEA,SAAO;AACT;AAEO,IAAM,wBAAwB,CAAC,UAAyB;AAC7D,UAAQ,MAAM,KAAK;AACnB,MAAI,iBAAiB,KAAK,GAAG;AAC3B,WAAO,MAAM,WAAW,GAAG,IAAI,QAAQ,IAAI,KAAK;AAAA,EAClD;AAEA,MAAI,kBAAkB,KAAK,GAAG;AAC5B,WAAO,gBAAgB,KAAK;AAAA,EAC9B;AAEA,MAAI,kBAAkB,KAAK,GAAG;AAC5B,WAAO,gBAAgB,KAAK;AAAA,EAC9B;AAEA,MAAI,cAAc,KAAK,GAAG;AACxB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEO,IAAM,wBAAwB,CAAC,UAA4C;AAChF,MAAI,OAAO,UAAU,aAAa,iBAAiB,KAAK,KAAK,cAAc,KAAK,IAAI;AAClF,WAAO;AAAA,EACT;AAEA,MAAI,WAAW,KAAK,GAAG;AACrB,WAAO,sBAAsB,KAAK;AAAA,EACpC;AAEA,MAAI,WAAW,KAAK,GAAG;AACrB,WAAO,sBAAsB,KAAK;AAAA,EACpC;AAEA,SAAO;AACT;AAEO,IAAM,uBAAuB,CAAC,QAA2B;AAC9D,QAAM,IAAI,QAAQ,KAAK,EAAE;AACzB,QAAM,IAAI,SAAS,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE;AAC1C,QAAM,IAAI,SAAS,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE;AAC1C,QAAM,IAAI,SAAS,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE;AAC1C,SAAO,EAAE,GAAG,GAAG,EAAE;AACnB;AAEA,IAAM,wBAAwB,CAAC,UAA6B;AAC1D,QAAM,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI;AACvB,SAAO,MAAM,MAAM,IAAI,gBAAgB,MAAM,KAAK,SAAY,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AAChH;AAEA,IAAM,wBAAwB,CAAC,UAA6B;AAC1D,QAAM,EAAE,GAAG,GAAAA,IAAG,GAAG,EAAE,IAAI;AACvB,QAAM,QAAQ,KAAK,MAAMA,KAAI,GAAG;AAChC,QAAM,QAAQ,KAAK,MAAM,IAAI,GAAG;AAChC,SAAO,MAAM,MAAM,IACf,gBACA,MAAM,KAAK,SACT,QAAQ,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC,MAClC,OAAO,CAAC,IAAI,KAAK,KAAK,KAAK;AACnC;AAEA,IAAM,uBAAuB,CAAC,QAA2B;AACvD,QAAM,aAAa,sBAAsB,qBAAqB,GAAG,CAAC;AAClE,SAAO,sBAAsB,UAAU;AACzC;AAEA,IAAM,wBAAwB,CAAC,SAA4B;AACzD,QAAM,YAAY,gBAAgB,IAAI;AACtC,QAAM,IAAI,UAAU,IAAI;AACxB,QAAM,IAAI,UAAU,IAAI;AACxB,QAAM,IAAI,UAAU,IAAI;AAExB,QAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC,GAC1B,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AACxB,MAAI,GAAGA;AACP,QAAM,KAAK,MAAM,OAAO;AAExB,MAAI,OAAO,KAAK;AACd,QAAIA,KAAI;AAAA,EACV,OAAO;AACL,UAAM,IAAI,MAAM;AAChB,IAAAA,KAAI,KAAK,MAAM,KAAK,KAAK,MAAM,QAAQ,KAAK,MAAM;AAClD,YAAQ,KAAK;AAAA,MACX,KAAK;AACH,aAAM,IAAI,KAAK,IAAK;AACpB;AAAA,MACF,KAAK;AACH,cAAM,IAAI,KAAK,IAAI,KAAK;AACxB;AAAA,MACF;AACE,cAAM,IAAI,KAAK,IAAI,KAAK;AACxB;AAAA,IACJ;AAAA,EACF;AAEA,QAAM,MAAiB,EAAE,GAAG,KAAK,MAAM,CAAC,GAAG,GAAAA,IAAG,EAAE;AAChD,QAAM,IAAI,UAAU;AACpB,MAAI,KAAK,QAAW;AAClB,QAAI,IAAI;AAAA,EACV;AACA,SAAO;AACT;AAEA,IAAM,kBAAkB,CAAC,QAA2B;AAClD,QAAM,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,IAClB,QAAQ,kBAAkB,EAAE,EAC5B,MAAM,GAAG,EACT,IAAI,OAAK,OAAO,WAAW,CAAC,CAAC;AAChC,SAAO,EAAE,GAAG,GAAG,GAAG,EAAE;AACtB;AAEA,IAAM,kBAAkB,CAAC,QAA2B;AAClD,QAAM,CAAC,GAAGA,IAAG,GAAG,CAAC,IAAI,IAClB,QAAQ,kBAAkB,EAAE,EAC5B,MAAM,GAAG,EACT,IAAI,OAAK,OAAO,WAAW,CAAC,CAAC;AAChC,SAAO,EAAE,GAAG,GAAGA,KAAI,KAAK,GAAG,IAAI,KAAK,EAAE;AACxC;;;ACjLA,IAAM,sBAAsB;AAErB,SAAS,iBAAiB,MAAoB;AACnD,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,SAAO,KAAK,eAAe,SAAS;AAAA,IAClC,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV,CAAC;AACH;AAEO,SAAS,yBAAyB,GAAS,GAAS,EAAE,WAAW,KAAK,IAAI,CAAC,GAAW;AAC3F,MAAI,CAAC,KAAK,CAAC,GAAG;AACZ,WAAO;AAAA,EACT;AACA,QAAM,OAAO,KAAK,IAAI,EAAE,YAAY,GAAG,EAAE,SAAS,GAAG,EAAE,QAAQ,CAAC;AAChE,QAAM,OAAO,KAAK,IAAI,EAAE,YAAY,GAAG,EAAE,SAAS,GAAG,EAAE,QAAQ,CAAC;AAChE,QAAM,OAAO,KAAK,OAAO,OAAO,QAAQ,mBAAmB;AAC3D,SAAO,WAAW,KAAK,IAAI,IAAI,IAAI;AACrC;AAEO,SAAS,cAAc,GAAiC;AAC7D,MAAI;AACF,WAAO,IAAI,KAAK,KAAK,oBAAI,KAAK,CAAC;AAAA,EACjC,QAAQ;AACN,WAAO,oBAAI,KAAK;AAAA,EAClB;AACF;AAUO,SAAS,eAAe,OAAqD;AAClF,QAAM,EAAE,MAAM,WAAW,IAAI;AAC7B,MAAI,CAAC,QAAQ,CAAC,YAAY;AACxB,WAAO;AAAA,EACT;AACA,QAAM,IAAI,cAAc,IAAI;AAC5B,QAAM,IAAI,cAAc,UAAU;AAClC,QAAM,mBAAmB,yBAAyB,GAAG,GAAG,EAAE,UAAU,MAAM,CAAC;AAE3E,MAAI,mBAAmB,IAAI;AACzB,WAAO,EAAE,kBAAkB,SAAS,MAAM,EAAE;AAAA,EAC9C;AACA,MAAI,mBAAmB,IAAI;AACzB,WAAO,EAAE,kBAAkB,iBAAiB,MAAM,EAAE;AAAA,EACtD;AACA,MAAI,qBAAqB,IAAI;AAC3B,WAAO,EAAE,kBAAkB,WAAW,MAAM,EAAE;AAAA,EAChD;AACA,MAAI,qBAAqB,GAAG;AAC1B,WAAO,EAAE,kBAAkB,WAAW,MAAM,EAAE;AAAA,EAChD;AACA,MAAI,qBAAqB,GAAG;AAC1B,WAAO,EAAE,kBAAkB,WAAW,MAAM,EAAE;AAAA,EAChD;AACA,MAAI,mBAAmB,GAAG;AACxB,WAAO,EAAE,kBAAkB,aAAa,MAAM,EAAE;AAAA,EAClD;AACA,SAAO,EAAE,kBAAkB,SAAS,MAAM,EAAE;AAC9C;AAEO,SAAS,SAAS,aAAqC,YAA0B;AACtF,QAAM,OAAO,cAAc,WAAW;AACtC,OAAK,YAAY,KAAK,YAAY,IAAI,UAAU;AAChD,SAAO;AACT;;;ACpDA,IAAM,oBAAoB,oBAAI,IAAY;AACnC,IAAM,aAAa,CAAC,QAAgB,SAAiB,QAAuB;AACjF,QAAM,cAAc,kBAAkB,KAAK,wBAAwB;AACnE,QAAM,YAAY,OAAO;AACzB,MAAI,kBAAkB,IAAI,SAAS,KAAK,aAAa;AACnD;AAAA,EACF;AACA,oBAAkB,IAAI,SAAS;AAE/B,UAAQ;AAAA,IACN,iCAAiC,MAAM;AAAA,EAAmE,OAAO;AAAA,EACnH;AACF;AAyBO,IAAM,qBAAqB,CAAC,KAAe,UAAkB,SAAiB,WAAW,UAAgB;AAC9G,QAAM,SAAS,WAAW,MAAM,IAAI;AAEpC,MAAI,QAAQ,OAAO,QAAQ;AAC3B,SAAO,eAAe,QAAQ,UAAU;AAAA,IACtC,MAAM;AACJ,iBAAW,UAAU,SAAS,GAAG,IAAI,IAAI,IAAI,QAAQ,EAAE;AACvD,aAAO;AAAA,IACT;AAAA,IACA,IAAI,GAAY;AACd,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACH;AAYO,IAAM,2BAA2B,CACtC,KACA,UACA,SACA,QACS;AACT,MAAI,QAAQ,IAAI,QAAQ;AACxB,SAAO,eAAe,KAAK,UAAU;AAAA,IACnC,MAAM;AACJ,iBAAW,UAAU,SAAS,GAAG;AACjC,aAAO;AAAA,IACT;AAAA,IACA,IAAI,GAAY;AACd,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACH;;;ACrFO,IAAM,cAAc,CAAC,kBAA2B,OAAkB,iBAA2C;AAClH,MAAI,CAAC,oBAAoB,cAAc;AACrC,WAAO,0BAA0B,YAAY;AAAA,EAC/C;AACA,SAAO,0BAA0B,KAAK;AACxC;AAEA,IAAM,4BAA4B,CAAC,iBAA+B;AAChE,QAAM,SAAS,aAAa;AAC5B,QAAM,OAAO,aAAa;AAC1B,QAAM,YAAY,aAAa;AAC/B,QAAM,gBAAgB,aAAa;AACnC,QAAM,gBAAgB,aAAa;AACnC,QAAM,UAAU,aAAa;AAC7B,QAAM,eAAe,aAAa;AAClC,QAAM,QAAQ,aAAa;AAC3B,QAAM,UAAU,aAAa;AAC7B,QAAM,iBAAiB,aAAa;AACpC,QAAM,UAAU,aAAa;AAC7B,QAAM,QAAQ,aAAa;AAC3B,QAAM,wBAAwB,aAAa;AAE3C,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,4BAA4B,CAAC,UAAqB;AACtD,QAAM,SAAoC,MAAM,OAAO,MAAM,KAAK,KAAK,MAAM;AAC7E,QAAM,OAAO,MAAM;AACnB,QAAM,YAAuC,MAAM,UAAU,MAAM,QAAQ,KAAK,MAAM;AACtF,QAAM,UAAU,MAAM;AACtB,QAAM,gBAAgB,MAAM,SAAS;AACrC,QAAM,gBAA+C,MAAM,UACvD,MAAM,QAAQ,iBAAiB,KAAK,SACpC;AACJ,QAAM,wBAAiD,MAAM,UAAU,MAAM,QAAQ,wBAAwB;AAC7G,QAAM,QAAQ,SAAS;AACvB,QAAM,eAAe,MAAM;AAC3B,QAAM,QAAmC,MAAM,eAAe,MAAM,aAAa,KAAK,MAAM;AAC5F,QAAM,UAAU,cAAc;AAC9B,QAAM,aAAa,eACf,MAAM,yBAAyB,KAAK,QAAM,GAAG,aAAa,OAAO,KAAK,IACtE;AACJ,QAAM,iBAAiB,aAAa,WAAW,cAAc;AAC7D,QAAM,UAAU,aAAa,WAAW,OAAO;AAE/C,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;ACtFO,SAAS,oBAAoB,GAAiB;AACnD,QAAM,SAAS,GAAG;AAClB,QAAM,OAAO,GAAG,SAAS,CAAC,GAAG;AAC7B,SAAO,SAAS,4BAA4B,WAAW;AACzD;AAEO,SAAS,eAAe,GAAmC;AAChE,SAAO,CAAC,mBAAmB,uBAAuB,uBAAuB,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,IAAI;AACtG;AAEO,SAAS,WAAW,GAAiB;AAC1C,QAAM,SAAS,GAAG;AAClB,SAAO,CAAC,CAAC,UAAU,UAAU,OAAO,SAAS;AAC/C;AAEO,SAAS,eAAe,GAAiB;AAE9C,QAAM,WAAW,GAAG,EAAE,OAAO,GAAG,EAAE,IAAI,MAAM,IAAI,YAAY,EAAE,QAAQ,QAAQ,EAAE;AAChF,SAAO,QAAQ,SAAS,cAAc;AACxC;AAiBO,SAAS,aAAa,OAAgF;AAC3G,SAAO,wBAAwB,KAAK,KAAK,gBAAgB,KAAK,KAAK,oBAAoB,KAAK;AAC9F;AAEO,SAAS,wBAAwB,KAAwC;AAC9E,SAAO,gBAAgB;AACzB;AAkBO,SAAS,oBAAoB,KAAoC;AACtE,SAAO,uBAAuB;AAChC;AAEO,SAAS,+BAA+B,KAAU;AACvD,SAAO,oBAAoB,GAAG,KAAK,IAAI,SAAS;AAClD;AAEO,SAAS,gBAAgB,KAAgC;AAC9D,SAAO,UAAU,OAAO,CAAC,MAAM,OAAO,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK,aAAa;AAClF;AAEO,SAAS,kBAAkB,KAAU;AAC1C,SAAO,wBAAwB,GAAG,KAAK,IAAI,SAAS,CAAC,GAAG,SAAS;AACnE;AAEO,SAAS,qBAAqB,KAAU;AAC7C,SAAO,wBAAwB,GAAG,KAAK,IAAI,SAAS,CAAC,GAAG,SAAS;AACnE;AAEO,SAAS,YAAY,OAA4B,CAAC,GAAoB;AAC3E,SAAO,KAAK,SAAS,IAAI,KAAK,IAAI,UAAU,IAAI,CAAC;AACnD;AAEO,SAAS,WAAW,OAAyC;AAClE,SAAO;AAAA,IACL,MAAM,MAAM;AAAA,IACZ,SAAS,MAAM;AAAA,IACf,aAAa,MAAM;AAAA,IACnB,MAAM;AAAA,MACJ,WAAW,OAAO,MAAM;AAAA,MACxB,WAAW,OAAO,MAAM;AAAA,MACxB,gBAAgB,OAAO,MAAM;AAAA,MAC7B,aAAa,OAAO,MAAM;AAAA,MAC1B,QAAQ,OAAO,MAAM;AAAA,MACrB,MAAM,OAAO,MAAM;AAAA,IACrB;AAAA,EACF;AACF;AAEO,SAAS,YAAY,OAAgD;AAC1E,SAAO;AAAA,IACL,MAAM,OAAO,QAAQ;AAAA,IACrB,SAAS,OAAO,WAAW;AAAA,IAC3B,cAAc,OAAO;AAAA,IACrB,MAAM;AAAA,MACJ,YAAY,OAAO,MAAM;AAAA,MACzB,YAAY,OAAO,MAAM;AAAA,MACzB,iBAAiB,OAAO,MAAM;AAAA,MAC9B,aAAa,OAAO,MAAM;AAAA,MAC1B,QAAQ,OAAO,MAAM;AAAA,MACrB,MAAM,OAAO,MAAM;AAAA,IACrB;AAAA,EACF;AACF;AAEO,IAAM,wBAAN,MAAM,+BAA8B,MAAM;AAAA,EAU/C,YAAY,SAAiB,EAAE,MAAM,QAAQ,cAAc,WAAW,GAA4B;AAChG,UAAM,OAAO;AAYf,SAAO,WAAW,MAAM;AACtB,UAAI,UAAU,IAAI,KAAK,IAAI;AAAA,UAAc,KAAK,OAAO;AAAA,SAAY,KAAK,MAAM;AAAA,qBAAwB,KAAK,OAAO;AAAA,QAC9G,OAAK,KAAK,UAAU,CAAC;AAAA,MACvB,CAAC;AAED,UAAI,KAAK,cAAc;AACrB,mBAAW;AAAA,kBAAqB,KAAK,YAAY;AAAA,MACnD;AAEA,aAAO;AAAA,IACT;AApBE,WAAO,eAAe,MAAM,uBAAsB,SAAS;AAE3D,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,SAAS,YAAY,IAAI;AAAA,EAChC;AAaF;AASO,IAAM,oBAAN,MAAM,2BAA0B,MAAM;AAAA,EAiB3C,YAAY,SAAiB,EAAE,KAAK,GAAqB;AACvD,UAAM,SAAS;AACf,UAAM,QAAQ,IAAI,OAAO,OAAO,QAAQ,KAAK,MAAM,GAAG,GAAG;AACzD,UAAM,YAAY,QAAQ,QAAQ,OAAO,EAAE;AAC3C,UAAM,WAAW,GAAG,MAAM,IAAI,UAAU,KAAK,CAAC;AAAA;AAAA,SAAc,IAAI;AAAA;AAChE,UAAM,QAAQ;AAehB;AAAA;AAAA;AAAA;AAAA;AAAA,SAAO,WAAW,MAAM;AACtB,aAAO,IAAI,KAAK,IAAI;AAAA,UAAc,KAAK,OAAO;AAAA,IAChD;AAfE,WAAO,eAAe,MAAM,mBAAkB,SAAS;AAEvD,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,oBAAoB;AACzB,SAAK,OAAO;AAAA,EACd;AAUF;AAEO,IAAM,iBAAN,MAAM,wBAAuB,MAAM;AAAA,EAGxC,YAAY,MAAc;AACxB,UAAM,IAAI;AACV,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,WAAO,eAAe,MAAM,gBAAe,SAAS;AAAA,EACtD;AACF;AAEO,SAAS,iBAAiB,KAAmC;AAClE,SAAO,IAAI,SAAS;AACtB;AAOO,IAAM,qBAAqB;AAAA,EAChC,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,gBAAgB;AAClB;AAEO,IAAM,2BAA2B;AAAA,EACtC,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,gBAAgB;AAClB;AAEA,IAAM,kBAAkB,OAAO,OAAO;AAAA,EACpC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,sBAAsB;AACxB,CAAC;AA+BM,SAAS,kBAAkB,EAAE,aAAa,eAAe,GAAsC;AACpG,MAAI,MAAM;AAEV,QAAM,WAAW;AAAA,IACf,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AAEA,WAAS,aAAa,YAAoB,cAAgD;AACxF,QAAI,CAAC,cAAc;AACjB,aAAO,GAAG,GAAG,KAAK,UAAU;AAAA,IAC9B;AAEA,QAAI,MAAM;AACV,UAAM,UAAU,WAAW,SAAS,uBAAuB;AAE3D,eAAW,SAAS,SAAS;AAC3B,YAAM,eAAe,aAAa,MAAM,CAAC,CAAC,KAAK,IAAI,SAAS;AAC5D,YAAM,IAAI,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM,WAAW;AAAA,IAClD;AAEA,WAAO,GAAG,GAAG,KAAK,GAAG;AAAA,EACvB;AAEA,SAAO;AAAA,IACL,eAAe,EAAE,aAAAC,aAAY,GAAsC;AACjE,UAAI,OAAOA,iBAAgB,UAAU;AACnC,cAAMA;AAAA,MACR;AACA,aAAO;AAAA,IACT;AAAA,IAEA,YAAY,EAAE,gBAAAC,gBAAe,GAAsC;AACjE,aAAO,OAAO,UAAUA,mBAAkB,CAAC,CAAC;AAC5C,aAAO;AAAA,IACT;AAAA,IAEA,gCAAgC,QAAiC;AAC/D,YAAM,IAAI,MAAM,aAAa,SAAS,mCAAmC,MAAM,CAAC;AAAA,IAClF;AAAA,IAEA,qBAAqB,QAAiC;AACpD,YAAM,IAAI,MAAM,aAAa,SAAS,6BAA6B,MAAM,CAAC;AAAA,IAC5E;AAAA,IAEA,kCAAyC;AACvC,YAAM,IAAI,MAAM,aAAa,SAAS,iCAAiC,CAAC;AAAA,IAC1E;AAAA,IAEA,6BAAoC;AAClC,YAAM,IAAI,MAAM,aAAa,SAAS,4BAA4B,CAAC;AAAA,IACrE;AAAA,IAEA,+BAA+B,QAAoC;AACjE,YAAM,IAAI,MAAM,aAAa,SAAS,sBAAsB,MAAM,CAAC;AAAA,IACrE;AAAA,IAEA,MAAM,SAAwB;AAC5B,YAAM,IAAI,MAAM,aAAa,OAAO,CAAC;AAAA,IACvC;AAAA,EACF;AACF;AAgBO,IAAM,qBAAN,cAAiC,kBAAkB;AAAA,EAMxD,YAAY,SAAiB,EAAE,KAAK,GAAqC;AACvE,UAAM,SAAS,EAAE,KAAK,CAAC;AACvB,SAAK,OAAO;AAAA,EACd;AACF;;;ACjWO,SAAS,aAAa,MAA8B;AACzD,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAM,SAAS,IAAI,WAAW;AAC9B,WAAO,iBAAiB,QAAQ,WAAY;AAC1C,YAAM,SAAS,KAAK,MAAM,OAAO,MAAgB;AACjD,cAAQ,MAAM;AAAA,IAChB,CAAC;AAED,WAAO,iBAAiB,SAAS,MAAM;AACvC,WAAO,WAAW,IAAI;AAAA,EACxB,CAAC;AACH;AAEA,IAAM,yBAAyB,OAAO,OAAO;AAAA,EAC3C,aAAa;AAAA,EACb,cAAc;AAAA,EACd,aAAa;AAAA,EACb,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,4BAA4B;AAC9B,CAAU;AAIH,IAAM,YAAY,CAAC,aAAwC;AAChE,SAAO,uBAAuB,QAAQ;AACxC;;;ACSA,IAAM,iBAAyC;AAAA,EAC7C,cAAc;AAAA,EACd,wBAAwB;AAAA,EACxB,QAAQ;AAAA,EACR,aAAa,CAACC,IAAY,cAAsB,YAAY;AAAA,EAC5D,kBAAkB;AAAA,EAClB,QAAQ;AACV;AAEA,IAAM,0BAA0B;AAEhC,IAAM,QAAQ,OAAO,OAAqB,IAAI,QAAQ,CAAAC,OAAK,WAAWA,IAAG,EAAE,CAAC;AAE5E,IAAM,cAAc,CAAC,OAAqB,WAAoB;AAC5D,SAAO,SAAS,SAAS,IAAI,KAAK,OAAO,KAAK;AAChD;AAEA,IAAM,gCAAgC,CACpC,SACG;AACH,MAAI,cAAc;AAElB,QAAM,qBAAqB,MAAM;AAC/B,UAAM,WAAW,KAAK;AACtB,UAAM,OAAO,KAAK;AAClB,QAAI,QAAQ,WAAW,KAAK,IAAI,MAAM,WAAW;AACjD,YAAQ,YAAY,OAAO,KAAK,MAAM;AACtC,WAAO,KAAK,IAAI,KAAK,0BAA0B,OAAO,KAAK;AAAA,EAC7D;AAEA,SAAO,YAA2B;AAChC,UAAM,MAAM,mBAAmB,CAAC;AAChC;AAAA,EACF;AACF;AAMO,IAAM,QAAQ,OAAU,UAAgC,UAAwB,CAAC,MAAkB;AACxG,MAAI,aAAa;AACjB,QAAM,EAAE,aAAa,cAAc,wBAAwB,QAAQ,kBAAkB,OAAO,IAAI;AAAA,IAC9F,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AAEA,QAAM,QAAQ,8BAA8B;AAAA,IAC1C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAED,SAAO,MAAM;AACX,QAAI;AACF,aAAO,MAAM,SAAS;AAAA,IACxB,SAAS,GAAG;AACV;AACA,UAAI,CAAC,YAAY,GAAG,UAAU,GAAG;AAC/B,cAAM;AAAA,MACR;AACA,UAAI,oBAAoB,eAAe,GAAG;AACxC,cAAM,MAAM,YAAY,yBAAyB,MAAM,CAAC;AAAA,MAC1D,OAAO;AACL,cAAM,MAAM;AAAA,MACd;AAAA,IACF;AAAA,EACF;AACF;;;AC5GA,IAAM,oBAAoB;AAC1B,IAAM,eAAe;AAUrB,eAAsB,WAAW,MAAM,IAAI,MAAqD;AAC9F,QAAM,EAAE,OAAO,OAAO,YAAY,aAAa,MAAM,IAAI,QAAQ,CAAC;AAElE,QAAM,OAAO,MAAM;AACjB,WAAO,IAAI,QAA2B,CAAC,SAAS,WAAW;AACzD,UAAI,CAAC,KAAK;AACR,eAAO,IAAI,MAAM,YAAY,CAAC;AAAA,MAChC;AAEA,UAAI,CAAC,YAAY,CAAC,SAAS,MAAM;AAC/B,eAAO,iBAAiB;AAAA,MAC1B;AAEA,YAAM,SAAS,SAAS,cAAc,QAAQ;AAE9C,UAAI,YAAa,QAAO,aAAa,eAAe,WAAW;AAC/D,aAAO,QAAQ,SAAS;AACxB,aAAO,QAAQ,SAAS;AAExB,aAAO,iBAAiB,QAAQ,MAAM;AACpC,eAAO,OAAO;AACd,gBAAQ,MAAM;AAAA,MAChB,CAAC;AAED,aAAO,iBAAiB,SAAS,MAAM;AACrC,eAAO,OAAO;AACd,eAAO;AAAA,MACT,CAAC;AAED,aAAO,MAAM;AACb,aAAO,QAAQ;AACf,mBAAa,MAAM;AACnB,eAAS,KAAK,YAAY,MAAM;AAAA,IAClC,CAAC;AAAA,EACH;AAEA,SAAO,MAAM,MAAM,EAAE,aAAa,CAACC,IAAG,eAAe,cAAc,EAAE,CAAC;AACxE;;;AClDO,SAAS,gBAAgB,KAAyB;AACvD,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AAEA,SAAO,cAAc,GAAG,KAAK,mBAAmB,GAAG;AACrD;AAEO,SAAS,cAAc,KAAyB;AACrD,SAAO,iBAAiB,KAAK,OAAO,EAAE;AACxC;AAEO,SAAS,mBAAmB,KAAa;AAC9C,SAAO,IAAI,WAAW,GAAG;AAC3B;AAEO,SAAS,sBAAsB,KAAiC;AACrE,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AACA,SAAO,mBAAmB,GAAG,IAAI,IAAI,IAAI,KAAK,OAAO,SAAS,MAAM,EAAE,SAAS,IAAI;AACrF;;;AClBO,SAAS,kBAAkB,cAAc,IAAqB;AACnE,MAAI,YAAY,WAAW,GAAG,GAAG;AAC/B,kBAAc,YAAY,MAAM,CAAC;AAAA,EACnC;AACA,SAAO,IAAI,gBAAgB,WAAW;AACxC;AAEO,SAAS,YAAY,MAAM,IAAY;AAC5C,UAAQ,OAAO,IAAI,QAAQ,YAAY,EAAE;AAC3C;AAEO,SAAS,eAAe,KAAyB;AACtD,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AACA,MAAI;AACJ,MAAI,IAAI,MAAM,iBAAiB,GAAG;AAChC,YAAQ;AAAA,EACV,WAAW,IAAI,MAAM,kBAAkB,GAAG;AACxC,WAAO;AAAA,EACT,OAAO;AACL,YAAQ;AAAA,EACV;AAEA,QAAM,WAAW,IAAI,QAAQ,OAAO,EAAE;AACtC,SAAO,SAAS,QAAQ;AAC1B;AAQO,IAAM,8BAA8B,CAAC,aAAqB,YAAqB;AACpF,MAAI,CAAC,WAAW,UAAU,WAAW,GAAG;AACtC,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AAEA,SAAO,QAAQ,MAAM,GAAG,EAAE,CAAC,KAAK;AAClC;AAOO,IAAM,eAAe,CAAC,aAAqB,EAAE,eAAe,MAAmC;AACpG,QAAM,sBAAsB,YAAY,QAAQ,iBAAiB,EAAE;AACnE,QAAM,QAAQ,4BAA4B,aAAa,cAAc;AACrE,SAAO,WAAW,mBAAmB,wBAAwB,kBAAkB,KAAK;AACtF;AAMO,SAAS,+BAA+B,MAAuB;AACpE,SAAO,6BAA6B,KAAK,qBAAmB;AAC1D,WAAO,KAAK,WAAW,WAAW,KAAK,KAAK,SAAS,eAAe;AAAA,EACtE,CAAC;AACH;AAQO,SAAS,gCAAgC,MAAuB;AACrE,SAAO,8BAA8B,KAAK,sBAAoB;AAC5D,WAAO,KAAK,SAAS,gBAAgB,KAAK,CAAC,KAAK,SAAS,WAAW,gBAAgB;AAAA,EACtF,CAAC;AACH;AAIA,IAAM,oBAAoB;AAEnB,SAAS,iBAAiB,QAAQ,IAAI,yBAA4C;AACvF,MAAI,CAAC,yBAAyB;AAC5B,WAAO,MAAM,SAAS,GAAG;AAAA,EAC3B;AACA,SAAO,kBAAkB,KAAK,KAAK;AACrC;AAEO,SAAS,kBAAkB,QAAQ,IAAI,yBAA2C;AACvF,MAAI,CAAC,yBAAyB;AAC5B,WAAO,MAAM,SAAS,GAAG,IAAI,QAAQ,QAAQ;AAAA,EAC/C;AACA,MAAI,iBAAiB,OAAO,IAAI,GAAG;AACjC,WAAO,SAAS;AAAA,EAClB;AACA,MAAI,OAAO;AACX,MAAI,WAAW;AACf,QAAM,gBAAgB,MAAM,QAAQ,GAAG;AACvC,MAAI,iBAAiB,GAAG;AACtB,WAAO,MAAM,MAAM,GAAG,aAAa;AACnC,eAAW,MAAM,MAAM,aAAa;AACpC,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,CAAC,IAAI,GAAGC,EAAC,IAAI,KAAK,MAAM,GAAG;AACjC,SAAO,KAAK,OAAOA,GAAE,SAAS,IAAI,IAAIA,GAAE,KAAK,GAAG,CAAC,KAAK,MAAM;AAC9D;AAEO,SAAS,qBAAqB,QAAQ,IAAI,yBAA2C;AAC1F,MAAI,CAAC,yBAAyB;AAC5B,YAAQ,iBAAiB,KAAK,IAAI,MAAM,MAAM,GAAG,EAAE,IAAI,UAAU;AAAA,EACnE;AACA,MAAI,CAAC,iBAAiB,OAAO,IAAI,GAAG;AAClC,WAAO,SAAS;AAAA,EAClB;AACA,MAAI,OAAO;AACX,MAAI,WAAW;AACf,QAAM,gBAAgB,MAAM,QAAQ,GAAG;AACvC,MAAI,iBAAiB,GAAG;AACtB,WAAO,MAAM,MAAM,GAAG,aAAa;AACnC,eAAW,MAAM,MAAM,aAAa;AAAA,EACtC;AACA,QAAM,CAAC,IAAI,GAAGA,EAAC,IAAI,KAAK,MAAM,GAAG;AACjC,UAAQ,GAAG,MAAM,GAAG,EAAE,KAAK,QAAQA,GAAE,SAAS,IAAI,IAAIA,GAAE,KAAK,GAAG,CAAC,KAAK,MAAM;AAC9E;AAEO,SAAS,gBAAgB,QAAQ,IAAa;AACnD,SAAO,MAAM,WAAW,GAAG;AAC7B;AAEO,SAAS,oBAAoB,QAAQ,IAAY;AACtD,UAAQ,gBAAgB,KAAK,IAAI,MAAM,MAAM,CAAC,IAAI,UAAU;AAC9D;AAEO,SAAS,iBAAiB,QAAQ,IAAY;AACnD,SAAO,gBAAgB,KAAK,IAAI,QAAQ,MAAM;AAChD;AAEO,SAAS,mBAAmB,QAAQ,IAAY;AACrD,SAAO,MACJ,MAAM,KAAK,EACX,IAAI,aAAW,QAAQ,QAAQ,WAAW,GAAG,CAAC,EAC9C,KAAK,KAAK;AACf;AAEO,SAAS,cAAc,KAAa;AACzC,SAAO,OAAO,QAAQ;AACxB;AAEA,IAAM,wBAAwB;AAEvB,SAAS,QAAQ,SAAiB,OAAyB;AAChE,MAAI,MAAM,QAAQ;AAElB,aAAW,WAAW,MAAM,OAAO,CAAAC,SAAO,cAAcA,IAAG,CAAC,GAAG;AAC7D,QAAI,KAAK;AAEP,YAAM,WAAW,QAAQ,QAAQ,uBAAuB,EAAE;AAC1D,YAAM,kBAAkB,GAAG,IAAI;AAAA,IACjC,OAAO;AACL,YAAM;AAAA,IACR;AAAA,EACF;AAEA,SAAO;AACT;AAMA,IAAM,qBAAqB;AACpB,IAAM,gBAAgB,CAAC,QAAgB,mBAAmB,KAAK,GAAG;;;ACxKlE,IAAM,kBAAkB,CAAC,gBAAoC,iBAAiB,aAAuB;AAC1G,MAAI,gBAAgB;AAClB,WAAO;AAAA,EACT;AAEA,QAAM,gBAAgB,iBAAiB,cAAc;AACrD,MAAI,eAAe;AACjB,QAAI,kBAAkB,YAAY;AAChC,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,gBAAgB,cAAc;AACvC;AAEA,IAAM,mBAAmB,CAAC,mBACxB,eACG,KAAK,EACL,QAAQ,MAAM,EAAE,EAChB,MAAM,cAAc,IAAI,CAAC;AAEvB,IAAM,kBAAkB,CAAC,mBAA2B,eAAe,KAAK,EAAE,QAAQ,MAAM,EAAE,EAAE,MAAM,GAAG,EAAE,CAAC;;;ACxB/G,IAAM,uBAAuB;AAE7B,IAAM,EAAE,kBAAkB,IAAI,2BAA2B;AAEzD,IAAM,eAAe,kBAAkB,EAAE,aAAa,gBAAgB,CAAC;AAQhE,SAAS,kCAAkC,aAAqB;AACrE,eAAa,eAAe,EAAE,YAAY,CAAC;AAC7C;AA0BA,IAAM,oBAAoB,OAAO,SAAoC;AACnE,QAAM,iBAAiB,SAAS,cAAiC,8BAA8B;AAE/F,MAAI,gBAAgB;AAClB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,qBAAe,iBAAiB,QAAQ,MAAM;AAC5C,gBAAQ,cAAc;AAAA,MACxB,CAAC;AAED,qBAAe,iBAAiB,SAAS,MAAM;AAC7C,eAAO,oBAAoB;AAAA,MAC7B,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,MAAI,CAAC,MAAM,gBAAgB;AACzB,iBAAa,gCAAgC;AAC7C;AAAA,EACF;AAEA,SAAO,WAAW,iBAAiB,IAAI,GAAG;AAAA,IACxC,OAAO;AAAA,IACP,aAAa;AAAA,IACb,OAAO,KAAK;AAAA,IACZ,YAAY,6BAA6B,IAAI;AAAA,EAC/C,CAAC,EAAE,MAAM,MAAM;AACb,UAAM,IAAI,MAAM,oBAAoB;AAAA,EACtC,CAAC;AACH;AAUA,IAAM,mBAAmB,CAAC,SAAmC;AAC3D,QAAM,EAAE,YAAY,gBAAgB,gBAAgB,UAAU,QAAQ,eAAe,IAAI;AAEzF,MAAI,YAAY;AACd,WAAO;AAAA,EACT;AAEA,MAAI,aAAa;AACjB,MAAI,CAAC,CAAC,YAAY,gBAAgB,QAAQ,GAAG;AAC3C,iBAAa,sBAAsB,QAAQ,EAAE,QAAQ,iBAAiB,EAAE;AAAA,EAC1E,WAAW,UAAU,CAAC,kBAAkB,oBAAoB,cAAc,GAAG,eAAe,EAAE,GAAG;AAC/F,iBAAa,eAAe,MAAM;AAAA,EACpC,OAAO;AACL,iBAAa,oBAAoB,cAAc,GAAG,eAAe;AAAA,EACnE;AAEA,QAAM,UAAU,iBAAiB,GAAG,eAAe,QAAQ,QAAQ,EAAE,CAAC,MAAM;AAC5E,QAAM,UAAU,gBAAgB,cAAc;AAC9C,SAAO,WAAW,UAAU,wBAAwB,OAAO,eAAe,OAAO;AACnF;AAKA,IAAM,+BAA+B,CAAC,YAAsC;AAC1E,QAAM,MAA8B,CAAC;AAErC,MAAI,QAAQ,gBAAgB;AAC1B,QAAI,4BAA4B,IAAI,QAAQ;AAAA,EAC9C;AAEA,MAAI,QAAQ,UAAU;AACpB,QAAI,sBAAsB,IAAI,QAAQ;AAAA,EACxC;AAEA,MAAI,QAAQ,QAAQ;AAClB,QAAI,mBAAmB,IAAI,QAAQ;AAAA,EACrC;AAEA,MAAI,QAAQ,OAAO;AACjB,QAAI,QAAQ,QAAQ;AAAA,EACtB;AAEA,SAAO;AACT;AAEA,IAAM,+BAA+B,CAAC,YAAsC,CAAC,WAA8B;AACzG,QAAM,aAAa,6BAA6B,OAAO;AACvD,aAAW,aAAa,YAAY;AAClC,WAAO,aAAa,WAAW,WAAW,SAAS,CAAC;AAAA,EACtD;AACF;;;ACxIA,IAAM,aAAa;AAEZ,IAAM,+BAAN,MAAsC;AAAA,EAI3C,YAAY,MAAc;AAH1B,SAAiB,cAAc;AAQ/B,SAAO,cAAc,CAAC,SAAkB;AACtC,UAAI,OAAO,WAAW,aAAa;AAEjC;AAAA,MACF;AAEA,UAAI;AACF,eAAO,aAAa,QAAQ,KAAK,YAAY,KAAK,UAAU,IAAI,CAAC;AACjE,eAAO,aAAa,WAAW,KAAK,UAAU;AAAA,MAChD,QAAQ;AAAA,MAER;AAAA,IACF;AAEA,SAAO,mBAAmB,CAAC,WAAsB,aAAgC;AAC/E,WAAK,YAAY,iBAAiB,KAAK,gBAAgB,SAAS,GAAG,OAAK;AACtE,iBAAS,CAAiB;AAAA,MAC5B,CAAC;AAAA,IACH;AAEA,SAAQ,4BAA4B,MAAM;AACxC,YAAM,kBAAkB,CAAC,MAAoB;AAC3C,YAAI,EAAE,QAAQ,KAAK,cAAc,CAAC,EAAE,UAAU;AAC5C;AAAA,QACF;AAEA,YAAI;AACF,gBAAM,OAAO,KAAK,MAAM,EAAE,YAAY,EAAE;AACxC,gBAAM,QAAQ,IAAI,aAAa,KAAK,gBAAgB,SAAS,GAAG;AAAA,YAC9D;AAAA,UACF,CAAC;AACD,eAAK,YAAY,cAAc,KAAK;AAAA,QACtC,QAAQ;AAAA,QAER;AAAA,MACF;AAEA,aAAO,iBAAiB,WAAW,eAAe;AAAA,IACpD;AA1CE,SAAK,aAAa,aAAa;AAC/B,SAAK,0BAA0B;AAAA,EACjC;AAAA,EA0CQ,gBAAgB,WAA2B;AACjD,WAAO,KAAK,aAAa;AAAA,EAC3B;AACF;;;ACxDA;;;ACaA,IAAM,kBAAkB,CAAC,QAAgB,OAAgD,CAAC,MAAqB;AAC7G,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO;AAAA,EACT;AAEA,MAAI;AACF,UAAM,OAAO,IAAI,KAAK,CAAC,MAAM,GAAG,EAAE,MAAM,wCAAwC,CAAC;AACjF,UAAM,eAAe,WAAW,IAAI,gBAAgB,IAAI;AACxD,WAAO,IAAI,OAAO,cAAc,IAAI;AAAA,EACtC,QAAQ;AACN,YAAQ,KAAK,sFAAsF;AACnG,WAAO;AAAA,EACT;AACF;AAEA,IAAM,iBAAiB,MAAM;AAC3B,QAAMC,cAAa,WAAW,WAAW,KAAK,UAAU;AACxD,QAAM,cAAc,WAAW,YAAY,KAAK,UAAU;AAC1D,QAAM,eAAe,WAAW,aAAa,KAAK,UAAU;AAC5D,QAAM,gBAAgB,WAAW,cAAc,KAAK,UAAU;AAC9D,SAAO,EAAE,YAAAA,aAAY,aAAa,cAAc,eAAe,SAAS,KAAK;AAC/E;AAEO,IAAM,qBAAqB,MAAM;AACtC,MAAI,KAAK;AACT,QAAM,aAAa,MAAM;AACzB,QAAM,YAAY,oBAAI,IAA0C;AAChE,QAAM,OAAO,CAAC,GAAkB,MAAwB,GAAG,YAAY,CAAC;AACxE,QAAM,gBAAgB,CAAC,MAA8C;AACnE,cAAU,IAAI,EAAE,KAAK,EAAE,IAAI;AAAA,EAC7B;AAEA,MAAI,SAAS,gBAAgB,6BAAoB,EAAE,MAAM,eAAe,CAAC;AACzE,UAAQ,iBAAiB,WAAW,aAAa;AAEjD,MAAI,CAAC,QAAQ;AACX,WAAO,eAAe;AAAA,EACxB;AAEA,QAAM,OAAO,MAAM;AACjB,QAAI,CAAC,QAAQ;AACX,eAAS,gBAAgB,6BAAoB,EAAE,MAAM,eAAe,CAAC;AACrE,cAAQ,iBAAiB,WAAW,aAAa;AAAA,IACnD;AAAA,EACF;AAEA,QAAM,UAAU,MAAM;AACpB,QAAI,QAAQ;AACV,aAAO,UAAU;AACjB,eAAS;AACT,gBAAU,MAAM;AAAA,IAClB;AAAA,EACF;AAEA,QAAMA,cAA+B,CAAC,IAAI,OAAO;AAC/C,SAAK;AACL,UAAMC,MAAK,WAAW;AACtB,cAAU,IAAIA,KAAI,MAAM;AACtB,SAAG;AACH,gBAAU,OAAOA,GAAE;AAAA,IACrB,CAAC;AACD,SAAK,QAAQ,EAAE,MAAM,cAAc,IAAAA,KAAI,GAAG,CAAC;AAC3C,WAAOA;AAAA,EACT;AAEA,QAAM,cAAgC,CAAC,IAAI,OAAO;AAChD,SAAK;AACL,UAAMA,MAAK,WAAW;AACtB,cAAU,IAAIA,KAAI,EAAE;AACpB,SAAK,QAAQ,EAAE,MAAM,eAAe,IAAAA,KAAI,GAAG,CAAC;AAC5C,WAAOA;AAAA,EACT;AAEA,QAAM,eAAmC,CAAAA,QAAM;AAC7C,SAAK;AACL,cAAU,OAAOA,GAAE;AACnB,SAAK,QAAQ,EAAE,MAAM,gBAAgB,IAAAA,IAAG,CAAC;AAAA,EAC3C;AAEA,QAAM,gBAAoC,CAAAA,QAAM;AAC9C,SAAK;AACL,cAAU,OAAOA,GAAE;AACnB,SAAK,QAAQ,EAAE,MAAM,iBAAiB,IAAAA,IAAG,CAAC;AAAA,EAC5C;AAEA,SAAO,EAAE,YAAAD,aAAY,aAAa,cAAc,eAAe,QAAQ;AACzE;;;ACpFO,SAAS,OAAO,EAAE,UAAU,IAAmB,EAAE,WAAW,IAAK,GAAW;AACjF,QAAM,eAAe,mBAAmB;AAExC,MAAI;AACJ,MAAI,UAAU;AAEd,QAAM,OAAmB,MAAM;AAC7B,QAAI,SAAS;AACX,mBAAa,aAAa,OAAO;AACjC,mBAAa,QAAQ;AAAA,IACvB;AACA,cAAU;AAAA,EACZ;AAEA,QAAM,MAAiB,OAAM,OAAM;AACjC,cAAU;AACV,UAAM,GAAG,IAAI;AACb,QAAI,SAAS;AACX;AAAA,IACF;AAEA,cAAU,aAAa,WAAW,MAAM;AACtC,WAAK,IAAI,EAAE;AAAA,IACb,GAAG,SAAS;AAAA,EACd;AAEA,SAAO,EAAE,KAAK,KAAK;AACrB;;;ACpCO,IAAM,aAAa,CAAC,UAA4B;AAErD,MAAI,MAAM,UAAU,GAAG;AACrB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,UAAU,GAAG;AACrB,WAAO,MAAM,CAAC;AAAA,EAChB;AACA,MAAI,WAAW,MAAM,MAAM,GAAG,EAAE,EAAE,KAAK,IAAI;AAC3C,cAAY,QAAQ,MAAM,MAAM,EAAE,CAAC;AACnC,SAAO;AACT;AAEA,IAAM,sBACJ;AAOK,SAAS,cAAc,KAAyC;AACrE,SAAO,oBAAoB,KAAK,OAAO,EAAE;AAC3C;AAaO,SAAS,SAAS,KAAwC;AAC/D,QAAME,KAAI,OAAO;AACjB,SAAOA,GAAE,OAAO,CAAC,EAAE,YAAY,IAAIA,GAAE,MAAM,CAAC;AAC9C;AAKO,SAAS,aAAa,KAAiC;AAC5D,SAAO,MAAM,IAAI,QAAQ,gBAAgB,WAAS,MAAM,YAAY,EAAE,QAAQ,OAAO,EAAE,CAAC,IAAI;AAC9F;AAKO,SAAS,aAAa,KAAiC;AAC5D,SAAO,MAAM,IAAI,QAAQ,UAAU,YAAU,IAAI,OAAO,YAAY,CAAC,EAAE,IAAI;AAC7E;AAEA,IAAM,8BAA8B,CAAC,cAAmB;AACtD,QAAM,gBAAgB,CAAC,QAAkB;AACvC,QAAI,CAAC,KAAK;AACR,aAAO;AAAA,IACT;AAEA,QAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,aAAO,IAAI,IAAI,QAAM;AACnB,YAAI,OAAO,OAAO,YAAY,MAAM,QAAQ,EAAE,GAAG;AAC/C,iBAAO,cAAc,EAAE;AAAA,QACzB;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAEA,UAAM,OAAO,EAAE,GAAG,IAAI;AACtB,UAAM,OAAO,OAAO,KAAK,IAAI;AAC7B,eAAW,WAAW,MAAM;AAC1B,YAAM,UAAU,UAAU,QAAQ,SAAS,CAAC;AAC5C,UAAI,YAAY,SAAS;AACvB,aAAK,OAAO,IAAI,KAAK,OAAO;AAC5B,eAAO,KAAK,OAAO;AAAA,MACrB;AACA,UAAI,OAAO,KAAK,OAAO,MAAM,UAAU;AACrC,aAAK,OAAO,IAAI,cAAc,KAAK,OAAO,CAAC;AAAA,MAC7C;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AASO,IAAM,mBAAmB,4BAA4B,YAAY;AASjE,IAAM,mBAAmB,4BAA4B,YAAY;AAOjE,SAAS,SAAS,OAAyB;AAEhD,MAAI,OAAO,UAAU,WAAW;AAC9B,WAAO;AAAA,EACT;AAGA,MAAI,UAAU,UAAa,UAAU,MAAM;AACzC,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI,MAAM,YAAY,MAAM,QAAQ;AAClC,aAAO;AAAA,IACT;AAEA,QAAI,MAAM,YAAY,MAAM,SAAS;AACnC,aAAO;AAAA,IACT;AAAA,EACF;AAGA,QAAM,SAAS,SAAS,OAAiB,EAAE;AAC3C,MAAI,MAAM,MAAM,GAAG;AACjB,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,GAAG;AACd,WAAO;AAAA,EACT;AAGA,SAAO;AACT;AAKO,SAAS,sBAAwC,KAAoB;AAC1E,SAAO,OAAO,QAAQ,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AACvD,QAAI,UAAU,QAAW;AACvB,UAAI,GAAc,IAAI;AAAA,IACxB;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAe;AACrB;;;ACnKO,IAAM,UAAU,CAAsC,QAAW,UAA2B;AACjG,QAAM,OAAO,EAAE,GAAG,IAAI;AACtB,aAAW,QAAQ,OAAO;AACxB,WAAO,KAAK,IAAI;AAAA,EAClB;AACA,SAAO;AACT;AAEO,IAAM,kBAAkB,CAAmB,QAAuB;AACvE,SAAO,OAAO,QAAQ,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AACvD,QAAI,UAAU,UAAa,UAAU,MAAM;AACzC,UAAI,GAAc,IAAI;AAAA,IACxB;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAe;AACrB;AAEO,IAAM,qBAAqB,CAChC,KACA,OACsB;AACtB,QAAM,SAAS,CAAC;AAChB,aAAW,OAAO,KAAK;AACrB,WAAO,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG;AAAA,EAChC;AACA,SAAO;AACT;AAEO,IAAM,cAAc,CAAgC,KAAQ,WAAmC;AACpG,QAAM,SAAS,CAAC;AAChB,aAAW,OAAO,KAAK;AACrB,QAAI,IAAI,GAAG,KAAK,OAAO,IAAI,GAAG,CAAC,GAAG;AAChC,aAAO,GAAG,IAAI,IAAI,GAAG;AAAA,IACvB;AAAA,EACF;AACA,SAAO;AACT;;;ACpCA,IAAM,iBAA8B,oBAAI,IAAI;AAErC,IAAM,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,UAAU,CAAC,QAAgB;AACzB,QAAI,eAAe,IAAI,GAAG,GAAG;AAC3B;AAAA,IACF;AAEA,mBAAe,IAAI,GAAG;AACtB,YAAQ,KAAK,GAAG;AAAA,EAClB;AAAA,EACA,SAAS,CAAC,QAAgB;AACxB,QAAI,eAAe,IAAI,GAAG,GAAG;AAC3B;AAAA,IACF;AAEA,YAAQ,IAAI,GAAG;AACf,mBAAe,IAAI,GAAG;AAAA,EACxB;AACF;;;ACvBO,IAAM,sBAAsB;AAI5B,SAAS,sBAAsB,KAAU,KAAkB;AAChE,QAAM,YAAY,IAAI,IAAI,GAAG;AAG7B,QAAM,gBAAgB,UAAU,aAAa,IAAI,mBAAmB;AACpE,YAAU,aAAa,OAAO,mBAAmB;AAGjD,QAAM,WAAW,iBAAiB;AAElC,MAAI,UAAU;AACZ,cAAU,aAAa,IAAI,qBAAqB,QAAQ;AAAA,EAC1D;AAEA,SAAO;AACT;AAOO,SAAS,4BAA4B,KAAkB;AAC5D,QAAM,MAAM,kCAAkC,GAAG;AACjD,QAAM,WAAW,oBAAoB,GAAG;AACxC,MAAI,SAAS,SAAS,IAAI,QAAQ,OAAO,WAAW,YAAY,aAAa;AAC3E,eAAW,QAAQ,aAAa,MAAM,IAAI,oBAAoB,GAAG,CAAC;AAAA,EACpE;AACA,SAAO;AACT;AAEA,IAAM,oCAAoC,CAAC,QAAa;AACtD,SAAO,IAAI,aAAa,IAAI,mBAAmB,KAAK;AACtD;AAEA,IAAM,sBAAsB,CAAC,QAAa;AACxC,SAAO,uCAAuC,0BAA0B,GAAG,CAAC;AAC9E;AAEA,IAAM,yCAAyC,CAAC,SAAc;AAC5D,QAAM,MAAM,IAAI,IAAI,IAAI;AACxB,MAAI,aAAa,OAAO,mBAAmB;AAC3C,SAAO;AACT;AAaA,IAAM,4BAA4B,CAAC,SAAc;AAC/C,QAAM,gCAAgC;AACtC,QAAM,6BAA6B;AACnC,QAAM,MAAM,IAAI,IAAI,IAAI;AACxB,MAAI,aAAa,OAAO,0BAA0B;AAClD,MAAI,OAAO,UAAU,IAAI,IAAI,EAAE,QAAQ,+BAA+B,EAAE;AACxE,MAAI,IAAI,KAAK,SAAS,GAAG,GAAG;AAC1B,QAAI,OAAO;AAAA,EACb;AACA,SAAO;AACT;;;ACtEA;AAEA,IAAM,4BAA4B,CAAC,YAA2D;AAC5F,SAAO,CAAC,CAAC,SAAS,YAAY;AAChC;AAEA,IAAM,uBAAuB,CAAC,YAA2C;AACvE,SAAO,CAAC,CAAC,SAAS;AACpB;AAQO,IAAM,iBAAiB,CAAC,MAAc,YAA0C;AAErF,MAAI,OAAO,YAAY,eAAe,QAAQ,OAAO,OAAO,QAAQ,IAAI,IAAI,MAAM,UAAU;AAC1F,WAAO,QAAQ,IAAI,IAAI;AAAA,EACzB;AAGA,MAAI,OAAO,gBAAgB,eAAe,YAAY,OAAO,OAAO,YAAY,IAAI,IAAI,MAAM,UAAU;AACtG,WAAO,YAAY,IAAI,IAAI;AAAA,EAC7B;AAEA,MAAI,0BAA0B,OAAO,GAAG;AACtC,WAAO,QAAQ,WAAW,IAAI,IAAI,KAAK;AAAA,EACzC;AAGA,MAAI,qBAAqB,OAAO,GAAG;AACjC,WAAO,QAAQ,IAAI,IAAI,KAAK;AAAA,EAC9B;AAGA,MAAI,WAAW,OAAO,QAAQ,IAAI,MAAM,UAAU;AAChD,WAAO,QAAQ,IAAI;AAAA,EACrB;AAGA,MAAI;AACF,WAAO,WAAW,IAA+B;AAAA,EACnD,QAAQ;AAAA,EAER;AAEA,SAAO;AACT;;;AC/CA,SAAS,EAAE,GAAG;AACZ,WAAS,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE,UAAU;AACtC,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK;AACvC,QAAE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO,EAAE,GAAG;AAAA,MACd,CAAC;AACD;AAAA,IACF;AACA,QAAI,MAAM,MAAM;AACd,QAAE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO,EAAE,GAAG;AAAA,MACd,CAAC;AACD;AAAA,IACF;AACA,QAAI,MAAM,KAAK;AACb,QAAE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO,EAAE,GAAG;AAAA,MACd,CAAC;AACD;AAAA,IACF;AACA,QAAI,MAAM,KAAK;AACb,QAAE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO,EAAE,GAAG;AAAA,MACd,CAAC;AACD;AAAA,IACF;AACA,QAAI,MAAM,KAAK;AACb,eAAS,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,UAAU;AAC1C,YAAI,IAAI,EAAE,WAAW,CAAC;AACtB,YAAK,KAAK,MAAM,KAAK,MAAQ,KAAK,MAAM,KAAK,MAAQ,KAAK,MAAM,KAAK,OAAQ,MAAM,IAAI;AACrF,eAAK,EAAE,GAAG;AACV;AAAA,QACF;AACA;AAAA,MACF;AACA,UAAI,CAAC,EAAG,OAAM,IAAI,UAAU,6BAA6B,OAAO,CAAC,CAAC;AAClE,QAAE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,MACT,CAAC,GACE,IAAI;AACP;AAAA,IACF;AACA,QAAI,MAAM,KAAK;AACb,UAAI,IAAI,GACN,IAAI,IACJ,IAAI,IAAI;AACV,UAAI,EAAE,CAAC,MAAM,IAAK,OAAM,IAAI,UAAU,oCAAoC,OAAO,CAAC,CAAC;AACnF,aAAO,IAAI,EAAE,UAAU;AACrB,YAAI,EAAE,CAAC,MAAM,MAAM;AACjB,eAAK,EAAE,GAAG,IAAI,EAAE,GAAG;AACnB;AAAA,QACF;AACA,YAAI,EAAE,CAAC,MAAM,KAAK;AAChB,cAAK,KAAK,MAAM,GAAI;AAClB;AACA;AAAA,UACF;AAAA,QACF,WAAW,EAAE,CAAC,MAAM,QAAQ,KAAK,EAAE,IAAI,CAAC,MAAM;AAC5C,gBAAM,IAAI,UAAU,uCAAuC,OAAO,CAAC,CAAC;AACtE,aAAK,EAAE,GAAG;AAAA,MACZ;AACA,UAAI,EAAG,OAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,CAAC;AAC7D,UAAI,CAAC,EAAG,OAAM,IAAI,UAAU,sBAAsB,OAAO,CAAC,CAAC;AAC3D,QAAE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,MACT,CAAC,GACE,IAAI;AACP;AAAA,IACF;AACA,MAAE,KAAK;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO,EAAE,GAAG;AAAA,IACd,CAAC;AAAA,EACH;AACA,SACE,EAAE,KAAK;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,EACT,CAAC,GACD;AAEJ;AAEA,SAAS,EAAE,GAAG,GAAG;AACf,QAAM,WAAW,IAAI,CAAC;AACtB,WACM,IAAI,EAAE,CAAC,GACT,IAAI,EAAE,UACN,IAAI,MAAM,SAAS,OAAO,GAC1B,IAAI,EAAE,WACN,IAAI,MAAM,SAAS,QAAQ,GAC3B,IAAI,CAAC,GACL,IAAI,GACJ,IAAI,GACJ,IAAI,IACJ,IAAI,SAAU,GAAG;AACf,QAAI,IAAI,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAG,QAAO,EAAE,GAAG,EAAE;AAAA,EACrD,GACA,IAAI,SAAU,GAAG;AACf,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,MAAM,OAAQ,QAAO;AACzB,QAAI,IAAI,EAAE,CAAC,GACT,IAAI,EAAE,MACN,IAAI,EAAE;AACR,UAAM,IAAI,UAAU,cAAc,OAAO,GAAG,MAAM,EAAE,OAAO,GAAG,aAAa,EAAE,OAAO,CAAC,CAAC;AAAA,EACxF,GACA,IAAI,WAAY;AACd,aAAS,IAAI,IAAI,GAAI,IAAI,EAAE,MAAM,KAAK,EAAE,cAAc,IAAM,MAAK;AACjE,WAAO;AAAA,EACT,GACA,IAAI,SAAU,GAAG;AACf,aAAS,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACxC,UAAI,IAAI,EAAE,CAAC;AACX,UAAI,EAAE,QAAQ,CAAC,IAAI,GAAI,QAAO;AAAA,IAChC;AACA,WAAO;AAAA,EACT,GACA,IAAI,SAAU,GAAG;AACf,QAAI,IAAI,EAAE,EAAE,SAAS,CAAC,GACpB,IAAI,MAAM,KAAK,OAAO,KAAK,WAAW,IAAI;AAC5C,QAAI,KAAK,CAAC;AACR,YAAM,IAAI,UAAU,8DAA8D,OAAO,EAAE,MAAM,GAAG,CAAC;AACvG,WAAO,CAAC,KAAK,EAAE,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC,GAAG,KAAK,IAAI,SAAS,OAAO,EAAE,CAAC,GAAG,KAAK,EAAE,OAAO,EAAE,CAAC,GAAG,MAAM;AAAA,EACjG,GACF,IAAI,EAAE,UAEN;AACA,QAAI,IAAI,EAAE,MAAM,GACd,IAAI,EAAE,MAAM,GACZ,IAAI,EAAE,SAAS;AACjB,QAAI,KAAK,GAAG;AACV,UAAI,IAAI,KAAK;AACb,QAAE,QAAQ,CAAC,MAAM,OAAQ,KAAK,GAAK,IAAI,KACrC,MAAM,EAAE,KAAK,CAAC,GAAI,IAAI,KACtB,EAAE,KAAK;AAAA,QACL,MAAM,KAAK;AAAA,QACX,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,SAAS,KAAK,EAAE,CAAC;AAAA,QACjB,UAAU,EAAE,UAAU,KAAK;AAAA,MAC7B,CAAC;AACH;AAAA,IACF;AACA,QAAI,IAAI,KAAK,EAAE,cAAc;AAC7B,QAAI,GAAG;AACL,WAAK;AACL;AAAA,IACF;AACA,UAAM,EAAE,KAAK,CAAC,GAAI,IAAI;AACtB,QAAI,IAAI,EAAE,MAAM;AAChB,QAAI,GAAG;AACL,UAAI,IAAI,EAAE,GACR,IAAI,EAAE,MAAM,KAAK,IACjB,IAAI,EAAE,SAAS,KAAK,IACpB,IAAI,EAAE;AACR,QAAE,OAAO,GACP,EAAE,KAAK;AAAA,QACL,MAAM,MAAM,IAAI,MAAM;AAAA,QACtB,SAAS,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI;AAAA,QAC1B,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,UAAU,EAAE,UAAU,KAAK;AAAA,MAC7B,CAAC;AACH;AAAA,IACF;AACA,MAAE,KAAK;AAAA,EACT;AACA,SAAO;AACT;AA8CA,SAAS,EAAE,GAAG;AACZ,SAAO,EAAE,QAAQ,6BAA6B,MAAM;AACtD;AAEA,SAAS,EAAE,GAAG;AACZ,SAAO,KAAK,EAAE,YAAY,KAAK;AACjC;AAEA,SAAS,EAAE,GAAG,GAAG;AACf,MAAI,CAAC,EAAG,QAAO;AACf,WAAS,IAAI,2BAA2B,IAAI,GAAG,IAAI,EAAE,KAAK,EAAE,MAAM,GAAG;AACnE,MAAE,KAAK;AAAA,MACL,MAAM,EAAE,CAAC,KAAK;AAAA,MACd,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,SAAS;AAAA,IACX,CAAC,GACE,IAAI,EAAE,KAAK,EAAE,MAAM;AACxB,SAAO;AACT;AAEA,SAAS,EAAE,GAAG,GAAG,GAAG;AAClB,MAAI,IAAI,EAAE,IAAI,SAAU,GAAG;AACzB,WAAO,EAAE,GAAG,GAAG,CAAC,EAAE;AAAA,EACpB,CAAC;AACD,SAAO,IAAI,OAAO,MAAM,OAAO,EAAE,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AACxD;AAEA,SAAS,EAAE,GAAG,GAAG,GAAG;AAClB,SAAO,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC;AACxB;AAEA,SAAS,EAAE,GAAG,GAAG,GAAG;AAClB,QAAM,WAAW,IAAI,CAAC;AACtB,WACM,IAAI,EAAE,QACR,IAAI,MAAM,SAAS,QAAK,GACxB,IAAI,EAAE,OACN,IAAI,MAAM,SAAS,OAAK,GACxB,IAAI,EAAE,KACN,IAAI,MAAM,SAAS,OAAK,GACxB,IAAI,EAAE,QACN,IACE,MAAM,SACF,SAAU,GAAG;AACX,WAAO;AAAA,EACT,IACA,GACN,IAAI,EAAE,WACN,IAAI,MAAM,SAAS,QAAQ,GAC3B,IAAI,EAAE,UACN,IAAI,MAAM,SAAS,KAAK,GACxB,IAAI,IAAI,OAAO,EAAE,CAAC,GAAG,KAAK,GAC1B,IAAI,IAAI,OAAO,EAAE,CAAC,GAAG,GAAG,GACxB,IAAI,IAAI,MAAM,IACd,IAAI,GACJ,IAAI,GACN,IAAI,EAAE,QACN,KACA;AACA,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,OAAO,KAAK,SAAU,MAAK,EAAE,EAAE,CAAC,CAAC;AAAA,SAChC;AACH,UAAI,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,GACnB,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC;AACnB,UAAI,EAAE;AACJ,YAAK,KAAK,EAAE,KAAK,CAAC,GAAG,KAAK;AACxB,cAAI,EAAE,aAAa,OAAO,EAAE,aAAa,KAAK;AAC5C,gBAAI,IAAI,EAAE,aAAa,MAAM,MAAM;AACnC,iBAAK,MACF,OAAO,GAAG,MAAM,EAChB,OAAO,EAAE,SAAS,MAAM,EACxB,OAAO,CAAC,EACR,OAAO,GAAG,KAAK,EACf,OAAO,EAAE,SAAS,MAAM,EACxB,OAAO,GAAG,GAAG,EACb,OAAO,CAAC;AAAA,UACb,MAAO,MAAK,MAAM,OAAO,GAAG,GAAG,EAAE,OAAO,EAAE,SAAS,GAAG,EAAE,OAAO,GAAG,GAAG,EAAE,OAAO,EAAE,QAAQ;AAAA,aACrF;AACH,cAAI,EAAE,aAAa,OAAO,EAAE,aAAa;AACvC,kBAAM,IAAI,UAAU,mBAAmB,OAAO,EAAE,MAAM,+BAA+B,CAAC;AACxF,eAAK,IAAI,OAAO,EAAE,SAAS,GAAG,EAAE,OAAO,EAAE,QAAQ;AAAA,QACnD;AAAA,UACG,MAAK,MAAM,OAAO,CAAC,EAAE,OAAO,GAAG,GAAG,EAAE,OAAO,EAAE,QAAQ;AAAA,IAC5D;AAAA,EACF;AACA,MAAI,EAAG,OAAM,KAAK,GAAG,OAAO,GAAG,GAAG,IAAK,KAAK,EAAE,WAAW,MAAM,OAAO,GAAG,GAAG,IAAI;AAAA,OAC3E;AACH,QAAI,IAAI,EAAE,EAAE,SAAS,CAAC,GACpB,IAAI,OAAO,KAAK,WAAW,EAAE,QAAQ,EAAE,EAAE,SAAS,CAAC,CAAC,IAAI,KAAK,MAAM;AACrE,UAAM,KAAK,MAAM,OAAO,GAAG,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,MAAM,KAAK,MAAM,OAAO,GAAG,GAAG,EAAE,OAAO,GAAG,GAAG;AAAA,EACpG;AACA,SAAO,IAAI,OAAO,GAAG,EAAE,CAAC,CAAC;AAC3B;AAEA,SAAS,EAAE,GAAG,GAAG,GAAG;AAClB,SAAO,aAAa,SAAS,EAAE,GAAG,CAAC,IAAI,MAAM,QAAQ,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC;AAClF;;;AC/TO,IAAM,eAAe,CAAC,SAAiB;AAC5C,MAAI;AAEF,WAAO,EAAiB,IAAI;AAAA,EAC9B,SAAS,GAAQ;AACf,UAAM,IAAI;AAAA,MACR,iBAAiB,IAAI;AAAA;AAAA,EAA6G,EAAE,OAAO;AAAA,IAC7I;AAAA,EACF;AACF;;;ACXA,IAAM,sBAAsB,CAAC,aAAqC;AAChE,SAAO,SAAS,IAAI,aAAY,mBAAmB,SAAS,UAAU,aAAa,OAAO,CAAE;AAC9F;AAQO,IAAM,oBAAoB,CAAC,aAA+B;AAC/D,QAAM,gBAAgB,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,OAAO,OAAO;AAC5D,QAAM,WAAW,oBAAoB,aAAa;AAClD,SAAO,CAAC,aAAqB,SAAS,KAAK,aAAW,QAAQ,KAAK,QAAQ,CAAC;AAC9E;;;ACXO,IAAM,iCAAiC;AAcvC,SAAS,gCAAgC;AAAA,EAC9C;AAAA,EACA;AAAA,EACA;AACF,GAIG;AACD,QAAM,cACJ,QAAQ,IAAI,WAAW,QAAQ,IAAI,KAAK,SAAS,aAAa,KAAK,QAAQ,QAAQ,IAAI,uBAAuB;AAChH,QAAM,wBAAwB,gCAAgC,cAAc;AAC5E,MAAI,eAAe,uBAAuB;AACxC,UAAM,yBAAyB,eAAe,SAAS,mBAAmB;AAG1E,QAAI,CAAC,wBAAwB;AAC3B,YAAM,MAAM,IAAI,IAAI,cAAc;AAClC,UAAI,aAAa,OAAO,gCAAgC,KAAK,IAAI,EAAE,SAAS,CAAC;AAC7E,0BAAoB,IAAI,YAAY,IAAI,SAAS,CAAC;AAAA,IACpD;AAAA,EACF;AACF;", "names": ["s", "s", "packageName", "customMessages", "_", "s", "_", "s", "url", "setTimeout", "id", "s"]}