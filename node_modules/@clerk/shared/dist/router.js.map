{"version": 3, "sources": ["../src/router.ts", "../src/url.ts", "../src/router/router.ts", "../src/router/react.tsx"], "sourcesContent": ["export { type <PERSON><PERSON><PERSON><PERSON>, type ClerkHostRouter, type Routing<PERSON><PERSON>, createClerkRouter } from './router/router';\nexport {\n  Router,\n  useClerkRouter,\n  useClerkHostRouter,\n  Route,\n  ClerkRouterContext,\n  ClerkHostRouterContext,\n} from './router/react';\n", "import { CURRENT_DEV_INSTANCE_SUFFIXES, LEGACY_DEV_INSTANCE_SUFFIXES } from './constants';\nimport { isStaging } from './utils/instance';\n\nexport function parseSearchParams(queryString = ''): URLSearchParams {\n  if (queryString.startsWith('?')) {\n    queryString = queryString.slice(1);\n  }\n  return new URLSearchParams(queryString);\n}\n\nexport function stripScheme(url = ''): string {\n  return (url || '').replace(/^.+:\\/\\//, '');\n}\n\nexport function addClerkPrefix(str: string | undefined) {\n  if (!str) {\n    return '';\n  }\n  let regex;\n  if (str.match(/^(clerk\\.)+\\w*$/)) {\n    regex = /(clerk\\.)*(?=clerk\\.)/;\n  } else if (str.match(/\\.clerk.accounts/)) {\n    return str;\n  } else {\n    regex = /^(clerk\\.)*/gi;\n  }\n\n  const stripped = str.replace(regex, '');\n  return `clerk.${stripped}`;\n}\n\n/**\n *\n * Retrieve the clerk-js major tag using the major version from the pkgVersion\n * param or use the frontendApi to determine if the canary tag should be used.\n * The default tag is `latest`.\n */\nexport const getClerkJsMajorVersionOrTag = (frontendApi: string, version?: string) => {\n  if (!version && isStaging(frontendApi)) {\n    return 'canary';\n  }\n\n  if (!version) {\n    return 'latest';\n  }\n\n  return version.split('.')[0] || 'latest';\n};\n\n/**\n *\n * Retrieve the clerk-js script url from the frontendApi and the major tag\n * using the {@link getClerkJsMajorVersionOrTag} or a provided clerkJSVersion tag.\n */\nexport const getScriptUrl = (frontendApi: string, { clerkJSVersion }: { clerkJSVersion?: string }) => {\n  const noSchemeFrontendApi = frontendApi.replace(/http(s)?:\\/\\//, '');\n  const major = getClerkJsMajorVersionOrTag(frontendApi, clerkJSVersion);\n  return `https://${noSchemeFrontendApi}/npm/@clerk/clerk-js@${clerkJSVersion || major}/dist/clerk.browser.js`;\n};\n\n// Returns true for hosts such as:\n// * accounts.foo.bar-13.lcl.dev\n// * accounts.foo.bar-13.lclstage.dev\n// * accounts.foo.bar-13.dev.lclclerk.com\nexport function isLegacyDevAccountPortalOrigin(host: string): boolean {\n  return LEGACY_DEV_INSTANCE_SUFFIXES.some(legacyDevSuffix => {\n    return host.startsWith('accounts.') && host.endsWith(legacyDevSuffix);\n  });\n}\n\n// Returns true for hosts such as:\n// * foo-bar-13.accounts.dev\n// * foo-bar-13.accountsstage.dev\n// * foo-bar-13.accounts.lclclerk.com\n// But false for:\n// * foo-bar-13.clerk.accounts.lclclerk.com\nexport function isCurrentDevAccountPortalOrigin(host: string): boolean {\n  return CURRENT_DEV_INSTANCE_SUFFIXES.some(currentDevSuffix => {\n    return host.endsWith(currentDevSuffix) && !host.endsWith('.clerk' + currentDevSuffix);\n  });\n}\n\n/* Functions below are taken from https://github.com/unjs/ufo/blob/main/src/utils.ts. LICENSE: MIT */\n\nconst TRAILING_SLASH_RE = /\\/$|\\/\\?|\\/#/;\n\nexport function hasTrailingSlash(input = '', respectQueryAndFragment?: boolean): boolean {\n  if (!respectQueryAndFragment) {\n    return input.endsWith('/');\n  }\n  return TRAILING_SLASH_RE.test(input);\n}\n\nexport function withTrailingSlash(input = '', respectQueryAndFragment?: boolean): string {\n  if (!respectQueryAndFragment) {\n    return input.endsWith('/') ? input : input + '/';\n  }\n  if (hasTrailingSlash(input, true)) {\n    return input || '/';\n  }\n  let path = input;\n  let fragment = '';\n  const fragmentIndex = input.indexOf('#');\n  if (fragmentIndex >= 0) {\n    path = input.slice(0, fragmentIndex);\n    fragment = input.slice(fragmentIndex);\n    if (!path) {\n      return fragment;\n    }\n  }\n  const [s0, ...s] = path.split('?');\n  return s0 + '/' + (s.length > 0 ? `?${s.join('?')}` : '') + fragment;\n}\n\nexport function withoutTrailingSlash(input = '', respectQueryAndFragment?: boolean): string {\n  if (!respectQueryAndFragment) {\n    return (hasTrailingSlash(input) ? input.slice(0, -1) : input) || '/';\n  }\n  if (!hasTrailingSlash(input, true)) {\n    return input || '/';\n  }\n  let path = input;\n  let fragment = '';\n  const fragmentIndex = input.indexOf('#');\n  if (fragmentIndex >= 0) {\n    path = input.slice(0, fragmentIndex);\n    fragment = input.slice(fragmentIndex);\n  }\n  const [s0, ...s] = path.split('?');\n  return (s0.slice(0, -1) || '/') + (s.length > 0 ? `?${s.join('?')}` : '') + fragment;\n}\n\nexport function hasLeadingSlash(input = ''): boolean {\n  return input.startsWith('/');\n}\n\nexport function withoutLeadingSlash(input = ''): string {\n  return (hasLeadingSlash(input) ? input.slice(1) : input) || '/';\n}\n\nexport function withLeadingSlash(input = ''): string {\n  return hasLeadingSlash(input) ? input : '/' + input;\n}\n\nexport function cleanDoubleSlashes(input = ''): string {\n  return input\n    .split('://')\n    .map(string_ => string_.replace(/\\/{2,}/g, '/'))\n    .join('://');\n}\n\nexport function isNonEmptyURL(url: string) {\n  return url && url !== '/';\n}\n\nconst JOIN_LEADING_SLASH_RE = /^\\.?\\//;\n\nexport function joinURL(base: string, ...input: string[]): string {\n  let url = base || '';\n\n  for (const segment of input.filter(url => isNonEmptyURL(url))) {\n    if (url) {\n      // TODO: Handle .. when joining\n      const _segment = segment.replace(JOIN_LEADING_SLASH_RE, '');\n      url = withTrailingSlash(url) + _segment;\n    } else {\n      url = segment;\n    }\n  }\n\n  return url;\n}\n\n/* Code below is taken from https://github.com/vercel/next.js/blob/fe7ff3f468d7651a92865350bfd0f16ceba27db5/packages/next/src/shared/lib/utils.ts. LICENSE: MIT */\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url);\n", "import type { ClerkHostRouter, RoutingMode } from '@clerk/types';\n\nimport { isAbsoluteUrl, withLeadingSlash, withoutTrailingSlash } from '../url';\n\nexport const PRESERVED_QUERYSTRING_PARAMS = ['after_sign_in_url', 'after_sign_up_url', 'redirect_url'];\n\n/**\n * Internal Clerk router, used by Clerk components to interact with the host's router.\n */\nexport type ClerkRouter = {\n  makeDestinationUrlWithPreservedQueryParameters: (path: string) => string;\n  /**\n   * The basePath the router is currently mounted on.\n   */\n  basePath: string;\n  /**\n   * Creates a child router instance scoped to the provided base path.\n   */\n  child: (childBasePath: string) => ClerkRouter;\n  /**\n   * Matches the provided path against the router's current path. If index is provided, matches against the root route of the router.\n   */\n  match: (path?: string, index?: boolean) => boolean;\n\n  /**\n   * Mode of the router instance, path-based or virtual\n   */\n  readonly mode: RoutingMode;\n\n  /**\n   * Name of the router instance\n   */\n  readonly name: string;\n\n  /**\n   * Navigates to the provided path via a history push\n   */\n  push: ClerkHostRouter['push'];\n  /**\n   * Navigates to the provided path via a history replace\n   */\n  replace: ClerkHostRouter['replace'];\n  /**\n   * If supported by the host router, navigates to the provided path without triggering a full navigation\n   */\n  shallowPush: ClerkHostRouter['shallowPush'];\n  /**\n   * Returns the current pathname (including the base path)\n   */\n  pathname: ClerkHostRouter['pathname'];\n  /**\n   * Returns the current search params\n   */\n  searchParams: ClerkHostRouter['searchParams'];\n};\n\n/**\n * Ensures the provided path has a leading slash and no trailing slash\n */\nfunction normalizePath(path: string) {\n  return withoutTrailingSlash(withLeadingSlash(path));\n}\n\n/**\n * Factory function to create an instance of ClerkRouter with the provided host router.\n *\n * @param router host router instance to be used by the router\n * @param basePath base path of the router, navigation and matching will be scoped to this path\n * @returns A ClerkRouter instance\n */\nexport function createClerkRouter(router: ClerkHostRouter, basePath: string = '/'): ClerkRouter {\n  const normalizedBasePath = normalizePath(basePath);\n\n  /**\n   * Certain query parameters need to be preserved when navigating internally. These query parameters are ultimately used by Clerk to dictate behavior, so we keep them around.\n   */\n  function makeDestinationUrlWithPreservedQueryParameters(path: string) {\n    // If the provided path is an absolute URL, return it unmodified.\n    if (isAbsoluteUrl(path)) {\n      return path;\n    }\n\n    const destinationUrl = new URL(path, window.location.origin);\n    const currentSearchParams = router.searchParams();\n\n    PRESERVED_QUERYSTRING_PARAMS.forEach(key => {\n      const maybeValue = currentSearchParams.get(key);\n      if (maybeValue) {\n        destinationUrl.searchParams.set(key, maybeValue);\n      }\n    });\n\n    return `${destinationUrl.pathname}${destinationUrl.search}`;\n  }\n\n  function match(path?: string, index?: boolean) {\n    const pathToMatch = path ?? (index && '/');\n\n    if (!pathToMatch) {\n      throw new Error('[clerk] router.match() requires either a path to match, or the index flag must be set to true.');\n    }\n\n    const normalizedPath = normalizePath(pathToMatch);\n\n    return normalizePath(`${normalizedBasePath}${normalizedPath}`) === normalizePath(router.pathname());\n  }\n\n  function child(childBasePath: string) {\n    return createClerkRouter(router, `${normalizedBasePath}${normalizePath(childBasePath)}`);\n  }\n\n  function push(path: string) {\n    const destinationUrl = makeDestinationUrlWithPreservedQueryParameters(path);\n    return router.push(destinationUrl);\n  }\n\n  function replace(path: string) {\n    const destinationUrl = makeDestinationUrlWithPreservedQueryParameters(path);\n    return router.replace(destinationUrl);\n  }\n\n  function shallowPush(path: string) {\n    const destinationUrl = makeDestinationUrlWithPreservedQueryParameters(path);\n    return router.shallowPush(destinationUrl);\n  }\n\n  function pathname() {\n    return router.pathname();\n  }\n\n  function searchParams() {\n    return router.searchParams();\n  }\n\n  return {\n    makeDestinationUrlWithPreservedQueryParameters,\n    child,\n    match,\n    mode: router.mode,\n    name: router.name,\n    push,\n    replace,\n    shallowPush,\n    pathname,\n    searchParams,\n    basePath: normalizedBasePath,\n  };\n}\n\nexport type { ClerkHostRouter, RoutingMode };\n", "/**\n * React-specific binding's for interacting with Clerk's router interface.\n */\nimport React, { createContext, useContext } from 'react';\n\nimport type { <PERSON><PERSON>ost<PERSON>outer, ClerkRouter } from './router';\nimport { createClerkRouter } from './router';\n\nexport const ClerkHostRouterContext = createContext<ClerkHostRouter | null>(null);\nexport const ClerkRouterContext = createContext<ClerkRouter | null>(null);\n\nexport function useClerkHostRouter() {\n  const ctx = useContext(ClerkHostRouterContext);\n\n  if (!ctx) {\n    throw new Error(\n      'clerk: Unable to locate <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, make sure this is rendered within `<ClerkHostRouterContext.Provider>`.',\n    );\n  }\n\n  return ctx;\n}\n\nexport function useClerkRouter() {\n  const ctx = useContext(ClerkRouterContext);\n\n  if (!ctx) {\n    throw new Error('clerk: Unable to locate <PERSON><PERSON><PERSON><PERSON>, make sure this is rendered within `<Router>`.');\n  }\n\n  return ctx;\n}\n\n/**\n * Construct a Clerk Router using the provided host router. The router instance is accessible using `useClerkRouter()`.\n */\nexport function Router({\n  basePath,\n  children,\n  router,\n}: {\n  children: React.ReactNode;\n  basePath?: string;\n  router?: ClerkHostRouter;\n}) {\n  const hostRouter = useClerkHostRouter();\n  const clerkRouter = createClerkRouter(router ?? hostRouter, basePath);\n\n  return <ClerkRouterContext.Provider value={clerkRouter}>{children}</ClerkRouterContext.Provider>;\n}\n\ntype RouteProps = { path?: string; index?: boolean };\n\n/**\n * Used to conditionally render its children based on whether or not the current path matches the provided path.\n */\nexport function Route({ path, children, index }: RouteProps & { children: React.ReactNode }) {\n  const parentRouter = useClerkRouter();\n\n  if (!path && !index) {\n    return children;\n  }\n\n  if (!parentRouter?.match(path, index)) {\n    return null;\n  }\n\n  return children;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACoFA,IAAM,oBAAoB;AAEnB,SAAS,iBAAiB,QAAQ,IAAI,yBAA4C;AACvF,MAAI,CAAC,yBAAyB;AAC5B,WAAO,MAAM,SAAS,GAAG;AAAA,EAC3B;AACA,SAAO,kBAAkB,KAAK,KAAK;AACrC;AAuBO,SAAS,qBAAqB,QAAQ,IAAI,yBAA2C;AAC1F,MAAI,CAAC,yBAAyB;AAC5B,YAAQ,iBAAiB,KAAK,IAAI,MAAM,MAAM,GAAG,EAAE,IAAI,UAAU;AAAA,EACnE;AACA,MAAI,CAAC,iBAAiB,OAAO,IAAI,GAAG;AAClC,WAAO,SAAS;AAAA,EAClB;AACA,MAAI,OAAO;AACX,MAAI,WAAW;AACf,QAAM,gBAAgB,MAAM,QAAQ,GAAG;AACvC,MAAI,iBAAiB,GAAG;AACtB,WAAO,MAAM,MAAM,GAAG,aAAa;AACnC,eAAW,MAAM,MAAM,aAAa;AAAA,EACtC;AACA,QAAM,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,GAAG;AACjC,UAAQ,GAAG,MAAM,GAAG,EAAE,KAAK,QAAQ,EAAE,SAAS,IAAI,IAAI,EAAE,KAAK,GAAG,CAAC,KAAK,MAAM;AAC9E;AAEO,SAAS,gBAAgB,QAAQ,IAAa;AACnD,SAAO,MAAM,WAAW,GAAG;AAC7B;AAMO,SAAS,iBAAiB,QAAQ,IAAY;AACnD,SAAO,gBAAgB,KAAK,IAAI,QAAQ,MAAM;AAChD;AAmCA,IAAM,qBAAqB;AACpB,IAAM,gBAAgB,CAAC,QAAgB,mBAAmB,KAAK,GAAG;;;AC9KlE,IAAM,+BAA+B,CAAC,qBAAqB,qBAAqB,cAAc;AAuDrG,SAAS,cAAc,MAAc;AACnC,SAAO,qBAAqB,iBAAiB,IAAI,CAAC;AACpD;AASO,SAAS,kBAAkB,QAAyB,WAAmB,KAAkB;AAC9F,QAAM,qBAAqB,cAAc,QAAQ;AAKjD,WAAS,+CAA+C,MAAc;AAEpE,QAAI,cAAc,IAAI,GAAG;AACvB,aAAO;AAAA,IACT;AAEA,UAAM,iBAAiB,IAAI,IAAI,MAAM,OAAO,SAAS,MAAM;AAC3D,UAAM,sBAAsB,OAAO,aAAa;AAEhD,iCAA6B,QAAQ,SAAO;AAC1C,YAAM,aAAa,oBAAoB,IAAI,GAAG;AAC9C,UAAI,YAAY;AACd,uBAAe,aAAa,IAAI,KAAK,UAAU;AAAA,MACjD;AAAA,IACF,CAAC;AAED,WAAO,GAAG,eAAe,QAAQ,GAAG,eAAe,MAAM;AAAA,EAC3D;AAEA,WAAS,MAAM,MAAe,OAAiB;AAC7C,UAAM,cAAc,SAAS,SAAS;AAEtC,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,gGAAgG;AAAA,IAClH;AAEA,UAAM,iBAAiB,cAAc,WAAW;AAEhD,WAAO,cAAc,GAAG,kBAAkB,GAAG,cAAc,EAAE,MAAM,cAAc,OAAO,SAAS,CAAC;AAAA,EACpG;AAEA,WAAS,MAAM,eAAuB;AACpC,WAAO,kBAAkB,QAAQ,GAAG,kBAAkB,GAAG,cAAc,aAAa,CAAC,EAAE;AAAA,EACzF;AAEA,WAAS,KAAK,MAAc;AAC1B,UAAM,iBAAiB,+CAA+C,IAAI;AAC1E,WAAO,OAAO,KAAK,cAAc;AAAA,EACnC;AAEA,WAAS,QAAQ,MAAc;AAC7B,UAAM,iBAAiB,+CAA+C,IAAI;AAC1E,WAAO,OAAO,QAAQ,cAAc;AAAA,EACtC;AAEA,WAAS,YAAY,MAAc;AACjC,UAAM,iBAAiB,+CAA+C,IAAI;AAC1E,WAAO,OAAO,YAAY,cAAc;AAAA,EAC1C;AAEA,WAAS,WAAW;AAClB,WAAO,OAAO,SAAS;AAAA,EACzB;AAEA,WAAS,eAAe;AACtB,WAAO,OAAO,aAAa;AAAA,EAC7B;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM,OAAO;AAAA,IACb,MAAM,OAAO;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,EACZ;AACF;;;AChJA,mBAAiD;AAK1C,IAAM,6BAAyB,4BAAsC,IAAI;AACzE,IAAM,yBAAqB,4BAAkC,IAAI;AAEjE,SAAS,qBAAqB;AACnC,QAAM,UAAM,yBAAW,sBAAsB;AAE7C,MAAI,CAAC,KAAK;AACR,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEO,SAAS,iBAAiB;AAC/B,QAAM,UAAM,yBAAW,kBAAkB;AAEzC,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,MAAM,oFAAoF;AAAA,EACtG;AAEA,SAAO;AACT;AAKO,SAAS,OAAO;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AACF,GAIG;AACD,QAAM,aAAa,mBAAmB;AACtC,QAAM,cAAc,kBAAkB,UAAU,YAAY,QAAQ;AAEpE,SAAO,6BAAAA,QAAA,cAAC,mBAAmB,UAAnB,EAA4B,OAAO,eAAc,QAAS;AACpE;AAOO,SAAS,MAAM,EAAE,MAAM,UAAU,MAAM,GAA+C;AAC3F,QAAM,eAAe,eAAe;AAEpC,MAAI,CAAC,QAAQ,CAAC,OAAO;AACnB,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,cAAc,MAAM,MAAM,KAAK,GAAG;AACrC,WAAO;AAAA,EACT;AAEA,SAAO;AACT;", "names": ["React"]}