import { OAuthProviderData, OAuthProvider, OAuthStrategy } from '@clerk/types';

declare const OAUTH_PROVIDERS: OAuthProviderData[];
interface getOAuthProviderDataProps {
    provider?: OAuthProvider;
    strategy?: OAuthStrategy;
}
declare function getOAuthProviderData({ provider, strategy, }: getOAuthProviderDataProps): OAuthProviderData | undefined | null;

export { OAUTH_PROVIDERS, getOAuthProviderData };
