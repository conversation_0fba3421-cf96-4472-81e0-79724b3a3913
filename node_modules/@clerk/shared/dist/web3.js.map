{"version": 3, "sources": ["../src/web3.ts"], "sourcesContent": ["import type { Web3ProviderData } from '@clerk/types';\n\nexport const WEB3_PROVIDERS: Web3ProviderData[] = [\n  {\n    provider: 'metamask',\n    strategy: 'web3_metamask_signature',\n    name: 'MetaMask',\n  },\n  {\n    provider: 'coinbase_wallet',\n    strategy: 'web3_coinbase_wallet_signature',\n    name: 'Coinbase Wallet',\n  },\n  {\n    provider: 'okx_wallet',\n    strategy: 'web3_okx_wallet_signature',\n    name: 'OKX Wallet',\n  },\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,IAAM,iBAAqC;AAAA,EAChD;AAAA,IACE,UAAU;AAAA,IACV,UAAU;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,UAAU;AAAA,IACV,UAAU;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,UAAU;AAAA,IACV,UAAU;AAAA,IACV,MAAM;AAAA,EACR;AACF;", "names": []}