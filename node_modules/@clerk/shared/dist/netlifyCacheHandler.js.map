{"version": 3, "sources": ["../src/netlifyCacheHandler.ts", "../src/keys.ts"], "sourcesContent": ["/* eslint-disable turbo/no-undeclared-env-vars */\nimport { isDevelopmentFromPublishableKey } from './keys';\n\n/**\n * Cache busting parameter for Netlify to prevent cached responses\n * during handshake flows with Clerk development instances.\n *\n * Note: This query parameter will be removed in the \"@clerk/clerk-js\" package.\n *\n * @internal\n */\nexport const CLERK_NETLIFY_CACHE_BUST_PARAM = '__clerk_netlify_cache_bust';\n\n/**\n * Prevents infinite redirects in Netlify's functions by adding a cache bust parameter\n * to the original redirect URL. This ensures that <PERSON>lify doesn't serve a cached response\n * during the handshake flow.\n *\n * The issue happens only on Clerk development instances running on Netlify. This is\n * a workaround until we find a better solution.\n *\n * See https://answers.netlify.com/t/cache-handling-recommendation-for-authentication-handshake-redirects/143969/1\n *\n * @internal\n */\nexport function handleNetlifyCacheInDevInstance({\n  locationHeader,\n  requestStateHeaders,\n  publishableKey,\n}: {\n  locationHeader: string;\n  requestStateHeaders: Headers;\n  publishableKey: string;\n}) {\n  const isOnNetlify =\n    process.env.NETLIFY || process.env.URL?.endsWith('netlify.app') || Boolean(process.env.NETLIFY_FUNCTIONS_TOKEN);\n  const isDevelopmentInstance = isDevelopmentFromPublishableKey(publishableKey);\n  if (isOnNetlify && isDevelopmentInstance) {\n    const hasHandshakeQueryParam = locationHeader.includes('__clerk_handshake');\n    // If location header is the original URL before the handshake flow, add cache bust param\n    // The param should be removed in clerk-js\n    if (!hasHandshakeQueryParam) {\n      const url = new URL(locationHeader);\n      url.searchParams.append(CLERK_NETLIFY_CACHE_BUST_PARAM, Date.now().toString());\n      requestStateHeaders.set('Location', url.toString());\n    }\n  }\n}\n", "import type { Publishable<PERSON>ey } from '@clerk/types';\n\nimport { DEV_OR_STAGING_SUFFIXES, LEGACY_DEV_INSTANCE_SUFFIXES } from './constants';\nimport { isomorphicAtob } from './isomorphicAtob';\nimport { isomorphicBtoa } from './isomorphicBtoa';\n\ntype ParsePublishableKeyOptions = {\n  fatal?: boolean;\n  domain?: string;\n  proxyUrl?: string;\n  isSatellite?: boolean;\n};\n\nconst PUBLISHABLE_KEY_LIVE_PREFIX = 'pk_live_';\nconst PUBLISHABLE_KEY_TEST_PREFIX = 'pk_test_';\n\n// This regex matches the publishable like frontend API keys (e.g. foo-bar-13.clerk.accounts.dev)\nconst PUBLISHABLE_FRONTEND_API_DEV_REGEX = /^(([a-z]+)-){2}([0-9]{1,2})\\.clerk\\.accounts([a-z.]*)(dev|com)$/i;\n\nexport function buildPublishableKey(frontendApi: string): string {\n  const isDevKey =\n    PUBLISHABLE_FRONTEND_API_DEV_REGEX.test(frontendApi) ||\n    (frontendApi.startsWith('clerk.') && LEGACY_DEV_INSTANCE_SUFFIXES.some(s => frontendApi.endsWith(s)));\n  const keyPrefix = isDevKey ? PUBLISHABLE_KEY_TEST_PREFIX : PUBLISHABLE_KEY_LIVE_PREFIX;\n  return `${keyPrefix}${isomorphicBtoa(`${frontendApi}$`)}`;\n}\n\nexport function parsePublishableKey(\n  key: string | undefined,\n  options: ParsePublishableKeyOptions & { fatal: true },\n): PublishableKey;\nexport function parsePublishableKey(\n  key: string | undefined,\n  options?: ParsePublishableKeyOptions,\n): PublishableKey | null;\nexport function parsePublishableKey(\n  key: string | undefined,\n  options: { fatal?: boolean; domain?: string; proxyUrl?: string; isSatellite?: boolean } = {},\n): PublishableKey | null {\n  key = key || '';\n\n  if (!key || !isPublishableKey(key)) {\n    if (options.fatal && !key) {\n      throw new Error(\n        'Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys',\n      );\n    }\n    if (options.fatal && !isPublishableKey(key)) {\n      throw new Error('Publishable key not valid.');\n    }\n    return null;\n  }\n\n  const instanceType = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) ? 'production' : 'development';\n\n  let frontendApi = isomorphicAtob(key.split('_')[2]);\n\n  // TODO(@dimkl): validate packages/clerk-js/src/utils/instance.ts\n  frontendApi = frontendApi.slice(0, -1);\n\n  if (options.proxyUrl) {\n    frontendApi = options.proxyUrl;\n  } else if (instanceType !== 'development' && options.domain && options.isSatellite) {\n    frontendApi = `clerk.${options.domain}`;\n  }\n\n  return {\n    instanceType,\n    frontendApi,\n  };\n}\n\n/**\n * Checks if the provided key is a valid publishable key.\n *\n * @param key - The key to be checked. Defaults to an empty string if not provided.\n * @returns `true` if 'key' is a valid publishable key, `false` otherwise.\n */\nexport function isPublishableKey(key: string = '') {\n  try {\n    const hasValidPrefix = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) || key.startsWith(PUBLISHABLE_KEY_TEST_PREFIX);\n\n    const hasValidFrontendApiPostfix = isomorphicAtob(key.split('_')[2] || '').endsWith('$');\n\n    return hasValidPrefix && hasValidFrontendApiPostfix;\n  } catch {\n    return false;\n  }\n}\n\nexport function createDevOrStagingUrlCache() {\n  const devOrStagingUrlCache = new Map<string, boolean>();\n\n  return {\n    isDevOrStagingUrl: (url: string | URL): boolean => {\n      if (!url) {\n        return false;\n      }\n\n      const hostname = typeof url === 'string' ? url : url.hostname;\n      let res = devOrStagingUrlCache.get(hostname);\n      if (res === undefined) {\n        res = DEV_OR_STAGING_SUFFIXES.some(s => hostname.endsWith(s));\n        devOrStagingUrlCache.set(hostname, res);\n      }\n      return res;\n    },\n  };\n}\n\nexport function isDevelopmentFromPublishableKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('pk_test_');\n}\n\nexport function isProductionFromPublishableKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('pk_live_');\n}\n\nexport function isDevelopmentFromSecretKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('sk_test_');\n}\n\nexport function isProductionFromSecretKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('sk_live_');\n}\n\nexport async function getCookieSuffix(\n  publishableKey: string,\n  subtle: SubtleCrypto = globalThis.crypto.subtle,\n): Promise<string> {\n  const data = new TextEncoder().encode(publishableKey);\n  const digest = await subtle.digest('sha-1', data);\n  const stringDigest = String.fromCharCode(...new Uint8Array(digest));\n  // Base 64 Encoding with URL and Filename Safe Alphabet: https://datatracker.ietf.org/doc/html/rfc4648#section-5\n  return isomorphicBtoa(stringDigest).replace(/\\+/gi, '-').replace(/\\//gi, '_').substring(0, 8);\n}\n\nexport const getSuffixedCookieName = (cookieName: string, cookieSuffix: string): string => {\n  return `${cookieName}_${cookieSuffix}`;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AC8GO,SAAS,gCAAgC,QAAyB;AACvE,SAAO,OAAO,WAAW,OAAO,KAAK,OAAO,WAAW,UAAU;AACnE;;;ADrGO,IAAM,iCAAiC;AAcvC,SAAS,gCAAgC;AAAA,EAC9C;AAAA,EACA;AAAA,EACA;AACF,GAIG;AACD,QAAM,cACJ,QAAQ,IAAI,WAAW,QAAQ,IAAI,KAAK,SAAS,aAAa,KAAK,QAAQ,QAAQ,IAAI,uBAAuB;AAChH,QAAM,wBAAwB,gCAAgC,cAAc;AAC5E,MAAI,eAAe,uBAAuB;AACxC,UAAM,yBAAyB,eAAe,SAAS,mBAAmB;AAG1E,QAAI,CAAC,wBAAwB;AAC3B,YAAM,MAAM,IAAI,IAAI,cAAc;AAClC,UAAI,aAAa,OAAO,gCAAgC,KAAK,IAAI,EAAE,SAAS,CAAC;AAC7E,0BAAoB,IAAI,YAAY,IAAI,SAAS,CAAC;AAAA,IACpD;AAAA,EACF;AACF;", "names": []}