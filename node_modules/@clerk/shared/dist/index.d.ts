export { allSettled, createDeferredPromise, fastDeepMergeAndKeep, fastDeepMergeAndReplace, isDevelopmentEnvironment, isProductionEnvironment, isStaging, isTestEnvironment, logErrorInDevMode, noop } from './utils/index.js';
export { h as handleValueOrFn } from './handleValueOrFn-D2uLOn6s.js';
export { apiUrlFromPublishableKey } from './apiUrlFromPublishableKey.js';
export { inBrowser, isBrowserOnline, isValidBrowser, isValidBrowserOnline, userAgentIsRobot } from './browser.js';
export { colorToSameTypeString, hasAlpha, hexStringToRgbaColor, isHSLColor, isRGBColor, isTransparent, isValidHexString, isValidHslaString, isValidRgbaString, stringToHslaColor, stringToSameTypeColor } from './color.js';
export { CURRENT_DEV_INSTANCE_SUFFIXES, DEV_OR_STAGING_SUFFIXES, LEGACY_DEV_INSTANCE_SUFFIXES, LOCAL_API_URL, LOCAL_ENV_SUFFIXES, PROD_API_URL, STAGING_API_URL, STAGING_ENV_SUFFIXES, iconImageUrl } from './constants.js';
export { RelativeDateCase, addYears, dateTo12HourTime, differenceInCalendarDays, formatRelative, normalizeDate } from './date.js';
export { deprecated, deprecatedObjectProperty, deprecatedProperty } from './deprecated.js';
export { deriveState } from './deriveState.js';
export { ClerkAPIResponseError, ClerkRuntimeError, ClerkWebAuthnError, EmailLinkError, EmailLinkErrorCode, EmailLinkErrorCodeStatus, ErrorThrower, ErrorThrowerOptions, MetamaskError, buildErrorThrower, errorToJSON, is4xxError, isCaptchaError, isClerkAPIResponseError, isClerkRuntimeError, isEmailLinkError, isKnownError, isMetamaskError, isNetworkError, isPasswordPwnedError, isReverificationCancelledError, isUnauthorizedError, isUserLockedError, parseError, parseErrors } from './error.js';
export { SupportedMimeType, extension, readJSONFile } from './file.js';
export { isomorphicAtob } from './isomorphicAtob.js';
export { isomorphicBtoa } from './isomorphicBtoa.js';
export { buildPublishableKey, createDevOrStagingUrlCache, getCookieSuffix, getSuffixedCookieName, isDevelopmentFromPublishableKey, isDevelopmentFromSecretKey, isProductionFromPublishableKey, isProductionFromSecretKey, isPublishableKey, parsePublishableKey } from './keys.js';
export { LoadClerkJsScriptOptions, buildClerkJsScriptAttributes, clerkJsScriptUrl, loadClerkJsScript, setClerkJsLoadingErrorPackageName } from './loadClerkJsScript.js';
export { loadScript } from './loadScript.js';
export { LocalStorageBroadcastChannel } from './localStorageBroadcastChannel.js';
export { Poller, PollerCallback, PollerRun, PollerStop } from './poller.js';
export { isHttpOrHttps, isProxyUrlRelative, isValidProxyUrl, proxyUrlToAbsoluteURL } from './proxy.js';
export { camelToSnake, deepCamelToSnake, deepSnakeToCamel, getNonUndefinedValues, isIPV4Address, isTruthy, snakeToCamel, titleize, toSentence } from './underscore.js';
export { addClerkPrefix, cleanDoubleSlashes, getClerkJsMajorVersionOrTag, getScriptUrl, hasLeadingSlash, hasTrailingSlash, isAbsoluteUrl, isCurrentDevAccountPortalOrigin, isLegacyDevAccountPortalOrigin, isNonEmptyURL, joinURL, parseSearchParams, stripScheme, withLeadingSlash, withTrailingSlash, withoutLeadingSlash, withoutTrailingSlash } from './url.js';
export { versionSelector } from './versionSelector.js';
export { applyFunctionToObj, filterProps, removeUndefined, without } from './object.js';
export { logger } from './logger.js';
export { createWorkerTimers } from './workerTimers/index.js';
export { DEV_BROWSER_JWT_KEY, extractDevBrowserJWTFromURL, setDevBrowserJWTInURL } from './devBrowser.js';
export { getEnvVariable } from './getEnvVariable.js';
export { PathMatcherParam, PathPattern, WithPathPatternWildcard, createPathMatcher } from './pathMatcher.js';
export { CLERK_NETLIFY_CACHE_BUST_PARAM, handleNetlifyCacheInDevInstance } from './netlifyCacheHandler.js';
import '@clerk/types';
