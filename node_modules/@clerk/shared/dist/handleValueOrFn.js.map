{"version": 3, "sources": ["../src/handleValueOrFn.ts", "../src/utils/handleValueOrFn.ts"], "sourcesContent": ["import { handleValueOrFn as origHandleValueOrFn } from './utils/handleValueOrFn';\n\n/**\n * @deprecated - Use `handleValueOrFn` from `@clerk/shared/utils` instead.\n */\nexport const handleValueOrFn = origHandleValueOrFn;\n", "type VOrFnReturnsV<T> = T | undefined | ((v: URL) => T);\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL): T | undefined;\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL, defaultValue: T): T;\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL, defaultValue?: unknown): unknown {\n  if (typeof value === 'function') {\n    return (value as (v: URL) => T)(url);\n  }\n\n  if (typeof value !== 'undefined') {\n    return value;\n  }\n\n  if (typeof defaultValue !== 'undefined') {\n    return defaultValue;\n  }\n\n  return undefined;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,yBAAAA;AAAA;AAAA;;;ACGO,SAAS,gBAAmB,OAAyB,KAAU,cAAiC;AACrG,MAAI,OAAO,UAAU,YAAY;AAC/B,WAAQ,MAAwB,GAAG;AAAA,EACrC;AAEA,MAAI,OAAO,UAAU,aAAa;AAChC,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,iBAAiB,aAAa;AACvC,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ADZO,IAAMC,mBAAkB;", "names": ["handleValueOrFn", "handleValueOrFn"]}