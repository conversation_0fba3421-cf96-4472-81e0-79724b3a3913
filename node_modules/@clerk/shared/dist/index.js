"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var index_exports = {};
__export(index_exports, {
  CLERK_NETLIFY_CACHE_BUST_PARAM: () => CLERK_NETLIFY_CACHE_BUST_PARAM,
  CURRENT_DEV_INSTANCE_SUFFIXES: () => CURRENT_DEV_INSTANCE_SUFFIXES,
  ClerkAPIResponseError: () => ClerkAPIResponseError,
  ClerkRuntimeError: () => ClerkRuntimeError,
  ClerkWebAuthnError: () => ClerkWebAuthnError,
  DEV_BROWSER_JWT_KEY: () => DEV_BROWSER_JWT_KEY,
  DEV_OR_STAGING_SUFFIXES: () => DEV_OR_STAGING_SUFFIXES,
  EmailLinkError: () => EmailLinkError,
  EmailLinkErrorCode: () => EmailLinkErrorCode,
  EmailLinkErrorCodeStatus: () => EmailLinkErrorCodeStatus,
  LEGACY_DEV_INSTANCE_SUFFIXES: () => LEGACY_DEV_INSTANCE_SUFFIXES,
  LOCAL_API_URL: () => LOCAL_API_URL,
  LOCAL_ENV_SUFFIXES: () => LOCAL_ENV_SUFFIXES,
  LocalStorageBroadcastChannel: () => LocalStorageBroadcastChannel,
  PROD_API_URL: () => PROD_API_URL,
  Poller: () => Poller,
  STAGING_API_URL: () => STAGING_API_URL,
  STAGING_ENV_SUFFIXES: () => STAGING_ENV_SUFFIXES,
  addClerkPrefix: () => addClerkPrefix,
  addYears: () => addYears,
  allSettled: () => allSettled,
  apiUrlFromPublishableKey: () => apiUrlFromPublishableKey,
  applyFunctionToObj: () => applyFunctionToObj,
  buildClerkJsScriptAttributes: () => buildClerkJsScriptAttributes,
  buildErrorThrower: () => buildErrorThrower,
  buildPublishableKey: () => buildPublishableKey,
  camelToSnake: () => camelToSnake,
  cleanDoubleSlashes: () => cleanDoubleSlashes,
  clerkJsScriptUrl: () => clerkJsScriptUrl,
  colorToSameTypeString: () => colorToSameTypeString,
  createDeferredPromise: () => createDeferredPromise,
  createDevOrStagingUrlCache: () => createDevOrStagingUrlCache,
  createPathMatcher: () => createPathMatcher,
  createWorkerTimers: () => createWorkerTimers,
  dateTo12HourTime: () => dateTo12HourTime,
  deepCamelToSnake: () => deepCamelToSnake,
  deepSnakeToCamel: () => deepSnakeToCamel,
  deprecated: () => deprecated,
  deprecatedObjectProperty: () => deprecatedObjectProperty,
  deprecatedProperty: () => deprecatedProperty,
  deriveState: () => deriveState,
  differenceInCalendarDays: () => differenceInCalendarDays,
  errorToJSON: () => errorToJSON,
  extension: () => extension,
  extractDevBrowserJWTFromURL: () => extractDevBrowserJWTFromURL,
  fastDeepMergeAndKeep: () => fastDeepMergeAndKeep,
  fastDeepMergeAndReplace: () => fastDeepMergeAndReplace,
  filterProps: () => filterProps,
  formatRelative: () => formatRelative,
  getClerkJsMajorVersionOrTag: () => getClerkJsMajorVersionOrTag,
  getCookieSuffix: () => getCookieSuffix,
  getEnvVariable: () => getEnvVariable,
  getNonUndefinedValues: () => getNonUndefinedValues,
  getScriptUrl: () => getScriptUrl,
  getSuffixedCookieName: () => getSuffixedCookieName,
  handleNetlifyCacheInDevInstance: () => handleNetlifyCacheInDevInstance,
  handleValueOrFn: () => handleValueOrFn,
  hasAlpha: () => hasAlpha,
  hasLeadingSlash: () => hasLeadingSlash,
  hasTrailingSlash: () => hasTrailingSlash,
  hexStringToRgbaColor: () => hexStringToRgbaColor,
  iconImageUrl: () => iconImageUrl,
  inBrowser: () => inBrowser,
  is4xxError: () => is4xxError,
  isAbsoluteUrl: () => isAbsoluteUrl,
  isBrowserOnline: () => isBrowserOnline,
  isCaptchaError: () => isCaptchaError,
  isClerkAPIResponseError: () => isClerkAPIResponseError,
  isClerkRuntimeError: () => isClerkRuntimeError,
  isCurrentDevAccountPortalOrigin: () => isCurrentDevAccountPortalOrigin,
  isDevelopmentEnvironment: () => isDevelopmentEnvironment,
  isDevelopmentFromPublishableKey: () => isDevelopmentFromPublishableKey,
  isDevelopmentFromSecretKey: () => isDevelopmentFromSecretKey,
  isEmailLinkError: () => isEmailLinkError,
  isHSLColor: () => isHSLColor,
  isHttpOrHttps: () => isHttpOrHttps,
  isIPV4Address: () => isIPV4Address,
  isKnownError: () => isKnownError,
  isLegacyDevAccountPortalOrigin: () => isLegacyDevAccountPortalOrigin,
  isMetamaskError: () => isMetamaskError,
  isNetworkError: () => isNetworkError,
  isNonEmptyURL: () => isNonEmptyURL,
  isPasswordPwnedError: () => isPasswordPwnedError,
  isProductionEnvironment: () => isProductionEnvironment,
  isProductionFromPublishableKey: () => isProductionFromPublishableKey,
  isProductionFromSecretKey: () => isProductionFromSecretKey,
  isProxyUrlRelative: () => isProxyUrlRelative,
  isPublishableKey: () => isPublishableKey,
  isRGBColor: () => isRGBColor,
  isReverificationCancelledError: () => isReverificationCancelledError,
  isStaging: () => isStaging,
  isTestEnvironment: () => isTestEnvironment,
  isTransparent: () => isTransparent,
  isTruthy: () => isTruthy,
  isUnauthorizedError: () => isUnauthorizedError,
  isUserLockedError: () => isUserLockedError,
  isValidBrowser: () => isValidBrowser,
  isValidBrowserOnline: () => isValidBrowserOnline,
  isValidHexString: () => isValidHexString,
  isValidHslaString: () => isValidHslaString,
  isValidProxyUrl: () => isValidProxyUrl,
  isValidRgbaString: () => isValidRgbaString,
  isomorphicAtob: () => isomorphicAtob,
  isomorphicBtoa: () => isomorphicBtoa,
  joinURL: () => joinURL,
  loadClerkJsScript: () => loadClerkJsScript,
  loadScript: () => loadScript,
  logErrorInDevMode: () => logErrorInDevMode,
  logger: () => logger,
  noop: () => noop,
  normalizeDate: () => normalizeDate,
  parseError: () => parseError,
  parseErrors: () => parseErrors,
  parsePublishableKey: () => parsePublishableKey,
  parseSearchParams: () => parseSearchParams,
  proxyUrlToAbsoluteURL: () => proxyUrlToAbsoluteURL,
  readJSONFile: () => readJSONFile,
  removeUndefined: () => removeUndefined,
  setClerkJsLoadingErrorPackageName: () => setClerkJsLoadingErrorPackageName,
  setDevBrowserJWTInURL: () => setDevBrowserJWTInURL,
  snakeToCamel: () => snakeToCamel,
  stringToHslaColor: () => stringToHslaColor,
  stringToSameTypeColor: () => stringToSameTypeColor,
  stripScheme: () => stripScheme,
  titleize: () => titleize,
  toSentence: () => toSentence,
  userAgentIsRobot: () => userAgentIsRobot,
  versionSelector: () => versionSelector,
  withLeadingSlash: () => withLeadingSlash,
  withTrailingSlash: () => withTrailingSlash,
  without: () => without,
  withoutLeadingSlash: () => withoutLeadingSlash,
  withoutTrailingSlash: () => withoutTrailingSlash
});
module.exports = __toCommonJS(index_exports);

// src/utils/noop.ts
var noop = (..._args) => {
};

// src/utils/createDeferredPromise.ts
var createDeferredPromise = () => {
  let resolve = noop;
  let reject = noop;
  const promise = new Promise((res, rej) => {
    resolve = res;
    reject = rej;
  });
  return { promise, resolve, reject };
};

// src/utils/allSettled.ts
function allSettled(iterable) {
  const promises = Array.from(iterable).map(
    (p) => p.then(
      (value) => ({ status: "fulfilled", value }),
      (reason) => ({ status: "rejected", reason })
    )
  );
  return Promise.all(promises);
}

// src/utils/instance.ts
function isStaging(frontendApi) {
  return frontendApi.endsWith(".lclstage.dev") || frontendApi.endsWith(".stgstage.dev") || frontendApi.endsWith(".clerkstage.dev") || frontendApi.endsWith(".accountsstage.dev");
}

// src/utils/runtimeEnvironment.ts
var isDevelopmentEnvironment = () => {
  try {
    return process.env.NODE_ENV === "development";
  } catch {
  }
  return false;
};
var isTestEnvironment = () => {
  try {
    return process.env.NODE_ENV === "test";
  } catch {
  }
  return false;
};
var isProductionEnvironment = () => {
  try {
    return process.env.NODE_ENV === "production";
  } catch {
  }
  return false;
};

// src/utils/logErrorInDevMode.ts
var logErrorInDevMode = (message) => {
  if (isDevelopmentEnvironment()) {
    console.error(`Clerk: ${message}`);
  }
};

// src/utils/handleValueOrFn.ts
function handleValueOrFn(value, url, defaultValue) {
  if (typeof value === "function") {
    return value(url);
  }
  if (typeof value !== "undefined") {
    return value;
  }
  if (typeof defaultValue !== "undefined") {
    return defaultValue;
  }
  return void 0;
}

// src/utils/fastDeepMerge.ts
var fastDeepMergeAndReplace = (source, target) => {
  if (!source || !target) {
    return;
  }
  for (const key in source) {
    if (Object.prototype.hasOwnProperty.call(source, key) && source[key] !== null && typeof source[key] === `object`) {
      if (target[key] === void 0) {
        target[key] = new (Object.getPrototypeOf(source[key])).constructor();
      }
      fastDeepMergeAndReplace(source[key], target[key]);
    } else if (Object.prototype.hasOwnProperty.call(source, key)) {
      target[key] = source[key];
    }
  }
};
var fastDeepMergeAndKeep = (source, target) => {
  if (!source || !target) {
    return;
  }
  for (const key in source) {
    if (Object.prototype.hasOwnProperty.call(source, key) && source[key] !== null && typeof source[key] === `object`) {
      if (target[key] === void 0) {
        target[key] = new (Object.getPrototypeOf(source[key])).constructor();
      }
      fastDeepMergeAndKeep(source[key], target[key]);
    } else if (Object.prototype.hasOwnProperty.call(source, key) && target[key] === void 0) {
      target[key] = source[key];
    }
  }
};

// src/constants.ts
var LEGACY_DEV_INSTANCE_SUFFIXES = [".lcl.dev", ".lclstage.dev", ".lclclerk.com"];
var CURRENT_DEV_INSTANCE_SUFFIXES = [".accounts.dev", ".accountsstage.dev", ".accounts.lclclerk.com"];
var DEV_OR_STAGING_SUFFIXES = [
  ".lcl.dev",
  ".stg.dev",
  ".lclstage.dev",
  ".stgstage.dev",
  ".dev.lclclerk.com",
  ".stg.lclclerk.com",
  ".accounts.lclclerk.com",
  "accountsstage.dev",
  "accounts.dev"
];
var LOCAL_ENV_SUFFIXES = [".lcl.dev", "lclstage.dev", ".lclclerk.com", ".accounts.lclclerk.com"];
var STAGING_ENV_SUFFIXES = [".accountsstage.dev"];
var LOCAL_API_URL = "https://api.lclclerk.com";
var STAGING_API_URL = "https://api.clerkstage.dev";
var PROD_API_URL = "https://api.clerk.com";
function iconImageUrl(id, format = "svg") {
  return `https://img.clerk.com/static/${id}.${format}`;
}

// src/isomorphicAtob.ts
var isomorphicAtob = (data) => {
  if (typeof atob !== "undefined" && typeof atob === "function") {
    return atob(data);
  } else if (typeof global !== "undefined" && global.Buffer) {
    return new global.Buffer(data, "base64").toString();
  }
  return data;
};

// src/isomorphicBtoa.ts
var isomorphicBtoa = (data) => {
  if (typeof btoa !== "undefined" && typeof btoa === "function") {
    return btoa(data);
  } else if (typeof global !== "undefined" && global.Buffer) {
    return new global.Buffer(data).toString("base64");
  }
  return data;
};

// src/keys.ts
var PUBLISHABLE_KEY_LIVE_PREFIX = "pk_live_";
var PUBLISHABLE_KEY_TEST_PREFIX = "pk_test_";
var PUBLISHABLE_FRONTEND_API_DEV_REGEX = /^(([a-z]+)-){2}([0-9]{1,2})\.clerk\.accounts([a-z.]*)(dev|com)$/i;
function buildPublishableKey(frontendApi) {
  const isDevKey = PUBLISHABLE_FRONTEND_API_DEV_REGEX.test(frontendApi) || frontendApi.startsWith("clerk.") && LEGACY_DEV_INSTANCE_SUFFIXES.some((s2) => frontendApi.endsWith(s2));
  const keyPrefix = isDevKey ? PUBLISHABLE_KEY_TEST_PREFIX : PUBLISHABLE_KEY_LIVE_PREFIX;
  return `${keyPrefix}${isomorphicBtoa(`${frontendApi}$`)}`;
}
function parsePublishableKey(key, options = {}) {
  key = key || "";
  if (!key || !isPublishableKey(key)) {
    if (options.fatal && !key) {
      throw new Error(
        "Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys"
      );
    }
    if (options.fatal && !isPublishableKey(key)) {
      throw new Error("Publishable key not valid.");
    }
    return null;
  }
  const instanceType = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) ? "production" : "development";
  let frontendApi = isomorphicAtob(key.split("_")[2]);
  frontendApi = frontendApi.slice(0, -1);
  if (options.proxyUrl) {
    frontendApi = options.proxyUrl;
  } else if (instanceType !== "development" && options.domain && options.isSatellite) {
    frontendApi = `clerk.${options.domain}`;
  }
  return {
    instanceType,
    frontendApi
  };
}
function isPublishableKey(key = "") {
  try {
    const hasValidPrefix = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) || key.startsWith(PUBLISHABLE_KEY_TEST_PREFIX);
    const hasValidFrontendApiPostfix = isomorphicAtob(key.split("_")[2] || "").endsWith("$");
    return hasValidPrefix && hasValidFrontendApiPostfix;
  } catch {
    return false;
  }
}
function createDevOrStagingUrlCache() {
  const devOrStagingUrlCache = /* @__PURE__ */ new Map();
  return {
    isDevOrStagingUrl: (url) => {
      if (!url) {
        return false;
      }
      const hostname = typeof url === "string" ? url : url.hostname;
      let res = devOrStagingUrlCache.get(hostname);
      if (res === void 0) {
        res = DEV_OR_STAGING_SUFFIXES.some((s2) => hostname.endsWith(s2));
        devOrStagingUrlCache.set(hostname, res);
      }
      return res;
    }
  };
}
function isDevelopmentFromPublishableKey(apiKey) {
  return apiKey.startsWith("test_") || apiKey.startsWith("pk_test_");
}
function isProductionFromPublishableKey(apiKey) {
  return apiKey.startsWith("live_") || apiKey.startsWith("pk_live_");
}
function isDevelopmentFromSecretKey(apiKey) {
  return apiKey.startsWith("test_") || apiKey.startsWith("sk_test_");
}
function isProductionFromSecretKey(apiKey) {
  return apiKey.startsWith("live_") || apiKey.startsWith("sk_live_");
}
async function getCookieSuffix(publishableKey, subtle = globalThis.crypto.subtle) {
  const data = new TextEncoder().encode(publishableKey);
  const digest = await subtle.digest("sha-1", data);
  const stringDigest = String.fromCharCode(...new Uint8Array(digest));
  return isomorphicBtoa(stringDigest).replace(/\+/gi, "-").replace(/\//gi, "_").substring(0, 8);
}
var getSuffixedCookieName = (cookieName, cookieSuffix) => {
  return `${cookieName}_${cookieSuffix}`;
};

// src/apiUrlFromPublishableKey.ts
var apiUrlFromPublishableKey = (publishableKey) => {
  const frontendApi = parsePublishableKey(publishableKey)?.frontendApi;
  if (frontendApi?.startsWith("clerk.") && LEGACY_DEV_INSTANCE_SUFFIXES.some((suffix) => frontendApi?.endsWith(suffix))) {
    return PROD_API_URL;
  }
  if (LOCAL_ENV_SUFFIXES.some((suffix) => frontendApi?.endsWith(suffix))) {
    return LOCAL_API_URL;
  }
  if (STAGING_ENV_SUFFIXES.some((suffix) => frontendApi?.endsWith(suffix))) {
    return STAGING_API_URL;
  }
  return PROD_API_URL;
};

// src/browser.ts
function inBrowser() {
  return typeof window !== "undefined";
}
var botAgents = [
  "bot",
  "spider",
  "crawl",
  "APIs-Google",
  "AdsBot",
  "Googlebot",
  "mediapartners",
  "Google Favicon",
  "FeedFetcher",
  "Google-Read-Aloud",
  "DuplexWeb-Google",
  "googleweblight",
  "bing",
  "yandex",
  "baidu",
  "duckduck",
  "yahoo",
  "ecosia",
  "ia_archiver",
  "facebook",
  "instagram",
  "pinterest",
  "reddit",
  "slack",
  "twitter",
  "whatsapp",
  "youtube",
  "semrush"
];
var botAgentRegex = new RegExp(botAgents.join("|"), "i");
function userAgentIsRobot(userAgent) {
  return !userAgent ? false : botAgentRegex.test(userAgent);
}
function isValidBrowser() {
  const navigator = inBrowser() ? window?.navigator : null;
  if (!navigator) {
    return false;
  }
  return !userAgentIsRobot(navigator?.userAgent) && !navigator?.webdriver;
}
function isBrowserOnline() {
  const navigator = inBrowser() ? window?.navigator : null;
  if (!navigator) {
    return false;
  }
  const isNavigatorOnline = navigator?.onLine;
  const isExperimentalConnectionOnline = navigator?.connection?.rtt !== 0 && navigator?.connection?.downlink !== 0;
  return isExperimentalConnectionOnline && isNavigatorOnline;
}
function isValidBrowserOnline() {
  return isBrowserOnline() && isValidBrowser();
}

// src/color.ts
var IS_HEX_COLOR_REGEX = /^#?([A-F0-9]{6}|[A-F0-9]{3})$/i;
var IS_RGB_COLOR_REGEX = /^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/i;
var IS_RGBA_COLOR_REGEX = /^rgba\((\d+),\s*(\d+),\s*(\d+)(,\s*\d+(\.\d+)?)\)$/i;
var IS_HSL_COLOR_REGEX = /^hsl\((\d+),\s*([\d.]+)%,\s*([\d.]+)%\)$/i;
var IS_HSLA_COLOR_REGEX = /^hsla\((\d+),\s*([\d.]+)%,\s*([\d.]+)%(,\s*\d+(\.\d+)?)*\)$/i;
var isValidHexString = (s2) => {
  return !!s2.match(IS_HEX_COLOR_REGEX);
};
var isValidRgbaString = (s2) => {
  return !!(s2.match(IS_RGB_COLOR_REGEX) || s2.match(IS_RGBA_COLOR_REGEX));
};
var isValidHslaString = (s2) => {
  return !!s2.match(IS_HSL_COLOR_REGEX) || !!s2.match(IS_HSLA_COLOR_REGEX);
};
var isRGBColor = (c) => {
  return typeof c !== "string" && "r" in c;
};
var isHSLColor = (c) => {
  return typeof c !== "string" && "h" in c;
};
var isTransparent = (c) => {
  return c === "transparent";
};
var hasAlpha = (color) => {
  return typeof color !== "string" && color.a != void 0 && color.a < 1;
};
var CLEAN_HSLA_REGEX = /[hsla()]/g;
var CLEAN_RGBA_REGEX = /[rgba()]/g;
var stringToHslaColor = (value) => {
  if (value === "transparent") {
    return { h: 0, s: 0, l: 0, a: 0 };
  }
  if (isValidHexString(value)) {
    return hexStringToHslaColor(value);
  }
  if (isValidHslaString(value)) {
    return parseHslaString(value);
  }
  if (isValidRgbaString(value)) {
    return rgbaStringToHslaColor(value);
  }
  return null;
};
var stringToSameTypeColor = (value) => {
  value = value.trim();
  if (isValidHexString(value)) {
    return value.startsWith("#") ? value : `#${value}`;
  }
  if (isValidRgbaString(value)) {
    return parseRgbaString(value);
  }
  if (isValidHslaString(value)) {
    return parseHslaString(value);
  }
  if (isTransparent(value)) {
    return value;
  }
  return "";
};
var colorToSameTypeString = (color) => {
  if (typeof color === "string" && (isValidHexString(color) || isTransparent(color))) {
    return color;
  }
  if (isRGBColor(color)) {
    return rgbaColorToRgbaString(color);
  }
  if (isHSLColor(color)) {
    return hslaColorToHslaString(color);
  }
  return "";
};
var hexStringToRgbaColor = (hex) => {
  hex = hex.replace("#", "");
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  return { r, g, b };
};
var rgbaColorToRgbaString = (color) => {
  const { a, b, g, r } = color;
  return color.a === 0 ? "transparent" : color.a != void 0 ? `rgba(${r},${g},${b},${a})` : `rgb(${r},${g},${b})`;
};
var hslaColorToHslaString = (color) => {
  const { h, s: s2, l, a } = color;
  const sPerc = Math.round(s2 * 100);
  const lPerc = Math.round(l * 100);
  return color.a === 0 ? "transparent" : color.a != void 0 ? `hsla(${h},${sPerc}%,${lPerc}%,${a})` : `hsl(${h},${sPerc}%,${lPerc}%)`;
};
var hexStringToHslaColor = (hex) => {
  const rgbaString = colorToSameTypeString(hexStringToRgbaColor(hex));
  return rgbaStringToHslaColor(rgbaString);
};
var rgbaStringToHslaColor = (rgba) => {
  const rgbaColor = parseRgbaString(rgba);
  const r = rgbaColor.r / 255;
  const g = rgbaColor.g / 255;
  const b = rgbaColor.b / 255;
  const max = Math.max(r, g, b), min = Math.min(r, g, b);
  let h, s2;
  const l = (max + min) / 2;
  if (max == min) {
    h = s2 = 0;
  } else {
    const d = max - min;
    s2 = l >= 0.5 ? d / (2 - (max + min)) : d / (max + min);
    switch (max) {
      case r:
        h = (g - b) / d * 60;
        break;
      case g:
        h = ((b - r) / d + 2) * 60;
        break;
      default:
        h = ((r - g) / d + 4) * 60;
        break;
    }
  }
  const res = { h: Math.round(h), s: s2, l };
  const a = rgbaColor.a;
  if (a != void 0) {
    res.a = a;
  }
  return res;
};
var parseRgbaString = (str) => {
  const [r, g, b, a] = str.replace(CLEAN_RGBA_REGEX, "").split(",").map((c) => Number.parseFloat(c));
  return { r, g, b, a };
};
var parseHslaString = (str) => {
  const [h, s2, l, a] = str.replace(CLEAN_HSLA_REGEX, "").split(",").map((c) => Number.parseFloat(c));
  return { h, s: s2 / 100, l: l / 100, a };
};

// src/date.ts
var MILLISECONDS_IN_DAY = 864e5;
function dateTo12HourTime(date) {
  if (!date) {
    return "";
  }
  return date.toLocaleString("en-US", {
    hour: "2-digit",
    minute: "numeric",
    hour12: true
  });
}
function differenceInCalendarDays(a, b, { absolute = true } = {}) {
  if (!a || !b) {
    return 0;
  }
  const utcA = Date.UTC(a.getFullYear(), a.getMonth(), a.getDate());
  const utcB = Date.UTC(b.getFullYear(), b.getMonth(), b.getDate());
  const diff = Math.floor((utcB - utcA) / MILLISECONDS_IN_DAY);
  return absolute ? Math.abs(diff) : diff;
}
function normalizeDate(d) {
  try {
    return new Date(d || /* @__PURE__ */ new Date());
  } catch {
    return /* @__PURE__ */ new Date();
  }
}
function formatRelative(props) {
  const { date, relativeTo } = props;
  if (!date || !relativeTo) {
    return null;
  }
  const a = normalizeDate(date);
  const b = normalizeDate(relativeTo);
  const differenceInDays = differenceInCalendarDays(b, a, { absolute: false });
  if (differenceInDays < -6) {
    return { relativeDateCase: "other", date: a };
  }
  if (differenceInDays < -1) {
    return { relativeDateCase: "previous6Days", date: a };
  }
  if (differenceInDays === -1) {
    return { relativeDateCase: "lastDay", date: a };
  }
  if (differenceInDays === 0) {
    return { relativeDateCase: "sameDay", date: a };
  }
  if (differenceInDays === 1) {
    return { relativeDateCase: "nextDay", date: a };
  }
  if (differenceInDays < 7) {
    return { relativeDateCase: "next6Days", date: a };
  }
  return { relativeDateCase: "other", date: a };
}
function addYears(initialDate, yearsToAdd) {
  const date = normalizeDate(initialDate);
  date.setFullYear(date.getFullYear() + yearsToAdd);
  return date;
}

// src/deprecated.ts
var displayedWarnings = /* @__PURE__ */ new Set();
var deprecated = (fnName, warning, key) => {
  const hideWarning = isTestEnvironment() || isProductionEnvironment();
  const messageId = key ?? fnName;
  if (displayedWarnings.has(messageId) || hideWarning) {
    return;
  }
  displayedWarnings.add(messageId);
  console.warn(
    `Clerk - DEPRECATION WARNING: "${fnName}" is deprecated and will be removed in the next major release.
${warning}`
  );
};
var deprecatedProperty = (cls, propName, warning, isStatic = false) => {
  const target = isStatic ? cls : cls.prototype;
  let value = target[propName];
  Object.defineProperty(target, propName, {
    get() {
      deprecated(propName, warning, `${cls.name}:${propName}`);
      return value;
    },
    set(v) {
      value = v;
    }
  });
};
var deprecatedObjectProperty = (obj, propName, warning, key) => {
  let value = obj[propName];
  Object.defineProperty(obj, propName, {
    get() {
      deprecated(propName, warning, key);
      return value;
    },
    set(v) {
      value = v;
    }
  });
};

// src/deriveState.ts
var deriveState = (clerkOperational, state, initialState) => {
  if (!clerkOperational && initialState) {
    return deriveFromSsrInitialState(initialState);
  }
  return deriveFromClientSideState(state);
};
var deriveFromSsrInitialState = (initialState) => {
  const userId = initialState.userId;
  const user = initialState.user;
  const sessionId = initialState.sessionId;
  const sessionStatus = initialState.sessionStatus;
  const sessionClaims = initialState.sessionClaims;
  const session = initialState.session;
  const organization = initialState.organization;
  const orgId = initialState.orgId;
  const orgRole = initialState.orgRole;
  const orgPermissions = initialState.orgPermissions;
  const orgSlug = initialState.orgSlug;
  const actor = initialState.actor;
  const factorVerificationAge = initialState.factorVerificationAge;
  return {
    userId,
    user,
    sessionId,
    session,
    sessionStatus,
    sessionClaims,
    organization,
    orgId,
    orgRole,
    orgPermissions,
    orgSlug,
    actor,
    factorVerificationAge
  };
};
var deriveFromClientSideState = (state) => {
  const userId = state.user ? state.user.id : state.user;
  const user = state.user;
  const sessionId = state.session ? state.session.id : state.session;
  const session = state.session;
  const sessionStatus = state.session?.status;
  const sessionClaims = state.session ? state.session.lastActiveToken?.jwt?.claims : null;
  const factorVerificationAge = state.session ? state.session.factorVerificationAge : null;
  const actor = session?.actor;
  const organization = state.organization;
  const orgId = state.organization ? state.organization.id : state.organization;
  const orgSlug = organization?.slug;
  const membership = organization ? user?.organizationMemberships?.find((om) => om.organization.id === orgId) : organization;
  const orgPermissions = membership ? membership.permissions : membership;
  const orgRole = membership ? membership.role : membership;
  return {
    userId,
    user,
    sessionId,
    session,
    sessionStatus,
    sessionClaims,
    organization,
    orgId,
    orgRole,
    orgSlug,
    orgPermissions,
    actor,
    factorVerificationAge
  };
};

// src/error.ts
function isUnauthorizedError(e) {
  const status = e?.status;
  const code = e?.errors?.[0]?.code;
  return code === "authentication_invalid" && status === 401;
}
function isCaptchaError(e) {
  return ["captcha_invalid", "captcha_not_enabled", "captcha_missing_token"].includes(e.errors[0].code);
}
function is4xxError(e) {
  const status = e?.status;
  return !!status && status >= 400 && status < 500;
}
function isNetworkError(e) {
  const message = (`${e.message}${e.name}` || "").toLowerCase().replace(/\s+/g, "");
  return message.includes("networkerror");
}
function isKnownError(error) {
  return isClerkAPIResponseError(error) || isMetamaskError(error) || isClerkRuntimeError(error);
}
function isClerkAPIResponseError(err) {
  return "clerkError" in err;
}
function isClerkRuntimeError(err) {
  return "clerkRuntimeError" in err;
}
function isReverificationCancelledError(err) {
  return isClerkRuntimeError(err) && err.code === "reverification_cancelled";
}
function isMetamaskError(err) {
  return "code" in err && [4001, 32602, 32603].includes(err.code) && "message" in err;
}
function isUserLockedError(err) {
  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === "user_locked";
}
function isPasswordPwnedError(err) {
  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === "form_password_pwned";
}
function parseErrors(data = []) {
  return data.length > 0 ? data.map(parseError) : [];
}
function parseError(error) {
  return {
    code: error.code,
    message: error.message,
    longMessage: error.long_message,
    meta: {
      paramName: error?.meta?.param_name,
      sessionId: error?.meta?.session_id,
      emailAddresses: error?.meta?.email_addresses,
      identifiers: error?.meta?.identifiers,
      zxcvbn: error?.meta?.zxcvbn,
      plan: error?.meta?.plan
    }
  };
}
function errorToJSON(error) {
  return {
    code: error?.code || "",
    message: error?.message || "",
    long_message: error?.longMessage,
    meta: {
      param_name: error?.meta?.paramName,
      session_id: error?.meta?.sessionId,
      email_addresses: error?.meta?.emailAddresses,
      identifiers: error?.meta?.identifiers,
      zxcvbn: error?.meta?.zxcvbn,
      plan: error?.meta?.plan
    }
  };
}
var ClerkAPIResponseError = class _ClerkAPIResponseError extends Error {
  constructor(message, { data, status, clerkTraceId, retryAfter }) {
    super(message);
    this.toString = () => {
      let message = `[${this.name}]
Message:${this.message}
Status:${this.status}
Serialized errors: ${this.errors.map(
        (e) => JSON.stringify(e)
      )}`;
      if (this.clerkTraceId) {
        message += `
Clerk Trace ID: ${this.clerkTraceId}`;
      }
      return message;
    };
    Object.setPrototypeOf(this, _ClerkAPIResponseError.prototype);
    this.status = status;
    this.message = message;
    this.clerkTraceId = clerkTraceId;
    this.retryAfter = retryAfter;
    this.clerkError = true;
    this.errors = parseErrors(data);
  }
};
var ClerkRuntimeError = class _ClerkRuntimeError extends Error {
  constructor(message, { code }) {
    const prefix = "\u{1F512} Clerk:";
    const regex = new RegExp(prefix.replace(" ", "\\s*"), "i");
    const sanitized = message.replace(regex, "");
    const _message = `${prefix} ${sanitized.trim()}

(code="${code}")
`;
    super(_message);
    /**
     * Returns a string representation of the error.
     *
     * @returns {string} A formatted string with the error name and message.
     */
    this.toString = () => {
      return `[${this.name}]
Message:${this.message}`;
    };
    Object.setPrototypeOf(this, _ClerkRuntimeError.prototype);
    this.code = code;
    this.message = _message;
    this.clerkRuntimeError = true;
    this.name = "ClerkRuntimeError";
  }
};
var EmailLinkError = class _EmailLinkError extends Error {
  constructor(code) {
    super(code);
    this.code = code;
    this.name = "EmailLinkError";
    Object.setPrototypeOf(this, _EmailLinkError.prototype);
  }
};
function isEmailLinkError(err) {
  return err.name === "EmailLinkError";
}
var EmailLinkErrorCode = {
  Expired: "expired",
  Failed: "failed",
  ClientMismatch: "client_mismatch"
};
var EmailLinkErrorCodeStatus = {
  Expired: "expired",
  Failed: "failed",
  ClientMismatch: "client_mismatch"
};
var DefaultMessages = Object.freeze({
  InvalidProxyUrlErrorMessage: `The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})`,
  InvalidPublishableKeyErrorMessage: `The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})`,
  MissingPublishableKeyErrorMessage: `Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,
  MissingSecretKeyErrorMessage: `Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,
  MissingClerkProvider: `{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider`
});
function buildErrorThrower({ packageName, customMessages }) {
  let pkg = packageName;
  const messages = {
    ...DefaultMessages,
    ...customMessages
  };
  function buildMessage(rawMessage, replacements) {
    if (!replacements) {
      return `${pkg}: ${rawMessage}`;
    }
    let msg = rawMessage;
    const matches = rawMessage.matchAll(/{{([a-zA-Z0-9-_]+)}}/g);
    for (const match of matches) {
      const replacement = (replacements[match[1]] || "").toString();
      msg = msg.replace(`{{${match[1]}}}`, replacement);
    }
    return `${pkg}: ${msg}`;
  }
  return {
    setPackageName({ packageName: packageName2 }) {
      if (typeof packageName2 === "string") {
        pkg = packageName2;
      }
      return this;
    },
    setMessages({ customMessages: customMessages2 }) {
      Object.assign(messages, customMessages2 || {});
      return this;
    },
    throwInvalidPublishableKeyError(params) {
      throw new Error(buildMessage(messages.InvalidPublishableKeyErrorMessage, params));
    },
    throwInvalidProxyUrl(params) {
      throw new Error(buildMessage(messages.InvalidProxyUrlErrorMessage, params));
    },
    throwMissingPublishableKeyError() {
      throw new Error(buildMessage(messages.MissingPublishableKeyErrorMessage));
    },
    throwMissingSecretKeyError() {
      throw new Error(buildMessage(messages.MissingSecretKeyErrorMessage));
    },
    throwMissingClerkProviderError(params) {
      throw new Error(buildMessage(messages.MissingClerkProvider, params));
    },
    throw(message) {
      throw new Error(buildMessage(message));
    }
  };
}
var ClerkWebAuthnError = class extends ClerkRuntimeError {
  constructor(message, { code }) {
    super(message, { code });
    this.code = code;
  }
};

// src/file.ts
function readJSONFile(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.addEventListener("load", function() {
      const result = JSON.parse(reader.result);
      resolve(result);
    });
    reader.addEventListener("error", reject);
    reader.readAsText(file);
  });
}
var MimeTypeToExtensionMap = Object.freeze({
  "image/png": "png",
  "image/jpeg": "jpg",
  "image/gif": "gif",
  "image/webp": "webp",
  "image/x-icon": "ico",
  "image/vnd.microsoft.icon": "ico"
});
var extension = (mimeType) => {
  return MimeTypeToExtensionMap[mimeType];
};

// src/retry.ts
var defaultOptions = {
  initialDelay: 125,
  maxDelayBetweenRetries: 0,
  factor: 2,
  shouldRetry: (_2, iteration) => iteration < 5,
  retryImmediately: false,
  jitter: true
};
var RETRY_IMMEDIATELY_DELAY = 100;
var sleep = async (ms) => new Promise((s2) => setTimeout(s2, ms));
var applyJitter = (delay, jitter) => {
  return jitter ? delay * (1 + Math.random()) : delay;
};
var createExponentialDelayAsyncFn = (opts) => {
  let timesCalled = 0;
  const calculateDelayInMs = () => {
    const constant = opts.initialDelay;
    const base = opts.factor;
    let delay = constant * Math.pow(base, timesCalled);
    delay = applyJitter(delay, opts.jitter);
    return Math.min(opts.maxDelayBetweenRetries || delay, delay);
  };
  return async () => {
    await sleep(calculateDelayInMs());
    timesCalled++;
  };
};
var retry = async (callback, options = {}) => {
  let iterations = 0;
  const { shouldRetry, initialDelay, maxDelayBetweenRetries, factor, retryImmediately, jitter } = {
    ...defaultOptions,
    ...options
  };
  const delay = createExponentialDelayAsyncFn({
    initialDelay,
    maxDelayBetweenRetries,
    factor,
    jitter
  });
  while (true) {
    try {
      return await callback();
    } catch (e) {
      iterations++;
      if (!shouldRetry(e, iterations)) {
        throw e;
      }
      if (retryImmediately && iterations === 1) {
        await sleep(applyJitter(RETRY_IMMEDIATELY_DELAY, jitter));
      } else {
        await delay();
      }
    }
  }
};

// src/loadScript.ts
var NO_DOCUMENT_ERROR = "loadScript cannot be called when document does not exist";
var NO_SRC_ERROR = "loadScript cannot be called without a src";
async function loadScript(src = "", opts) {
  const { async, defer, beforeLoad, crossOrigin, nonce } = opts || {};
  const load = () => {
    return new Promise((resolve, reject) => {
      if (!src) {
        reject(new Error(NO_SRC_ERROR));
      }
      if (!document || !document.body) {
        reject(NO_DOCUMENT_ERROR);
      }
      const script = document.createElement("script");
      if (crossOrigin) script.setAttribute("crossorigin", crossOrigin);
      script.async = async || false;
      script.defer = defer || false;
      script.addEventListener("load", () => {
        script.remove();
        resolve(script);
      });
      script.addEventListener("error", () => {
        script.remove();
        reject();
      });
      script.src = src;
      script.nonce = nonce;
      beforeLoad?.(script);
      document.body.appendChild(script);
    });
  };
  return retry(load, { shouldRetry: (_2, iterations) => iterations <= 5 });
}

// src/proxy.ts
function isValidProxyUrl(key) {
  if (!key) {
    return true;
  }
  return isHttpOrHttps(key) || isProxyUrlRelative(key);
}
function isHttpOrHttps(key) {
  return /^http(s)?:\/\//.test(key || "");
}
function isProxyUrlRelative(key) {
  return key.startsWith("/");
}
function proxyUrlToAbsoluteURL(url) {
  if (!url) {
    return "";
  }
  return isProxyUrlRelative(url) ? new URL(url, window.location.origin).toString() : url;
}

// src/url.ts
function parseSearchParams(queryString = "") {
  if (queryString.startsWith("?")) {
    queryString = queryString.slice(1);
  }
  return new URLSearchParams(queryString);
}
function stripScheme(url = "") {
  return (url || "").replace(/^.+:\/\//, "");
}
function addClerkPrefix(str) {
  if (!str) {
    return "";
  }
  let regex;
  if (str.match(/^(clerk\.)+\w*$/)) {
    regex = /(clerk\.)*(?=clerk\.)/;
  } else if (str.match(/\.clerk.accounts/)) {
    return str;
  } else {
    regex = /^(clerk\.)*/gi;
  }
  const stripped = str.replace(regex, "");
  return `clerk.${stripped}`;
}
var getClerkJsMajorVersionOrTag = (frontendApi, version) => {
  if (!version && isStaging(frontendApi)) {
    return "canary";
  }
  if (!version) {
    return "latest";
  }
  return version.split(".")[0] || "latest";
};
var getScriptUrl = (frontendApi, { clerkJSVersion }) => {
  const noSchemeFrontendApi = frontendApi.replace(/http(s)?:\/\//, "");
  const major = getClerkJsMajorVersionOrTag(frontendApi, clerkJSVersion);
  return `https://${noSchemeFrontendApi}/npm/@clerk/clerk-js@${clerkJSVersion || major}/dist/clerk.browser.js`;
};
function isLegacyDevAccountPortalOrigin(host) {
  return LEGACY_DEV_INSTANCE_SUFFIXES.some((legacyDevSuffix) => {
    return host.startsWith("accounts.") && host.endsWith(legacyDevSuffix);
  });
}
function isCurrentDevAccountPortalOrigin(host) {
  return CURRENT_DEV_INSTANCE_SUFFIXES.some((currentDevSuffix) => {
    return host.endsWith(currentDevSuffix) && !host.endsWith(".clerk" + currentDevSuffix);
  });
}
var TRAILING_SLASH_RE = /\/$|\/\?|\/#/;
function hasTrailingSlash(input = "", respectQueryAndFragment) {
  if (!respectQueryAndFragment) {
    return input.endsWith("/");
  }
  return TRAILING_SLASH_RE.test(input);
}
function withTrailingSlash(input = "", respectQueryAndFragment) {
  if (!respectQueryAndFragment) {
    return input.endsWith("/") ? input : input + "/";
  }
  if (hasTrailingSlash(input, true)) {
    return input || "/";
  }
  let path = input;
  let fragment = "";
  const fragmentIndex = input.indexOf("#");
  if (fragmentIndex >= 0) {
    path = input.slice(0, fragmentIndex);
    fragment = input.slice(fragmentIndex);
    if (!path) {
      return fragment;
    }
  }
  const [s0, ...s2] = path.split("?");
  return s0 + "/" + (s2.length > 0 ? `?${s2.join("?")}` : "") + fragment;
}
function withoutTrailingSlash(input = "", respectQueryAndFragment) {
  if (!respectQueryAndFragment) {
    return (hasTrailingSlash(input) ? input.slice(0, -1) : input) || "/";
  }
  if (!hasTrailingSlash(input, true)) {
    return input || "/";
  }
  let path = input;
  let fragment = "";
  const fragmentIndex = input.indexOf("#");
  if (fragmentIndex >= 0) {
    path = input.slice(0, fragmentIndex);
    fragment = input.slice(fragmentIndex);
  }
  const [s0, ...s2] = path.split("?");
  return (s0.slice(0, -1) || "/") + (s2.length > 0 ? `?${s2.join("?")}` : "") + fragment;
}
function hasLeadingSlash(input = "") {
  return input.startsWith("/");
}
function withoutLeadingSlash(input = "") {
  return (hasLeadingSlash(input) ? input.slice(1) : input) || "/";
}
function withLeadingSlash(input = "") {
  return hasLeadingSlash(input) ? input : "/" + input;
}
function cleanDoubleSlashes(input = "") {
  return input.split("://").map((string_) => string_.replace(/\/{2,}/g, "/")).join("://");
}
function isNonEmptyURL(url) {
  return url && url !== "/";
}
var JOIN_LEADING_SLASH_RE = /^\.?\//;
function joinURL(base, ...input) {
  let url = base || "";
  for (const segment of input.filter((url2) => isNonEmptyURL(url2))) {
    if (url) {
      const _segment = segment.replace(JOIN_LEADING_SLASH_RE, "");
      url = withTrailingSlash(url) + _segment;
    } else {
      url = segment;
    }
  }
  return url;
}
var ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\d+\-.]*?:/;
var isAbsoluteUrl = (url) => ABSOLUTE_URL_REGEX.test(url);

// src/versionSelector.ts
var versionSelector = (clerkJSVersion, packageVersion = "5.69.0") => {
  if (clerkJSVersion) {
    return clerkJSVersion;
  }
  const prereleaseTag = getPrereleaseTag(packageVersion);
  if (prereleaseTag) {
    if (prereleaseTag === "snapshot") {
      return "5.69.0";
    }
    return prereleaseTag;
  }
  return getMajorVersion(packageVersion);
};
var getPrereleaseTag = (packageVersion) => packageVersion.trim().replace(/^v/, "").match(/-(.+?)(\.|$)/)?.[1];
var getMajorVersion = (packageVersion) => packageVersion.trim().replace(/^v/, "").split(".")[0];

// src/loadClerkJsScript.ts
var FAILED_TO_LOAD_ERROR = "Clerk: Failed to load Clerk";
var { isDevOrStagingUrl } = createDevOrStagingUrlCache();
var errorThrower = buildErrorThrower({ packageName: "@clerk/shared" });
function setClerkJsLoadingErrorPackageName(packageName) {
  errorThrower.setPackageName({ packageName });
}
var loadClerkJsScript = async (opts) => {
  const existingScript = document.querySelector("script[data-clerk-js-script]");
  if (existingScript) {
    return new Promise((resolve, reject) => {
      existingScript.addEventListener("load", () => {
        resolve(existingScript);
      });
      existingScript.addEventListener("error", () => {
        reject(FAILED_TO_LOAD_ERROR);
      });
    });
  }
  if (!opts?.publishableKey) {
    errorThrower.throwMissingPublishableKeyError();
    return;
  }
  return loadScript(clerkJsScriptUrl(opts), {
    async: true,
    crossOrigin: "anonymous",
    nonce: opts.nonce,
    beforeLoad: applyClerkJsScriptAttributes(opts)
  }).catch(() => {
    throw new Error(FAILED_TO_LOAD_ERROR);
  });
};
var clerkJsScriptUrl = (opts) => {
  const { clerkJSUrl, clerkJSVariant, clerkJSVersion, proxyUrl, domain, publishableKey } = opts;
  if (clerkJSUrl) {
    return clerkJSUrl;
  }
  let scriptHost = "";
  if (!!proxyUrl && isValidProxyUrl(proxyUrl)) {
    scriptHost = proxyUrlToAbsoluteURL(proxyUrl).replace(/http(s)?:\/\//, "");
  } else if (domain && !isDevOrStagingUrl(parsePublishableKey(publishableKey)?.frontendApi || "")) {
    scriptHost = addClerkPrefix(domain);
  } else {
    scriptHost = parsePublishableKey(publishableKey)?.frontendApi || "";
  }
  const variant = clerkJSVariant ? `${clerkJSVariant.replace(/\.+$/, "")}.` : "";
  const version = versionSelector(clerkJSVersion);
  return `https://${scriptHost}/npm/@clerk/clerk-js@${version}/dist/clerk.${variant}browser.js`;
};
var buildClerkJsScriptAttributes = (options) => {
  const obj = {};
  if (options.publishableKey) {
    obj["data-clerk-publishable-key"] = options.publishableKey;
  }
  if (options.proxyUrl) {
    obj["data-clerk-proxy-url"] = options.proxyUrl;
  }
  if (options.domain) {
    obj["data-clerk-domain"] = options.domain;
  }
  if (options.nonce) {
    obj.nonce = options.nonce;
  }
  return obj;
};
var applyClerkJsScriptAttributes = (options) => (script) => {
  const attributes = buildClerkJsScriptAttributes(options);
  for (const attribute in attributes) {
    script.setAttribute(attribute, attributes[attribute]);
  }
};

// src/localStorageBroadcastChannel.ts
var KEY_PREFIX = "__lsbc__";
var LocalStorageBroadcastChannel = class {
  constructor(name) {
    this.eventTarget = window;
    this.postMessage = (data) => {
      if (typeof window === "undefined") {
        return;
      }
      try {
        window.localStorage.setItem(this.channelKey, JSON.stringify(data));
        window.localStorage.removeItem(this.channelKey);
      } catch {
      }
    };
    this.addEventListener = (eventName, listener) => {
      this.eventTarget.addEventListener(this.prefixEventName(eventName), (e) => {
        listener(e);
      });
    };
    this.setupLocalStorageListener = () => {
      const notifyListeners = (e) => {
        if (e.key !== this.channelKey || !e.newValue) {
          return;
        }
        try {
          const data = JSON.parse(e.newValue || "");
          const event = new MessageEvent(this.prefixEventName("message"), {
            data
          });
          this.eventTarget.dispatchEvent(event);
        } catch {
        }
      };
      window.addEventListener("storage", notifyListeners);
    };
    this.channelKey = KEY_PREFIX + name;
    this.setupLocalStorageListener();
  }
  prefixEventName(eventName) {
    return this.channelKey + eventName;
  }
};

// src/workerTimers/workerTimers.worker.ts
var workerTimers_worker_default = 'const respond=r=>{self.postMessage(r)},workerToTabIds={};self.addEventListener("message",r=>{const e=r.data;switch(e.type){case"setTimeout":workerToTabIds[e.id]=setTimeout(()=>{respond({id:e.id}),delete workerToTabIds[e.id]},e.ms);break;case"clearTimeout":workerToTabIds[e.id]&&(clearTimeout(workerToTabIds[e.id]),delete workerToTabIds[e.id]);break;case"setInterval":workerToTabIds[e.id]=setInterval(()=>{respond({id:e.id})},e.ms);break;case"clearInterval":workerToTabIds[e.id]&&(clearInterval(workerToTabIds[e.id]),delete workerToTabIds[e.id]);break}});\n';

// src/workerTimers/createWorkerTimers.ts
var createWebWorker = (source, opts = {}) => {
  if (typeof Worker === "undefined") {
    return null;
  }
  try {
    const blob = new Blob([source], { type: "application/javascript; charset=utf-8" });
    const workerScript = globalThis.URL.createObjectURL(blob);
    return new Worker(workerScript, opts);
  } catch {
    console.warn("Clerk: Cannot create worker from blob. Consider adding worker-src blob:; to your CSP");
    return null;
  }
};
var fallbackTimers = () => {
  const setTimeout2 = globalThis.setTimeout.bind(globalThis);
  const setInterval = globalThis.setInterval.bind(globalThis);
  const clearTimeout = globalThis.clearTimeout.bind(globalThis);
  const clearInterval = globalThis.clearInterval.bind(globalThis);
  return { setTimeout: setTimeout2, setInterval, clearTimeout, clearInterval, cleanup: noop };
};
var createWorkerTimers = () => {
  let id = 0;
  const generateId = () => id++;
  const callbacks = /* @__PURE__ */ new Map();
  const post = (w, p) => w?.postMessage(p);
  const handleMessage = (e) => {
    callbacks.get(e.data.id)?.();
  };
  let worker = createWebWorker(workerTimers_worker_default, { name: "clerk-timers" });
  worker?.addEventListener("message", handleMessage);
  if (!worker) {
    return fallbackTimers();
  }
  const init = () => {
    if (!worker) {
      worker = createWebWorker(workerTimers_worker_default, { name: "clerk-timers" });
      worker?.addEventListener("message", handleMessage);
    }
  };
  const cleanup = () => {
    if (worker) {
      worker.terminate();
      worker = null;
      callbacks.clear();
    }
  };
  const setTimeout2 = (cb, ms) => {
    init();
    const id2 = generateId();
    callbacks.set(id2, () => {
      cb();
      callbacks.delete(id2);
    });
    post(worker, { type: "setTimeout", id: id2, ms });
    return id2;
  };
  const setInterval = (cb, ms) => {
    init();
    const id2 = generateId();
    callbacks.set(id2, cb);
    post(worker, { type: "setInterval", id: id2, ms });
    return id2;
  };
  const clearTimeout = (id2) => {
    init();
    callbacks.delete(id2);
    post(worker, { type: "clearTimeout", id: id2 });
  };
  const clearInterval = (id2) => {
    init();
    callbacks.delete(id2);
    post(worker, { type: "clearInterval", id: id2 });
  };
  return { setTimeout: setTimeout2, setInterval, clearTimeout, clearInterval, cleanup };
};

// src/poller.ts
function Poller({ delayInMs } = { delayInMs: 1e3 }) {
  const workerTimers = createWorkerTimers();
  let timerId;
  let stopped = false;
  const stop = () => {
    if (timerId) {
      workerTimers.clearTimeout(timerId);
      workerTimers.cleanup();
    }
    stopped = true;
  };
  const run = async (cb) => {
    stopped = false;
    await cb(stop);
    if (stopped) {
      return;
    }
    timerId = workerTimers.setTimeout(() => {
      void run(cb);
    }, delayInMs);
  };
  return { run, stop };
}

// src/underscore.ts
var toSentence = (items) => {
  if (items.length == 0) {
    return "";
  }
  if (items.length == 1) {
    return items[0];
  }
  let sentence = items.slice(0, -1).join(", ");
  sentence += `, or ${items.slice(-1)}`;
  return sentence;
};
var IP_V4_ADDRESS_REGEX = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
function isIPV4Address(str) {
  return IP_V4_ADDRESS_REGEX.test(str || "");
}
function titleize(str) {
  const s2 = str || "";
  return s2.charAt(0).toUpperCase() + s2.slice(1);
}
function snakeToCamel(str) {
  return str ? str.replace(/([-_][a-z])/g, (match) => match.toUpperCase().replace(/-|_/, "")) : "";
}
function camelToSnake(str) {
  return str ? str.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`) : "";
}
var createDeepObjectTransformer = (transform) => {
  const deepTransform = (obj) => {
    if (!obj) {
      return obj;
    }
    if (Array.isArray(obj)) {
      return obj.map((el) => {
        if (typeof el === "object" || Array.isArray(el)) {
          return deepTransform(el);
        }
        return el;
      });
    }
    const copy = { ...obj };
    const keys = Object.keys(copy);
    for (const oldName of keys) {
      const newName = transform(oldName.toString());
      if (newName !== oldName) {
        copy[newName] = copy[oldName];
        delete copy[oldName];
      }
      if (typeof copy[newName] === "object") {
        copy[newName] = deepTransform(copy[newName]);
      }
    }
    return copy;
  };
  return deepTransform;
};
var deepCamelToSnake = createDeepObjectTransformer(camelToSnake);
var deepSnakeToCamel = createDeepObjectTransformer(snakeToCamel);
function isTruthy(value) {
  if (typeof value === `boolean`) {
    return value;
  }
  if (value === void 0 || value === null) {
    return false;
  }
  if (typeof value === `string`) {
    if (value.toLowerCase() === `true`) {
      return true;
    }
    if (value.toLowerCase() === `false`) {
      return false;
    }
  }
  const number = parseInt(value, 10);
  if (isNaN(number)) {
    return false;
  }
  if (number > 0) {
    return true;
  }
  return false;
}
function getNonUndefinedValues(obj) {
  return Object.entries(obj).reduce((acc, [key, value]) => {
    if (value !== void 0) {
      acc[key] = value;
    }
    return acc;
  }, {});
}

// src/object.ts
var without = (obj, ...props) => {
  const copy = { ...obj };
  for (const prop of props) {
    delete copy[prop];
  }
  return copy;
};
var removeUndefined = (obj) => {
  return Object.entries(obj).reduce((acc, [key, value]) => {
    if (value !== void 0 && value !== null) {
      acc[key] = value;
    }
    return acc;
  }, {});
};
var applyFunctionToObj = (obj, fn) => {
  const result = {};
  for (const key in obj) {
    result[key] = fn(obj[key], key);
  }
  return result;
};
var filterProps = (obj, filter) => {
  const result = {};
  for (const key in obj) {
    if (obj[key] && filter(obj[key])) {
      result[key] = obj[key];
    }
  }
  return result;
};

// src/logger.ts
var loggedMessages = /* @__PURE__ */ new Set();
var logger = {
  /**
   * A custom logger that ensures messages are logged only once.
   * Reduces noise and duplicated messages when logs are in a hot codepath.
   */
  warnOnce: (msg) => {
    if (loggedMessages.has(msg)) {
      return;
    }
    loggedMessages.add(msg);
    console.warn(msg);
  },
  logOnce: (msg) => {
    if (loggedMessages.has(msg)) {
      return;
    }
    console.log(msg);
    loggedMessages.add(msg);
  }
};

// src/devBrowser.ts
var DEV_BROWSER_JWT_KEY = "__clerk_db_jwt";
function setDevBrowserJWTInURL(url, jwt) {
  const resultURL = new URL(url);
  const jwtFromSearch = resultURL.searchParams.get(DEV_BROWSER_JWT_KEY);
  resultURL.searchParams.delete(DEV_BROWSER_JWT_KEY);
  const jwtToSet = jwtFromSearch || jwt;
  if (jwtToSet) {
    resultURL.searchParams.set(DEV_BROWSER_JWT_KEY, jwtToSet);
  }
  return resultURL;
}
function extractDevBrowserJWTFromURL(url) {
  const jwt = readDevBrowserJwtFromSearchParams(url);
  const cleanUrl = removeDevBrowserJwt(url);
  if (cleanUrl.href !== url.href && typeof globalThis.history !== "undefined") {
    globalThis.history.replaceState(null, "", removeDevBrowserJwt(url));
  }
  return jwt;
}
var readDevBrowserJwtFromSearchParams = (url) => {
  return url.searchParams.get(DEV_BROWSER_JWT_KEY) || "";
};
var removeDevBrowserJwt = (url) => {
  return removeDevBrowserJwtFromURLSearchParams(removeLegacyDevBrowserJwt(url));
};
var removeDevBrowserJwtFromURLSearchParams = (_url) => {
  const url = new URL(_url);
  url.searchParams.delete(DEV_BROWSER_JWT_KEY);
  return url;
};
var removeLegacyDevBrowserJwt = (_url) => {
  const DEV_BROWSER_JWT_MARKER_REGEXP = /__clerk_db_jwt\[(.*)\]/;
  const DEV_BROWSER_JWT_LEGACY_KEY = "__dev_session";
  const url = new URL(_url);
  url.searchParams.delete(DEV_BROWSER_JWT_LEGACY_KEY);
  url.hash = decodeURI(url.hash).replace(DEV_BROWSER_JWT_MARKER_REGEXP, "");
  if (url.href.endsWith("#")) {
    url.hash = "";
  }
  return url;
};

// src/getEnvVariable.ts
var import_meta = {};
var hasCloudflareProxyContext = (context) => {
  return !!context?.cloudflare?.env;
};
var hasCloudflareContext = (context) => {
  return !!context?.env;
};
var getEnvVariable = (name, context) => {
  if (typeof process !== "undefined" && process.env && typeof process.env[name] === "string") {
    return process.env[name];
  }
  if (typeof import_meta !== "undefined" && import_meta.env && typeof import_meta.env[name] === "string") {
    return import_meta.env[name];
  }
  if (hasCloudflareProxyContext(context)) {
    return context.cloudflare.env[name] || "";
  }
  if (hasCloudflareContext(context)) {
    return context.env[name] || "";
  }
  if (context && typeof context[name] === "string") {
    return context[name];
  }
  try {
    return globalThis[name];
  } catch {
  }
  return "";
};

// src/compiled/path-to-regexp/index.js
function _(r) {
  for (var n = [], e = 0; e < r.length; ) {
    var a = r[e];
    if (a === "*" || a === "+" || a === "?") {
      n.push({
        type: "MODIFIER",
        index: e,
        value: r[e++]
      });
      continue;
    }
    if (a === "\\") {
      n.push({
        type: "ESCAPED_CHAR",
        index: e++,
        value: r[e++]
      });
      continue;
    }
    if (a === "{") {
      n.push({
        type: "OPEN",
        index: e,
        value: r[e++]
      });
      continue;
    }
    if (a === "}") {
      n.push({
        type: "CLOSE",
        index: e,
        value: r[e++]
      });
      continue;
    }
    if (a === ":") {
      for (var u = "", t = e + 1; t < r.length; ) {
        var c = r.charCodeAt(t);
        if (c >= 48 && c <= 57 || c >= 65 && c <= 90 || c >= 97 && c <= 122 || c === 95) {
          u += r[t++];
          continue;
        }
        break;
      }
      if (!u) throw new TypeError("Missing parameter name at ".concat(e));
      n.push({
        type: "NAME",
        index: e,
        value: u
      }), e = t;
      continue;
    }
    if (a === "(") {
      var o = 1, m = "", t = e + 1;
      if (r[t] === "?") throw new TypeError('Pattern cannot start with "?" at '.concat(t));
      for (; t < r.length; ) {
        if (r[t] === "\\") {
          m += r[t++] + r[t++];
          continue;
        }
        if (r[t] === ")") {
          if (o--, o === 0) {
            t++;
            break;
          }
        } else if (r[t] === "(" && (o++, r[t + 1] !== "?"))
          throw new TypeError("Capturing groups are not allowed at ".concat(t));
        m += r[t++];
      }
      if (o) throw new TypeError("Unbalanced pattern at ".concat(e));
      if (!m) throw new TypeError("Missing pattern at ".concat(e));
      n.push({
        type: "PATTERN",
        index: e,
        value: m
      }), e = t;
      continue;
    }
    n.push({
      type: "CHAR",
      index: e,
      value: r[e++]
    });
  }
  return n.push({
    type: "END",
    index: e,
    value: ""
  }), n;
}
function F(r, n) {
  n === void 0 && (n = {});
  for (var e = _(r), a = n.prefixes, u = a === void 0 ? "./" : a, t = n.delimiter, c = t === void 0 ? "/#?" : t, o = [], m = 0, h = 0, p = "", f = function(l) {
    if (h < e.length && e[h].type === l) return e[h++].value;
  }, w = function(l) {
    var v = f(l);
    if (v !== void 0) return v;
    var E = e[h], N = E.type, S = E.index;
    throw new TypeError("Unexpected ".concat(N, " at ").concat(S, ", expected ").concat(l));
  }, d = function() {
    for (var l = "", v; v = f("CHAR") || f("ESCAPED_CHAR"); ) l += v;
    return l;
  }, M = function(l) {
    for (var v = 0, E = c; v < E.length; v++) {
      var N = E[v];
      if (l.indexOf(N) > -1) return true;
    }
    return false;
  }, A = function(l) {
    var v = o[o.length - 1], E = l || (v && typeof v == "string" ? v : "");
    if (v && !E)
      throw new TypeError('Must have text between two parameters, missing text after "'.concat(v.name, '"'));
    return !E || M(E) ? "[^".concat(s(c), "]+?") : "(?:(?!".concat(s(E), ")[^").concat(s(c), "])+?");
  }; h < e.length; ) {
    var T = f("CHAR"), x = f("NAME"), C = f("PATTERN");
    if (x || C) {
      var g = T || "";
      u.indexOf(g) === -1 && (p += g, g = ""), p && (o.push(p), p = ""), o.push({
        name: x || m++,
        prefix: g,
        suffix: "",
        pattern: C || A(g),
        modifier: f("MODIFIER") || ""
      });
      continue;
    }
    var i = T || f("ESCAPED_CHAR");
    if (i) {
      p += i;
      continue;
    }
    p && (o.push(p), p = "");
    var R = f("OPEN");
    if (R) {
      var g = d(), y = f("NAME") || "", O = f("PATTERN") || "", b = d();
      w("CLOSE"), o.push({
        name: y || (O ? m++ : ""),
        pattern: y && !O ? A(g) : O,
        prefix: g,
        suffix: b,
        modifier: f("MODIFIER") || ""
      });
      continue;
    }
    w("END");
  }
  return o;
}
function s(r) {
  return r.replace(/([.+*?=^!:${}()[\]|/\\])/g, "\\$1");
}
function D(r) {
  return r && r.sensitive ? "" : "i";
}
function $(r, n) {
  if (!n) return r;
  for (var e = /\((?:\?<(.*?)>)?(?!\?)/g, a = 0, u = e.exec(r.source); u; )
    n.push({
      name: u[1] || a++,
      prefix: "",
      suffix: "",
      modifier: "",
      pattern: ""
    }), u = e.exec(r.source);
  return r;
}
function W(r, n, e) {
  var a = r.map(function(u) {
    return P(u, n, e).source;
  });
  return new RegExp("(?:".concat(a.join("|"), ")"), D(e));
}
function L(r, n, e) {
  return U(F(r, e), n, e);
}
function U(r, n, e) {
  e === void 0 && (e = {});
  for (var a = e.strict, u = a === void 0 ? false : a, t = e.start, c = t === void 0 ? true : t, o = e.end, m = o === void 0 ? true : o, h = e.encode, p = h === void 0 ? function(v) {
    return v;
  } : h, f = e.delimiter, w = f === void 0 ? "/#?" : f, d = e.endsWith, M = d === void 0 ? "" : d, A = "[".concat(s(M), "]|$"), T = "[".concat(s(w), "]"), x = c ? "^" : "", C = 0, g = r; C < g.length; C++) {
    var i = g[C];
    if (typeof i == "string") x += s(p(i));
    else {
      var R = s(p(i.prefix)), y = s(p(i.suffix));
      if (i.pattern)
        if (n && n.push(i), R || y)
          if (i.modifier === "+" || i.modifier === "*") {
            var O = i.modifier === "*" ? "?" : "";
            x += "(?:".concat(R, "((?:").concat(i.pattern, ")(?:").concat(y).concat(R, "(?:").concat(i.pattern, "))*)").concat(y, ")").concat(O);
          } else x += "(?:".concat(R, "(").concat(i.pattern, ")").concat(y, ")").concat(i.modifier);
        else {
          if (i.modifier === "+" || i.modifier === "*")
            throw new TypeError('Can not repeat "'.concat(i.name, '" without a prefix and suffix'));
          x += "(".concat(i.pattern, ")").concat(i.modifier);
        }
      else x += "(?:".concat(R).concat(y, ")").concat(i.modifier);
    }
  }
  if (m) u || (x += "".concat(T, "?")), x += e.endsWith ? "(?=".concat(A, ")") : "$";
  else {
    var b = r[r.length - 1], l = typeof b == "string" ? T.indexOf(b[b.length - 1]) > -1 : b === void 0;
    u || (x += "(?:".concat(T, "(?=").concat(A, "))?")), l || (x += "(?=".concat(T, "|").concat(A, ")"));
  }
  return new RegExp(x, D(e));
}
function P(r, n, e) {
  return r instanceof RegExp ? $(r, n) : Array.isArray(r) ? W(r, n, e) : L(r, n, e);
}

// src/pathToRegexp.ts
var pathToRegexp = (path) => {
  try {
    return P(path);
  } catch (e) {
    throw new Error(
      `Invalid path: ${path}.
Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x
${e.message}`
    );
  }
};

// src/pathMatcher.ts
var precomputePathRegex = (patterns) => {
  return patterns.map((pattern) => pattern instanceof RegExp ? pattern : pathToRegexp(pattern));
};
var createPathMatcher = (patterns) => {
  const routePatterns = [patterns || ""].flat().filter(Boolean);
  const matchers = precomputePathRegex(routePatterns);
  return (pathname) => matchers.some((matcher) => matcher.test(pathname));
};

// src/netlifyCacheHandler.ts
var CLERK_NETLIFY_CACHE_BUST_PARAM = "__clerk_netlify_cache_bust";
function handleNetlifyCacheInDevInstance({
  locationHeader,
  requestStateHeaders,
  publishableKey
}) {
  const isOnNetlify = process.env.NETLIFY || process.env.URL?.endsWith("netlify.app") || Boolean(process.env.NETLIFY_FUNCTIONS_TOKEN);
  const isDevelopmentInstance = isDevelopmentFromPublishableKey(publishableKey);
  if (isOnNetlify && isDevelopmentInstance) {
    const hasHandshakeQueryParam = locationHeader.includes("__clerk_handshake");
    if (!hasHandshakeQueryParam) {
      const url = new URL(locationHeader);
      url.searchParams.append(CLERK_NETLIFY_CACHE_BUST_PARAM, Date.now().toString());
      requestStateHeaders.set("Location", url.toString());
    }
  }
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  CLERK_NETLIFY_CACHE_BUST_PARAM,
  CURRENT_DEV_INSTANCE_SUFFIXES,
  ClerkAPIResponseError,
  ClerkRuntimeError,
  ClerkWebAuthnError,
  DEV_BROWSER_JWT_KEY,
  DEV_OR_STAGING_SUFFIXES,
  EmailLinkError,
  EmailLinkErrorCode,
  EmailLinkErrorCodeStatus,
  LEGACY_DEV_INSTANCE_SUFFIXES,
  LOCAL_API_URL,
  LOCAL_ENV_SUFFIXES,
  LocalStorageBroadcastChannel,
  PROD_API_URL,
  Poller,
  STAGING_API_URL,
  STAGING_ENV_SUFFIXES,
  addClerkPrefix,
  addYears,
  allSettled,
  apiUrlFromPublishableKey,
  applyFunctionToObj,
  buildClerkJsScriptAttributes,
  buildErrorThrower,
  buildPublishableKey,
  camelToSnake,
  cleanDoubleSlashes,
  clerkJsScriptUrl,
  colorToSameTypeString,
  createDeferredPromise,
  createDevOrStagingUrlCache,
  createPathMatcher,
  createWorkerTimers,
  dateTo12HourTime,
  deepCamelToSnake,
  deepSnakeToCamel,
  deprecated,
  deprecatedObjectProperty,
  deprecatedProperty,
  deriveState,
  differenceInCalendarDays,
  errorToJSON,
  extension,
  extractDevBrowserJWTFromURL,
  fastDeepMergeAndKeep,
  fastDeepMergeAndReplace,
  filterProps,
  formatRelative,
  getClerkJsMajorVersionOrTag,
  getCookieSuffix,
  getEnvVariable,
  getNonUndefinedValues,
  getScriptUrl,
  getSuffixedCookieName,
  handleNetlifyCacheInDevInstance,
  handleValueOrFn,
  hasAlpha,
  hasLeadingSlash,
  hasTrailingSlash,
  hexStringToRgbaColor,
  iconImageUrl,
  inBrowser,
  is4xxError,
  isAbsoluteUrl,
  isBrowserOnline,
  isCaptchaError,
  isClerkAPIResponseError,
  isClerkRuntimeError,
  isCurrentDevAccountPortalOrigin,
  isDevelopmentEnvironment,
  isDevelopmentFromPublishableKey,
  isDevelopmentFromSecretKey,
  isEmailLinkError,
  isHSLColor,
  isHttpOrHttps,
  isIPV4Address,
  isKnownError,
  isLegacyDevAccountPortalOrigin,
  isMetamaskError,
  isNetworkError,
  isNonEmptyURL,
  isPasswordPwnedError,
  isProductionEnvironment,
  isProductionFromPublishableKey,
  isProductionFromSecretKey,
  isProxyUrlRelative,
  isPublishableKey,
  isRGBColor,
  isReverificationCancelledError,
  isStaging,
  isTestEnvironment,
  isTransparent,
  isTruthy,
  isUnauthorizedError,
  isUserLockedError,
  isValidBrowser,
  isValidBrowserOnline,
  isValidHexString,
  isValidHslaString,
  isValidProxyUrl,
  isValidRgbaString,
  isomorphicAtob,
  isomorphicBtoa,
  joinURL,
  loadClerkJsScript,
  loadScript,
  logErrorInDevMode,
  logger,
  noop,
  normalizeDate,
  parseError,
  parseErrors,
  parsePublishableKey,
  parseSearchParams,
  proxyUrlToAbsoluteURL,
  readJSONFile,
  removeUndefined,
  setClerkJsLoadingErrorPackageName,
  setDevBrowserJWTInURL,
  snakeToCamel,
  stringToHslaColor,
  stringToSameTypeColor,
  stripScheme,
  titleize,
  toSentence,
  userAgentIsRobot,
  versionSelector,
  withLeadingSlash,
  withTrailingSlash,
  without,
  withoutLeadingSlash,
  withoutTrailingSlash
});
//# sourceMappingURL=index.js.map