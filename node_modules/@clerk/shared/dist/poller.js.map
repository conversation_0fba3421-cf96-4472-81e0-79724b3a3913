{"version": 3, "sources": ["../src/poller.ts", "../src/utils/noop.ts", "../src/workerTimers/workerTimers.worker.ts", "../src/workerTimers/createWorkerTimers.ts"], "sourcesContent": ["import { createWorkerTimers } from './workerTimers';\n\nexport type PollerStop = () => void;\nexport type PollerCallback = (stop: PollerStop) => Promise<unknown>;\nexport type PollerRun = (cb: PollerCallback) => Promise<void>;\n\ntype PollerOptions = {\n  delayInMs: number;\n};\n\nexport type Poller = {\n  run: PollerRun;\n  stop: PollerStop;\n};\n\nexport function Poller({ delayInMs }: PollerOptions = { delayInMs: 1000 }): Poller {\n  const workerTimers = createWorkerTimers();\n\n  let timerId: number | undefined;\n  let stopped = false;\n\n  const stop: PollerStop = () => {\n    if (timerId) {\n      workerTimers.clearTimeout(timerId);\n      workerTimers.cleanup();\n    }\n    stopped = true;\n  };\n\n  const run: PollerRun = async cb => {\n    stopped = false;\n    await cb(stop);\n    if (stopped) {\n      return;\n    }\n\n    timerId = workerTimers.setTimeout(() => {\n      void run(cb);\n    }, delayInMs) as any as number;\n  };\n\n  return { run, stop };\n}\n", "export const noop = (..._args: any[]): void => {\n  // do nothing.\n};\n", "const respond=r=>{self.postMessage(r)},workerToTabIds={};self.addEventListener(\"message\",r=>{const e=r.data;switch(e.type){case\"setTimeout\":workerToTabIds[e.id]=setTimeout(()=>{respond({id:e.id}),delete workerToTabIds[e.id]},e.ms);break;case\"clearTimeout\":workerToTabIds[e.id]&&(clearTimeout(workerToTabIds[e.id]),delete workerToTabIds[e.id]);break;case\"setInterval\":workerToTabIds[e.id]=setInterval(()=>{respond({id:e.id})},e.ms);break;case\"clearInterval\":workerToTabIds[e.id]&&(clearInterval(workerToTabIds[e.id]),delete workerToTabIds[e.id]);break}});\n", "import { noop } from '../utils/noop';\nimport type {\n  WorkerClearTimeout,\n  WorkerSetTimeout,\n  WorkerTimeoutCallback,\n  WorkerTimerEvent,\n  WorkerTimerId,\n  WorkerTimerResponseEvent,\n} from './workerTimers.types';\n// @ts-ignore\n// eslint-disable-next-line import/default\nimport pollerWorkerSource from './workerTimers.worker';\n\nconst createWebWorker = (source: string, opts: ConstructorParameters<typeof Worker>[1] = {}): Worker | null => {\n  if (typeof Worker === 'undefined') {\n    return null;\n  }\n\n  try {\n    const blob = new Blob([source], { type: 'application/javascript; charset=utf-8' });\n    const workerScript = globalThis.URL.createObjectURL(blob);\n    return new Worker(workerScript, opts);\n  } catch {\n    console.warn('Clerk: Cannot create worker from blob. Consider adding worker-src blob:; to your CSP');\n    return null;\n  }\n};\n\nconst fallbackTimers = () => {\n  const setTimeout = globalThis.setTimeout.bind(globalThis) as WorkerSetTimeout;\n  const setInterval = globalThis.setInterval.bind(globalThis) as WorkerSetTimeout;\n  const clearTimeout = globalThis.clearTimeout.bind(globalThis) as WorkerClearTimeout;\n  const clearInterval = globalThis.clearInterval.bind(globalThis) as WorkerClearTimeout;\n  return { setTimeout, setInterval, clearTimeout, clearInterval, cleanup: noop };\n};\n\nexport const createWorkerTimers = () => {\n  let id = 0;\n  const generateId = () => id++;\n  const callbacks = new Map<WorkerTimerId, WorkerTimeoutCallback>();\n  const post = (w: Worker | null, p: WorkerTimerEvent) => w?.postMessage(p);\n  const handleMessage = (e: MessageEvent<WorkerTimerResponseEvent>) => {\n    callbacks.get(e.data.id)?.();\n  };\n\n  let worker = createWebWorker(pollerWorkerSource, { name: 'clerk-timers' });\n  worker?.addEventListener('message', handleMessage);\n\n  if (!worker) {\n    return fallbackTimers();\n  }\n\n  const init = () => {\n    if (!worker) {\n      worker = createWebWorker(pollerWorkerSource, { name: 'clerk-timers' });\n      worker?.addEventListener('message', handleMessage);\n    }\n  };\n\n  const cleanup = () => {\n    if (worker) {\n      worker.terminate();\n      worker = null;\n      callbacks.clear();\n    }\n  };\n\n  const setTimeout: WorkerSetTimeout = (cb, ms) => {\n    init();\n    const id = generateId();\n    callbacks.set(id, () => {\n      cb();\n      callbacks.delete(id);\n    });\n    post(worker, { type: 'setTimeout', id, ms });\n    return id;\n  };\n\n  const setInterval: WorkerSetTimeout = (cb, ms) => {\n    init();\n    const id = generateId();\n    callbacks.set(id, cb);\n    post(worker, { type: 'setInterval', id, ms });\n    return id;\n  };\n\n  const clearTimeout: WorkerClearTimeout = id => {\n    init();\n    callbacks.delete(id);\n    post(worker, { type: 'clearTimeout', id });\n  };\n\n  const clearInterval: WorkerClearTimeout = id => {\n    init();\n    callbacks.delete(id);\n    post(worker, { type: 'clearInterval', id });\n  };\n\n  return { setTimeout, setInterval, clearTimeout, clearInterval, cleanup };\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAO,IAAM,OAAO,IAAI,UAAuB;AAE/C;;;ACFA;;;ACaA,IAAM,kBAAkB,CAAC,QAAgB,OAAgD,CAAC,MAAqB;AAC7G,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO;AAAA,EACT;AAEA,MAAI;AACF,UAAM,OAAO,IAAI,KAAK,CAAC,MAAM,GAAG,EAAE,MAAM,wCAAwC,CAAC;AACjF,UAAM,eAAe,WAAW,IAAI,gBAAgB,IAAI;AACxD,WAAO,IAAI,OAAO,cAAc,IAAI;AAAA,EACtC,QAAQ;AACN,YAAQ,KAAK,sFAAsF;AACnG,WAAO;AAAA,EACT;AACF;AAEA,IAAM,iBAAiB,MAAM;AAC3B,QAAM,aAAa,WAAW,WAAW,KAAK,UAAU;AACxD,QAAM,cAAc,WAAW,YAAY,KAAK,UAAU;AAC1D,QAAM,eAAe,WAAW,aAAa,KAAK,UAAU;AAC5D,QAAM,gBAAgB,WAAW,cAAc,KAAK,UAAU;AAC9D,SAAO,EAAE,YAAY,aAAa,cAAc,eAAe,SAAS,KAAK;AAC/E;AAEO,IAAM,qBAAqB,MAAM;AACtC,MAAI,KAAK;AACT,QAAM,aAAa,MAAM;AACzB,QAAM,YAAY,oBAAI,IAA0C;AAChE,QAAM,OAAO,CAAC,GAAkB,MAAwB,GAAG,YAAY,CAAC;AACxE,QAAM,gBAAgB,CAAC,MAA8C;AACnE,cAAU,IAAI,EAAE,KAAK,EAAE,IAAI;AAAA,EAC7B;AAEA,MAAI,SAAS,gBAAgB,6BAAoB,EAAE,MAAM,eAAe,CAAC;AACzE,UAAQ,iBAAiB,WAAW,aAAa;AAEjD,MAAI,CAAC,QAAQ;AACX,WAAO,eAAe;AAAA,EACxB;AAEA,QAAM,OAAO,MAAM;AACjB,QAAI,CAAC,QAAQ;AACX,eAAS,gBAAgB,6BAAoB,EAAE,MAAM,eAAe,CAAC;AACrE,cAAQ,iBAAiB,WAAW,aAAa;AAAA,IACnD;AAAA,EACF;AAEA,QAAM,UAAU,MAAM;AACpB,QAAI,QAAQ;AACV,aAAO,UAAU;AACjB,eAAS;AACT,gBAAU,MAAM;AAAA,IAClB;AAAA,EACF;AAEA,QAAM,aAA+B,CAAC,IAAI,OAAO;AAC/C,SAAK;AACL,UAAMA,MAAK,WAAW;AACtB,cAAU,IAAIA,KAAI,MAAM;AACtB,SAAG;AACH,gBAAU,OAAOA,GAAE;AAAA,IACrB,CAAC;AACD,SAAK,QAAQ,EAAE,MAAM,cAAc,IAAAA,KAAI,GAAG,CAAC;AAC3C,WAAOA;AAAA,EACT;AAEA,QAAM,cAAgC,CAAC,IAAI,OAAO;AAChD,SAAK;AACL,UAAMA,MAAK,WAAW;AACtB,cAAU,IAAIA,KAAI,EAAE;AACpB,SAAK,QAAQ,EAAE,MAAM,eAAe,IAAAA,KAAI,GAAG,CAAC;AAC5C,WAAOA;AAAA,EACT;AAEA,QAAM,eAAmC,CAAAA,QAAM;AAC7C,SAAK;AACL,cAAU,OAAOA,GAAE;AACnB,SAAK,QAAQ,EAAE,MAAM,gBAAgB,IAAAA,IAAG,CAAC;AAAA,EAC3C;AAEA,QAAM,gBAAoC,CAAAA,QAAM;AAC9C,SAAK;AACL,cAAU,OAAOA,GAAE;AACnB,SAAK,QAAQ,EAAE,MAAM,iBAAiB,IAAAA,IAAG,CAAC;AAAA,EAC5C;AAEA,SAAO,EAAE,YAAY,aAAa,cAAc,eAAe,QAAQ;AACzE;;;AHpFO,SAAS,OAAO,EAAE,UAAU,IAAmB,EAAE,WAAW,IAAK,GAAW;AACjF,QAAM,eAAe,mBAAmB;AAExC,MAAI;AACJ,MAAI,UAAU;AAEd,QAAM,OAAmB,MAAM;AAC7B,QAAI,SAAS;AACX,mBAAa,aAAa,OAAO;AACjC,mBAAa,QAAQ;AAAA,IACvB;AACA,cAAU;AAAA,EACZ;AAEA,QAAM,MAAiB,OAAM,OAAM;AACjC,cAAU;AACV,UAAM,GAAG,IAAI;AACb,QAAI,SAAS;AACX;AAAA,IACF;AAEA,cAAU,aAAa,WAAW,MAAM;AACtC,WAAK,IAAI,EAAE;AAAA,IACb,GAAG,SAAS;AAAA,EACd;AAEA,SAAO,EAAE,KAAK,KAAK;AACrB;", "names": ["id"]}