{"version": 3, "sources": ["../../../../src/server/fs/utils.ts"], "sourcesContent": ["/**\n * Attention: Only import this module when the node runtime is used.\n * We are using conditional imports to mitigate bundling issues with Next.js server actions on version prior to 14.1.0.\n */\n// @ts-ignore\nimport nodeRuntime from '#safe-node-apis';\n\nconst throwMissingFsModule = (module: string) => {\n  throw new Error(`Clerk: ${module} is missing. This is an internal error. Please contact Clerk's support.`);\n};\n\nconst nodeFsOrThrow = () => {\n  if (!nodeRuntime.fs) {\n    throwMissingFsModule('fs');\n  }\n  return nodeRuntime.fs;\n};\n\nconst nodePathOrThrow = () => {\n  if (!nodeRuntime.path) {\n    throwMissingFsModule('path');\n  }\n  return nodeRuntime.path;\n};\n\nconst nodeCwdOrThrow = () => {\n  if (!nodeRuntime.cwd) {\n    throwMissingFsModule('cwd');\n  }\n  return nodeRuntime.cwd;\n};\n\nexport { nodeCwdOrThrow, nodeFsOrThrow, nodePathOrThrow };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,4BAAwB;AAExB,MAAM,uBAAuB,CAACA,YAAmB;AAC/C,QAAM,IAAI,MAAM,UAAUA,OAAM,yEAAyE;AAC3G;AAEA,MAAM,gBAAgB,MAAM;AAC1B,MAAI,CAAC,sBAAAC,QAAY,IAAI;AACnB,yBAAqB,IAAI;AAAA,EAC3B;AACA,SAAO,sBAAAA,QAAY;AACrB;AAEA,MAAM,kBAAkB,MAAM;AAC5B,MAAI,CAAC,sBAAAA,QAAY,MAAM;AACrB,yBAAqB,MAAM;AAAA,EAC7B;AACA,SAAO,sBAAAA,QAAY;AACrB;AAEA,MAAM,iBAAiB,MAAM;AAC3B,MAAI,CAAC,sBAAAA,QAAY,KAAK;AACpB,yBAAqB,KAAK;AAAA,EAC5B;AACA,SAAO,sBAAAA,QAAY;AACrB;", "names": ["module", "nodeRuntime"]}