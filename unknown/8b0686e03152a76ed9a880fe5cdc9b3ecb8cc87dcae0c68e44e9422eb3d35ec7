"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/web3.ts
var web3_exports = {};
__export(web3_exports, {
  WEB3_PROVIDERS: () => WEB3_PROVIDERS
});
module.exports = __toCommonJS(web3_exports);
var WEB3_PROVIDERS = [
  {
    provider: "metamask",
    strategy: "web3_metamask_signature",
    name: "MetaMask"
  },
  {
    provider: "coinbase_wallet",
    strategy: "web3_coinbase_wallet_signature",
    name: "Coinbase Wallet"
  },
  {
    provider: "okx_wallet",
    strategy: "web3_okx_wallet_signature",
    name: "OKX Wallet"
  }
];
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  WEB3_PROVIDERS
});
//# sourceMappingURL=web3.js.map