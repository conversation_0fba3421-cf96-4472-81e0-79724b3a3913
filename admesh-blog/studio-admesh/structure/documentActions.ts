import {DocumentActionComponent, DocumentActionsContext} from 'sanity'
import {TrashIcon} from '@sanity/icons'

// Custom delete action with confirmation
export const deleteAction: DocumentActionComponent = (props) => {
  const {id, type, draft, published, onComplete} = props

  return {
    label: 'Delete',
    icon: TrashIcon,
    color: 'critical',
    onHandle: () => {
      const isConfirmed = window.confirm(
        `Are you sure you want to delete this ${type}? This action cannot be undone.`
      )
      
      if (isConfirmed) {
        // Delete both draft and published versions
        const deletePromises = []
        
        if (draft) {
          deletePromises.push(
            props.client.delete(draft._id)
          )
        }
        
        if (published) {
          deletePromises.push(
            props.client.delete(published._id)
          )
        }
        
        Promise.all(deletePromises)
          .then(() => {
            console.log(`${type} deleted successfully`)
            onComplete()
          })
          .catch((error) => {
            console.error(`Error deleting ${type}:`, error)
            alert(`Error deleting ${type}. Please try again.`)
          })
      }
    },
  }
}

// Enhanced delete action for posts with reference checking
export const deletePostAction: DocumentActionComponent = (props) => {
  const {id, type, draft, published, onComplete} = props

  return {
    label: 'Delete Post',
    icon: TrashIcon,
    color: 'critical',
    onHandle: async () => {
      // Check if post has any references (future enhancement)
      const isConfirmed = window.confirm(
        `Are you sure you want to delete this blog post? This action cannot be undone and will remove the post from your website.`
      )
      
      if (isConfirmed) {
        try {
          const deletePromises = []
          
          if (draft) {
            deletePromises.push(props.client.delete(draft._id))
          }
          
          if (published) {
            deletePromises.push(props.client.delete(published._id))
          }
          
          await Promise.all(deletePromises)
          console.log('Blog post deleted successfully')
          onComplete()
        } catch (error) {
          console.error('Error deleting blog post:', error)
          alert('Error deleting blog post. Please try again.')
        }
      }
    },
  }
}

// Enhanced delete action for authors with reference checking
export const deleteAuthorAction: DocumentActionComponent = (props) => {
  const {id, type, draft, published, onComplete} = props

  return {
    label: 'Delete Author',
    icon: TrashIcon,
    color: 'critical',
    onHandle: async () => {
      try {
        const authorId = published?._id || draft?._id

        // Check if author has any posts
        const postsWithAuthor = await props.client.fetch(
          `*[_type == "post" && author._ref == $authorId]`,
          {authorId}
        )

        if (postsWithAuthor.length > 0) {
          const referenceDetails = await showReferenceDetails(props.client, authorId, 'author')
          alert(referenceDetails || `Cannot delete this author because they have ${postsWithAuthor.length} blog post(s). Please reassign or delete those posts first.`)
          return
        }

        const isConfirmed = window.confirm(
          `Are you sure you want to delete this author? This action cannot be undone.`
        )

        if (isConfirmed) {
          const deletePromises = []

          if (draft) {
            deletePromises.push(props.client.delete(draft._id))
          }

          if (published) {
            deletePromises.push(props.client.delete(published._id))
          }

          await Promise.all(deletePromises)
          console.log('Author deleted successfully')
          onComplete()
        }
      } catch (error) {
        console.error('Error deleting author:', error)
        alert('Error deleting author. Please try again.')
      }
    },
  }
}

// Enhanced delete action for categories with reference checking
export const deleteCategoryAction: DocumentActionComponent = (props) => {
  const {id, type, draft, published, onComplete} = props

  return {
    label: 'Delete Category',
    icon: TrashIcon,
    color: 'critical',
    onHandle: async () => {
      try {
        const categoryId = published?._id || draft?._id

        // Check if category is used in any posts
        const postsWithCategory = await props.client.fetch(
          `*[_type == "post" && $categoryId in categories[]._ref]`,
          {categoryId}
        )

        if (postsWithCategory.length > 0) {
          const referenceDetails = await showReferenceDetails(props.client, categoryId, 'category')
          alert(referenceDetails || `Cannot delete this category because it's used in ${postsWithCategory.length} blog post(s). Please remove this category from those posts first.`)
          return
        }

        const isConfirmed = window.confirm(
          `Are you sure you want to delete this category? This action cannot be undone.`
        )

        if (isConfirmed) {
          const deletePromises = []

          if (draft) {
            deletePromises.push(props.client.delete(draft._id))
          }

          if (published) {
            deletePromises.push(props.client.delete(published._id))
          }

          await Promise.all(deletePromises)
          console.log('Category deleted successfully')
          onComplete()
        }
      } catch (error) {
        console.error('Error deleting category:', error)
        alert('Error deleting category. Please try again.')
      }
    },
  }
}

// Utility function to show detailed reference information
const showReferenceDetails = async (client: any, documentId: string, documentType: string) => {
  try {
    let query = ''
    let params = {}

    if (documentType === 'author') {
      query = `*[_type == "post" && author._ref == $authorId]{title, slug, publishedAt}`
      params = {authorId: documentId}
    } else if (documentType === 'category') {
      query = `*[_type == "post" && $categoryId in categories[]._ref]{title, slug, publishedAt}`
      params = {categoryId: documentId}
    }

    const references = await client.fetch(query, params)

    if (references.length > 0) {
      const referenceList = references
        .map((ref: any) => `• ${ref.title}`)
        .join('\n')

      return `This ${documentType} is referenced in ${references.length} post(s):\n\n${referenceList}\n\nPlease remove these references before deleting.`
    }

    return null
  } catch (error) {
    console.error('Error checking references:', error)
    return `Error checking references. Please try again.`
  }
}

// Document actions resolver
export const resolveDocumentActions = (props: DocumentActionsContext) => {
  const {schemaType} = props

  // Get default actions
  const defaultActions = props.actions

  // Add custom delete actions based on document type
  switch (schemaType) {
    case 'post':
      return [
        ...defaultActions.filter((action) => action.action !== 'delete'),
        deletePostAction,
      ]
    case 'author':
      return [
        ...defaultActions.filter((action) => action.action !== 'delete'),
        deleteAuthorAction,
      ]
    case 'category':
      return [
        ...defaultActions.filter((action) => action.action !== 'delete'),
        deleteCategoryAction,
      ]
    default:
      return defaultActions
  }
}
