import {StructureBuilder} from 'sanity/structure'
import {
  DocumentTextIcon,
  UserIcon,
  TagIcon,
  EditIcon,
  EyeOpenIcon,
  StarIcon,
  CalendarIcon,
  UsersIcon,
  TagsIcon,
} from '@sanity/icons'

export const structure = (S: StructureBuilder) =>
  S.list()
    .title('AdMesh Blog Content')
    .items([
      // Blog Posts Section
      S.listItem()
        .title('Blog Posts')
        .icon(DocumentTextIcon)
        .child(
          S.list()
            .title('Blog Posts')
            .items([
              // All Posts
              S.listItem()
                .title('All Posts')
                .icon(DocumentTextIcon)
                .child(
                  S.documentTypeList('post')
                    .title('All Blog Posts')
                    .filter('_type == "post"')
                    .defaultOrdering([{field: 'publishedAt', direction: 'desc'}])
                ),

              // Featured Posts
              S.listItem()
                .title('Featured Posts')
                .icon(StarIcon)
                .child(
                  S.documentTypeList('post')
                    .title('Featured Posts')
                    .filter('_type == "post" && featured == true')
                    .defaultOrdering([{field: 'publishedAt', direction: 'desc'}])
                ),

              // Draft Posts
              S.listItem()
                .title('Draft Posts')
                .icon(EditIcon)
                .child(
                  S.documentTypeList('post')
                    .title('Draft Posts')
                    .filter('_type == "post" && !defined(publishedAt)')
                    .defaultOrdering([{field: '_createdAt', direction: 'desc'}])
                ),

              // Posts by Date
              S.listItem()
                .title('Posts by Date')
                .icon(CalendarIcon)
                .child(
                  S.documentTypeList('post')
                    .title('Posts by Date')
                    .filter('_type == "post" && defined(publishedAt)')
                    .defaultOrdering([{field: 'publishedAt', direction: 'desc'}])
                ),
            ])
        ),

      // Authors Section
      S.listItem()
        .title('Authors')
        .icon(UsersIcon)
        .child(
          S.documentTypeList('author')
            .title('Authors')
            .filter('_type == "author"')
            .defaultOrdering([{field: 'name', direction: 'asc'}])
        ),

      // Categories Section
      S.listItem()
        .title('Categories')
        .icon(TagsIcon)
        .child(
          S.documentTypeList('category')
            .title('Categories')
            .filter('_type == "category"')
            .defaultOrdering([{field: 'title', direction: 'asc'}])
        ),

      // Divider
      S.divider(),

      // All other document types (if any)
      ...S.documentTypeListItems().filter(
        (listItem) =>
          !['post', 'author', 'category'].includes(listItem.getId() || '')
      ),
    ])
