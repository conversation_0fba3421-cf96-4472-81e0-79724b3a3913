'use client'

import { BlogImage } from './BlogImage'
import { urlFor } from '@/lib/sanity'

interface PortableTextImageProps {
  value: any
}

export function PortableTextImage({ value }: PortableTextImageProps) {
  return (
    <div className="my-10">
      <div className="relative rounded-xl overflow-hidden shadow-lg bg-muted">
        <BlogImage
          src={urlFor(value).width(1000).height(600).url()}
          alt={value.alt || 'Blog post image'}
          width={1000}
          height={600}
          className="w-full h-auto"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1000px"
        />
      </div>
      {value.caption && (
        <p className="text-sm text-muted-foreground text-center mt-3 italic">
          {value.caption}
        </p>
      )}
    </div>
  )
}
