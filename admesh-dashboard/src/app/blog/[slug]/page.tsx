import { client, blogPostQuery, relatedPostsQuery, BlogPost, urlFor, getCategoryColor } from '@/lib/sanity'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { format } from 'date-fns'
import { CalendarDays, Clock, Twitter, Linkedin, ExternalLink } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { BlogImage } from '@/components/blog/BlogImage'
import { PortableTextImage } from '@/components/blog/PortableTextImage'
import { SocialShareButtons } from '@/components/blog/SocialShareButtons'
import { BackButton } from '@/components/blog/BackButton'
import { PortableText } from '@portabletext/react'

interface BlogPostPageProps {
  params: {
    slug: string
  }
}

async function getBlogPost(slug: string): Promise<BlogPost | null> {
  try {
    const post = await client.fetch(blogPostQuery, { slug })
    return post
  } catch (error) {
    console.error('Error fetching blog post:', error)
    return null
  }
}

async function getRelatedPosts(postId: string, categoryIds: string[]): Promise<BlogPost[]> {
  try {
    const posts = await client.fetch(relatedPostsQuery, { postId, categoryIds })
    return posts || []
  } catch (error) {
    console.error('Error fetching related posts:', error)
    return []
  }
}

// Portable Text components for rich text rendering
const portableTextComponents = {
  types: {
    image: ({ value }: any) => <PortableTextImage value={value} />,
    codeBlock: ({ value }: any) => (
      <div className="my-8">
        <div className="bg-muted rounded-lg p-4 overflow-x-auto">
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs text-muted-foreground uppercase tracking-wide">
              {value.language || 'Code'}
            </span>
          </div>
          <pre className="text-sm">
            <code className="text-foreground">{value.code}</code>
          </pre>
        </div>
      </div>
    ),
  },
  marks: {
    link: ({ children, value }: any) => (
      <a
        href={value.href}
        className="text-primary hover:text-primary/80 underline underline-offset-2 decoration-2"
        target={value.blank ? '_blank' : undefined}
        rel={value.blank ? 'noopener noreferrer' : undefined}
      >
        {children}
        {value.blank && <ExternalLink className="inline h-3 w-3 ml-1" />}
      </a>
    ),
    code: ({ children }: any) => (
      <code className="bg-muted px-2 py-1 rounded text-sm font-mono text-foreground">
        {children}
      </code>
    ),
  },
  block: {
    h1: ({ children }: any) => (
      <h1 className="text-4xl font-bold text-foreground mt-12 mb-6 leading-tight">{children}</h1>
    ),
    h2: ({ children }: any) => (
      <h2 className="text-3xl font-semibold text-foreground mt-10 mb-5 leading-tight">{children}</h2>
    ),
    h3: ({ children }: any) => (
      <h3 className="text-2xl font-semibold text-foreground mt-8 mb-4 leading-tight">{children}</h3>
    ),
    h4: ({ children }: any) => (
      <h4 className="text-xl font-semibold text-foreground mt-6 mb-3 leading-tight">{children}</h4>
    ),
    normal: ({ children }: any) => (
      <p className="text-foreground leading-relaxed mb-6 text-lg">{children}</p>
    ),
    blockquote: ({ children }: any) => (
      <blockquote className="border-l-4 border-primary pl-6 py-2 italic text-muted-foreground my-8 bg-muted/30 rounded-r-lg">
        <div className="text-lg">{children}</div>
      </blockquote>
    ),
  },
  list: {
    bullet: ({ children }: any) => (
      <ul className="list-disc list-outside ml-6 space-y-2 mb-6 text-foreground text-lg">{children}</ul>
    ),
    number: ({ children }: any) => (
      <ol className="list-decimal list-outside ml-6 space-y-2 mb-6 text-foreground text-lg">{children}</ol>
    ),
  },
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params
  const post = await getBlogPost(slug)

  if (!post) {
    notFound()
  }

  const categoryIds = post.categories?.map(cat => cat._id) || []
  const relatedPosts = await getRelatedPosts(post._id, categoryIds)

  return (
    <>
      {/* JSON-LD Structured Data for Article */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'Article',
            headline: post.title,
            description: post.excerpt || post.title,
            image: post.mainImage
              ? [urlFor(post.mainImage).width(1200).height(630).url()]
              : undefined,
            datePublished: post.publishedAt,
            dateModified: post.updatedAt || post.publishedAt,
            author: post.author
              ? {
                  '@type': 'Person',
                  name: post.author.name,
                  url: post.author.socialLinks?.website,
                  sameAs: [
                    post.author.socialLinks?.twitter,
                    post.author.socialLinks?.linkedin,
                  ].filter(Boolean),
                }
              : undefined,
            publisher: {
              '@type': 'Organization',
              name: 'AdMesh',
              url: 'https://useadmesh.com',
              logo: {
                '@type': 'ImageObject',
                url: 'https://useadmesh.com/logo.svg',
                width: 200,
                height: 200,
              },
            },
            mainEntityOfPage: {
              '@type': 'WebPage',
              '@id': `https://useadmesh.com/blog/${post.slug.current}`,
            },
            articleSection: post.categories?.map(cat => cat.title) || undefined,
            keywords: post.seo?.keywords?.join(', ') || undefined,
            wordCount: post.body ? JSON.stringify(post.body).length / 5 : undefined,
            timeRequired: post.readingTime || post.estimatedReadingTime
              ? `PT${post.readingTime || post.estimatedReadingTime}M`
              : undefined,
            inLanguage: 'en-US',
          }),
        }}
      />

      {/* Breadcrumb Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'BreadcrumbList',
            itemListElement: [
              {
                '@type': 'ListItem',
                position: 1,
                name: 'Home',
                item: 'https://useadmesh.com',
              },
              {
                '@type': 'ListItem',
                position: 2,
                name: 'Blog',
                item: 'https://useadmesh.com/blog',
              },
              {
                '@type': 'ListItem',
                position: 3,
                name: post.title,
                item: `https://useadmesh.com/blog/${post.slug.current}`,
              },
            ],
          }),
        }}
      />

      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Back Button */}
        <div className="mb-12">
          <BackButton />
        </div>

        {/* Article Header */}
        <article className="space-y-16">
          <header className="space-y-10">
            {/* Categories */}
            {post.categories && post.categories.length > 0 && (
              <div className="flex flex-wrap gap-3">
                {post.categories.map((category) => (
                  <Badge
                    key={category._id}
                    variant="secondary"
                    className={`${getCategoryColor(category.color)} px-3 py-1 text-sm font-medium`}
                  >
                    {category.title}
                  </Badge>
                ))}
              </div>
            )}

            {/* Title */}
            <div className="space-y-6">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground leading-tight tracking-tight">
                {post.title}
              </h1>

              {/* Excerpt */}
              {post.excerpt && (
                <p className="text-xl md:text-2xl text-muted-foreground leading-relaxed max-w-4xl">
                  {post.excerpt}
                </p>
              )}
            </div>

            {/* Meta Information */}
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-8 py-8 border-y border-border bg-muted/20 rounded-lg px-6">
              <div className="flex flex-col sm:flex-row sm:items-center gap-6">
                {post.author && (
                  <div className="flex items-center gap-4">
                    {post.author.image && (
                      <Avatar className="h-14 w-14 ring-2 ring-background">
                        <AvatarImage
                          src={urlFor(post.author.image).width(56).height(56).url()}
                          alt={post.author.name}
                        />
                        <AvatarFallback className="text-lg font-semibold">
                          {post.author.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                    )}
                    <div>
                      <p className="font-semibold text-foreground text-lg">{post.author.name}</p>
                      <p className="text-sm text-muted-foreground">Author</p>
                    </div>
                  </div>
                )}

                <div className="flex flex-col sm:flex-row sm:items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <CalendarDays className="h-4 w-4" />
                    <span className="font-medium">{format(new Date(post.publishedAt), 'MMMM dd, yyyy')}</span>
                  </div>

                  {(post.readingTime || post.estimatedReadingTime) && (
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      <span className="font-medium">{post.readingTime || post.estimatedReadingTime} min read</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Share Buttons */}
              <SocialShareButtons
                title={post.title}
                url={`https://useadmesh.com/blog/${post.slug.current}`}
                className="lg:flex-shrink-0"
              />
            </div>

            {/* Featured Image */}
            {post.mainImage && (
              <div className="relative h-64 md:h-96 lg:h-[500px] w-full overflow-hidden rounded-2xl shadow-2xl bg-muted">
                <BlogImage
                  src={urlFor(post.mainImage).width(1400).height(800).url()}
                  alt={post.mainImage.alt || post.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1200px"
                  priority
                />
              </div>
            )}
          </header>

          {/* Article Content */}
          {post.body && (
            <div className="max-w-4xl mx-auto px-4">
              <div className="prose prose-xl max-w-none prose-headings:text-foreground prose-p:text-foreground prose-strong:text-foreground prose-a:text-primary hover:prose-a:text-primary/80 prose-blockquote:border-primary prose-code:text-primary">
                <PortableText
                  value={post.body}
                  components={portableTextComponents}
                />
              </div>
            </div>
          )}

          {/* Author Bio */}
          {post.author && post.author.bio && (
            <div className="border-t border-border pt-16 mt-20">
              <div className="max-w-4xl mx-auto px-4">
                <Card className="p-8 md:p-10 bg-gradient-to-br from-background to-muted/30 border-2">
                  <div className="flex flex-col md:flex-row gap-8">
                    {post.author.image && (
                      <Avatar className="h-24 w-24 md:h-28 md:w-28 ring-4 ring-background shadow-lg mx-auto md:mx-0">
                        <AvatarImage
                          src={urlFor(post.author.image).width(112).height(112).url()}
                          alt={post.author.name}
                        />
                        <AvatarFallback className="text-3xl font-bold">
                          {post.author.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                    )}
                    <div className="flex-1 text-center md:text-left">
                      <h3 className="text-3xl font-bold text-foreground mb-4">
                        About {post.author.name}
                      </h3>
                      <div className="text-muted-foreground mb-6 text-lg leading-relaxed">
                        <PortableText
                          value={post.author.bio}
                          components={portableTextComponents}
                        />
                      </div>
                      {post.author.socialLinks && (
                        <div className="flex flex-wrap gap-3 justify-center md:justify-start">
                          {post.author.socialLinks.twitter && (
                            <Button variant="outline" size="sm" asChild className="hover:bg-blue-50 hover:border-blue-300">
                              <a href={post.author.socialLinks.twitter} target="_blank" rel="noopener noreferrer">
                                <Twitter className="h-4 w-4 mr-2" />
                                Twitter
                              </a>
                            </Button>
                          )}
                          {post.author.socialLinks.linkedin && (
                            <Button variant="outline" size="sm" asChild className="hover:bg-blue-50 hover:border-blue-300">
                              <a href={post.author.socialLinks.linkedin} target="_blank" rel="noopener noreferrer">
                                <Linkedin className="h-4 w-4 mr-2" />
                                LinkedIn
                              </a>
                            </Button>
                          )}
                          {post.author.socialLinks.website && (
                            <Button variant="outline" size="sm" asChild className="hover:bg-gray-50 hover:border-gray-300">
                              <a href={post.author.socialLinks.website} target="_blank" rel="noopener noreferrer">
                                <ExternalLink className="h-4 w-4 mr-2" />
                                Website
                              </a>
                            </Button>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          )}

          {/* Related Posts */}
          {relatedPosts.length > 0 && (
            <div className="border-t border-border pt-16 mt-20">
              <div className="max-w-6xl mx-auto px-4">
                <div className="text-center mb-12">
                  <h3 className="text-3xl font-bold text-foreground mb-4">Related Posts</h3>
                  <p className="text-muted-foreground text-lg">Continue reading with these related articles</p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {relatedPosts.map((relatedPost) => (
                    <Card key={relatedPost._id} className="group hover:shadow-xl transition-all duration-300 border-2 hover:border-primary/20">
                      <CardContent className="p-0">
                        {relatedPost.mainImage && (
                          <div className="relative h-52 w-full overflow-hidden rounded-t-lg bg-muted">
                            <BlogImage
                              src={urlFor(relatedPost.mainImage).width(400).height(200).url()}
                              alt={relatedPost.mainImage.alt || relatedPost.title}
                              fill
                              className="object-cover group-hover:scale-105 transition-transform duration-300"
                              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            />
                          </div>
                        )}
                        <div className="p-6">
                          <h4 className="text-xl font-semibold text-foreground group-hover:text-primary transition-colors line-clamp-2 mb-3 leading-tight">
                            {relatedPost.title}
                          </h4>
                          {relatedPost.excerpt && (
                            <p className="text-muted-foreground line-clamp-3 mb-4 leading-relaxed">
                              {relatedPost.excerpt}
                            </p>
                          )}
                          <div className="flex items-center justify-between text-sm text-muted-foreground pt-2 border-t border-border">
                            {relatedPost.author && (
                              <span className="font-medium">{relatedPost.author.name}</span>
                            )}
                            <span>{format(new Date(relatedPost.publishedAt), 'MMM dd')}</span>
                          </div>
                          <Link
                            href={`/blog/${relatedPost.slug.current}`}
                            className="absolute inset-0"
                          />
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </div>
          )}
        </article>
        </div>
      </div>
    </>
  )
}

// Generate metadata for SEO
export async function generateMetadata({ params }: BlogPostPageProps) {
  const { slug } = await params
  const post = await getBlogPost(slug)

  if (!post) {
    return {
      title: 'Post Not Found | AdMesh Blog',
      description: 'The requested blog post could not be found.',
    }
  }

  const title = post.seo?.metaTitle || post.title
  const description = post.seo?.metaDescription || post.excerpt || `Read ${post.title} on the AdMesh blog`
  const keywords = post.seo?.keywords || []
  const noIndex = post.seo?.noIndex || false

  return {
    title: `${title} | AdMesh Blog`,
    description,
    keywords: keywords.length > 0 ? keywords.join(', ') : undefined,
    authors: post.author ? [{ name: post.author.name }] : undefined,
    robots: noIndex ? 'noindex, nofollow' : 'index, follow',
    openGraph: {
      title,
      description,
      type: 'article',
      publishedTime: post.publishedAt,
      modifiedTime: post.updatedAt,
      authors: post.author ? [post.author.name] : undefined,
      tags: post.categories?.map(cat => cat.title) || undefined,
      images: post.mainImage
        ? [
            {
              url: urlFor(post.mainImage).width(1200).height(630).url(),
              width: 1200,
              height: 630,
              alt: post.mainImage.alt || post.title,
            },
          ]
        : undefined,
      siteName: 'AdMesh',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: post.mainImage
        ? [urlFor(post.mainImage).width(1200).height(630).url()]
        : undefined,
      creator: post.author?.socialLinks?.twitter ? `@${post.author.socialLinks.twitter.split('/').pop()}` : undefined,
    },
    alternates: {
      canonical: `/blog/${post.slug.current}`,
    },
  }
}
