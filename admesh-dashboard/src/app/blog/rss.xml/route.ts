import { client, blogPostsQuery } from '@/lib/sanity'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const posts = await client.fetch(blogPostsQuery)
    
    const rssItems = posts
      .slice(0, 20) // Limit to 20 most recent posts
      .map((post: any) => {
        const pubDate = new Date(post.publishedAt).toUTCString()
        const link = `https://useadmesh.com/blog/${post.slug.current}`
        
        return `
          <item>
            <title><![CDATA[${post.title}]]></title>
            <description><![CDATA[${post.excerpt || post.title}]]></description>
            <link>${link}</link>
            <guid isPermaLink="true">${link}</guid>
            <pubDate>${pubDate}</pubDate>
            ${post.author ? `<author><EMAIL> (${post.author.name})</author>` : ''}
            ${post.categories?.map((cat: any) => `<category>${cat.title}</category>`).join('') || ''}
          </item>
        `
      })
      .join('')

    const rssXml = `<?xml version="1.0" encoding="UTF-8"?>
      <rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
        <channel>
          <title>AdMesh Blog</title>
          <description>Insights, updates, and stories from the AdMesh ecosystem</description>
          <link>https://useadmesh.com/blog</link>
          <atom:link href="https://useadmesh.com/blog/rss.xml" rel="self" type="application/rss+xml"/>
          <language>en-US</language>
          <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>
          <managingEditor><EMAIL> (AdMesh Team)</managingEditor>
          <webMaster><EMAIL> (AdMesh Team)</webMaster>
          <generator>AdMesh Blog RSS Generator</generator>
          <image>
            <url>https://useadmesh.com/logo.svg</url>
            <title>AdMesh Blog</title>
            <link>https://useadmesh.com/blog</link>
            <width>200</width>
            <height>200</height>
          </image>
          ${rssItems}
        </channel>
      </rss>`

    return new NextResponse(rssXml, {
      headers: {
        'Content-Type': 'application/rss+xml; charset=utf-8',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600',
      },
    })
  } catch (error) {
    console.error('Error generating RSS feed:', error)
    return new NextResponse('Error generating RSS feed', { status: 500 })
  }
}
