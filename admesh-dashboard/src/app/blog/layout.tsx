import { Metadata } from 'next'

export const metadata: Metadata = {
  title: {
    template: '%s | AdMesh Blog',
    default: 'AdMesh Blog - AI Marketing Insights & Updates',
  },
  description: 'Discover the latest insights, updates, and stories from the AdMesh ecosystem. Learn about AI marketing, performance optimization, and industry trends.',
  keywords: ['AI marketing', 'performance marketing', 'AdMesh blog', 'marketing insights', 'AI agents', 'generative engine optimization'],
  authors: [{ name: 'AdMesh Team' }],
  creator: 'AdM<PERSON>',
  publisher: 'AdMesh',
  robots: 'index, follow',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://useadmesh.com/blog',
    siteName: 'AdMesh',
    title: 'AdMesh Blog - AI Marketing Insights & Updates',
    description: 'Discover the latest insights, updates, and stories from the AdMesh ecosystem.',
    images: [
      {
        url: 'https://useadmesh.com/og-images/blog-og.png',
        width: 1200,
        height: 630,
        alt: 'AdMesh Blog',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AdMesh Blog - AI Marketing Insights & Updates',
    description: 'Discover the latest insights, updates, and stories from the AdMesh ecosystem.',
    images: ['https://useadmesh.com/og-images/blog-og.png'],
    creator: '@useadmesh',
    site: '@useadmesh',
  },
  alternates: {
    canonical: 'https://useadmesh.com/blog',
    types: {
      'application/rss+xml': [
        {
          url: 'https://useadmesh.com/blog/rss.xml',
          title: 'AdMesh Blog RSS Feed',
        },
      ],
    },
  },
  other: {
    'article:publisher': 'https://www.facebook.com/useadmesh',
    'article:author': 'https://www.linkedin.com/company/useadmesh',
  },
}

export default function BlogLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <>
      {/* JSON-LD Structured Data for Blog */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'Blog',
            name: 'AdMesh Blog',
            description: 'Insights, updates, and stories from the AdMesh ecosystem',
            url: 'https://useadmesh.com/blog',
            publisher: {
              '@type': 'Organization',
              name: 'AdMesh',
              url: 'https://useadmesh.com',
              logo: {
                '@type': 'ImageObject',
                url: 'https://useadmesh.com/logo.svg',
                width: 200,
                height: 200,
              },
              sameAs: [
                'https://twitter.com/useadmesh',
                'https://linkedin.com/company/useadmesh',
                'https://github.com/useadmesh',
              ],
            },
            inLanguage: 'en-US',
            copyrightYear: new Date().getFullYear(),
            copyrightHolder: {
              '@type': 'Organization',
              name: 'AdMesh',
            },
          }),
        }}
      />
      
      {/* Breadcrumb Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'BreadcrumbList',
            itemListElement: [
              {
                '@type': 'ListItem',
                position: 1,
                name: 'Home',
                item: 'https://useadmesh.com',
              },
              {
                '@type': 'ListItem',
                position: 2,
                name: 'Blog',
                item: 'https://useadmesh.com/blog',
              },
            ],
          }),
        }}
      />
      
      {children}
    </>
  )
}
