import { client, blogPostsQuery } from '@/lib/sanity'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const posts = await client.fetch(blogPostsQuery)
    
    const blogUrls = posts.map((post: any) => {
      const lastmod = post.updatedAt || post.publishedAt
      return `
        <url>
          <loc>https://useadmesh.com/blog/${post.slug.current}</loc>
          <lastmod>${new Date(lastmod).toISOString()}</lastmod>
          <changefreq>weekly</changefreq>
          <priority>0.8</priority>
        </url>
      `
    }).join('')

    const sitemapXml = `<?xml version="1.0" encoding="UTF-8"?>
      <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
        <url>
          <loc>https://useadmesh.com/blog</loc>
          <lastmod>${new Date().toISOString()}</lastmod>
          <changefreq>daily</changefreq>
          <priority>0.9</priority>
        </url>
        ${blogUrls}
      </urlset>`

    return new NextResponse(sitemapXml, {
      headers: {
        'Content-Type': 'application/xml; charset=utf-8',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600',
      },
    })
  } catch (error) {
    console.error('Error generating blog sitemap:', error)
    return new NextResponse('Error generating sitemap', { status: 500 })
  }
}
