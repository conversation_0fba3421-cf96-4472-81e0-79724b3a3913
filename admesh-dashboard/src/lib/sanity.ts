import { createClient } from 'next-sanity'
import imageUrlBuilder from '@sanity/image-url'

export const client = createClient({
  projectId: 'h0kukqbi',
  dataset: 'production',
  apiVersion: '2024-01-01',
  useCdn: false, // Set to false to ensure fresh data
})

// Get a pre-configured url-builder from your sanity client
const builder = imageUrlBuilder(client)

// Then we like to make a simple function like this that gives the
// builder an image and returns the builder for you to specify additional
// parameters:
export function urlFor(source: any) {
  return builder.image(source)
}

// GROQ queries for blog posts
export const blogPostsQuery = `*[_type == "post"] | order(publishedAt desc) {
  _id,
  title,
  slug,
  publishedAt,
  updatedAt,
  excerpt,
  mainImage{
    asset,
    alt
  },
  featured,
  readingTime,
  author->{
    _id,
    name,
    slug,
    image{
      asset,
      alt
    }
  },
  categories[]->{
    _id,
    title,
    slug,
    color
  },
  "estimatedReadingTime": round(length(pt::text(body)) / 5 / 180 )
}`

export const blogPostQuery = `*[_type == "post" && slug.current == $slug][0] {
  _id,
  title,
  slug,
  publishedAt,
  updatedAt,
  excerpt,
  mainImage{
    asset,
    alt
  },
  body,
  featured,
  readingTime,
  author->{
    _id,
    name,
    slug,
    image{
      asset,
      alt
    },
    bio,
    email,
    socialLinks
  },
  categories[]->{
    _id,
    title,
    slug,
    description,
    color
  },
  seo,
  "estimatedReadingTime": round(length(pt::text(body)) / 5 / 180 )
}`

export const featuredPostsQuery = `*[_type == "post" && featured == true] | order(publishedAt desc) [0...3] {
  _id,
  title,
  slug,
  publishedAt,
  excerpt,
  mainImage{
    asset,
    alt
  },
  author->{
    name,
    image{
      asset,
      alt
    }
  },
  categories[]->{
    title,
    color
  }
}`

export const relatedPostsQuery = `*[_type == "post" && _id != $postId && count(categories[@._ref in $categoryIds]) > 0] | order(publishedAt desc) [0...3] {
  _id,
  title,
  slug,
  publishedAt,
  excerpt,
  mainImage{
    asset,
    alt
  },
  author->{
    name,
    image{
      asset,
      alt
    }
  }
}`

export const categoriesQuery = `*[_type == "category"] | order(title asc) {
  _id,
  title,
  slug,
  description,
  color,
  "postCount": count(*[_type == "post" && references(^._id)])
}`

export const authorsQuery = `*[_type == "author"] | order(name asc) {
  _id,
  name,
  slug,
  image{
    asset,
    alt
  },
  bio,
  "postCount": count(*[_type == "post" && references(^._id)])
}`

// TypeScript types for blog posts
export interface SanityImage {
  asset: {
    _ref: string
  }
  alt?: string
}

export interface Author {
  _id: string
  name: string
  slug: {
    current: string
  }
  image?: SanityImage
  bio?: any[] // Portable Text content
  email?: string
  socialLinks?: {
    twitter?: string
    linkedin?: string
    github?: string
    website?: string
  }
}

export interface Category {
  _id: string
  title: string
  slug: {
    current: string
  }
  description?: string
  color: string
  postCount?: number
}

export interface SEOSettings {
  metaTitle?: string
  metaDescription?: string
  keywords?: string[]
  noIndex?: boolean
}

export interface BlogPost {
  _id: string
  title: string
  slug: {
    current: string
  }
  publishedAt: string
  updatedAt?: string
  excerpt?: string
  mainImage?: SanityImage
  body?: any[] // Portable Text content
  featured?: boolean
  readingTime?: number
  estimatedReadingTime?: number
  author?: Author
  categories?: Category[]
  seo?: SEOSettings
}

// Utility functions
export function calculateReadingTime(text: string): number {
  const wordsPerMinute = 200
  const words = text.trim().split(/\s+/).length
  return Math.ceil(words / wordsPerMinute)
}

export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}

export function getCategoryColor(color: string): string {
  const colors: Record<string, string> = {
    blue: 'bg-blue-100 text-blue-800',
    green: 'bg-green-100 text-green-800',
    purple: 'bg-purple-100 text-purple-800',
    red: 'bg-red-100 text-red-800',
    orange: 'bg-orange-100 text-orange-800',
    yellow: 'bg-yellow-100 text-yellow-800',
    pink: 'bg-pink-100 text-pink-800',
    gray: 'bg-gray-100 text-gray-800',
  }
  return colors[color] || colors.gray
}
